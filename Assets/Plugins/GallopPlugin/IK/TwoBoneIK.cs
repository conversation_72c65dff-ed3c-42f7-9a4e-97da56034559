using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace IK
{
    /// <summary>
    /// TwoBone IK
    /// </summary>
    public class TwoBoneIK
    {
        private class JointInfo
        {
            public Transform transform;
            public float distance;
        }

        private const int JointNum = 3; //root,center,endと必ず3つになる
        private const int Root = 0;
        private const int Center = 1;
        private const int EndEffector = 2;

        private JointInfo[] _joint;

        public enum EffectorRotationType
        {
            None,   //何もしない
            World,  //ワールド空間上で回転する
            Local,  //ローカル空間上で回転する
        }

        /// <summary>
        /// IK目標地点
        /// </summary>
        public Vector3 TargetPosition { set; get; }

        /// <summary>
        /// 関節の曲がる方向(Pole Vector)
        /// </summary>
        public Vector3 JointTargetPosition { set; get; }

        public Transform EndEffectorTransform
        {
            get { return _joint[EndEffector].transform; }
        }

        /// <summary>
        /// ブレンド率
        /// </summary>
        public float BlendAlpha { set; get; }

        /// <summary>
        /// 末端エフェクターの回転角度
        /// </summary>
        public Quaternion EndEffectorRotation { set; get; }

        public EffectorRotationType EndEffectorRotationType { set; get; }

        public void RecalcJointDistance()
        {
            for (int i = Root; i < EndEffector; i++)
            {
                _joint[i].distance = Vector3.Distance(_joint[i].transform.position, _joint[i + 1].transform.position);
            }
        }

        public void Initialize(Transform endEffector)
        {
            var current = endEffector;

            _joint = new JointInfo[JointNum];
            _joint[EndEffector] = new JointInfo();
            _joint[EndEffector].transform = current;

            current = current.parent;

            _joint[Center] = new JointInfo();
            _joint[Center].transform = current;

            current = current.parent;

            _joint[Root] = new JointInfo();
            _joint[Root].transform = current;

            RecalcJointDistance();
        }

        public void Solve()
        {
            float alpha = BlendAlpha;
            if (alpha <= 0.0f)
                return;

            //Rootの空間で計算する事でWorld座標変換が不要となるので高速化出来る余地がある
            var rootPosition = _joint[Root].transform.position;
            var jointPosition = _joint[Center].transform.position;
            var endEffectorPosition = _joint[EndEffector].transform.position;

            var upperLength = _joint[Root].distance;
            var lowerLength = _joint[Center].distance;

            var targetDir = TargetPosition - rootPosition;
            var targetDistance = targetDir.magnitude;
            if(targetDistance < Quaternion.kEpsilon)
            {
                targetDistance = Quaternion.kEpsilon;
                targetDir = Helper.AxisUp;
            }
            else
            {
                targetDir = targetDir / targetDistance;
            }

            //Jointが曲がる方向を決める
            Vector3 jointBendDir;
            {
                var jointTargetPos = JointTargetPosition;
                var jointTargetDelta = jointTargetPos - rootPosition;
                var jointTargetLengthSqr = jointTargetDelta.sqrMagnitude;

                if (jointTargetLengthSqr < Helper.SqrEpsilon)
                {
                    jointBendDir = Helper.AxisUp;
                }
                else
                {
                    var jointPlaneNormal = Vector3.Cross(targetDir, jointTargetDelta);
                    if (jointPlaneNormal.sqrMagnitude < Helper.SqrEpsilon)
                    {
                        //大きさが０で向きが求まらないので、適当な向きを決める
                        Helper.FindBestAxisVectors(jointPlaneNormal, out jointPlaneNormal, out jointBendDir);
                    }
                    else
                    {
                        jointBendDir = Vector3.Normalize(jointTargetDelta - (targetDir * Vector3.Dot(jointTargetDelta, targetDir)));
                    }
                }
            }

            //Two Bone IKメイン処理
            var outEndPos = TargetPosition;
            var outJointPos = jointPosition;

            float maxLength = (upperLength + lowerLength);
            if (targetDistance > maxLength)
            {
                outEndPos = rootPosition + (maxLength* targetDir);
                outJointPos = rootPosition + (upperLength * targetDir);
            }
            else
            {
                float twoAB = (2.0f * upperLength * targetDistance);
                var cosAngle = (twoAB != 0.0f) ? ((upperLength * upperLength) + (targetDistance*targetDistance) - (lowerLength * lowerLength)) / twoAB : 0.0f;

                //3角形を作る事が出来ないので、解が求まらない
                if ((cosAngle > 1.0f) || (cosAngle < -1.0f))
                {
                    //正方向に戻す事で合わせられるようにする
                    if (upperLength > lowerLength)
                    {
                        outJointPos = rootPosition + (upperLength * targetDir);
                        outEndPos = outJointPos - (lowerLength * targetDir);
                    }
                    else
                    {
                        outJointPos = rootPosition - (upperLength * targetDir);
                        outEndPos = outJointPos + (lowerLength * targetDir);
                    }
                }
                else
                {
                    //Rootと目標地点の角度
                    float angle = Mathf.Acos(cosAngle);
                    bool reverseUpperBone = (cosAngle < 0.0f);

                    float jointLineDistance = upperLength * Mathf.Sin(angle);
                    float projJointDistSqr = (upperLength * upperLength) - (jointLineDistance * jointLineDistance);
                    float projJointDist = (projJointDistSqr > 0.0f) ? Mathf.Sqrt(projJointDistSqr) : 0.0f;
                    if (reverseUpperBone)
                    {
                        projJointDist *= -1.0f;
                    }

                    outJointPos = rootPosition + (projJointDist * targetDir) + (jointLineDistance * jointBendDir);
                }
            }

            //Rootを曲げる
            {
                var newDir = Vector3.Normalize(outJointPos - rootPosition);
                var oldDir = Vector3.Normalize(jointPosition - rootPosition);
                var dot = Vector3.Dot(oldDir, newDir);
                if (dot < Helper.DotSmall)
                {
                    var finalRotation = Quaternion.Lerp(Helper.QuaternionIdentity, Quaternion.FromToRotation(oldDir, newDir), alpha);
                    _joint[Root].transform.rotation = finalRotation * _joint[Root].transform.rotation;

                    //Rootが動いた事による子の座標更新
                    jointPosition = _joint[Center].transform.position;
                    endEffectorPosition = _joint[EndEffector].transform.position;
                }
            }

            //Jointを曲げる
            {
                var newDir = Vector3.Normalize(outEndPos - jointPosition);
                var oldDir = Vector3.Normalize(endEffectorPosition - jointPosition);

                var dot = Vector3.Dot(oldDir, newDir);
                if (dot < Helper.DotSmall)
                {
                    var finalRotation = Quaternion.Lerp(Helper.QuaternionIdentity, Quaternion.FromToRotation(oldDir, newDir), alpha);
                    _joint[Center].transform.rotation = finalRotation * _joint[Center].transform.rotation;
                }
            }

            //EndEffector角度を何に合わせるか
            switch(EndEffectorRotationType)
            {
                case EffectorRotationType.Local:
                    _joint[EndEffector].transform.localRotation = Quaternion.Lerp(_joint[EndEffector].transform.localRotation,EndEffectorRotation,alpha);
                    break;
                case EffectorRotationType.World:
                    _joint[EndEffector].transform.rotation = Quaternion.Lerp(_joint[EndEffector].transform.rotation, EndEffectorRotation, alpha);
                    break;
            }
        }
    }
}