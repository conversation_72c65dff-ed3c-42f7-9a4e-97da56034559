#if CYG_DEBUG
using UnityEngine.Serialization;
using Random = System.Random;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;
using System;

namespace Gallop
{

    public class RaceDirectSceneController : SceneControllerBase<RaceDirectScene>
    {
        public override void BeginScene()
        {
            _scene.IsSceneLoaded = true;
        }

        public override IEnumerator InitializeScene()
        {
            yield return _scene.InitializeScene();

            yield return base.InitializeScene();
        }

        public override void RegisterDownload(DownloadPathRegister register)
        {
            RaceUtil.RegisterCharaModelAssets(register);

            register.RegisterPathWithoutInfo(ResourcePath.RaceParamDefinePath);

            var storyRacePathArray = GetStoryRaceAssetName(true);
            foreach (var storyRacePath in storyRacePathArray)
            {
                register.RegisterPathWithoutInfo(storyRacePath);
            }
        }

        public static string[] GetStoryRaceAssetName(bool isPath)
        {
            return MasterDataManager.Instance.masterMainStoryData.dictionary.Values
                .Where(m => m.GetStoryRaceId() > 0)
                .Select(m =>
                    isPath
                        ? ResourcePath.GetStoryRaceReplayPath(m.GetStoryRaceId())
                        : ResourcePath.GetStoryRaceReplayName(m.GetStoryRaceId()))
                .ToArray();
        }
    }

    /// <summary>
    /// レース直起動
    /// </summary>
    public class RaceDirectScene : DirectSceneBase
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        #region <定義>
        public enum LoadType
        {
            None,
            Play,
            Simulation,
            ImportJson,
        }

        public const int NPC_GROUP_ID_NULL = 0;

        // dress_data.csvのid。デフォルトは体操服。
        private const int DEFAULT_DRESS_ID = (int)ModelLoader.DressID.TrackSuit;

        private const int DEFAULT_PLAYER_INDEX = 0;

        private const int FILTER_GRADE_NULL = 0;
        private const int FILTER_COURSE_ID_NULL = 0;
        private const RaceDefine.RaceGroup FILTER_RACEGROUP_NULL = RaceDefine.RaceGroup.None;

        private const GameDefine.BgSeason SEASON_SETTING_START = GameDefine.BgSeason.None/*GameDefine.BgSeason.Spring*/;
        private const RaceDefine.Weather WEATHER_SETTING_START = RaceDefine.Weather.Sunny;
        private const RaceDefine.GroundCondition GROUND_CONDITION_SETTING_START = RaceDefine.GroundCondition.Good;
        private const RaceDefine.Time TIME_SETTING_START = RaceDefine.Time.Morning;

        private static readonly RaceDefine.RaceGroup[] RACEGROUP_INDEX_ARRAY = EnumUtil.GetEnumArray<RaceDefine.RaceGroup>();

        private const int RANDOM_SKILL_NUM = 5;

        private const int TEAM_STADIUM_SIMULATE_ROUND = 3; // チーム競技場レースタイトルの「第〇レース」。

        private const int TEAM_STADIUM_SELF_EVALUATE = 10000;
        private const int TEAM_STADIUM_OPPONENT_EVALUATE = 12000;
        private const int TEAM_STADIUM_WIN_COUNT = 3;
        private const int SUPPORT_CARD_BONUS = 500;
        
        private const string EDITOR_PREFS_PREFIX = "RaceDirect_";
        private const string EDITOR_PREFS_IS_SIMULATE_SERVER = EDITOR_PREFS_PREFIX + "IsSimulateServer";
        private const string EDITOR_PREFS_SIMULATE_APP_VER = EDITOR_PREFS_PREFIX + "SimulateServerAppVer";
        private const string EDITOR_PREFS_SIMULATE_RES_VER = EDITOR_PREFS_PREFIX + "SimulateServerResVer";
        private const string SIMULATER_SERVER_APP_VER_DEFAULT = "1.0.0";
        private const string SIMULATER_SERVER_RES_VER_DEFAULT = "10000000";
        #endregion


        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        #region <変数>
        public static RaceDirectScene Instance { get; private set; }
        public override bool IsNeedLogin() { return _isNeedLogin; }
        public override bool IsInvalidDayChange() { return true; }
        public static bool IsBoot { get; private set; }

        [SerializeField]
        private RaceSkillCutInReserveCreator.UniqueRareSkillCutInInfo[] _availableCutInArray = null;

        [SerializeField]
        private Canvas _canvas = null;
        [SerializeField]
        private bool _isNeedLogin = true;

        [SerializeField] 
        private RectTransform _rightMenuScrollContentRoot = null;

        [SerializeField]
        private Toggle _eventCameraToggle = null;

        [SerializeField]
        private Dropdown _raceDropDown = null;

        private List<int> _raceInstanceIdList = new List<int>(256);

        [SerializeField]
        private Dropdown _raceTypeDropDown = null;

        [SerializeField]
        private Dropdown _courseDropDown = null;
        private List<int> _courseIdList = new List<int>(16);

        [SerializeField]
        private Dropdown _gradeDropDown = null;
        private List<int> _gradeList = new List<int>(8);

        [SerializeField]
        private Toggle _randomRace = null;
        [SerializeField]
        private Toggle _randomChara = null;

        [SerializeField]
        private InputField _inputRaceInstanceId = null;

        [SerializeField]
        private Button _resetRandomSeedButton = null;
        
        private bool _loadScene = false;
        private Transform _keepDefaultParent;

        [SerializeField]
        private Dropdown _randomRankDropDown = null;

        [SerializeField]
        private Dropdown _jikkyouVoiceDropDown = null;

        /// <summary>プレイヤー不在レースのトグル。</summary>
        [SerializeField]
        private Toggle _isNotPlayerExistToggle = null;

        [SerializeField]
        private Dropdown _storyRaceSelectDropDown = null;

        [SerializeField]
        private Toggle _screenLandscapeToggle = null;

        [SerializeField]
        private Toggle _startGateOnlyToggle = null;

        [SerializeField]
        private RaceDirectAgingSetting _agingSetting = null;

        [SerializeField]
        private Button _startRaceButton = null;

        [SerializeField]
        private Text _startRaceButtonCaption = null;

        [SerializeField]
        private Toggle _overrunEndlessToggle = null;

        [SerializeField]
        private Toggle _championsToggle = null;

        /// <summary>
        /// ライバル戦フラグ
        /// ライバルリザルト確認用
        /// </summary>
        [SerializeField]
        private Toggle _rivalToggle = null;

        [SerializeField]
        private Button _skillCutInButton = null;
        [SerializeField]
        private RaceDirectSkillCutInSelect _skillCutInSetting = null;
        
        private int _horseNum = RaceDefine.RACE_HORSE_MAX;

        [SerializeField]
        private Toggle _skipMemberListToggle = null;
        [SerializeField]
        private Toggle _skipRaceTitleToggleToggle = null;
        [SerializeField]
        private Toggle _skipGateInToggle = null;

        private GameDefine.FinalTrainingRank _randomRank = GameDefine.FinalTrainingRank.None;

        /// <summary>パドックへ遷移するかどうかのトグル。</summary>
        [SerializeField]
        private Toggle _paddockToggle = null;
        /// <summary>StartRaceでレースではなくパドックへ遷移する。</summary>
        private bool _isPaddock = false;
        /// <summary>パドック遷移にLoadRaceInfo必要なので保持。</summary>
        private static RaceInitializer.LoadRaceInfo _loadRaceInfo;
        
        [SerializeField]
        private Toggle _isUseScenarioToggle = null;

        [SerializeField]
        private RaceDirectCharaStatusSetting _charaStatusSetting = null;
        [SerializeField]
        private Button _charaStatusSettingButton = null;

        [SerializeField]
        private RaceDirectCharaPresetSetting _charaPresetSetting = null;
        [SerializeField]
        private Button _charaPresetSettingButton = null;

        [SerializeField]
        private Text _benchButtonLabel = null;

        [SerializeField]
        private Text _benchScoreLabel = null;

        [SerializeField]
        private RaceDirectCharaSelect _charaSelect = null;

        [SerializeField]
        private AssetHolder _assets = null;

        [SerializeField]
        private Camera _camera = null;

        public bool IsSceneLoaded { get; set; }

        /// <summary>管理画面からjsonをロードするボタン。</summary>
        [SerializeField]
        private Button _importJsonFromAdminToolButton = null;
        
        [SerializeField]
        private Toggle _serverSimulateToggle = null;
        [SerializeField]
        private InputField _serverSimulateAppVerInput = null;
        [SerializeField]
        private InputField _serverSimulateResVerInput = null;
        [SerializeField]
        private ImageCommon _serverSimulateBg = null;

        [SerializeField] 
        private Dropdown _teamStadiumDropdown = null;

        [SerializeField]
        private Toggle _useAudienceImpostorsToggle = null;

        /// <summary>
        /// レースランダム選択のON/OFF。
        /// </summary>
        public bool IsRandomRace
        {
            get { return _randomRace.isOn; }
            set { _randomRace.isOn = value; }
        }

        private int _storyRaceSelectIndex;
        private string[] _storyRaceReplayNameArray;

        // レース後に戻ってきても変えたくない値。----- (ここから) -----
        public static LoadType _loadType = LoadType.None;
        private static int _raceInstanceId = 0;

        private static RaceDefine.RaceGroup _filterRaceGroup = RaceDefine.RaceGroup.Common/*FILTER_RACEGROUP_NULL*/;
        private static int _filterCourseId = FILTER_COURSE_ID_NULL;
        private static int _filterGrade = FILTER_GRADE_NULL;

        public static int PlayerTrainedCharaIndex = 0;

        private static bool _isSetEnvToRace = false;
        private static int _seasonIndex;
        private static int _weatherIndex;
        private static int _timeIndex;
        private static int _groundConditionIndex;

        private static bool _isChampionsMode = false;

        /// <summary>
        /// ライバル戦用フラグ
        /// ライバルリザルト確認用
        /// </summary>
        private static bool _isRivalMode = false;
        public static bool IsRandomCharaParam { get; set; }
        private static bool _isSkillRandom = false;

        public static bool IsAutoRaceReplay { get; set; }
        private static List<string> _jsonFileNames = new List<string>();

        public static bool IsSimulateServer { get; set; } = false;
        public static string SimulateAppVer { get; set; } = SIMULATER_SERVER_APP_VER_DEFAULT;
        public static string SimulateResVer { get; set; } = SIMULATER_SERVER_RES_VER_DEFAULT;
        // レース後に戻ってきても変えたくない値。----- (ここまで) -----
        #endregion


        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期選択レースのランダム選択。
        /// </summary>
        private void SelectRaceInstanceIdRandom()
        {
            ClearFilter();

            // RaceInstanceIdの抽選。
            var allRaceInstance = GetSelectableRace();
            int index = UnityEngine.Random.Range(0, allRaceInstance.Count);
            var raceInstanceElement = allRaceInstance[index];

            SetRaceInstanceId(raceInstanceElement.Id, true);
        }

        /// <summary>
        /// 指定のRaceInstanceIdに基づいて選択中のグレード、競馬場を更新。
        /// </summary>
        private void UpdateSelectGradeAndCourseId(int raceInstanceId)
        {
            var raceInstanceMaster = MasterDataManager.Instance.masterRaceInstance.Get(raceInstanceId);
            if (null == raceInstanceMaster)
            {
                return;
            }

            // 選ばれたレースに基づいて、グレード、競馬場を更新。
            MasterRace.Race raceMaster = MasterDataManager.Instance.masterRace.Get(raceInstanceMaster.RaceId);
            if (null == raceMaster)
            {
                return;
            }
            var courseSetMaster = MasterDataManager.Instance.masterRaceCourseSet.Get(raceMaster.CourseSet);
            if (null == courseSetMaster)
            {
                return;
            }

            _filterGrade = raceMaster.Grade;
            _filterCourseId = courseSetMaster.RaceTrackId;

            SetupGradeDropDown();
            SetupCourseDropDown();
        }

        public static bool IsSelectableRaceGroup(RaceDefine.RaceGroup group)
        {
            return Array.IndexOf(RACEGROUP_INDEX_ARRAY, group) >= 0;
        }

        private static int _GetFirstNpcGroupId()
        {
            var masterNpc = MasterDataManager.Instance.masterSingleModeNpc.GetListAllEntries();
            return masterNpc[0].NpcGroupId;
        }

        public static int GetRaceInstanceId() { return _raceInstanceId; }
        public void SetRaceInstanceId(int raceInstanceId, bool isUpdateRaceDropDown)
        {
            _raceInstanceId = raceInstanceId;

            // 指定レースでの出走人数を反映。
            _ApplyRaceHorseNum();

            // レース選択ドロップダウンの表示を更新。※常に行うと同ドロップダウンで値変更時にこの関数が呼ばれた場合、スタックオーバーフローするため選択式にしてある。
            if (isUpdateRaceDropDown)
            {
                ClearFilter();
                UpdateFilter(raceInstanceId);
                SetupRaceDropDown();
            }
        }

        public IEnumerator InitializeScene()
        {
#if UNITY_EDITOR
            // UnityEditor実行時は画面の変更を行わない。
#else
            // 強制的に縦画面にする。
            if (Screen.ScreenOrientation != ScreenOrientation.Portrait)
            {
                yield return Screen.ChangeScreenOrientationPortraitAsync();
            }
#endif

            UIManager.Instance.SetCameraTargetFromUITexture(_camera);
            _canvas.worldCamera = UIManager.UICamera;
            _canvas.planeDistance = 0;

            Instance = this;
            IsBoot = false;

            //開始ボタンを押せなくしておく
            _startRaceButton.interactable = false;
            _startRaceButtonCaption.text = "初期化中";

            //iPhoneX対応を切替可能にするように、UImanagerの子供に移動しておく
            var scaler = GetComponentInParent<CanvasScaler>();
            _keepDefaultParent = scaler.transform.parent;
            scaler.transform.SetParent(UIManager.Instance.transform);

#if UNITY_EDITOR
            if (RaceSimulateParam.IsActive)
            {
                IsRandomRace = false;
                if (0 == _raceInstanceId)
                {
                    _raceInstanceId = RaceInfo.DEFAULT_RACE_INSTANCE_ID;
                }
            }
            else
#endif
            if (0 == _raceInstanceId)
            {
                // レースランダム選択デフォルトON。※この値代入によりランダム選択も行われる。
                IsRandomRace = true;
            }

            // イベントカメラ再生、デフォルトON。
            _eventCameraToggle.isOn = true;

            // キャラランダム選択デフォルトOFF。
            _randomChara.isOn = false;

            SetupAvailableCutIn();
            
            // RaceNpc.csvの最初のグループをデフォルトとして使用する。
            RaceSimulateParam.CreateInstance(_GetFirstNpcGroupId(), DEFAULT_PLAYER_INDEX);
            RaceSimulateParam.Instance.ResetRaceScenario();
            InitServerSimulateUI();
            UpdateUmabenchText();
            SetupCourseDropDown();
            SetupRaceDropDown();
            SetupRaceGroupDropDown();
            SetupGradeDropDown();
            SetupRandomRankDropDown();
            _charaSelect.Init();
            _skillCutInSetting.Init();
            InitRaceEnvSelect();
            SetupJikkyoVoiceDropDown();
            SetupStoryRaceSelectDropDown();
            SetupScreenLandscapeToggle();
            SetupStartGateToggle();
            SetupOverrunEndlessToggle();
            SetupTeamStadiumToggle();
            SetupChampionsToggle();
            SetupRivalToggle();
            SetupNotPlayerExistToggle();
            SetupSkillCutInButton();
            SetupResetRandomSeedButton();
            IsBoot = true;

            _startRaceButton.interactable = true;
            _startRaceButtonCaption.text = "Start Race";

            _skipMemberListToggle.isOn = RaceInfo.IsSkipMemberList;
            _skipRaceTitleToggleToggle.isOn = RaceDebugger.IsSkipRaceTitle;
            _skipGateInToggle.isOn = RaceDebugger.IsDebugSkipGateIn;

            _importJsonFromAdminToolButton.onClick.RemoveAllListeners();
            _importJsonFromAdminToolButton.onClick.AddListener(OnClickAdminToolJson);

            _useAudienceImpostorsToggle.isOn = RaceQualitySettings.IsAudienceImpostors;

#if GALLOP_BOT
            RaceDebugger.IsDebugBenchMode = true;
            LoadRaceReplay();
#endif

            // json連続再生が有効であれば、自動レース開始。
            if (HasAutoPlayJson())
            {
                _loadType = LoadType.Simulation;
                IsAutoRaceReplay = true;
            }

            // キャラステータス編集画面初期化。
            _charaStatusSetting.Init(this);
            _charaStatusSettingButton.onClick.RemoveAllListeners();
            _charaStatusSettingButton.onClick.AddListener(OnPush_CharaStatusSetting);

            // キャラプリセット画面初期化。
            _charaPresetSetting.Init(this);
            _charaPresetSettingButton.onClick.RemoveAllListeners();
            _charaPresetSettingButton.onClick.AddListener(OnPush_CharaPresetSetting);

            // パドック遷移トグル初期化。
            _paddockToggle.isOn = false;
            _paddockToggle.onValueChanged.RemoveAllListeners();
            _paddockToggle.onValueChanged.AddListener(OnPushToggle);
            
            // キャラのパラメータをランダム。
            if (IsRandomCharaParam)
            {
                // キャラをランダム。
                OnClickRandomChara(true);
                // キャラパラメータをランダム。
                _charaStatusSetting.RandomParam(HorseRaceInfo.STATUS_MIN, HorseRaceInfo.STATUS_MAX);
            }

            // エージング中のレース自動再生。
            if (RaceDirectAgingSetting.IsEnableAging())
            {
                _loadType = LoadType.Play;
                IsAutoRaceReplay = true;
            }

            // 入場後、自動でレース開始。
            if (IsAutoRaceReplay)
            {
                IsAutoRaceReplay = false;
                StartCoroutine(AutoRaceReplay());
            }

            // メモリのチェックを行う場合があるので極力クリアにする。
            {
                // キャラモデルのキャッシュをクリアする。
                CharaModelCache.Instance.ClearCache();

                // 未使用アセットをアンロードする。
                yield return Resources.UnloadUnusedAssets();

                // 明示的にガベージコレクションを実行する。
                GC.Collect();
            }

            // 横画面の時は右側のメニューをスクロール可能にし、サイズ調整。
            if(Screen.Width > Screen.Height)
            {
                SetupRightMenuForLandscape();
            }
            
            yield break;
        }

        private void SetupRightMenuForLandscape()
        {
            _rightMenuScrollContentRoot.localScale = new Vector3(0.5f, 0.5f, 1);
            var verticalLayout = _rightMenuScrollContentRoot.GetComponent<VerticalLayoutGroup>();
            verticalLayout.padding.right = -280;
            verticalLayout.childForceExpandHeight = true;
            LayoutRebuilder.ForceRebuildLayoutImmediate(_rightMenuScrollContentRoot);
        }

        private void SetupResetRandomSeedButton()
        {
            _resetRandomSeedButton.onClick.RemoveAllListeners();
            _resetRandomSeedButton.onClick.AddListener(OnClickResetRandomSeed);
        }

        private void OnClickResetRandomSeed()
        {
            RaceSimulateParam.Instance.ResetRandomSeed();
        }

        private void SetupSkillCutInButton()
        {
            _skillCutInButton.onClick.RemoveAllListeners();
            _skillCutInButton.onClick.AddListener(OnClickSkillCutIn);
        }

        private void OnClickSkillCutIn()
        {
            if (!IsBoot)
            {
                return;
            }
            ShowObjectFromAssetHolder("SkillCutInSelect");
            _skillCutInSetting.SetupOnOpen();
        }
        
        private IEnumerator AutoRaceReplay()
        {
            yield return new WaitWhile(() => { return !IsBoot || !IsSceneLoaded; });
            StartRace();
        }

        public void OnRaceStartReplayButtonClick()
        {
            if (!IsBoot)
            {
                Debug.LogWarning("初期化中！！！");
                return;
            }
            StartRace();
        }

    #region <サーバーシミュレート>
        public IEnumerator SimulateServerFromRaceInfo(string simulateVersion, string resourceVersion, RaceInfo raceInfo)
        {
            var teamMember = raceInfo.RaceHorse.FirstOrDefault(h => h.TeamId != HorseData.TEAM_ID_NULL);
            int scoreCalcTeamId = teamMember != null ? teamMember.TeamId : HorseData.TEAM_ID_NULL;
            
            var req = new DebugRaceSimulateRequest()
            {
                simulator_version = simulateVersion,
                simulate_resource_version = resourceVersion,
                random_seed = UnityEngine.Random.Range(1, int.MaxValue),
                race_instance_id = _raceInstanceId,
                season = (int)raceInfo.Season,
                weather = (int)raceInfo.Weather,
                ground_condition = (int)raceInfo.GroundCondition,
                race_horse_data = raceInfo.RaceHorse.Select(h => h.ResponseHorseData).ToArray(),
                
                race_type = teamMember != null ? (int)RaceDefine.RaceType.TeamStadium : 1,
                self_evaluate = TEAM_STADIUM_SELF_EVALUATE,
                opponent_evaluate = TEAM_STADIUM_OPPONENT_EVALUATE,
                score_calc_team_id = scoreCalcTeamId,
                win_count = TEAM_STADIUM_WIN_COUNT,
                support_card_bonus = SUPPORT_CARD_BONUS,
                challenge_match_difficulty = (int)RaceDefine.Difficulty.Hard,
                challenge_match_retry_count = 0,
            };

            bool isEndSimulate = false;
            req.Send(res =>
            {
                // サーバーで計算された人気を受け取る。
                for (int i = 0; i < res.data.result_array.Length; ++i)
                {
                    var horseResult = res.data.result_array[i];
                    var horseData = raceInfo.RaceHorse[i];
                    horseData.DbgSetPopularity(horseResult.popularity);
                    horseData.DbgSetPopularityMarkRank(horseResult.popularity_mark_rank_array);
                }
                // HorseDataに設定された人気をもとに、RaceInfo.HorseIndexByPopularityを初期化。
                RaceManager.RaceInfo.DbgInitHorseIndexByPopularity();

                // シミュレート結果のシナリオをレースで再生するため、RaceInfoに設定。
                isEndSimulate = true;
                RaceSimulateParam.Instance.SetupRaceScenario(res.data.race_scenario);
                raceInfo.SetAndDeserializeBase64(res.data.race_scenario);

                // スコア獲得結果をログ出力。
                {
                    var strBuf = new System.Text.StringBuilder();
                    foreach (var result in res.data.result_array)
                    {
                        if (result.viewer_id != Certification.ViewerId)
                        {
                            continue;
                        }

                        strBuf.AppendLine($"{result.finish_order}位");
                        foreach (var score in result.score.score_array)
                        {
                            strBuf.AppendLine($"\t{score.raw_score_id}x{score.num}={score.score}pt");
                            strBuf.AppendLine($"\tボーナス内訳");
                            foreach (var bonus in score.bonus_array)
                            {
                                strBuf.AppendLine($"\t\t{bonus.score_bonus_id}={bonus.bonus_score}pt");
                            }
                        }
                    }
                    Debug.Log(strBuf);
                }

                // チーム競技場モード用のWorkData生成。
                if (HasTeamMember())
                {
                    CreateWorkTeamStadiumDataByResponseData(res.data.result_array, res.data.result_team);
                }
            });

            yield return new WaitWhile(() => !isEndSimulate);
        }
        
    #endregion

        private void PrepareStoryRace()
        {
            var assetName = GetStoryRaceReplayAssetName();
            if (string.IsNullOrEmpty(assetName))
            {
                return;
            }

            StoryRaceEditorSceneController.SetupStoryRace(assetName, false);
            RaceSimulateParam.Instance.IsUseScenario = true;
        }

        /// <summary>
        /// レース再生。
        /// </summary>
        private void StartRace()
        {
            // ボタン連打防止。
            if (_loadScene)
            {
                return;
            }

            // ビューワーIDが割り振られていない場合、ユーザーが作られていない。
            DebugUtils.Assert(Gallop.Certification.ViewerId > 0, "ビューワーIDが割り振られていません。_Bootシーンでタイトル画面から進んでユーザーを作成してください");

            if (_raceInstanceId <= 0)
            {
                DebugUtils.Assert(false, "不正なレースインスタンスID:" + _raceInstanceId);
                return;
            }

            // ストーリーレースは_loadType変更されないので他のLoadTypeよりも先に判定。
            RaceDebugger.IsRaceDirectStoryRace = IsReqStoryRace();
            if (IsReqStoryRace())
            {
                PrepareStoryRace();
            }
            else if (_loadType == LoadType.ImportJson)
            {
                // RaceInfo生成済みのため処理不要。
            }
            else
            {
                if (RaceDirectAgingSetting.IsEnableAging())
                {
                    RaceDirectAgingSetting.IsExportJson = true;
                    var loadRaceInfo = _agingSetting.CreateLoadRaceInfo();
                    CreateRaceInfo(loadRaceInfo);
                }
                else
                {
                    // jsonから自動再生。
                    if (HasAutoPlayJson())
                    {
                        ImportRaceInfoFromJsonQueue(true);
                    }

                    SetUseScenarioToggle(RaceSimulateParam.Instance.IsUseScenario);
                    var loadRaceInfo = CreateLoadRaceInfoFromSimulateParam();
                    CreateRaceInfo(loadRaceInfo);
                }
            }

            // ※※※　この時点でレース開始のためのRaceManager.RaceInfoは生成済み　※※※

            RaceInfo.IsSkipMemberList |= _skipMemberListToggle.isOn || RaceDirectAgingSetting.IsEnableAging();
            RaceDebugger.IsDebugSimulationMode = (_loadType == LoadType.Simulation);

            // レースの環境情報を設定する。
            // 時間帯だけはRaceInfo生成時に外部から指定する機能を提供していないため、ここで強制上書き。
            if (_isSetEnvToRace)
            {
                RaceManager.RaceInfo.DbgSetCourseTime(TIME_SETTING_START + _timeIndex);
            }

            //Canvasの階層位置を戻す
            var scaler = GetComponentInParent<CanvasScaler>();
            scaler.transform.SetParent(_keepDefaultParent);

            // 計算済みシナリオ使用しない場合、RaceInfoに登録済みのシナリオデータ関連の変数クリア。
            if (!RaceSimulateParam.Instance.IsUseScenario)
            {
                if (RaceManager.RaceInfo != null)
                {
                    RaceManager.RaceInfo.DbgClearSimulateData();
                }
            }

            // 画面の向きの変更が入った際にUITextureへの不要な参照が残るので外す。
            _camera.targetTexture = null;

            // 明示的にガベージコレクションを実行する。
            GC.Collect();

            // 使用メモリ計測関係の初期化
            RaceDebugger.InitializeUseMemory(RaceDebugger.IsDebugBenchMode);

#if UNITY_EDITOR
            // シミュレーターツールからのレース起動の場合は専用のViewへ遷移。
            if (RaceDebugger.IsDebugSimulationMode)
            {
                if (RaceSimulateParam.Instance.IsUseScenario)
                {
                    InitByScenario();
                }
                else
                {
                    var info = new RaceSimulatorToolViewInfo()
                    {
                        LoadRaceInfo = _loadRaceInfo,
                        IsRandom = RaceSimulateParam.Instance.IsRandomByTry,
                        RandomStatusMin = RaceSimulateParam.Instance.RandomStatusMin,
                        RandomStatusMax = RaceSimulateParam.Instance.RandomStatusMax,
                    };
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.RaceSimulatorTool, info);
                    _loadScene = true;
                }
            }
            // それ以外のレース起動は通常のRaceMainViewへ遷移。
            else
#endif
            {
                StartCoroutine(GoToRace());
                _loadScene = true;
            }
        }

    #if UNITY_EDITOR
        /// <summary>
        /// jsonに出力されたレースシナリオをもとに2Dシミュレーター用の初期化を行う。
        /// </summary>
        private void InitByScenario()
        {
            if (!RaceDebugger.IsDebugSimulationMode || !RaceSimulateParam.Instance.IsUseScenario)
            {
                return;
            }

            // 坂とコーナーは本来RaceManagerが初期化してくれるが、jsonで2Dシミュレーター初期化するときはRaceManagerがいないので自前で初期化する必要がある。
            {
                // 坂の初期化。
                var paramDefine = ResourceManager.LoadOnScene<RaceParamDefine>(ResourcePath.RaceParamDefinePath);
                string laneName = ResourcePath.GetLanePath(
                    RaceManager.RaceInfo.RaceTrackId,
                    RaceManager.RaceInfo.CourseDistance,
                    RaceManager.RaceInfo.GroundType,
                    RaceManager.RaceInfo.Inout,
                    RaceManager.RaceInfo.RaceType);
                var courseLaneAnim = ResourceManager.LoadOnScene<CourseLaneAnim>(laneName);
                var isNeedRotationFix = RaceManager.RaceInfo.RotationCategory != RaceDefine.Rotation.Left;
                var courseLane = new CourseLane();
                courseLane.Init(
                    isNeedRotationFix, 
                    courseLaneAnim, 
                    courseLaneAnim, 
                    RaceManager.RaceInfo.CourseDistance);
            
                // コーナーの初期化。
                string courseEventPath = ResourcePath.GetCourseParamPath(RaceManager.RaceInfo.RaceCourseSet.Id);
                var courseEventParamPrefab = ResourceManager.LoadOnView<GameObject>(courseEventPath);
                var courseEventManager = new CourseEventManager();
                courseEventManager.Init(courseEventParamPrefab);
                RaceManager.RaceInfo.Corners = RaceUtil.CalcCornerList(courseEventManager.GetCourseEventCorner(), RaceManager.RaceInfo.CourseDistance);
                RaceManager.RaceInfo.SlopeList = RaceUtil.CalcSlopeList(courseEventManager.GetCourseEventSlope());
            }

            // RaceSimulateLogにレースシナリオとRaceInfoを設定することで、2Dシミュレーター開けるようになる。
            RaceSimulateLog.SetSimulateData(RaceSimulateParam.Instance.SimulateData);
            RaceSimulateLog.SetRaceInfo(RaceManager.RaceInfo);
        }
    #endif
        
        private IEnumerator GoToRace()
        {
            DebugUtils.Assert(RaceManager.RaceInfo != null);

            bool isNeedTeamStadiumWorkData = true;
            
            // シナリオが既にRaceInfoにセットされているなら、シミュレートは不要。
            if (!RaceManager.RaceInfo.HasRaceSimData())
            {
                bool isSimulateServer = IsSimulateServer;
#if UNITY_EDITOR
                // サーバーシミュレートさせるのは通常の１回シミュレートの場合のみ。
                // 複数回のシミュレートを走らせる場合は、サーバー負荷と速度を考慮し、サーバーシミュレートさせない。
                isSimulateServer &= (RaceSimulateLog.TryCount == 1);
#endif
                if (isSimulateServer)
                {
                    yield return SimulateServerFromRaceInfo(
                        SimulateAppVer,
                        SimulateResVer,
                        RaceManager.RaceInfo);
                    
                    // サーバーシミュレートした場合はレスポンスデータからWorkDataを生成しているので、ここでの生成は不要。
                    isNeedTeamStadiumWorkData = false;
                }
                else
                {
                    yield return RaceSimulator.LoadAndSimulate();
                }
            }

            // チーム競技場モードで起動するなら、スコア獲得ダイアログ・スコア詳細ダイアログのためにworkdata捏造。
            if (_isChampionsMode)
            {
                CreateWorkChampionsDataDummy();
            }
            if (HasTeamMember() && isNeedTeamStadiumWorkData)
            {
                CreateWorkTeamStadiumDataDummy();
            }
            if (RaceManager.RaceInfo.RaceType == RaceDefine.RaceType.Practice)
            {
                CreatePracticeTempDataDummy();
            }
            
            // SimulateRaceRoundはチーム競技場レースタイトルで「第〇レース」のテクスチャを読み込むのに指定しておく必要がある。
            WorkDataManager.Instance.TeamStadiumData.TeamStadiumStatus.SimulateRaceRound = TEAM_STADIUM_SIMULATE_ROUND;

            // パドックへ遷移。
            if (_isPaddock)
            {
                var viewInfo = new PaddockViewControllerBase.ViewInfo
                {
                    raceInfo = _loadRaceInfo,
                    prevViewId = SceneDefine.ViewId.HomeHub,
                    raceProgramId = MasterDataManager.Instance.masterSingleModeProgram.dictionary.Values.ToList()[0].Id,
                };
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.SingleModePaddock, viewInfo);
            }
            // レースへ遷移。
            else
            {
                RaceInitializer.GoToRace();
            }
        }

        /// <summary>
        /// 練習モードのレースに必要なTempDataの生成。
        /// </summary>
        private void CreatePracticeTempDataDummy()
        {
            TempData.Instance.PracticeRaceData.RaceMainHorseIndexList.Clear();
            foreach (var horse in RaceManager.RaceInfo.RaceHorse)
            {
                if (horse.IsUser)
                {
                    TempData.Instance.PracticeRaceData.RaceMainHorseIndexList.Add(horse.horseIndex);
                }
            }
        }

        /// <summary>
        /// チャンピオンズミーティングモードのレースリザルトで必要なWorkDataの生成。
        /// </summary>
        private void CreateWorkChampionsDataDummy()
        {
            var simHorseData = RaceSimulateParam.Instance.SimulateHorseData;
            
            var entryList = WorkDataManager.Instance.ChampionsData.MatchingData.EntryList;
            entryList.Clear();

            // チーム数は３人x３チーム。
            const int TEAM_NUM = 3;
            
            // SetChampionsStatusで割り振るteam_idが1始まりなので、1からスタート。
            for (int team = 1; team <= TEAM_NUM; ++team)
            {
                var user = new ChampionsRoomUser();
                
                // 1チーム目がプレイヤーのチーム。
                user.viewer_id = team == 1 ? Certification.ViewerId : 0;
                user.name = "";
                user.honor_id = 100101; // 適当。
                user.team_id = team;

                var simHorseByTeam = simHorseData.Where(x => x.TeamId == team).ToArray();
                user.entry_chara_array = new ChampionsUserChara[simHorseByTeam.Length];
                for (int i = 0; i < simHorseByTeam.Length; ++i)
                {
                    user.entry_chara_array[i] = new ChampionsUserChara();
                    user.entry_chara_array[i].chara_id = simHorseByTeam[i].CharaId;
                    user.entry_chara_array[i].team_member_id = simHorseByTeam[i].TeamMemberId;
                    user.entry_chara_array[i].race_cloth_id = simHorseByTeam[i].RaceDressId;
                }
                
                var entryData = new WorkChampionsData.ChampionsEntryData(user);
                entryList.Add(entryData);
            }
        }
        
        /// <summary>
        /// チーム競技場モードのレースリザルトで必要なWorkDataの生成。
        /// </summary>
        /// <remarks>
        /// クライアントエラーが出ないように最低限の情報を入れるだけなので、レースシミュレートによる実際のスコア獲得情報などを入れるわけではない。
        /// </remarks>
        private void CreateWorkTeamStadiumDataDummy()
        {
            MasterTeamStadiumRawScore.TeamStadiumRawScore GetTeamRawScoreRandom()
            {
                var teamScoreList = MasterDataManager.Instance.masterTeamStadiumRawScore.GetListWithConditionTypeOrderByPriorityAsc((int) HorseScoreCalculator.ConditionType.TeamResult);
                int index = UnityEngine.Random.Range(0, teamScoreList.Count);
                return teamScoreList[index];
            }
            
            List<MasterTeamStadiumRawScore.TeamStadiumRawScore> GetRawScoreRandom(int num)
            {
                var retList = new List<MasterTeamStadiumRawScore.TeamStadiumRawScore>(num);
                var masterRawScore = MasterDataManager.Instance.masterTeamStadiumRawScore.dictionary.Values.ToList();
                for (int i = 0; i < num; ++i)
                {
                    int index = UnityEngine.Random.Range(0, masterRawScore.Count);
                    retList.Add(masterRawScore[index]);
                }

                // 着差によるスコアを追加。レースリザルトでの演出確認用。
                {
                    var finishDiffMasters = masterRawScore.Where(m => m.ConditionType == (int) HorseScoreCalculator.ConditionType.FinishDiff).ToList();
                    int index = UnityEngine.Random.Range(0, finishDiffMasters.Count());
                    retList.Add(finishDiffMasters[index]);
                }
                
                // 着順によるスコアを追加。レースリザルトでの演出確認用。
                {
                    var finishOrderMasters = masterRawScore.Where(m => m.ConditionType == (int) HorseScoreCalculator.ConditionType.FinishOrder).ToList();
                    int index = UnityEngine.Random.Range(0, finishOrderMasters.Count());
                    retList.Add(finishOrderMasters[index]);
                }
                
                return retList;
            }

            
            //-------------------------------------------------------
            // キャラのリザルト情報構築。
            //-------------------------------------------------------
            int playerCount = RaceSimulateParam.Instance.SimulateHorseData.Count(x => x.IsPlayer);
            var horseResultArray = new RaceSimulateResult[RaceManager.RaceInfo.RaceHorse.Length];
            for (int i = 0; i < horseResultArray.Length; ++i)
            {
                var fromHorseData = RaceManager.RaceInfo.RaceHorse[i];
                horseResultArray[i] = new RaceSimulateResult();
                horseResultArray[i].finish_order = fromHorseData.FinishOrder;
                horseResultArray[i].popularity = fromHorseData.Popularity;
                horseResultArray[i].popularity_mark_rank_array = new int[]
                {
                    fromHorseData.PopularityRankLeft,
                    fromHorseData.PopularityRankCenter,
                    fromHorseData.PopularityRankRight
                };
                horseResultArray[i].time = 0; // 参照しないので0。
                horseResultArray[i].single_mode_chara_id = 0;
                
                // プレイヤーチームメンバーとして登録した数分だけ、自分のViewerIdを入れる。このデータがレースのリザルトUIで読みだされる。
                horseResultArray[i].viewer_id = i < playerCount ? Certification.ViewerId : 0;

                // 適当な個数のスコアを獲得したことにする。
                var fromScoreList = GetRawScoreRandom(3);
                
                var score = new TeamStadiumResultHorseScore();
                score.score_array = new TeamStadiumScoreData[fromScoreList.Count];
                for (int scoreIndex = 0; scoreIndex < fromScoreList.Count; ++scoreIndex)
                {
                    score.score_array[scoreIndex] = new TeamStadiumScoreData();
                    score.score_array[scoreIndex].raw_score_id = fromScoreList[scoreIndex].Id;
                    score.score_array[scoreIndex].num = 1;
                    score.score_array[scoreIndex].score = fromScoreList[scoreIndex].Score;
                    score.score_array[scoreIndex].bonus_array = new TeamStadiumBonusData[0];
                }
                horseResultArray[i].score = score;
            }

            //-------------------------------------------------------
            // チームのリザルト情報構築。
            //-------------------------------------------------------
            // 適当なチームスコアを獲得したことにする。
            var fromTeamScore = GetTeamRawScoreRandom();
            var playerTeamMember = RaceManager.RaceInfo.RaceHorse.FirstOrDefault(x => x.IsUser && x.TeamId != HorseData.TEAM_ID_NULL);
            var teamResult = new TeamStadiumResultTeam();
            teamResult.team_id = playerTeamMember.TeamId;
            teamResult.team_score_array = new TeamStadiumScoreData[1];
            teamResult.team_score_array[0] = new TeamStadiumScoreData();
            teamResult.team_score_array[0].raw_score_id = fromTeamScore.Id;
            teamResult.team_score_array[0].num = 1;
            teamResult.team_score_array[0].score = fromTeamScore.Score;
            teamResult.team_score_array[0].bonus_array = new TeamStadiumBonusData[0];
            teamResult.team_total_score = 0;
            
            // レスポンスデータをもとにWorkDataの生成。
            CreateWorkTeamStadiumDataByResponseData(horseResultArray, teamResult);
        }
        
        /// <summary>
        /// レスポンスデータをもとにチーム競技場モードのWorkDataを生成。
        /// </summary>
        private void CreateWorkTeamStadiumDataByResponseData(RaceSimulateResult[] horseResultArray, TeamStadiumResultTeam teamResult)
        {
            const int TEAM_CLASS = 3;

            var status = WorkDataManager.Instance.TeamStadiumData.TeamStadiumStatus;
            var trainedList = WorkDataManager.Instance.TrainedCharaData.List;

            var playerTeamMember = RaceManager.RaceInfo.RaceHorse.FirstOrDefault(x => x.IsUser && x.TeamId != HorseData.TEAM_ID_NULL);
            
            //-------------------------------------------------------
            // 現在のチームクラス設定。
            //-------------------------------------------------------
            var indexResDummy = new TeamStadiumIndexResponse()
            {
                data = new TeamStadiumIndexResponse.CommonResponse()
                {
                    team_stadium_user = new TeamStadiumUser()
                    {
                        team_class = TEAM_CLASS,
                    },
                },
            };
            WorkDataManager.Instance.TeamStadiumData.DbgSetTeamStadiumInfo(new TeamStadiumInfo(indexResDummy));

            //-------------------------------------------------------
            // 5レース分の結果を作る。
            //-------------------------------------------------------
            var raceHorseArray = RaceSimulateParam.Instance.SimulateHorseData.Select(h => h.horseData).ToArray();
            var raceResultArray = new TeamStadiumRaceResult[TeamStadiumDefine.MAX_RACE_NUMBER];
            var raceStartParams = new TeamStadiumRaceStartParams[TeamStadiumDefine.MAX_RACE_NUMBER];
            for (int i = 0; i < raceResultArray.Length; ++i)
            {
                int round = TeamStadiumDefine.MIN_RACE_NUMBER + i;
                
                raceResultArray[i] = new TeamStadiumRaceResult();
                raceResultArray[i].round = round;
                raceResultArray[i].team_total_score = teamResult.team_total_score;
                raceResultArray[i].win_type = (int)TeamStadiumDefine.RoundResultType.Win;
                
                raceStartParams[i] = new TeamStadiumRaceStartParams();
                raceStartParams[i].race_horse_data_array = raceHorseArray; 
                raceStartParams[i].race_instance_id = RaceManager.RaceInfo.RaceInstanceId;
                raceStartParams[i].round = round;
                
                //-------------------------------------------------------
                // チームとして獲得したスコア情報構築。
                //-------------------------------------------------------
                raceResultArray[i].team_score_array = new TeamStadiumResultScoreData[teamResult.team_score_array.Length];
                for (int scoreIndex = 0; scoreIndex < raceResultArray[i].team_score_array.Length; ++scoreIndex)
                {
                    var scoreFrom = teamResult.team_score_array[scoreIndex];
                    raceResultArray[i].team_score_array[scoreIndex] = new TeamStadiumResultScoreData()
                    {
                        raw_score_id = scoreFrom.raw_score_id,
                        num = scoreFrom.num,
                        score = scoreFrom.score,
                        bonus_array = new TeamStadiumResultBonusData[scoreFrom.bonus_array.Length],
                    };
                    for (int bonusIndex = 0;
                        bonusIndex < raceResultArray[i].team_score_array[scoreIndex].bonus_array.Length;
                        ++bonusIndex)
                    {
                        var bonusFrom = scoreFrom.bonus_array[bonusIndex];
                        raceResultArray[i].team_score_array[scoreIndex].bonus_array[bonusIndex] = new TeamStadiumResultBonusData()
                        {
                            score_bonus_id = bonusFrom.score_bonus_id,
                            bonus_score = bonusFrom.bonus_score,
                        };
                    }
                }
                
                //-------------------------------------------------------
                // キャラ毎の獲得したスコア情報構築。
                //-------------------------------------------------------
                int userCount = 0;
                int userCountMax = horseResultArray.Count(x => x.viewer_id == Certification.ViewerId);
                raceResultArray[i].chara_result_array = new TeamStadiumRaceCharaResult[userCountMax];
                for (int j = 0; j < horseResultArray.Length; ++j)
                {
                    var charaFrom = horseResultArray[j];
                    if (charaFrom.viewer_id != Certification.ViewerId)
                    {
                        continue;
                    }
                    
                    int trainedIndex = Mathf.Min(userCount, trainedList.Count-1);
                    var trainedChara = trainedList[trainedIndex];
                    if (trainedChara == null)
                    {
                        continue;
                    }

                    raceResultArray[i].chara_result_array[j] = new TeamStadiumRaceCharaResult();
                    raceResultArray[i].chara_result_array[j].viewer_id = charaFrom.viewer_id;
                    raceResultArray[i].chara_result_array[j].finish_order = charaFrom.finish_order;
                    raceResultArray[i].chara_result_array[j].finish_time = 0;
                    raceResultArray[i].chara_result_array[j].frame_order = RaceUtil.HorseIndex2FrameOrder(j);
                    raceResultArray[i].chara_result_array[j].team_id = playerTeamMember.TeamId;
                    raceResultArray[i].chara_result_array[j].trained_chara_id = trainedChara.Id;
                    raceResultArray[i].chara_result_array[j].score_array = new TeamStadiumResultScoreData[charaFrom.score.score_array.Length];
                    for (int scoreIndex = 0; scoreIndex < charaFrom.score.score_array.Length; ++scoreIndex)
                    {
                        var scoreFrom = charaFrom.score.score_array[scoreIndex];
                        raceResultArray[i].chara_result_array[j].score_array[scoreIndex] = new TeamStadiumResultScoreData()
                        {
                            raw_score_id = scoreFrom.raw_score_id,
                            num = scoreFrom.num,
                            score = scoreFrom.score,
                            bonus_array = new TeamStadiumResultBonusData[scoreFrom.bonus_array.Length],
                        };
                        for (int bonusIndex = 0; bonusIndex < scoreFrom.bonus_array.Length; ++bonusIndex)
                        {
                            var bonusFrom = scoreFrom.bonus_array[bonusIndex];
                            raceResultArray[i].chara_result_array[j].score_array[scoreIndex].bonus_array[bonusIndex] = new TeamStadiumResultBonusData()
                            {
                                score_bonus_id = bonusFrom.score_bonus_id,
                                bonus_score = bonusFrom.bonus_score,
                            };
                        }
                    }

                    if (++userCount >= userCountMax)
                    {
                        break;
                    }
                }
            }

            var res = new TeamStadiumStartResponse.CommonResponse()
            {
                race_result_array = raceResultArray,
                race_start_params_array = raceStartParams,
            };
            status.SetResult(new WorkTeamStadiumData.TeamStadiumResult(res));
        }

        public RaceInitializer.LoadRaceInfo CreateLoadRaceInfoFromSimulateParam()
        {
            // シミュレーションモードの場合はRaceSimulateParamからパラメータを引っ張る
            var simParam = RaceSimulateParam.Instance;
            GetHorseDataBySimulationData(out var horseList);
            _horseNum = horseList.Length;

            // シミュレーターツールの「シナリオ再生」にチェックが入っているならシナリオ再生を行う。
            string scenario = simParam.IsUseScenario ? simParam.RaceScenario : string.Empty;

            // 季節が指定されていればその季節を使う
            var season = simParam.Season != GameDefine.BgSeason.None
                ? simParam.Season
                : GetSeasonArrowDateError(_raceInstanceId);

            var raceType = RaceDefine.RaceType.Debug;
            int scoreCalcTeamId = HorseData.TEAM_ID_NULL;
            if (_isChampionsMode)
            {
                raceType = RaceDefine.RaceType.Champions;
            }
            else if (HasTeamMember())
            {
                raceType = RaceDefine.RaceType.TeamStadium;
                
                // 最初に見つかったチームメンバーのチームをスコア計算対象にする。
                var teamMember = horseList.FirstOrDefault(h => h.viewer_id == Certification.ViewerId && h.team_id != HorseData.TEAM_ID_NULL);
                if (teamMember != null)
                {
                    scoreCalcTeamId = teamMember.team_id;
                }
            }
            else if (horseList.Any(x => x.npc_type == (int)RaceDefine.RaceNpcType.Boss))
            {
                raceType = RaceDefine.RaceType.Legend;
            }
            
            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                _raceInstanceId,
                horseList,
                simParam.RandomSeed,
                raceType,
                season,
                simParam.Weather,
                simParam.GroundCondition,
                scenario,
                0,
                TEAM_STADIUM_OPPONENT_EVALUATE,
                scoreCalcTeamId,
                supportCardScoreBonus:SUPPORT_CARD_BONUS);
            var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);

#if UNITY_EDITOR
            loadRaceInfo.DbgTryCount = RaceSimulateLog.TryCount;
#else
            loadRaceInfo.DbgTryCount = 1;
#endif
            return loadRaceInfo;
        }

        private bool HasTeamMember()
        {
            return RaceSimulateParam.Instance.SimulateHorseData.Any(h => h.TeamId != HorseData.TEAM_ID_NULL);
        }

        /// <summary>
        /// race_instance.csvのDateカラムが0でもエラーと見なさず季節取得。
        /// </summary>
        /// <remarks>
        /// RaceDirectからだとDate=0のレースも選択できるため、エラーを出さずに季節を取得するための処理。
        /// Date=0なら春が返る。
        /// </remarks>
        private static GameDefine.BgSeason GetSeasonArrowDateError(int raceInstanceId)
        {
            var masterRaceInstance = MasterDataManager.Instance.masterRaceInstance.Get(raceInstanceId);
            if (null == masterRaceInstance)
            {
                Debug.LogError("不正なraceInstanceIdです : " + raceInstanceId);
                return GameDefine.BgSeason.Spring;
            }
            if (masterRaceInstance.Date == 0)
            {
                return GameDefine.BgSeason.Spring;
            }
            return RaceUtil.GetSeason(raceInstanceId);
        }

        public static bool HasAutoPlayJson()
        {
            return _jsonFileNames.Count > 0;
        }
        private static string _DequeueAutoPlayJson()
        {
            if (!HasAutoPlayJson())
            {
                return string.Empty;
            }
            var jsonFileName = _jsonFileNames[0];
            _jsonFileNames.RemoveAt(0);
            return jsonFileName;
        }
        private static string _GetAutoPlayJson()
        {
            if (!HasAutoPlayJson())
            {
                return string.Empty;
            }
            return _jsonFileNames[0];
        }


        private static void GetHorseDataBySimulationData(out RaceHorseData[] horseList)
        {
            var simParam = RaceSimulateParam.Instance;

            int entryNum = simParam.HorseNum;

            horseList = new Gallop.RaceHorseData[entryNum];

            for (int i = 0; i < entryNum; i++)
            {
                var simHorseData = simParam.GetSimulateHorseData(i);
                var horse = simHorseData.horseData;
                var isPlayer = simHorseData.IsPlayer;

                // 育成ランクが振られていなければ適当に割り振る。チーム競技場順位UIのキャラアイコンの確認のため。
                if (horse.final_grade == (int) GameDefine.FinalTrainingRank.None)
                {
                    horse.final_grade = UnityEngine.Random.Range(
                        (int)GameDefine.FinalTrainingRank.Min, 
                        (int)GameDefine.FinalTrainingRank.Max+1);
                }
                
                // デバッグ機能によるスキルランダム設定。
                if (_isSkillRandom)
                {
                    horse.skill_array = RaceDebugger.GetRandomSkillDataArray(RANDOM_SKILL_NUM, null);
                }

                if (!simParam.IsUseScenario)
                {
                    // プレイヤーのフラグが設定されているキャラは、ビューワーIDとトレーナー名を上書き。
                    if (isPlayer)
                    {
                        horse.viewer_id = Gallop.Certification.ViewerId;
                        horse.trainer_name = WorkDataManager.Instance.UserData != null ?
                            (string)WorkDataManager.Instance.UserData.UserName :
                            "ユーザー";
                    }
                    else
                    {
                        horse.viewer_id = 0;
                        horse.trainer_name = "NPC";
                    }
                }
                
                // Rarity代入。モブならNone,そうじゃないならcard_data.csvのDefaultRarityを入れておく。rarity入れておかないとCharacterButtonがテクスチャロード失敗する。
                if (simHorseData.IsMob)
                {
                    horse.rarity = (int)GameDefine.CardRarity.None;
                }
                else
                {
                    var masterCard = MasterDataManager.Instance.masterCardData.Get(horse.card_id);
                    if (masterCard != null)
                    {
                        horse.rarity = masterCard.DefaultRarity;
                    }
                }

                horseList[i] = horse;
            }
        }

        /// <summary>
        /// 走法適正値が最も高い走法を取得。最も高い走法が複数ある場合はランダム。
        /// </summary>
        public static RaceDefine.RunningStyle GetMaxProperRunningStyle(MasterSingleModeNpc.SingleModeNpc npc)
        {
            // 最も高い走法適正値を取得。
            var maxProper = new int[]
            {
                npc.ProperRunningStyleNige,
                npc.ProperRunningStyleSenko,
                npc.ProperRunningStyleSashi,
                npc.ProperRunningStyleOikomi
            }.Max();

            // 最も高い走法適正値を持っている走法をリストに詰める。
            var maxProperRunningStyle = new List<RaceDefine.RunningStyle>(4);
            if (maxProper == npc.ProperRunningStyleNige) { maxProperRunningStyle.Add(RaceDefine.RunningStyle.Nige); }
            if (maxProper == npc.ProperRunningStyleSenko) { maxProperRunningStyle.Add(RaceDefine.RunningStyle.Senko); }
            if (maxProper == npc.ProperRunningStyleSashi) { maxProperRunningStyle.Add(RaceDefine.RunningStyle.Sashi); }
            if (maxProper == npc.ProperRunningStyleOikomi) { maxProperRunningStyle.Add(RaceDefine.RunningStyle.Oikomi); }

            // 複数の走法が最高値を持っている可能性もあるので、その場合はランダムな走法を選ぶ。
            maxProperRunningStyle.Shuffle();
            return maxProperRunningStyle[0];
        }

        /// <summary>
        /// ステータスを元に育成ランク計算。
        /// </summary>
        /// <remarks>
        /// 本来はサーバーで計算され、通信によりクライアントに渡されてくる値だが、
        /// RaceDirectからのオフラインレースだと値が入らないため、自前で計算をする。
        /// </remarks>
        public static GameDefine.FinalTrainingRank CalcDummyFinalGrade(int speed, int stamina, int pow, int guts, int wiz)
        {
            return SingleModeDefine.GetTotalRank(speed + stamina + pow + guts + wiz);
        }

        /// <summary>
        /// コース選択ドロップダウン初期化。
        /// </summary>
        private void SetupCourseDropDown()
        {
            _courseDropDown.ClearOptions();
            _courseIdList.Clear();

            // 未選択追加。
            _courseDropDown.options.Add(new Dropdown.OptionData { text = "-" });
            _courseIdList.Add(0);

            int idx = 0;
            var allRaceTracks = MasterDataManager.Instance.masterRaceTrack.dictionary.Values.ToList();
            for (int i = 0; i < allRaceTracks.Count; i++)
            {
                _courseDropDown.options.Add(new Dropdown.OptionData { text = allRaceTracks[i].ShortName });
                _courseIdList.Add(allRaceTracks[i].Id);

                if (allRaceTracks[i].Id == _filterCourseId)
                {
                    idx = _courseDropDown.options.Count - 1;
                }
            }

            _courseDropDown.value = idx;
            _courseDropDown.RefreshShownValue();
        }

        /// <summary>
        /// レースグレード選択ドロップダウン初期化。
        /// </summary>
        private void SetupGradeDropDown()
        {
            _gradeDropDown.ClearOptions();
            _gradeList.Clear();

            // 未選択追加。
            _gradeDropDown.options.Add(new Dropdown.OptionData { text = "-" });
            _gradeList.Add(0);

            var gradeArray = new RaceDefine.Grade[]
            {
                RaceDefine.Grade.G1,
                RaceDefine.Grade.G2,
                RaceDefine.Grade.G3,
                RaceDefine.Grade.Open,
                RaceDefine.Grade.U_1600,
                RaceDefine.Grade.U_1000,
                RaceDefine.Grade.PreOpen,
                RaceDefine.Grade.NoWin,
                RaceDefine.Grade.NewHorses,
            };
            int idx = 0;
            foreach (var grade in gradeArray)
            {
                _gradeDropDown.options.Add(new Dropdown.OptionData { text = grade.Text() });
                _gradeList.Add((int)grade);

                if ((int)grade == _filterGrade)
                {
                    idx = _gradeDropDown.options.Count - 1;
                }
            }

            _gradeDropDown.value = idx;
            _gradeDropDown.RefreshShownValue();
        }

        /// <summary>
        /// レース選択ドロップダウン初期化。
        /// </summary>
        private void SetupRaceDropDown()
        {
            _raceDropDown.ClearOptions();
            _raceInstanceIdList.Clear();
            int curSelectIndex = 0;

            var selectableRace = GetSelectableRace();
            for (int i = 0, cnt = selectableRace.Count; i < cnt; ++i)
            {
                var raceInstance = selectableRace[i];
                var raceMaster = raceInstance.GetRaceMaster();
                if (null == raceMaster)
                {
                    continue;
                }
                var courseSet = raceInstance.GetRaceCourseSetMaster();
                if (null == courseSet)
                {
                    continue;
                }

                var dropDownStr = string.Format("{0} [{1}{2}{3}{4} {5}人]",
                    raceInstance.RaceNameShort,
                    courseSet.Distance,
                    ((RaceDefine.GroundType)courseSet.Ground).Text(),
                    ((RaceDefine.CourseAround)courseSet.Inout).Text(),
                    ((RaceDefine.Rotation)courseSet.Turn).Text(),
                    raceMaster.EntryNum);

                _raceDropDown.options.Add(new Dropdown.OptionData { text = dropDownStr });
                _raceInstanceIdList.Add(raceInstance.Id);

                // 選択中のRaceInstanceIdを見つけた場合、Indexを控える。
                if (raceInstance.Id == _raceInstanceId)
                {
                    curSelectIndex = _raceDropDown.options.Count - 1;
                }
            }

            RefreshRaceDropDown(curSelectIndex);
        }

        private void RefreshRaceDropDown(int idx)
        {
            _raceDropDown.value = idx;
            _raceDropDown.RefreshShownValue();
            OnChangeRaceID(idx); //指定レースが選択された状態にする
        }

        private void SetupRandomRankDropDown()
        {
            _randomRankDropDown.ClearOptions();

            for (int i = (int)GameDefine.FinalTrainingRank.None; i <= (int)GameDefine.FinalTrainingRank.SPlus; ++i)
            {
                var rank = (GameDefine.FinalTrainingRank)i;
                _randomRankDropDown.options.Add(new Dropdown.OptionData { text = rank.Text() });
            }

            _randomRankDropDown.value = 0;
            _randomRankDropDown.RefreshShownValue();
        }

        public static void ClearFilter()
        {
            _filterRaceGroup = FILTER_RACEGROUP_NULL;
            _filterGrade = FILTER_GRADE_NULL;
            _filterCourseId = FILTER_COURSE_ID_NULL;
            Instance.SetupRaceGroupDropDown();
            Instance.SetupGradeDropDown();
            Instance.SetupCourseDropDown();
        }

        public static void UpdateFilter(int raceInstanceId)
        {
            var raceInstanceMaster = MasterDataManager.Instance.masterRaceInstance.Get(raceInstanceId);
            if (null == raceInstanceMaster)
            {
                return;
            }
            var raceMaster = raceInstanceMaster.GetRaceMaster();
            if (null == raceMaster)
            {
                return;
            }
            var courseSetMaster = raceInstanceMaster.GetRaceCourseSetMaster();
            if (null == courseSetMaster)
            {
                return;
            }

            _filterRaceGroup = (RaceDefine.RaceGroup)raceMaster.Group;
            _filterGrade = raceMaster.Grade;
            _filterCourseId = courseSetMaster.RaceTrackId;
            Instance.SetupRaceGroupDropDown();
            Instance.SetupGradeDropDown();
            Instance.SetupCourseDropDown();
        }

        private static List<MasterRaceInstance.RaceInstance> GetSelectableRace()
        {
            var raceInstanceList = MasterDataManager.Instance.masterRaceInstance
                .dictionary.Values
                .Where(r => r.GetRaceMaster() != null && IsSelectableRaceGroup((RaceDefine.RaceGroup)r.GetRaceMaster().Group)) // RaceGroupだけはフィルタに優先して候補を絞る。
                .ToList();
            var retSelectableRaces = new List<MasterRaceInstance.RaceInstance>(raceInstanceList.Count);
            for (int i = 0, cnt = raceInstanceList.Count; i < cnt; ++i)
            {
                var raceInstance = raceInstanceList[i];

                var raceMaster = raceInstance.GetRaceMaster();
                var courseSetMaster = raceInstance.GetRaceCourseSetMaster();
                if (null == raceMaster || null == courseSetMaster)
                {
                    continue;
                }

                // レース種別によるフィルタ。
                if (_filterRaceGroup != FILTER_RACEGROUP_NULL)
                {
                    if (raceMaster.Group != (int)_filterRaceGroup)
                    {
                        continue;
                    }
                }

                // グレードによるフィルタ。
                if (_filterGrade != FILTER_GRADE_NULL)
                {
                    if (raceMaster.Grade != _filterGrade)
                    {
                        continue;
                    }
                }

                // 競馬場によるフィルタ。
                if (_filterCourseId != FILTER_COURSE_ID_NULL)
                {
                    if (courseSetMaster.RaceTrackId != _filterCourseId)
                    {
                        continue;
                    }
                }

                retSelectableRaces.Add(raceInstance);
            }

            return retSelectableRaces;
        }

        /// <summary>
        /// レース種類選択ドロップダウン初期化。
        /// </summary>
        private void SetupRaceGroupDropDown()
        {
            _raceTypeDropDown.ClearOptions();
            foreach (var group in RACEGROUP_INDEX_ARRAY)
            {
                _raceTypeDropDown.options.Add(new Dropdown.OptionData { text = group.ToString() });
            }
            _raceTypeDropDown.value = Array.IndexOf(RACEGROUP_INDEX_ARRAY, _filterRaceGroup);
            _raceTypeDropDown.RefreshShownValue();
        }

        /// <summary>
        /// レース種類選択更新時コールバック。
        /// </summary>
        /// <param name="idx"></param>
        public void OnChangeRaceType(int idx)
        {
            if (idx >= 0)
            {
                _filterRaceGroup = RACEGROUP_INDEX_ARRAY[idx];

                // 選択可能なレース更新。
                SetupRaceDropDown();
            }
        }

        public void OnChangeGrade(int idx)
        {
            if (idx >= 0)
            {
                _filterGrade = _gradeList[idx];

                // 選択可能なレース更新。
                SetupRaceDropDown();
            }
        }

        /// <summary>
        /// レース選択更新時コールバック。
        /// </summary>
        /// <param name="idx"></param>
        public void OnChangeRaceID(int idx)
        {
            if (idx >= 0 && idx < _raceInstanceIdList.Count)
            {
                SetRaceInstanceId(_raceInstanceIdList[idx], false);
            }
        }


        private void _ApplyRaceHorseNum()
        {
            var raceInstanceMaster = MasterDataManager.Instance.masterRaceInstance.Get(_raceInstanceId);
            if (null == raceInstanceMaster)
            {
                return;
            }

            var raceMaster = raceInstanceMaster.GetRaceMaster();
            if (null == raceMaster)
            {
                return;
            }

            _horseNum = raceMaster.EntryNum;

            if (null != RaceSimulateParam.Instance)
            {
                if (!RaceSimulateParam.Instance.IsHorseNumFix)
                {
                    RaceSimulateParam.Instance.HorseNum = _horseNum;
                }
            }
        }


        /// <summary>
        /// RaceInstanceId入力フィールド確定コールバック。
        /// </summary>
        public void OnEndEditRaceInstanceId()
        {
            if (string.IsNullOrEmpty(_inputRaceInstanceId.text)) return;
            if (!int.TryParse(_inputRaceInstanceId.text, out var raceInstanceId))
            {
                DebugUtils.Assert(false, "数値にパースできないRaceInstanceIdが入力されました。:" + _inputRaceInstanceId.text);
                return;
            }
            
            var raceInstanceMaster = MasterDataManager.Instance.masterRaceInstance.Get(raceInstanceId);
            if (null == raceInstanceMaster)
            {
                DebugUtils.Assert(false, "race_instance.csvに存在しないRaceInstanceIdが入力されました。:" + raceInstanceId);
                return;
            }
            
            ClearFilter();
            SetRaceInstanceId(raceInstanceId, true);
        }

        /// <summary>
        /// コース選択更新時コールバック。
        /// </summary>
        /// <param name="idx"></param>
        public void OnChangeCourseID(int idx)
        {
            if (idx >= 0 && idx < _courseIdList.Count)
            {
                _filterCourseId = _courseIdList[idx];

                // 選択可能なレース更新。
                SetupRaceDropDown();
            }
        }

        /// <summary>
        /// レースタイトルをスキップ。
        /// </summary>
        public void OnSkipRaceTitleToggleChange(bool value)
        {
            RaceDebugger.IsDebugSkipRaceTitle = value;
        }

        /// <summary>
        /// ゲートイン演出をスキップ。
        /// </summary>
        public void OnSkipGateInToggleChange(bool value)
        {
            RaceDebugger.IsDebugSkipGateIn = value;
        }

        public void OnBgToggleChange(bool value)
        {
            RaceDebugger.IsBgVisible = value;
        }

        public void OnUIToggleChange(bool value)
        {
            RaceDebugger.IsUIVisible = value;
        }

        public void OnMinimapUIToggleChange(bool value)
        {
            RaceDebugger.IsMinimapUIVisible = value;
        }

        public void OnClickIgnoreSkillCutin(bool value)
        {
            RaceDebugger.IsIgnoreSkillCutIn = value;
        }

        public void OnClickAlwaysDof(bool value)
        {
            RaceDebugger.IsAlwaysDof = value;
        }

        public void OnClickAlwaysBloom(bool value)
        {
            RaceDebugger.IsAlwaysBloom = value;
        }

        public void OnClickAlwaysColorCorrection(bool value)
        {
            RaceDebugger.IsAlwaysColorCorrection = value;
        }

        public void OnClickAlwaysSunShafts(bool value)
        {
            RaceDebugger.IsAlwaysSunShafts = value;
        }

        public void OnClickUseAudienceImpostors(bool value)
        {
            RaceQualitySettings.IsAudienceImpostors = value;
        }

        public void OnClickRandomRace(bool value)
        {
            // 任意のランダムなレースを選ぶため、レース選択フィルター解除。
            ClearFilter();

            if (value)
            {
                SelectRaceInstanceIdRandom();
            }
            else
            {
                SetRaceInstanceId(RaceInfo.DEFAULT_RACE_INSTANCE_ID, true);
            }
        }

        public void OnClickEventCameraEnable(bool value)
        {
            RaceCameraManager.EnableEvent = value;
        }

        public void OnClickLive(bool value)
        {
            RaceDebugger.IsRaceDirectToLive = value;
        }

        public void OnClickRandomChara(bool value)
        {
            if (value)
            {
                // NpcGroudIdをランダムに選ぶ。
                int index = UnityEngine.Random.Range(0, _charaSelect.NpcGroupIdList.Count);
                RaceSimulateParam.Instance.InitNpcData(_charaSelect.NpcGroupIdList[index]);

                // キャラパラメータをランダム。
                GameDefine.FinalTrainingRank rankMin;
                GameDefine.FinalTrainingRank rankMax;
                // 全ての育成ランクの中からランダム。
                if (_randomRank == GameDefine.FinalTrainingRank.None)
                {
                    rankMin = GameDefine.FinalTrainingRank.Min;
                    rankMax = GameDefine.FinalTrainingRank.Max;
                }
                // 選択した育成ランク±1。
                else
                {
                    rankMin = (GameDefine.FinalTrainingRank)Mathf.Max((int)_randomRank - 1, (int)GameDefine.FinalTrainingRank.Min);
                    rankMax = (GameDefine.FinalTrainingRank)Mathf.Min((int)_randomRank + 1, (int)GameDefine.FinalTrainingRank.Max);
                }

                _charaStatusSetting.RandomParamByRankRange(rankMin, rankMax);
            }

            _isSkillRandom = value;
        }

        public void OnChangeRandomRank(int idx)
        {
            _randomRank = (GameDefine.FinalTrainingRank)idx;
        }

        public void SetUseScenarioToggle(bool isOn)
        {
            _isUseScenarioToggle.isOn = isOn;
        }

        public void OnClickUseScenario(bool isUse)
        {
            RaceSimulateParam.Instance.IsUseScenario = isUse;
        }

        public void OnClickClipBoardJson()
        {
            var json = GUIUtility.systemCopyBuffer;
            if (string.IsNullOrEmpty(json))
            {
                Debug.LogError("クリップボードが空です");
                return;
            }
            var loadRaceInfo = OnInputRaceInfoJson(json);
            _loadType = LoadType.ImportJson;
            SetUseScenarioToggle(loadRaceInfo != null && !string.IsNullOrEmpty(loadRaceInfo.SimDataBase64));
        }

        public void OnClickFileJson()
        {
#if UNITY_EDITOR
            var path = LoadRaceInfoExporterJson.LoadRaceInfoPath;
            path = UnityEditor.EditorUtility.OpenFilePanel("jsonファイル", path, "json");
            if (!string.IsNullOrEmpty(path))
            {
                var loadRaceInfo = ImportRaceInfoFromJson(path);
                _loadType = LoadType.ImportJson;
                SetUseScenarioToggle(loadRaceInfo != null && !string.IsNullOrEmpty(loadRaceInfo.SimDataBase64));
            }
#endif
        }

        public void OnClickAdminToolJson()
        {
            var req = new DebugRaceInfoLoadRequest();
            req.Send(res =>
            {
                if (string.IsNullOrEmpty(res.data.json_data))
                {
                    Debug.LogError("管理画面でjson文字列がしていされていないため、jsonを取得できませんでした。");
                    return;
                }
                
                var importer = new LoadRaceInfoImporterJsonString();
                importer.ImportFromString(res.data.json_data);
                var loadRaceInfo = importer.ImportedData;
                _ApplyLoadRaceInfo(loadRaceInfo);
                _loadType = LoadType.ImportJson;
                SetUseScenarioToggle(loadRaceInfo != null && !string.IsNullOrEmpty(loadRaceInfo.SimDataBase64));
            });
        }

        public static RaceInitializer.LoadRaceInfo OnInputRaceInfoJson(string inputJson)
        {
            if (string.IsNullOrEmpty(inputJson))
            {
                return null;
            }
            return ImportRaceInfoFromString(inputJson);
        }

        public static void RegisterAutoPlayJson(string jsonDirPath)
        {
            _jsonFileNames.Clear();

            int fileLogCnt = 0;
            const int FILE_LOG_MAX = 10;

            var outputStr = new System.Text.StringBuilder();
            var files = System.IO.Directory.GetFiles(jsonDirPath, "*.json", System.IO.SearchOption.AllDirectories);
            outputStr.AppendLine(string.Format("合計{0}件。※最初の10件だけ表示", files.Length));
            foreach (var fileName in files)
            {
                _jsonFileNames.Add(fileName);
                if (++fileLogCnt < FILE_LOG_MAX)
                {
                    outputStr.AppendLine(System.IO.Path.GetFileName(fileName));
                }
            }

#if UNITY_EDITOR
            if (files.Length > 0)
            {
                UnityEditor.EditorUtility.DisplayDialog(
                    "json登録成功",
                    "以下のjsonファイルを登録しました。\n" + outputStr.ToString(),
                    "OK");
            }
            else
            {
                UnityEditor.EditorUtility.DisplayDialog(
                    "json登録失敗",
                    "jsonファイルが存在しません",
                    "OK");
            }
#endif
        }

        public static void ImportRaceInfoFromJsonQueue(bool isDequeue)
        {
            var jsonFileName = _GetAutoPlayJson();
            if (string.IsNullOrEmpty(jsonFileName))
            {
                return;
            }
            ImportRaceInfoFromJson(jsonFileName);

            if (isDequeue)
            {
                _DequeueAutoPlayJson();
            }
        }

        public static RaceInitializer.LoadRaceInfo ImportRaceInfoFromJson(string fileName = "")
        {
            var importerInfo = new LoadRaceInfoImporterJsonFromFile(fileName);
            importerInfo.ImportFromFile();

#if UNITY_EDITOR
            var simParam = RaceSimulateParam.Instance;
            if (null != simParam)
            {
                var jsonFileName = System.IO.Path.GetFileName(fileName);
                var jsonExt = System.IO.Path.GetExtension(fileName);
                simParam.JsonFileName = jsonFileName.Replace(jsonExt, "");
            }
#endif

            _ApplyLoadRaceInfo(importerInfo.ImportedData);
            return importerInfo.ImportedData;
        }
        public static RaceInitializer.LoadRaceInfo ImportRaceInfoFromString(string str)
        {
            var importerInfo = new LoadRaceInfoImporterJsonString();
            importerInfo.ImportFromString(str);
            _ApplyLoadRaceInfo(importerInfo.ImportedData);
            return importerInfo.ImportedData;
        }
        public static void ImportRaceInfoFromAsset(string fileName)
        {
            var serializedParam = ResourceManager.LoadOnView<RaceSerializedParam>(fileName);
            if (null == serializedParam)
            {
                DebugUtils.Assert(false, "assetファイルが読み込めません : " + fileName);
                return;
            }
            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(serializedParam, RaceDefine.RaceType.Debug);
            var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
            _ApplyLoadRaceInfo(loadRaceInfo);
        }
        public static void _ApplyLoadRaceInfo(RaceInitializer.LoadRaceInfo loadRaceInfo)
        {
            if (null == loadRaceInfo)
            {
                return;
            }

            // フォルダ読み込んで複数jsonを連続シミュレートするときはレースシナリオは使用しない。
            bool isInitRaceScenario = !HasAutoPlayJson();

            // RaceDirectの選択値更新。
            Instance.SetRaceInstanceId(loadRaceInfo.RaceInstaceId, true);
            // RaceSimulatorToolの設定値更新。
            RaceSimulateParam.Instance.InitByLoadRaceInfo(loadRaceInfo, isInitRaceScenario);
            RaceDebugger.CachedRaceSimulateData = RaceSimulateParam.Instance.SimulateData;
#if UNITY_EDITOR
            // レース試行回数。フォルダ読み込んで複数jsonを連続シミュレートするときはjsonの試行回数は使わない。
            if (!HasAutoPlayJson())
            {
                RaceSimulateLog.TryCount = Mathf.Max(loadRaceInfo.DbgTryCount, 1);
            }
#endif
            CreateRaceInfo(loadRaceInfo);
        }

        private static void CreateRaceInfo(RaceInitializer.LoadRaceInfo loadRaceInfo)
        {
            // 練習レースはユーザーキャラをRaceMainHorseIndexListに登録しておく。
            if (loadRaceInfo.RaceType == RaceDefine.RaceType.Practice)
            {
                const int MAIN_HORSE_MAX = 3;
                TempData.Instance.PracticeRaceData.RaceMainHorseIndexList.Clear();
                var mainHorseIndexList = loadRaceInfo.SortedRaceHorseDataArray
                    .Where(x => x.viewer_id == Certification.ViewerId)
                    .Select(x => RaceUtil.FrameOrder2HorseIndex(x.frame_order)).ToList();
                for(int i = 0, cnt = Mathf.Min(mainHorseIndexList.Count, MAIN_HORSE_MAX); i < cnt; ++i)
                {
                    TempData.Instance.PracticeRaceData.RaceMainHorseIndexList.Add(mainHorseIndexList[i]);
                }
            }
        
            // パドック遷移のためにLoadRaceInfo必要なので、退避しておく。
            _loadRaceInfo = loadRaceInfo;
            RaceInitializer.CreateRaceInfo(loadRaceInfo);
        }

#region キャラ選択画面

        /// <summary>
        /// キャラ選択
        /// </summary>
        public void OnPush_CharaSelect()
        {
            if (IsBoot)
            {
                ShowCharaSelect();
            }
        }

        /// <summary>
        /// 戻る
        /// </summary>
        public void OnPush_Back()
        {
            ShowObjectFromAssetHolder("AnchorLeft", "AnchorRight");
        }

        /// <summary>
        /// 表示物切り替え
        /// </summary>
        public void ShowObjectFromAssetHolder(string name1, string name2 = "")
        {
            //トップ画面が２つのオブジェクトから成り立っているので
            //２つパラメータを指定する
            foreach (var p in _assets.ObjectList)
            {
                var obj = p.Value as GameObject;
                if (null == obj)
                {
                    continue;
                }
                if (p.Key.Equals(name1) || p.Key.Equals(name2))
                {
                    obj.SetActive(true);
                }
                else
                {
                    obj.SetActive(false);
                }
            }
        }

        /// <summary>
        /// キャラ選択表示
        /// </summary>
        private void ShowCharaSelect()
        {
            _charaSelect.SetupOnOpen();
            ShowObjectFromAssetHolder("CharaSelect");
        }

        public void OnPush_CharaStatusSetting()
        {
            if (!IsBoot)
            {
                return;
            }
            ShowObjectFromAssetHolder("CharaStatusSetting");
            _charaStatusSetting.ApplyHorseParam2GUI();
        }

        private void OnPush_CharaPresetSetting()
        {
            if (!IsBoot)
            {
                return;
            }
            ShowObjectFromAssetHolder("CharaPresetSetting");
        }

        private void OnPushToggle(bool isPaddock)
        {
            _isPaddock = isPaddock;
        }

        public void OnPush_AgingSetting()
        {
            if (!IsBoot)
            {
                return;
            }
            ShowObjectFromAssetHolder("AgingSetting");
            _agingSetting.Open(() =>
           {
               if (RaceDirectAgingSetting.IsEnableAging())
               {
                   _loadType = LoadType.Play;
               }
               OnPush_Back();
           });
        }

        /// <summary>
        /// レース衣装Id取得。
        /// </summary>
        /// <returns>指定カードIdに設定されているレースの衣装Idを返却。</returns>
        public static int GetRaceDressId(int cardId)
        {
            var cardMaster = MasterDataManager.Instance.masterCardData.Get(cardId);
            if (null == cardMaster)
            {
                return DEFAULT_DRESS_ID;
            }

            var rarityCardData = MasterDataManager.Instance.masterCardRarityData.GetWithCardIdAndRarity(cardMaster.Id, cardMaster.DefaultRarity);
            if (null == rarityCardData)
            {
                return DEFAULT_DRESS_ID;
            }
            return rarityCardData.RaceDressId;
        }
        public static int GetRaceDressIdByCharaId(int charaId)
        {
            var charaMaster = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (charaMaster == null)
            {
                return DEFAULT_DRESS_ID;
            }
            
            var cardMaster = MasterDataManager.Instance.masterCardData.Get(charaMaster.DebugDefaultCardId);
            if (null == cardMaster)
            {
                return DEFAULT_DRESS_ID;
            }

            var rarityCardData = MasterDataManager.Instance.masterCardRarityData.GetWithCardIdAndRarity(cardMaster.Id, cardMaster.DefaultRarity);
            if (null == rarityCardData)
            {
                return DEFAULT_DRESS_ID;
            }
            return rarityCardData.RaceDressId;
        }
#endregion

        public void OnPush_SkipSetting()
        {
            if (!IsBoot)
            {
                return;
            }
            ShowObjectFromAssetHolder("RaceSkipSetting");
        }

        public void OnPush_DisplaySetting()
        {
            if (!IsBoot)
            {
                return;
            }
            ShowObjectFromAssetHolder("DisplaySetting");
        }

        public void OnPush_ResourceCheck()
        {
            if (!IsBoot)
            {
                return;
            }
            RaceResourceCheckerRaceTitle.CheckResourceRaceTitle();
            RaceResourceCheckerCourseEvent.CheckResourceCourseEvent();

            CheckSkillDemerit();
        }

        /// <summary>
        /// デメリットがあると見なされるスキルのログ出力。
        /// </summary>
        private static void CheckSkillDemerit()
        {
            var buf = new System.Text.StringBuilder(512);
            buf.AppendLine("=========== デメリットスキル一覧 ===========");
            var allSkillData = MasterDataManager.Instance.masterSkillData.dictionary.Values;
            foreach (var skillData in allSkillData)
            {
                bool isDemerit = RaceUtil.IsSkillDemerit(skillData);
                if (isDemerit)
                {
                    buf.AppendFormat("[{0}] {1}\n", skillData.Id, skillData.Name);
                }
            }
            Debug.Log(buf.ToString());
        }

        /// <summary>
        /// 実況ボイスドロップダウンの初期設定
        /// </summary>
        private void SetupJikkyoVoiceDropDown()
        {
            _jikkyouVoiceDropDown.ClearOptions();
            var list = new List<string>() { "男性", "女性", "ランダム" };
            _jikkyouVoiceDropDown.AddOptions(list);
            _jikkyouVoiceDropDown.value = (int)SaveDataManager.Instance.SaveLoader.JikkyoVoice;
            _jikkyouVoiceDropDown.RefreshShownValue();

            _jikkyouVoiceDropDown.onValueChanged.RemoveAllListeners();
            _jikkyouVoiceDropDown.onValueChanged.AddListener(OnChangeJikkyoVoice);
        }

        private void SetupStoryRaceSelectDropDown()
        {
            // ストーリーレースのassetリスト。
            // ファイル名全部入れるとドロップダウン内で文字が小さくなるので、
            // ファイル名前半の共通部分は削っておく。
            _storyRaceReplayNameArray = RaceDirectSceneController.GetStoryRaceAssetName(false);
            _storyRaceReplayNameArray = _storyRaceReplayNameArray
                .Select(s => s.Replace(ResourcePath.STORYRACE_REPLAY_NAME_PREFIX, "")).ToArray();

            _storyRaceSelectIndex = 0;

            var storyRaceNameArray = _storyRaceReplayNameArray;

            var storyRaceReplayAssetList = new List<string>() { "-" }; // 0番目は無効値。
            storyRaceReplayAssetList.AddRange(storyRaceNameArray);

            _storyRaceSelectDropDown.ClearOptions();
            _storyRaceSelectDropDown.AddOptions(storyRaceReplayAssetList);
            _storyRaceSelectDropDown.value = _storyRaceSelectIndex;
            _storyRaceSelectDropDown.RefreshShownValue();

            _storyRaceSelectDropDown.onValueChanged.RemoveAllListeners();
            _storyRaceSelectDropDown.onValueChanged.AddListener(OnChangeStoryRaceSelect);
        }

        private void OnChangeStoryRaceSelect(int index)
        {
            _storyRaceSelectIndex = index;
        }

        private bool IsReqStoryRace()
        {
            return _storyRaceSelectIndex > 0;
        }

        private string GetStoryRaceReplayAssetName()
        {
            int index = _storyRaceSelectIndex - 1;
            if (index < 0 || index >= _storyRaceReplayNameArray.Length)
            {
                return string.Empty;
            }

            return ResourcePath.STORYRACE_REPLAY_NAME_PREFIX + _storyRaceReplayNameArray[index];
        }

        /// <summary>
        /// 横画面トグルの初期化。
        /// </summary>
        private void SetupScreenLandscapeToggle()
        {
            _screenLandscapeToggle.onValueChanged.RemoveAllListeners();
            _screenLandscapeToggle.onValueChanged.AddListener(OnChangeScreenLandscape);

            _screenLandscapeToggle.isOn = SaveDataManager.Instance.SaveLoader.IsRaceLandscape;
        }

        private void OnChangeScreenLandscape(bool isEnable)
        {
            SaveDataManager.Instance.SaveLoader.IsRaceLandscape = isEnable;
        }

        /// <summary>
        /// 横画面トグルの初期化。
        /// </summary>
        private void SetupStartGateToggle()
        {
            _startGateOnlyToggle.onValueChanged.RemoveAllListeners();
            _startGateOnlyToggle.onValueChanged.AddListener(OnChangeStartGateOnly);
            _startGateOnlyToggle.isOn = RaceDebugger.IsStartGateOnly;
        }

        private void OnChangeStartGateOnly(bool isEnable)
        {
            RaceDebugger.IsStartGateOnly = isEnable;
        }

        /// <summary>
        /// オーバーラン終わらせないトグルの初期化。
        /// </summary>
        private void SetupOverrunEndlessToggle()
        {
            _overrunEndlessToggle.onValueChanged.RemoveAllListeners();
            _overrunEndlessToggle.onValueChanged.AddListener(OnChangeOverrunEndless);

            _overrunEndlessToggle.isOn = RaceDebugger.IsOverrunEndless;
        }
        
        private void OnChangeOverrunEndless(bool isEnable)
        {
            RaceDebugger.IsOverrunEndless = isEnable;
        }

        private void SetupTeamStadiumToggle()
        {
            _teamStadiumDropdown.onValueChanged.RemoveAllListeners();
            _teamStadiumDropdown.options.Clear();
            for(int i = 0; i <= RaceDefine.TEAM_STADIUM_MEMBER_MAX; ++i)
            {
                if (i == 0)
                {
                    _teamStadiumDropdown.options.Add(new Dropdown.OptionData($"---"));
                }
                else
                {
                    _teamStadiumDropdown.options.Add(new Dropdown.OptionData($"{i}人"));
                }
            }
            _teamStadiumDropdown.RefreshShownValue();
            _teamStadiumDropdown.onValueChanged.AddListener(OnChangeTeamStadiumDropdown);
        }

        private void OnChangeTeamStadiumDropdown(int memberNum)
        {
            const int TEAM_ID = 1; // 0以外ならなんでもいい。
            var allHorse = RaceSimulateParam.Instance.SimulateHorseData;
            for (int i = 0; i < allHorse.Length; ++i)
            {
                if (i < memberNum)
                {
                    allHorse[i].TeamId = TEAM_ID;
                    allHorse[i].TeamMemberId = i + 1;
                    allHorse[i].IsPlayer = true;
                }
                else
                {
                    allHorse[i].TeamId = HorseData.TEAM_ID_NULL;
                    allHorse[i].TeamMemberId = HorseData.TEAM_MEMBER_ID_NULL;
                    allHorse[i].IsPlayer = false;
                }
            }
        }

        private void SetupChampionsToggle()
        {
            _championsToggle.onValueChanged.RemoveAllListeners();
            _championsToggle.onValueChanged.AddListener(OnChangeChampionsToggle);

            _championsToggle.isOn = _isChampionsMode;
        }
        
        private void OnChangeChampionsToggle(bool isEnable)
        {
            _isChampionsMode = isEnable;
            SetChampionsStatus(_isChampionsMode);
        }

        /// <summary>
        /// ライバル戦用UI実装
        /// </summary>
        private void SetupRivalToggle()
        {
            _rivalToggle.onValueChanged.RemoveAllListeners();
            _rivalToggle.onValueChanged.AddListener(OnChangeRivalToggle);

            _rivalToggle.isOn = _isRivalMode;
        }

        /// <summary>
        /// ライバル戦切り替え
        /// </summary>
        /// <param name="isEnable"></param>
        private void OnChangeRivalToggle(bool isEnable)
        {
            _isRivalMode = isEnable;
            SetRivalMode(_isRivalMode);
        }

        /// <summary>
        /// キャラの出走情報をチャンピオンズミーティング用に設定。
        /// </summary>
        /// <param name="isChampionsMode">チャンピオンズミーティングにするかどうか。</param>
        private void SetChampionsStatus(bool isChampionsMode)
        {
            const int HORSE_NUM = 9; // レースは9人固定。
            const int PLAYER_HORSE_NUM = 3; // プレイヤーとして出走させるのは最初の３人。
            RaceSimulateParam.Instance.HorseNum = HORSE_NUM; 
            
            var allHorse = RaceSimulateParam.Instance.SimulateHorseData;
            for (int i = 0; i < allHorse.Length; ++i)
            {
                if (i < HORSE_NUM && isChampionsMode)
                {
                    allHorse[i].TeamId = (i / ChampionsDefines.ENTRY_CHARA_NUM) + 1; // 各チーム３人。TeamIdは1~なので+1する。
                    allHorse[i].TeamMemberId = (i % ChampionsDefines.ENTRY_CHARA_NUM) + 1; // TeamMemberIdは1~なので+1する。
                    allHorse[i].IsPlayer = i < PLAYER_HORSE_NUM;
                }
                else
                {
                    allHorse[i].TeamId = HorseData.TEAM_ID_NULL;
                    allHorse[i].TeamMemberId = HorseData.TEAM_MEMBER_ID_NULL;
                    allHorse[i].IsPlayer = false;
                }
            }
        }

        /// <summary>
        /// ライバルレース設定
        /// </summary>
        /// <param name="isRivalMode"></param>
        private void SetRivalMode( bool isRivalMode )
        {
            RaceSimulateParam.Instance.IsRivalMode = isRivalMode;
        }
        
        /// <summary>
        /// 実況ボイスの変更
        /// </summary>
        public void OnChangeJikkyoVoice(int idx)
        {
            SaveDataManager.Instance.SaveLoader.JikkyoVoice = (SaveDataManager.JikkyoVoiceType)idx;
            SaveDataManager.Instance.Save();
        }
        
        /// <summary>
        /// プレイヤー不在レーストグル初期化。
        /// </summary>
        private void SetupNotPlayerExistToggle()
        {
            _isNotPlayerExistToggle.onValueChanged.RemoveAllListeners();
            _isNotPlayerExistToggle.isOn = !IsExistPlayer();
            _isNotPlayerExistToggle.onValueChanged.AddListener(OnChangeNotPlayerExistToggle);
        }

        /// <summary>
        /// プレイヤー不在レーストグル変更コールバック。
        /// </summary>
        /// <param name="value"></param>
        private void OnChangeNotPlayerExistToggle(bool value)
        {
            if (value)
            {
                // 全員を非プレイヤーにする。
                var horseArray = RaceSimulateParam.Instance.SimulateHorseData;
                foreach (var horse in horseArray)
                {
                    horse.IsPlayer = false;
                }
            }
            else
            {
                // 不在レース解除時、もしプレイヤーが存在していないなら、１人目のキャラをプレイヤーにする。
                if (!IsExistPlayer())
                {
                    RaceSimulateParam.Instance.SimulateHorseData[0].IsPlayer = true;
                }
            }
        }

        private bool IsExistPlayer()
        {
            return RaceSimulateParam.Instance.SimulateHorseData.Any(h => h.IsPlayer);
        }

#region 競馬場環境

        /// <summary>
        /// レース環境設定を、レースに反映。
        /// </summary>
        public void OnPush_SetEnvToRace()
        {
            _isSetEnvToRace = true;

            RaceSimulateParam.Instance.Season = SEASON_SETTING_START + _seasonIndex;
            RaceSimulateParam.Instance.Weather = WEATHER_SETTING_START + _weatherIndex;
            RaceSimulateParam.Instance.GroundCondition = GROUND_CONDITION_SETTING_START + _groundConditionIndex;

            ShowObjectFromAssetHolder("AnchorLeft", "AnchorRight");
        }

        /// <summary>
        /// 天候選択
        /// </summary>
        public void OnPush_RaceEnvSelect()
        {
            if (IsBoot)
            {
                ShowRaceEnvSelect();
            }
        }

        private void ShowRaceEnvSelect()
        {
            ShowObjectFromAssetHolder("RaceEnvSelect");
        }

        private void InitRaceEnvSelect()
        {
            var rootObj = _assets.Get<GameObject>("RaceEnvSelect").transform.Find("Panel");
            {
                //天候設定
                var child = rootObj.Find("Weather");
                if (child != null)
                {
                    var dropDown = child.GetComponent<Dropdown>();
                    SetRaceEnv_DropDown(dropDown,
                                        (int)WEATHER_SETTING_START,
                                        (int)RaceDefine.Weather.Max,
                                        new UnityEngine.Events.UnityAction<int>
                                        (
                                            index =>
                                            {
                                                OnValueChange_WeatherSelect(index);
                                            }
                                        ),
                                        (object obj) =>
                                        {
                                            return ((RaceDefine.Weather)obj).Text();
                                        }
                                        );

                    dropDown.value = RaceSimulateParam.Instance.Weather - WEATHER_SETTING_START;
                }

                child = rootObj.Find("Condition");
                if (child != null)
                {
                    var dropDown = child.GetComponent<Dropdown>();
                    SetRaceEnv_DropDown(child.GetComponent<Dropdown>(), typeof(RaceDefine.GroundCondition),
                                        new UnityEngine.Events.UnityAction<int>
                                        (
                                            index =>
                                            {
                                                OnValueChange_GroundConditionSelect(index);
                                            }
                                        ),
                                        (object obj) =>
                                        {
                                            return ((RaceDefine.GroundCondition)obj).Text();
                                        }
                                        );
                    dropDown.value = RaceSimulateParam.Instance.GroundCondition - GROUND_CONDITION_SETTING_START;
                }

                child = rootObj.Find("Season");
                if (child != null)
                {
                    var dropDown = child.GetComponent<Dropdown>();
                    SetRaceEnv_DropDown(child.GetComponent<Dropdown>(),
                                        (int)SEASON_SETTING_START,
                                        (int)GameDefine.BgSeason.Max,
                                        new UnityEngine.Events.UnityAction<int>
                                        (
                                            index =>
                                            {
                                                OnValueChange_SeasonSelect(index);
                                            }
                                        ),
                                        (object obj) =>
                                        {
                                            return ((GameDefine.BgSeason)obj).Text();
                                        }
                                        );
                    dropDown.value = RaceSimulateParam.Instance.Season - SEASON_SETTING_START;
                }

                child = rootObj.Find("Time");
                if (child != null)
                {
                    var dropDown = child.GetComponent<Dropdown>();
                    SetRaceEnv_DropDown(child.GetComponent<Dropdown>(),
                                        (int)TIME_SETTING_START,
                                        (int)RaceDefine.Time.Max,
                                        new UnityEngine.Events.UnityAction<int>
                                        (
                                            index =>
                                            {
                                                OnValueChange_TimeSelect(index);
                                            }
                                        ),
                                        (object obj) =>
                                        {
                                            return ((RaceDefine.Time)obj).Text();
                                        }
                                        );
                    dropDown.value = _timeIndex;
                }
            }
            {
                var child = _assets.Get<GameObject>("RaceEnvSelect").transform.Find("AudienceRate");
                if (child != null)
                {
                    var dropDown = child.GetComponent<Dropdown>();
                    SetRaceEnv_DropDown(child.GetComponent<Dropdown>(),
                                        0,
                                        11,
                                        new UnityEngine.Events.UnityAction<int>
                                        (
                                            index =>
                                            {
                                                CourseAudience.AudienceRate = index / 10f;
                                            }
                                        ),
                                        (object obj) =>
                                        {
                                            return ((int)obj * 10).ToString();
                                        }
                                        );
                    dropDown.value = Math.Round(CourseAudience.AudienceRate * 10f);
                }
            }
        }

        private static void SetRaceEnv_DropDown(Dropdown dropdown, Type enumType, UnityEngine.Events.UnityAction<int> actionCallback, System.Func<object, string> action)
        {
            var enums = System.Enum.GetValues(enumType);
            dropdown.options.Clear();
            foreach (var enumItem in enums)
            {
                var name = action(enumItem);
                dropdown.options.Add(new Dropdown.OptionData(name));
            }
            dropdown.RefreshShownValue();
            dropdown.onValueChanged.AddListener(actionCallback);
        }
        private static void SetRaceEnv_DropDown(Dropdown dropdown, int valMin, int valMax, UnityEngine.Events.UnityAction<int> actionCallback, System.Func<object, string> action)
        {
            dropdown.options.Clear();

            for (int i = valMin; i < valMax; ++i)
            {
                var name = action(i);
                dropdown.options.Add(new Dropdown.OptionData(name));
            }

            dropdown.RefreshShownValue();
            dropdown.onValueChanged.AddListener(actionCallback);
        }

        private static void OnValueChange_TimeSelect(int index)
        {
            _timeIndex = index;
        }

        private static void OnValueChange_SeasonSelect(int index)
        {
            _seasonIndex = index;
        }

        private static void OnValueChange_WeatherSelect(int index)
        {
            _weatherIndex = index;
        }

        private static void OnValueChange_GroundConditionSelect(int index)
        {
            _groundConditionIndex = index;
        }

        private void InitServerSimulateUI()
        {
#if UNITY_EDITOR
            bool isSimulateServer = UnityEditor.EditorPrefs.GetBool(EDITOR_PREFS_IS_SIMULATE_SERVER, true);
            var appVer = UnityEditor.EditorPrefs.GetString(EDITOR_PREFS_SIMULATE_APP_VER, SIMULATER_SERVER_APP_VER_DEFAULT);
            var resVer = UnityEditor.EditorPrefs.GetString(EDITOR_PREFS_SIMULATE_RES_VER, SIMULATER_SERVER_RES_VER_DEFAULT);
#else
            bool isSimulateServer = IsSimulateServer;
            var appVer = SimulateAppVer;
            var resVer = SimulateResVer;
#endif

            _serverSimulateToggle.onValueChanged.RemoveAllListeners();
            _serverSimulateAppVerInput.onValueChanged.RemoveAllListeners();
            _serverSimulateResVerInput.onValueChanged.RemoveAllListeners();
            _serverSimulateToggle.onValueChanged.AddListener((isOn) =>
            {
                IsSimulateServer = isOn;
#if UNITY_EDITOR
                UnityEditor.EditorPrefs.SetBool(EDITOR_PREFS_IS_SIMULATE_SERVER, isOn);
#endif
                // 強調BGをON/OFF。
                _serverSimulateBg.gameObject.SetActive(isOn);
            });
            _serverSimulateAppVerInput.onValueChanged.AddListener((text) =>
            {
                SimulateAppVer = text;
#if UNITY_EDITOR
                UnityEditor.EditorPrefs.SetString(EDITOR_PREFS_SIMULATE_APP_VER, text);
#endif
            });
            _serverSimulateResVerInput.onValueChanged.AddListener((text) =>
            {
                SimulateResVer = text;
#if UNITY_EDITOR
                UnityEditor.EditorPrefs.SetString(EDITOR_PREFS_SIMULATE_RES_VER, text);
#endif
            });

            _serverSimulateToggle.isOn = isSimulateServer;
            _serverSimulateAppVerInput.text = appVer;
            _serverSimulateResVerInput.text = resVer;

            SetUseScenarioToggle(isSimulateServer);
        }
#endregion

        /// <summary>
        /// レース中でUniqueRareカットイン確認可能なカード&スキル情報をRaceSkillCutInReserveCreatorに設定。
        /// </summary>
        private void SetupAvailableCutIn()
        {
            if (_availableCutInArray != null)
            {
                RaceSkillCutInReserveCreator.DbgAvailableUniqueRareCutInList.Clear();
                foreach (var info in _availableCutInArray)
                {
                    if (RaceSkillCutInReserveCreator.DbgAvailableUniqueRareCutInList.Any(c => c.CardId == info.CardId))
                    {
                        Debug.LogWarning($"同じCardIdが複数登録されています。CardId={info.CardId}");
                        continue;
                    }
                    RaceSkillCutInReserveCreator.DbgAvailableUniqueRareCutInList.Add(new RaceSkillCutInReserveCreator.UniqueRareSkillCutInInfo()
                    {
                        CardId = info.CardId,
                        SkillId = info.SkillId,
                    });
                }
            }
        }
    
#region UmaBench//ベンチマーク系
        private void UpdateUmabenchText()
        {
#if CYG_DEBUG
            _benchButtonLabel.text = RaceDebugger.IsDebugBenchMode ? "馬ベンチ有効" : "馬ベンチ無効";
#endif
            float fps = Bench.LatestFps;
            string springPerf = Bench.SpringPerf.ToString("f2") + "%";
            string scoreText = string.Format("Q={5}: score:{0}, penalty:{1}, spring:{2}, fps:{3}, time:{4}", Bench.LatestScore, Bench.LatestDelay, springPerf, fps, Bench.LatestTime, GraphicSettings.Instance.GetQuality());
            string qualityText = GraphicSettings.Instance.GetQuality().ToString();
            _benchScoreLabel.text = scoreText + "\n" + qualityText;
        }
        public void OnClickBenchButton()
        {
            //ベンチマーク切り替え
#if CYG_DEBUG
            RaceDebugger.IsDebugBenchMode = !RaceDebugger.IsDebugBenchMode;
#endif
            UpdateUmabenchText();
        }
#endregion
    }
}

#endif // CYG_DEBUG //
