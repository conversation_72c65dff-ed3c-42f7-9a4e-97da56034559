#if CYG_DEBUG && UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine.TestTools;
using NUnit.Framework;

namespace Gallop
{
    namespace Test
    {
        //---------------------------------------------------------------
        /// <summary>
        /// テスト：キャラの基礎ステータス実体。
        /// </summary>
        //---------------------------------------------------------------
        public static class TestRaceParameter
        {
            //-----------------------------------------------------------
            // 関数。
            //-----------------------------------------------------------
            private static IEnumerable TestCase()
            {
                const int BASE_STATUS = 100;

                // BASE_STATUSがやる気で補正されたになることをテスト。
                yield return new TestCaseData(BASE_STATUS, RaceDefine.Motivation.Middle).Returns(null);
                yield return new TestCaseData(BASE_STATUS, RaceDefine.Motivation.Min).Returns(null);
                yield return new TestCaseData(BASE_STATUS, RaceDefine.Motivation.Max).Returns(null);
            }

            /// <summary>
            /// テスト：基礎ステータス最小値をやる気でマイナス補正しても最小値でクリッピングされるはず。
            /// </summary>
            /// <returns></returns>
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseSpeedMinClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(HorseRaceInfo.STATUS_MIN, 0, 0, 0, 0, RaceDefine.Motivation.Min, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseSpeed;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MIN));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseStaminaMinClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, HorseRaceInfo.STATUS_MIN, 0, 0, 0, RaceDefine.Motivation.Min, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseStamina;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MIN));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBasePowMinClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, HorseRaceInfo.STATUS_MIN, 0, 0, RaceDefine.Motivation.Min, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BasePow;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MIN));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseGutsMinClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, 0, HorseRaceInfo.STATUS_MIN, 0, RaceDefine.Motivation.Min, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseGuts;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MIN));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseWizMinClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, 0, 0, HorseRaceInfo.STATUS_MIN, RaceDefine.Motivation.Min, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseWiz;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MIN));
            }
            
            /// <summary>
            /// テスト：基礎ステータス最大値をやる気でプラス補正しても最大値でクリッピングされるはず。
            /// </summary>
            /// <returns></returns>
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseSpeedMaxClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(HorseRaceInfo.STATUS_MAX, 0, 0, 0, 0, RaceDefine.Motivation.Max, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseSpeed;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MAX));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseStaminaMaxClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, HorseRaceInfo.STATUS_MAX, 0, 0, 0, RaceDefine.Motivation.Max, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseStamina;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MAX));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBasePowMaxClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, HorseRaceInfo.STATUS_MAX, 0, 0, RaceDefine.Motivation.Max, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BasePow;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MAX));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseGutsMaxClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, 0, HorseRaceInfo.STATUS_MAX, 0, RaceDefine.Motivation.Max, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseGuts;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MAX));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE)]
            public static IEnumerator TestBaseWizMaxClip()
            {
                yield return TestUtil.InitMasterAndResource();

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, 0, 0, HorseRaceInfo.STATUS_MAX, RaceDefine.Motivation.Max, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseWiz;
                Assert.That(actual, Is.EqualTo(HorseRaceInfo.STATUS_MAX));
            }
            
            /// <summary>
            /// テスト：育成後の基礎ステータスがRaceParameterによりやる気で補正される。
            /// </summary>
            /// <param name="baseSpeed">育成後の基礎ステータス。</param>
            /// <param name="motivation">やる気。</param>
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE), TestCaseSource("TestCase")]
            public static IEnumerator TestBaseSpeed(int baseSpeed, RaceDefine.Motivation motivation)
            {
                yield return TestUtil.InitMasterAndResource();

                // 期待値は基礎ステータスにやる気補正を加えたもの。
                float expected = baseSpeed * RaceParameter.GetMotivationCoef(motivation);
                
                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(baseSpeed, 0, 0, 0, 0, motivation, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseSpeed;
                Assert.That(actual, Is.EqualTo(expected));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE), TestCaseSource("TestCase")]
            public static IEnumerator TestBaseStamina(int baseStamina, RaceDefine.Motivation motivation)
            {
                yield return TestUtil.InitMasterAndResource();
                
                // 期待値は基礎ステータスにやる気補正を加えたもの。
                float expected = baseStamina * RaceParameter.GetMotivationCoef(motivation);

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, baseStamina, 0, 0, 0, motivation, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseStamina;
                Assert.That(actual, Is.EqualTo(expected));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE), TestCaseSource("TestCase")]
            public static IEnumerator TestBasePow(int basePow, RaceDefine.Motivation motivation)
            {
                yield return TestUtil.InitMasterAndResource();
                
                // 期待値は基礎ステータスにやる気補正を加えたもの。
                float expected = basePow * RaceParameter.GetMotivationCoef(motivation);

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, basePow, 0, 0, motivation, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BasePow;
                Assert.That(actual, Is.EqualTo(expected));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE), TestCaseSource("TestCase")]
            public static IEnumerator TestBaseGuts(int baseGuts, RaceDefine.Motivation motivation)
            {
                yield return TestUtil.InitMasterAndResource();
                
                // 期待値は基礎ステータスにやる気補正を加えたもの。
                float expected = baseGuts * RaceParameter.GetMotivationCoef(motivation);
                
                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, 0, baseGuts, 0, motivation, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseGuts;
                Assert.That(actual, Is.EqualTo(expected));
            }
            [UnityTest, Category(TestDefine.TEST_CATEGORY_RACE), TestCaseSource("TestCase")]
            public static IEnumerator TestBaseWiz(int baseWiz, RaceDefine.Motivation motivation)
            {
                yield return TestUtil.InitMasterAndResource();
                
                // 期待値は基礎ステータスにやる気補正を加えたもの。
                float expected = baseWiz * RaceParameter.GetMotivationCoef(motivation);

                //-------------------------------------------------------
                // テスト対象となる操作。
                //-------------------------------------------------------
                var param = new RaceParameter(0, 0, 0, 0, baseWiz, motivation, 0, 0);

                //-------------------------------------------------------
                // 事後状態評価（テスト）。
                //-------------------------------------------------------
                float actual = param.BaseWiz;
                Assert.That(actual, Is.EqualTo(expected));
            }
        }
    }
}
#endif
