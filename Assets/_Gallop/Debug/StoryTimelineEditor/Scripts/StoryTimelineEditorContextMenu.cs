#if CYG_DEBUG && UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using TrackType = Gallop.StoryTimelineTrackData.TrackType;

namespace Gallop
{
    /// <summary>
    /// コンテクストメニューの表示制御およびメニュー実行時の処理を集約する
    /// </summary>
    public partial class StoryTimelineEditorWindow
    {
        #region 変数

        /// <summary>
        /// 途中から再生したい場合の地点
        /// </summary>
        private int _sectionAFrame = StoryTimelineController.INVALID_VALUE;

        /// <summary>
        /// 途中までで終えたいときの地点
        /// </summary>
        private int _sectionBFrame = StoryTimelineController.INVALID_VALUE;

        #endregion 変数

        #region Block Menu

        /// <summary>
        /// Block上で右クリックした場合にコンテクストメニューを表示
        /// </summary>
        private void ShowBlockContextMenu()
        {
            if (!_needsContextMenu)
            {
                // 右クリックされていないので処理を抜ける
                return;
            }

            if (_timelineController.IsPlayingEditor)
            {
                // ツール実行中でなければ終了
                return;
            }

            Repaint();

            var menu = new GenericMenu();
            if ( _selectedBlockData != null )
            {
                var contentDel = new GUIContent("削除");
                menu.AddItem(contentDel, false, OnClickDeleteBlock);
                var contentIns = new GUIContent("新規ブロック挿入");
                menu.AddItem(contentIns, false, OnClickInsertBlock);

                menu.AddSeparator("");
                AddCopyAndPasteBlockMenu(menu);
            }
            menu.ShowAsContext();
        }

        /// <summary>
        /// ブロックのコピペに関するメニューを追加
        /// </summary>
        private void AddCopyAndPasteBlockMenu(GenericMenu menu)
        {
            var contentCopy = new GUIContent("このブロックをコピー");
            menu.AddItem(contentCopy, false, OnClickCopyBlock);

            if (_copySourceBlock == null)
            {
                return;
            }
            var contentPaste = new GUIContent(TextUtil.Format("ブロック {0} からペースト", _copySourceBlock.BlockIndex));
            menu.AddItem(contentPaste, false, OnClickPasteBlock);

            var contentPasteFull = new GUIContent("Text含めてペースト");
            menu.AddItem(contentPasteFull, false, OnClickPasteBlockFull);
        }

        #endregion Block Menu

        // Clipの追加や削除などTrack上のデータに関する処理
        #region Track Menu

        /// <summary>
        /// Track上で右クリックした場合にコンテクストメニューを表示
        /// </summary>
        /// <param name="trackIndex"></param>
        /// <param name="clickedFrame"></param>
        /// <remarks>
        /// ClipやKeyを右クリックした等, 状況に応じてメニューを変更する.
        /// また, タイムラインを再生中かどうかでも変化させる.
        /// </remarks>
        private void ShowTrackContextMenu(StoryTimelineTrackData trackData, StoryTimelineClipData hitClipData)
        {
            if (!_needsContextMenu)
            {
                // 右クリックされていないので処理を抜ける
                return;
            }

            if (_clickedTrackFrame == StoryTimelineController.INVALID_VALUE)
            {
                // Track上をクリックしていないので処理を抜ける
                return;
            }

            if (trackData == null)
            {
                return;
            }

            Repaint();

            var contentAdd = new GUIContent("追加");
            var contentDel = new GUIContent("削除");
            var contentTrackDel = new GUIContent("選択トラックの全Clip削除");
            var contentBlockDel = new GUIContent("選択トラックブロックの全Clip削除");

            var menu = new GenericMenu();
            if (_timelineController.IsPlayingEditor || trackData.IsClipEditable == false)
            {
                // タイムラインを再生中はメニュー無効化
                // またTextClipは常に無効化
                menu.AddDisabledItem(contentAdd);
                menu.AddDisabledItem(contentDel);

                menu.AddSeparator("");
                menu.AddDisabledItem(contentBlockDel);
                menu.AddDisabledItem(contentTrackDel);
            }
            else if (hitClipData != null)
            {
                // KeyまたはClipをクリックされた場合
                menu.AddDisabledItem(contentAdd);
                if (trackData.HeadTrack.IsLock)
                {
                    // ロックされている場合は押せない
                    menu.AddDisabledItem(contentDel);
                }
                else
                {
                    menu.AddItem(contentDel, false, OnClickDelete);
                }

                //トラック削除
                menu.AddSeparator("");
                if (trackData.HeadTrack.IsLock)
                {
                    // ロックされている場合は押せない
                    menu.AddDisabledItem(contentBlockDel);
                    menu.AddDisabledItem(contentTrackDel);
                }
                else
                {
                    menu.AddItem(contentBlockDel, false, OnClickDeleteBlockTrackClip);
                    menu.AddItem(contentTrackDel, false, OnClickDeleteTrackClip);
                }
            }
            else
            {
                if (trackData.FindClipData(_clickedTrackFrame) == null)
                {
                    // データがない箇所をクリックされた場合
                    if (trackData.HeadTrack.IsLock)
                    {
                        // ロックされている場合は押せない
                        menu.AddDisabledItem(contentAdd);
                    }
                    else
                    {
                        menu.AddItem(contentAdd, false, () => OnClickAdd(_clickedTrackFrame));
                    }

                    menu.AddDisabledItem(contentDel);

                    //トラック削除
                    menu.AddSeparator("");
                    if (trackData.HeadTrack.IsLock)
                    {
                        menu.AddDisabledItem(contentBlockDel);
                        menu.AddDisabledItem(contentTrackDel);
                    }
                    else
                    {
                        menu.AddItem(contentBlockDel, false, OnClickDeleteBlockTrackClip);
                        menu.AddItem(contentTrackDel, false, OnClickDeleteTrackClip);
                    }
                }
                else
                {
                    // menuに項目が何もないので抜ける
                    return;
                }
            }

            menu.AddSeparator("");
            AddCopyAndPasteTrackMenu(menu);

            menu.ShowAsContext();
        }

        /// <summary>
        /// トラックのコピペに関するメニューを追加
        /// </summary>
        private void AddCopyAndPasteTrackMenu(GenericMenu menu)
        {
            var contentCopy = new GUIContent("このトラックをコピー");
            menu.AddItem(contentCopy, false, OnClickCopyTrack);

            var srcBlock = _copySourceTrackTuple.Item1;
            if (srcBlock == null)
            {
                return;
            }

            var srcTrack = _copySourceTrackTuple.Item2;
            if (srcTrack == null || srcTrack.Type != _selectedTrackData.Type)
            {
                return;
            }

            int blockIndex = srcBlock.BlockIndex;
            if (srcTrack.TrackIndex == _selectedTrackData.TrackIndex && blockIndex == _selectedBlockData.BlockIndex)
            {
                return;
            }

            var contentPaste = new GUIContent(TextUtil.Format("ブロック {0} からペースト", blockIndex));
            menu.AddItem(contentPaste, false, OnClickPasteTrack);
        }

        // 追加系のメソッド
        #region Add Data

        /// <summary>
        /// 追加を実行
        /// </summary>
        private bool OnClickAdd(int fixedStartFrame)
        {
            if (!IsAddClipData(fixedStartFrame))
            {
                return false;
            }

            StoryTimelineBlockData selectedBlockData = null;
            StoryTimelineClipData newClip = AddClipData(_selectedTrackData, fixedStartFrame, ref selectedBlockData);
            return newClip != null;
        }

        /// <summary>
        /// クリップが追加できるか
        /// </summary>
        private bool IsAddClipData(int fixedStartFrame)
        {
            if (_selectedTrackData is StoryTimelineStillTrackData &&
                _selectedTrackData.GetPrevClip(fixedStartFrame) == null)
            {
                // スチルはエディタから追加を試みるとStoryStillGroupIdが-1のためにエラーが出る
                // json出力のみをサポートする事にする
                EditorUtility.DisplayDialog("警告", "Still表示クリップはエディタで編集中は追加できません。\n新しくクリップ追加する場合はjson出力で設定してください。", "OK");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 連携したクリップの追加
        /// BgClipを追加すると、BgOffsetClipも一緒に追加されるような時に使われる
        /// </summary>
        /// <param name="blockData"></param>
        /// <param name="fixedStartFrame"></param>
        /// <param name="selectedBlock"></param>
        /// <returns></returns>
        private StoryTimelineClipData AddCooperationClipData(StoryTimelineTrackData trackData, int fixedStartFrame, ref StoryTimelineBlockData selectedBlock)
        {
            if (trackData == null)
                return null;

            int startFrame = fixedStartFrame - trackData.BlockData.StartFrame;

            bool isValidClipAdd = true;
            if (trackData.ClipList != null)
            {
                foreach (var targetClip in trackData.ClipList)
                {
                    if (targetClip.StartFrame == startFrame)
                    {
                        isValidClipAdd = false;
                        break;
                    }
                }
            }

            StoryTimelineClipData clipData = null;
            if (isValidClipAdd)
            {
                StoryTimelineBlockData tempBlock = null;
                clipData = AddClipData(trackData, fixedStartFrame, ref tempBlock);
            }
            return clipData;
        }

        /// <summary>
        /// Clipの追加
        /// ※selectedBlockの扱いについて、モーションClipは3つ続けて追加したりする。（Start、Loop、End）
        /// この場合にstartFrameでBlockを探してしまうと次のBlockへClipが移動してしまうので、Startモーションの段階で挿入したBlockを覚えておき、以降の追加時にそのまま使う
        /// 為用意している。そのためだけのもの。
        /// </summary>
        /// <returns></returns>
        public StoryTimelineClipData AddClipData(StoryTimelineTrackData trackData, int fixedStartFrame, ref StoryTimelineBlockData selectedBlock)
        {
            if(trackData == null )
            {
                return null;
            }

            // 指定がなければカーソルの位置
            if (fixedStartFrame <= 0)
            {
                fixedStartFrame = _timelineController.FrameCount;
            }

            // Blockを調べる（すでにBlockを選んでいた場合はそこに追加する。）
            StoryTimelineBlockData blockData = selectedBlock;
            if (blockData == null)
            {
                int blockCount = _timelineData.BlockList.Count;
                for (int i = 0; i < blockCount; i++)
                {
                    if (_timelineData.BlockList[i].Contains(fixedStartFrame))
                    {
                        blockData = _timelineData.BlockList[i];
                        break;
                    }
                }

                // ブロックの中になければ終了
                if (blockData == null)
                {
                    return null;
                }

                selectedBlock = blockData;
            }

            // フレーム数は現在のBlockの開始位置からのフレーム数になる
            int startFrame = fixedStartFrame - blockData.StartFrame;

            // 指定のBlockからTrackを探す
            int trackIndex = trackData.TrackIndex;
            trackData = blockData.FindTrackByTrackIndex(trackIndex);

            // ロックされているTrackであればやめる
            if(trackData.HeadTrack.IsLock == true)
            {
                return null;
            }

            // フレーム数が昇順で並ぶようにデータ追加
            int index = 0;
            for (index = 0; index < trackData.ClipList.Count; index++)
            {
                if (trackData.ClipList[index].StartFrame == startFrame)
                {
                    // 既にデータがある場合は追加しない
                    Debug.LogWarningFormat("<color=red>[ERROR]</color> 同じフレームにデータがあります: StartFrame = {0}", startFrame);
                    return null;
                }

                if (trackData.ClipList[index].StartFrame > startFrame)
                {
                    // 追加したいフレーム数より大きいフレーム数になっているので
                    // このデータのindexに新たなデータを追加する → ここでループ抜ける
                    break;
                }
            }

            // データ作成
            StoryTimelineClipData clip;
            switch (trackData.Type)
            {
                case StoryTimelineTrackData.TrackType.Bg:
                    clip = CreateClipData<StoryTimelineBgClipData>(index, startFrame, trackData);
                    if(!_timelineController.TimelineData.IsSingleMode)
                    {
                        //BgClipを追加するとBgOffsetClipも追加される
                        var targetTrack = FindTrackData(blockData.TrackList, TrackType.BgOffset);
                        StoryTimelineBlockData tempBlock = null;
                        AddCooperationClipData(targetTrack, fixedStartFrame, ref tempBlock);
                    }
                    break;
                case StoryTimelineTrackData.TrackType.Bg3D:
                    clip = CreateClipData<StoryTimelineBg3DClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.BgOffset:
                    clip = CreateClipData<StoryTimelineBgOffsetClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Chara:
                    clip = CreateClipData<StoryTimelineCharaClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaMotion:
                    clip = CreateClipData<StoryTimelineCharaMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaAddMotion:
                    clip = CreateClipData<StoryTimelineCharaAddMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaTailMotion:
                    clip = CreateClipData<StoryTimelineCharaTailMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaUpperMotion:
                    clip = CreateClipData<StoryTimelineCharaUpperMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaLowerMotion:
                    clip = CreateClipData<StoryTimelineCharaLowerMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaOverrideMotion:
                    clip = CreateClipData<StoryTimelineCharaOverrideMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEyebrow:
                    clip = CreateClipData<StoryTimelineCharaFaceEyebrowClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEye:
                    clip = CreateClipData<StoryTimelineCharaFaceEyeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceMouth:
                    clip = CreateClipData<StoryTimelineCharaFaceMouthClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEar:
                    clip = CreateClipData<StoryTimelineCharaFaceEarClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEffect:
                    clip = CreateClipData<StoryTimelineCharaFaceEffectClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaMoveEye:
                    clip = CreateClipData<StoryTimelineCharaMoveEyeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaMouth:
                    clip = CreateClipData<StoryTimelineCharaMouthClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaTurn:
                    clip = CreateClipData<StoryTimelineCharaTurnClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaProp:
                    clip = CreateClipData<StoryTimelineCharaPropClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CySpringReset:
                    clip = CreateClipData<StoryTimelineCySpringResetClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Se:
                    clip = CreateClipData<StoryTimelineSeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Bgm:
                    clip = CreateClipData<StoryTimelineBgmClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Text:
                    clip = CreateClipData<StoryTimelineTextClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Still:
                    clip = CreateClipData<StoryTimelineStillClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillSkin:
                    clip = CreateClipData<StoryTimelineStillSkinClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillEye:
                    clip = CreateClipData<StoryTimelineStillCharaEyeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillMouth:
                    clip = CreateClipData<StoryTimelineStillCharaMouthClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillCamera:
                    clip = CreateClipData<StoryTimelineStillCameraClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillAnimation:
                    clip = CreateClipData<StoryTimelineStillAnimationClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillCharaSkin:
                    clip = CreateClipData<StoryTimelineStillCharaSkinClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillObjectControl:
                    clip = CreateClipData<StoryTimelineStillObjectControlClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.StillAlpha:
                    clip = CreateClipData<StoryTimelineStillCharaAlphaClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Camera:
                    clip = CreateClipData<StoryTimelineCameraClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ScreenEffect:
                    clip = CreateClipData<StoryTimelineScreenEffectClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Wipe:
                    clip = CreateClipData<StoryTimelineWipeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Debug:
                    clip = CreateClipData<StoryTimelineDebugClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaOffsetTransform:
                    clip = CreateClipData<StoryTimelineCharaOffsetTransformClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectTiltShift:
                    clip = CreateClipData<StoryTimelineImageEffectTiltShiftClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectRadialBlur:
                    clip = CreateClipData<StoryTimelineImageEffectRadialBlurClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectDof:
                    clip = CreateClipData<StoryTimelineImageEffectDofClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectDiffusionBloom:
                    clip = CreateClipData<StoryTimelineImageEffectDiffusionBloomClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectOverlay:
                    clip = CreateClipData<StoryTimelineImageEffectOverlayClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectColorCorrection:
                    clip = CreateClipData<StoryTimelineImageEffectColorCorrectionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectLensDistortion:
                    clip = CreateClipData<StoryTimelineImageEffectLensDistortionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectFluctuation:
                    clip = CreateClipData<StoryTimelineImageEffectFluctuationClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectChromaticAberration:
                    clip = CreateClipData<StoryTimelineImageEffectChromaticAberrationClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectToneCurve:
                    clip = CreateClipData<StoryTimelineImageEffectToneCurveClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ImageEffectExposure:
                    clip = CreateClipData<StoryTimelineImageEffectExposureClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaEnv:
                    clip = CreateClipData<StoryTimelineCharaEnvClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaParts:
                    clip = CreateClipData<StoryTimelineCharaPartsClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.Warp:
                    clip = CreateClipData<StoryTimelineWarpClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.ObjectControl:
                    clip = CreateClipData<StoryTimelineObjectControlClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.CharaHeightMotion:
                    clip = CreateClipData<StoryTimelineCharaHeightMotionClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.BgmAudioVolume:
                    clip = CreateClipData<StoryTimelineBgmVolumeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.EnvAudioVolume:
                    clip = CreateClipData<StoryTimelineEnvVolumeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.TrainingCutt:
                    clip = CreateClipData<StoryTimelineTrainingCuttClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.DspBgm:
                    clip = CreateClipData<StoryTimelineDspBgmClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.DspBgmVolume:
                    clip = CreateClipData<StoryTimelineDspBgmVolumeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.BusParam:
                    clip = CreateClipData<StoryTimelineBusParamClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.MotionSeVolume:
                    clip = CreateClipData<StoryTimelineMotionSeVolumeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.MotionSeRange:
                    clip = CreateClipData<StoryTimelineMotionSeCameraRangeClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.SingleImage:
                    clip = CreateClipData<StoryTimelineSingleImageClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.SingleImageOffset:
                    clip = CreateClipData<StoryTimelineSingleImageOffsetClipData>(index, startFrame, trackData);
                    break;
                case StoryTimelineTrackData.TrackType.SingleImageScale:
                    clip = CreateClipData<StoryTimelineSingleImageScaleClipData>(index, startFrame, trackData);
                    break;
                default:
                    throw new Exception("Invalid Type.");
            }

            // バージョンをセット
            clip.DataVer = clip.GetLatestClipDataVersion();

            // ヒエラルキーで見えないようにする＆サブアセットとして保存
            clip.hideFlags = HideFlags.HideInHierarchy | HideFlags.HideInInspector;
            AssetDatabase.AddObjectToAsset(clip, _assetPath);

            // すでに選択中のものがある場合は解除する
            _selectedClipDataList.Clear();

            // 新規作成時は作成されたClipを選択中にする
            SetSelectedClip(clip);

            // キャッシュ削除
            TimelineData.ClearCache();

            // Clipの初期化
            clip.Initialize();

            // クリップ追加時のユーザー定義関数を呼び出す
            clip.OnCreateClipData();

            return clip;
        }

        /// <summary>
        /// 指定されたindexに新しいClipデータを追加
        /// </summary>
        private T CreateClipData<T>(int index, int startFrame, StoryTimelineTrackData trackData) where T : StoryTimelineClipData
        {
            Undo.RecordObject(_timelineData, "Create Clip");

            T data = CreateInstance<T>();

            if (trackData.IsCopyLastClip)
            {
                // 1つ前のキーをコピーする。
                int fixedStartFrame = trackData.BlockData.StartFrame + startFrame;
                T copyData = trackData.GetPrevClip(fixedStartFrame) as T;
                if (copyData != null)
                {
                    EditorUtility.CopySerialized(copyData, data);
                    EditorUtility.SetDirty(data);
                }
            }

            trackData.ClipList.Insert(index, data);
            data.StartFrame = startFrame;
            data.TrackData = trackData;
            data.EditorWindow = this;

            // 作成したClipに対して初期値以外のものを設定したい場合はここで行う
            switch (data)
            {
                case StoryTimelineCharaTurnClipData turnClip:
                    turnClip.SetClipLength();
                    break;

                case StoryTimelineWipeClipData wipeClip:
                    if (trackData.ClipList.Count == 1)
                    {
                        // コピーしない場合、CSVから設定
                        var row = PropertyWindow.GetWipeCsv()
                            .FirstOrDefault().Value;
                        wipeClip.SetWipe(row);
                    }
                    break;

                case StoryTimelineCameraClipData cameraClip:
                    // デフォルトでは簡易カメラになっているので
                    // この関数を呼び出してImageEffectのRadialBlurトラックを無効化する
                    TimelineController.CheckCameraType();
                    break;
            }

            return data;
        }

        private void OnClickInsertBlock()
        {
            if (_selectedBlockData == null)
                return;

            // Blockを追加
            var selectBlockIndex = _selectedBlockData.BlockIndex;
            var newBlock = _timelineData.InsertBlock(selectBlockIndex + 1);

            // TextClipを追加
            var textTrack = newBlock.TextTrack;
            var oldIsLock = textTrack.HeadTrack.IsLock;
            textTrack.HeadTrack.IsLock = false;
            var startFrame = TimelineData.GetStartFrame(newBlock, forceCalc: true);
            StoryTimelineBlockData selectedBlockData = null;
            AddClipData(textTrack, startFrame, ref selectedBlockData);
            textTrack.HeadTrack.IsLock = oldIsLock;

            // キャッシュが不正になるのでここで更新する
            _timelineData.GetStartFrame(_selectedBlockData, forceCalc: true);
        }

        #endregion Add Data

        // 削除系のメソッド
        #region Delete Data

        /// <summary>
        /// 削除を実行
        /// </summary>
        /// <param name="value">削除するキー</param>
        public void OnClickDelete()
        {
            bool isCameraDeleted = false;

            int count = _selectedClipDataList.Count;
            if (count > 0)
            {
                for (int i = 0; i < count; i++)
                {
                    // 選択中トラックを取り出す
                    var trackData = _selectedClipDataList[i].TrackData;

                    // ロックされているTrackであればやめる
                    if (trackData.HeadTrack.IsLock)
                    {
                        continue;
                    }

                    // サブアセットを削除
                    var list = trackData.ClipList;

                    Undo.RecordObject(_timelineData, "Delete Clip");

                    list.Remove(_selectedClipDataList[i]);

                    // トラックを再評価させる
                    trackData.ClearCache();

                    if (trackData.Type == StoryTimelineTrackData.TrackType.Camera)
                    {
                        isCameraDeleted = true;
                    }
                }

                // 選択中のものを全部解除
                _selectedClipDataList.Clear();

                // キャッシュ削除
                TimelineData.ClearCache();

                // アセットの更新がかかったのでインポートしなおし
                AssetDatabase.ImportAsset(_assetPath);

                // 表示も更新
                Repaint();
            }

            if (isCameraDeleted)
            {
                // カメラClipが消された場合はSelectCamera(簡易カメラ)が残っているかチェックする
                TimelineController.CheckCameraType();
            }
        }

        /// <summary>
        /// 子供トラックからトラックを検索する
        /// </summary>
        /// <param name="trackList"></param>
        /// <returns></returns>
        private StoryTimelineTrackData FindTrackData(List<StoryTimelineTrackData> trackList, StoryTimelineTrackData targetTrack)
        {
            return FindTrackData(trackList, targetTrack.Type, targetTrack.TrackIndex);
        }

        /// <summary>
        /// トラックを検索する
        /// </summary>
        /// <param name="trackList"></param>
        /// <returns></returns>
        private StoryTimelineTrackData FindTrackData(List<StoryTimelineTrackData> trackList, TrackType targetType, int trackIndex = -1)
        {
            if (trackList == null)
                return null;

            var index = trackList.FindIndex((track) =>
            {
                if (track.Type != targetType)
                    return false;
                if(trackIndex != -1)
                {
                    if (track.TrackIndex != trackIndex)
                        return false;
                }
                return true;
            });

            if (index >= 0)
                return trackList[index];

            int num = trackList.Count;
            for (int i = 0; i < num; i++)
            {
                var trackData = FindTrackData(trackList[i].ChildTrackList, targetType, trackIndex);
                if (trackData != null)
                    return trackData;
            }

            return null;
        }

        /// <summary>
        /// トラックのクリップ削除を実行
        /// </summary>
        /// <param name="value">削除するキー</param>
        public void OnClickDeleteTrackClip()
        {
            if (_selectedTrackData == null)
                return;

            if(!EditorUtility.DisplayDialog("削除確認", "選択されたトラックのクリップを全て削除します\nよろしいですか？", "はい","いいえ"))
            {
                return;
            }

            var isCameraDeleted = _selectedTrackData.Type == TrackType.Camera;

            bool isFirstDelete = true;
            int blockNum = TimelineController.TimelineData.BlockList.Count;
            for(int i=0;i<blockNum;i++)
            {
                var blockData = TimelineController.TimelineData.BlockList[i];
                //該当トラックをブロック内から探す
                StoryTimelineTrackData trackData = FindTrackData(blockData.TrackList,_selectedTrackData);
                if (trackData == null)
                {
                    continue;
                }

                // ロックされているTrackであればやめる
                if (trackData.IsLock)
                {
                    return;
                }

                //Ctrl+Zで一気にクリップが戻せるように最初の１回目だけRecordする
                if (isFirstDelete)
                {
                    Undo.RecordObject(_timelineData, "Delete Clip");
                }
                // サブアセットを削除
                trackData.ClipList.Clear();

                // トラックを再評価させる
                trackData.ClearCache();

                isFirstDelete = false;
            }

            if (!isFirstDelete)
            {
                // 選択中のものを全部解除
                _selectedClipDataList.Clear();

                // キャッシュ削除
                TimelineData.ClearCache();

                // アセットの更新がかかったのでインポートしなおし
                AssetDatabase.ImportAsset(_assetPath);

                // 表示も更新
                Repaint();
            }

            if (isCameraDeleted)
            {
                // カメラClipが消された場合はSelectCamera(簡易カメラ)が残っているかチェックする
                TimelineController.CheckCameraType();
            }
        }

        /// <summary>
        /// ブロックの選択トラッククリップを削除
        /// </summary>
        private void OnClickDeleteBlockTrackClip()
        {
            if (_selectedTrackData == null || _selectedBlockData == null)
                return;

            if (!EditorUtility.DisplayDialog("削除確認", "選択されたトラックのブロック内クリップを全て削除します\nよろしいですか？", "はい", "いいえ"))
            {
                return;
            }

            var isCameraDeleted = _selectedTrackData.Type == TrackType.Camera;

            int count = _selectedTrackData.ClipList.Count;
            if (count > 0)
            {
                // ロックされているTrackであればやめる
                if (_selectedTrackData.IsLock)
                {
                    return;
                }

                bool isFirstRemove = true;
                for (int i = count-1; i >= 0; i--)
                {
                    if (_selectedTrackData.ClipList[i].BlockData != _selectedBlockData)
                    {
                        //対象ブロックではない
                        continue;
                    }

                    //Ctrl+Zで一気にクリップが戻せるように最初の１回目だけRecordする
                    if (isFirstRemove)
                    {
                        Undo.RecordObject(_timelineData, "Delete Clip");
                    }

                    // サブアセットを削除
                    _selectedTrackData.ClipList.RemoveAt(i);

                    isFirstRemove = true;
                }
                // トラックを再評価させる
                _selectedTrackData.ClearCache();

                // 選択中のものを全部解除
                _selectedClipDataList.Clear();

                // キャッシュ削除
                TimelineData.ClearCache();

                // アセットの更新がかかったのでインポートしなおし
                AssetDatabase.ImportAsset(_assetPath);

                // 表示も更新
                Repaint();
            }

            if (isCameraDeleted)
            {
                // カメラClipが消された場合はSelectCamera(簡易カメラ)が残っているかチェックする
                TimelineController.CheckCameraType();
            }
        }

        private void OnClickDeleteBlock()
        {
            if (_selectedBlockData == null)
                return;

            var selectBlockIndex = _selectedBlockData.BlockIndex;

            //分岐先に指定されていないかチェック
            foreach (var block in _timelineData.BlockList)
            {
                if (block.TextTrack.ClipList.Count == 0)
                    continue;

                var textClip = block.TextTrack.ClipList[0] as StoryTimelineTextClipData;
                if (textClip == null || textClip.ChoiceDataList.Count == 0)
                    continue;

                foreach (var choiceData in textClip.ChoiceDataList)
                {
                    // エディタ中は差分を考慮しない
                    if (choiceData.NextBlock == selectBlockIndex)
                    {
                        EditorUtility.DisplayDialog("エラー", "Block" + block.BlockIndex + " の分岐先に指定されてるため削除不可", "OK");
                        return;
                    }
                }
            }

            Undo.RecordObject(_timelineData, "Delete Clip");

            // コピー元と削除対象が同じなら参照を外す
            if (_copySourceBlock?.BlockIndex == selectBlockIndex)
            {
                _copySourceBlock = null;
            }
            if (_copySourceTrackTuple.Item1?.BlockIndex == selectBlockIndex)
            {
                _copySourceTrackTuple = (null, null);
            }

            int count = _selectedBlockData.TrackList.Count;
            for (int i = 0; i < count; i++)
            {
                // 選択中トラックを取り出す
                var trackData = _selectedBlockData.TrackList[i];

                // サブアセットを削除
                var list = trackData.ClipList;

                list.Clear();
            }

            _timelineData.BlockList.Remove(_selectedBlockData);

            //ブロックはblockindexの整数値で管理されてるため、繋ぎ直してやる必要がある
            //(メンテ頻度多いようなら整数値じゃなくて参照で保持したほうがいいかも)

            foreach (var block in _timelineData.BlockList)
            {
                if (block.BlockIndex > selectBlockIndex)
                {
                    //indexが連続するように直す
                    block.BlockIndex--;
                }
                foreach (var clip in block.TextTrack.ClipList)
                {
                    if (!(clip is StoryTimelineTextClipData textClip))
                        continue;

                    int length = textClip.ChoiceDataList.Count;
                    if (length == 0)
                    {
                        // 選択肢がない場合
                        if (textClip.NextBlock > selectBlockIndex)
                        {
                            textClip.NextBlock--;
                            textClip.ResetNextBlockIndex();
                        }
                        continue;
                    }
                    for (int i = 0; i < length; i++)
                    {
                        var choiceData = textClip.ChoiceDataList[i];
                        if (choiceData.NextBlock > selectBlockIndex)
                        {
                            choiceData.NextBlock--;
                            choiceData.ResetNextBlockIndex();
                        }
                    }
                }
            }

            _timelineData.SetTotalLength();

            AssetDatabase.ImportAsset(_assetPath);

            _selectedBlockData = null;
            _timelineData.ClearCache();

            Repaint();
        }
        #endregion

        // コピペ
        #region Copy Data

        #region Classes

        /// <summary>
        /// クリップのプリセットクラス
        /// EditorPrefsにクリップを保存する
        /// </summary>
        [Serializable]
        private class ClipPreset
        {
            [Serializable]
            private class ClipCopy
            {
                [NonSerialized] private StoryTimelineClipData _clipData;
                [NonSerialized] private Type _trackType;
                [SerializeField] private string _serializedClipData;
                [SerializeField] private string _serializedClipTypeName;
                [SerializeField] private int _serializedTrackIndex;
                [SerializeField] private string _serializedAssemblyQualifiedTrackTypeName;

                public StoryTimelineClipData ClipData => _clipData != null ? _clipData : (_clipData = DeserializeClip());
                public Type TrackType => _trackType ?? (_trackType = Type.GetType(_serializedAssemblyQualifiedTrackTypeName));
                public int TrackIndex => _serializedTrackIndex;

                public ClipCopy(StoryTimelineClipData clipData)
                {
                    _serializedClipData = ClipPreset.Serialize(clipData);
                    _serializedClipTypeName = clipData.GetType().Name;
                    _serializedTrackIndex = clipData.TrackData.TrackIndex;
                    _serializedAssemblyQualifiedTrackTypeName = clipData.TrackData.GetType().AssemblyQualifiedName;
                }

                private StoryTimelineClipData DeserializeClip()
                {
                    return ClipPreset.Deserialize(_serializedClipData, _serializedClipTypeName) as StoryTimelineClipData;
                }
            }

            private const int NUM_PRESET = 5;
            private const string EDITORPREFS_KEY_FORMAT = "StoryTimelineEditor.ClipPresetData.Slot.{0}";
            private static string GetSaveKey(int i) => string.Format(EDITORPREFS_KEY_FORMAT, i.ToString());
            private static ClipPreset[] _instanceList = null;
            public static ClipPreset[] Instances => _instanceList ?? (_instanceList = LoadAll());

            [SerializeField]
            private List<ClipCopy> clipCopyList = null;
            public int ClipCount => clipCopyList?.Count ?? 0;

            /// <summary>
            /// クリップをプリセットに保存する
            /// </summary>
            public void SetClipList(List<StoryTimelineClipData> clipList)
            {
                clipCopyList = clipList.Select(clip => new ClipCopy(clip)).ToList();
                Save();
            }

            /// <summary>
            /// クリップをプリセットから取得する
            /// ここで取得したプリセットにはTrackDataが入っているが、
            /// ペースト用であり、実際にトラックに追加されているわけではないので注意
            /// </summary>
            /// <param name="selectedBlock">現在選択されているブロック</param>
            /// <returns>ペースト用に準備されたクリップのリスト</returns>
            public List<StoryTimelineClipData> GetClipList(StoryTimelineBlockData selectedBlock)
            {
                var clipCount = clipCopyList.Count;
                var clipDataList = new List<StoryTimelineClipData>(clipCount);
                for (var i = 0; i < clipCount; ++i)
                {
                    var clipData = clipCopyList[i].ClipData;
                    clipData.TrackData = findTrack(clipCopyList[i].TrackType, clipCopyList[i].TrackIndex);
                    clipDataList.Add(clipData);
                }

                // ペースト対象トラックを検索する
                StoryTimelineTrackData findTrack(Type trackType, int trackIndex) =>
                    // 非キャラトラックの場合、トラック型とTrackIndexが一致しているものが対象
                    selectedBlock.TrackList
                        .Where(t => t.TrackIndex == trackIndex)
                        .FirstOrDefault(t => t.GetType() == trackType)
                    // TrackIndexが合わなかったら型が一致しているものが対象
                    ?? selectedBlock.TrackList
                        .FirstOrDefault(t => t.GetType() == trackType)
                    // キャラトラックの場合、子トラックが対象となる
                    ?? selectedBlock.TrackList
                        .Where(t => t.ChildTrackList != null)
                        .SelectMany(t => t.ChildTrackList)
                        .FirstOrDefault(t => t.GetType() == trackType);

                return clipDataList;
            }

            private static ClipPreset[] LoadAll()
                => Enumerable.Range(0, NUM_PRESET).Select(Load).ToArray();

            private static ClipPreset Load(int index)
            {
                var serializedString = EditorPrefs.GetString(GetSaveKey(index), "");
                if (string.IsNullOrEmpty(serializedString))
                    return new ClipPreset();

                return Deserialize<ClipPreset>(serializedString);
            }

            private void Save()
            {
                var index = Array.IndexOf(_instanceList, this);
                if (index == -1) return;
                var serializedString = Serialize(this);
                EditorPrefs.SetString(GetSaveKey(index), serializedString);
            }

            private static string Serialize<T>(T obj) => JsonUtility.ToJson(obj);
            private static T Deserialize<T>(string str) => JsonUtility.FromJson<T>(str);
            private static ScriptableObject Deserialize(string json, string typeName)
            {
                if (string.IsNullOrEmpty(typeName)) return null;
                if (string.IsNullOrEmpty(json)) return null;
                var instance = ScriptableObject.CreateInstance(typeName);
                try
                {
                    JsonUtility.FromJsonOverwrite(json, instance);
                }
                catch (Exception exception)
                {
                    Debug.LogError("プリセット内のクリップが読み込めませんでした");
                    Debug.LogException(exception);
                    return null;
                }
                return instance;
            }
        }

        #endregion

        /// <summary>
        /// クリップをペースト向けにコピーする
        /// </summary>
        private void CopyClipList(List<StoryTimelineClipData> clipList)
        {
            // 初期化
            _copyDataList.Clear();
            // コピー
            _copyDataList.AddRange(clipList.Select(clipData => clipData.Clone()));
            // 一番早いStartFrame
            _copyStartFrame = clipList.Select(clipData => clipData.StartFrame).Min();
        }

        /// <summary>
        /// クリップをシークバー位置にペーストする
        /// </summary>
        private void PasteClipList(List<StoryTimelineClipData> copyClipDataList)
        {
            // 別トラックへのペースト（キャラ）用に、ペースト対象のトラックを比較用に保存
            var pasteCharaTrack = SelectedTrack?.ParentTrack as StoryTimelineCharaTrackData;

            // 別トラックへのペースト（画面効果）用に、ペースト対象のトラックを保存
            var pasteTrackScreenEffect = SelectedTrack as StoryTimelineScreenEffectTrackData;

            // ペースト
            int count = copyClipDataList.Count;
            for (int i = 0; i < count; i++)
            {
                // 通常はペースト先とコピー元のトラックは同じ
                var pasteTrack = copyClipDataList[i].TrackData;

                // 別トラックへのペースト（キャラ）時は違う
                if (pasteCharaTrack != null
                    && pasteTrack.ParentTrack is StoryTimelineCharaTrackData)
                {
                    // ペースト先トラックは別の親に含まれる同じ型のトラック
                    // 加算モーションやオーバーライドモーションは複数TrackあるのでGroupIndexも比較
                    pasteTrack = pasteCharaTrack.ChildTrackList
                        .Find(newPasteTrack => newPasteTrack.Type == pasteTrack.Type && newPasteTrack.GroupIndex == pasteTrack.GroupIndex) ?? pasteTrack;
                }

                // 画面効果トラックは複数ある
                if (pasteTrackScreenEffect != null
                    && pasteTrack is StoryTimelineScreenEffectTrackData)
                {
                    pasteTrack = pasteTrackScreenEffect;
                }

                // ペースト先トラックを選択
                SetSelectedTrack(pasteTrack);

                // ペーストされるクリップ内の相対フレームを
                var pasteOffsetFrame = copyClipDataList[i].StartFrame - _copyStartFrame;

                // シークバーのフレームに足してペースト位置とする
                var pasteFrame = pasteOffsetFrame + _timelineController.FrameCount;

                if (OnClickAdd(pasteFrame))
                {
                    // ペーストが成立したら
                    StoryTimelineClipData pasteClip = _selectedClipDataList[0];

                    // 中身をコピーして
                    EditorUtility.CopySerialized(copyClipDataList[i], pasteClip);

                    // フレームをブロック内フレーム数にして
                    pasteClip.StartFrame = pasteFrame - pasteClip.BlockData.StartFrame;

                    // 変化があったことを伝える
                    EditorUtility.SetDirty(pasteClip);
                }
            }

            Repaint();
        }

        /// <summary>
        /// ブロックをコピーする
        /// </summary>
        private void OnClickCopyBlock()
        {
            // コピー元ブロックの参照を設定するだけ
            _copySourceBlock = _selectedBlockData;
        }

        /// <summary>
        /// ブロックをペーストする
        /// </summary>
        private void OnClickPasteBlock()
        {
            Undo.RecordObject(_timelineData, "Paste Block");

            var trackIndexSet = new HashSet<int>();

            // TextTrackはコピーしない
            foreach (var track in _copySourceBlock.TrackList)
            {
                if (track.Type == TrackType.Text)
                {
                    continue;
                }

                trackIndexSet.Add(track.TrackIndex);
                if (track.ChildTrackList != null)
                {
                    foreach (var child in track.ChildTrackList)
                    {
                        trackIndexSet.Add(child.TrackIndex);
                    }
                }
            }

            // コピー実行
            _selectedBlockData.CopyFrom(this, _copySourceBlock, trackIndexSet);
        }

        /// <summary>
        /// ブロックをペーストする (全Track対象)
        /// </summary>
        private void OnClickPasteBlockFull()
        {
            Undo.RecordObject(_timelineData, "Paste Block Full");

            _selectedBlockData.CopyFrom(this, _copySourceBlock);
        }

        /// <summary>
        /// トラックをコピーする
        /// </summary>
        private void OnClickCopyTrack()
        {
            // コピー元のトラックおよびそれが所属するブロックの参照を設定する
            _copySourceTrackTuple = (_selectedBlockData, _selectedTrackData);
        }

        /// <summary>
        /// トラックをペーストする
        /// </summary>
        private void OnClickPasteTrack()
        {
            Undo.RecordObject(_timelineData, "Paste Track");

            var srcTrack = _copySourceTrackTuple.Item2;
            _selectedTrackData.CopyFrom(this, srcTrack);
        }

        #endregion

        #endregion // Track Menu

        // 新しいTrackの追加やグループ化などタイムライン全体の処理
        #region Timeline Menu

        #endregion

        #region KeyFrame Menu

        /// <summary>
        /// フレーム数表示部分のコンテキストメニュー
        /// </summary>
        private void ShowFrameContextMenu(int frame)
        {
            if (_timelineController.IsPlayingEditor)
            {
                // タイムライン実行中は何もしない
                return;
            }

            var contentAddSectionA = new GUIContent("途中開始地点(A)を追加");
            var contentAddSectionB = new GUIContent("途中終了地点(B)を追加");
            var contentAddCurrentSectionA = new GUIContent("途中開始地点(A)を現在のフレームに追加");
            var contentAddCurrentSectionB = new GUIContent("途中終了地点(B)を現在のフレームに追加");
            var contentRemoveSectionA = new GUIContent("途中開始地点(A)を削除");
            var contentRemoveSectionB = new GUIContent("途中終了地点(B)を削除");

            var menu = new GenericMenu();

            menu.AddItem(contentAddSectionA, false, () => { _sectionAFrame = frame; });
            menu.AddSeparator("");
            menu.AddItem(contentAddSectionB, false, () => { _sectionBFrame = frame; });
            menu.AddSeparator("");
            menu.AddItem(contentAddCurrentSectionA, false, () => { _sectionAFrame = _timelineController.FrameCount; });
            menu.AddSeparator("");
            menu.AddItem(contentAddCurrentSectionB, false, () => { _sectionBFrame = _timelineController.FrameCount; });
            menu.AddSeparator("");
            if ( _sectionAFrame >= 0 )
            {
                menu.AddItem(contentRemoveSectionA, false, () => { _sectionAFrame = StoryTimelineController.INVALID_VALUE; });
                menu.AddSeparator("");
            }
            if (_sectionBFrame >= 0)
            {
                menu.AddItem(contentRemoveSectionB, false, () => { _sectionBFrame = StoryTimelineController.INVALID_VALUE; });
                menu.AddSeparator("");
            }

            menu.ShowAsContext();
        }

        #endregion KeyFrame Menu
    }
}
#endif
