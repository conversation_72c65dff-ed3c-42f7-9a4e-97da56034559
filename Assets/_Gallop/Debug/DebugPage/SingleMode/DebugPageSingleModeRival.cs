#if !CYG_PRODUCT
using Cute.Core;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 育成ライバル関連
    /// </summary>
    [DebugPage(typeof(DebugPageTopMenuRoot))]
    public class DebugPageSingleModeRival : DebugPageButtonMenuBase
    {
        private GameObject _verticalLayoutRoot;

        private int _selectMyCharaId = 1001;
        private int _selectMyDressId = (int)ModelLoader.DressID.SRCommon;
        private int _selectMyCharaIndex = 0;

        private int _selectRivalCharaId = 1002;
        private int _selectRivalDressId = (int)ModelLoader.DressID.SRCommon;
        private int _selectRivalCharaIndex = 1;

        private List<MasterCharaData.CharaData> _masterCharaDataList = null;
        private string[] _charaNameArray = null;

        /// <summary>
        /// 制作されたメニューのオブジェクト
        /// </summary>
        private List<GameObject> _list = new List<GameObject>();

        /// <summary>
        /// キャラデータ作成フラグ
        /// </summary>
        private bool _isMakeCharacterData = false;

        /// <summary>
        /// メニュー作成
        /// </summary>
        protected override void BuildMenu()
        {
            DebugPageContentFactory factory = _parentController.UIPartsFactory;

            // 縦並びになるLayoutGroup
            _verticalLayoutRoot = DebugPageContentFactory.AddVerticalLayoutRoot(_debugPageRoot, 10.0f);

            MakeCharacterData();
            MakeHeightSettingUI(factory);
            //TODO:oi_shinya:いったんライバルリザルトは実装しないのでコメントアウトしておく
            //MakePlayerUI(factory);
            MakeRivalUI(factory);

            _list.Add(factory.AddDebugButtonWithLayoutElement(_verticalLayoutRoot, "演出再生", Play));
        }

        /// <summary>
        /// キャラ一覧作成
        /// </summary>
        /// <param name="factory"></param>
        private void MakeCharacterData()
        {
            if (_isMakeCharacterData)
                return;

            // デバッグ中はまだプレイアブルでないキャラも表示させたいのでchara_data.csvで制御する
            var charaEnumerable = MasterDataManager.Instance.masterCharaData.dictionary.Values.Where(c => c.CharaCategory == 0).OrderBy(c => c.Id);
            var nameList = charaEnumerable.Select(c => c.FormalName).ToList();

            _masterCharaDataList = charaEnumerable.ToList();
            _charaNameArray = nameList.ToArray();

            _isMakeCharacterData = true;
        }

        private void MakeHeightSettingUI(DebugPageContentFactory factory)
        {
            SingleModeRaceEntryViewController viewController = null;
            if (SceneManager.Instance.GetCurrentViewId() == SceneDefine.ViewId.SingleModeRaceEntry)
            {
                viewController = SceneManager.Instance.GetCurrentViewController<SingleModeRaceEntryViewController>();
            }
            GameObject obj = factory.AddTextLabel(_verticalLayoutRoot);
            Text text = obj.GetComponent<Text>();
            text.text = "身長差調整:";

            float min = 1.0f;
            float max = 1.07f;
            if( viewController != null )
            {
                min = viewController.RivalMinScale;
                max = viewController.RivalMaxScale;
            }
            

            var horizontal = DebugPageContentFactory.AddHorizontalLayoutRoot(_verticalLayoutRoot, 10f);
            obj = factory.AddInputFieldWithLabel(horizontal, "Min");
            InputField inputField = obj.GetComponentInChildren<InputField>();
            inputField.text = min.ToString();
            inputField.onValueChanged.AddListener(MinSetting);

            void MinSetting(string input)
            {
                input = input.Replace("．", ".");
                float result = 0.0f;
                if (float.TryParse(input, out result))
                {
                    if (viewController != null)
                    {
                        viewController.RivalMinScale = result;
                    }
                }
            }

            obj = factory.AddInputFieldWithLabel(horizontal, "Max");
            inputField = obj.GetComponentInChildren<InputField>();
            inputField.text = max.ToString();
            inputField.onValueChanged.AddListener(MaxSetting);

            void MaxSetting(string input)
            {
                input = input.Replace("．", ".");
                float result = 0.0f;
                if (float.TryParse(input, out result))
                {
                    if (viewController != null)
                    {
                        viewController.RivalMaxScale = result;
                    }
                }
            }
        }

        private void MakePlayerUI( DebugPageContentFactory factory )
        {
            GameObject obj = factory.AddTextLabel(_verticalLayoutRoot);
            Text text = obj.GetComponent<Text>();
            text.text = "プレイヤー";
            _list.Add(obj);

            obj = factory.AddDebugDropdown(_verticalLayoutRoot, _selectMyCharaIndex, _charaNameArray, (index) =>
            {
                if (_selectMyCharaIndex != index)
                {
                    _selectMyCharaIndex = index;
                    _selectMyCharaId = _masterCharaDataList[_selectMyCharaIndex].Id;
                    Build();
                }
            });
            obj.GetComponent<LayoutElement>().minWidth = 500;
            _list.Add(obj);

            if (_selectMyCharaId != GameDefine.INVALID_CHARA_ID)
            {
                var dressEnumerable = MasterDataManager.Instance.masterDressData.dictionary.Values.Where(c => c.CharaId == 0 || c.CharaId == _selectMyCharaId).OrderBy(c => c.Id);
                var nameList = dressEnumerable.Select(c => c.Name).ToList();
                var dressDataList = dressEnumerable.ToList();
                var dressNameDropDownStringArray = nameList.ToArray();

                int dressIndex = 0;
                for (int i = 0, count = dressDataList.Count; i < count; i++)
                {
                    if (dressDataList[i].CharaId == _selectMyCharaId)
                    {
                        dressIndex = i;
                        break;
                    }
                }

                obj = factory.AddDebugDropdown(_verticalLayoutRoot, dressIndex, dressNameDropDownStringArray, (index) =>
                {
                    _selectMyDressId = dressDataList[index].Id;

                });
                obj.GetComponent<LayoutElement>().minWidth = 500;
                _list.Add(obj);
            }
        }

        private void MakeRivalUI(DebugPageContentFactory factory)
        {
            GameObject obj = factory.AddTextLabel(_verticalLayoutRoot);
            Text text = obj.GetComponent<Text>();
            text.text = "ライバル";
            _list.Add(obj);

            obj = factory.AddDebugDropdown(_verticalLayoutRoot, _selectRivalCharaIndex, _charaNameArray, (index) =>
            {
                if (_selectRivalCharaIndex != index)
                {
                    _selectRivalCharaIndex = index;
                    _selectRivalCharaId = _masterCharaDataList[_selectRivalCharaIndex].Id;
                    Build();
                }
            });
            obj.GetComponent<LayoutElement>().minWidth = 500;
            _list.Add(obj);

            if (_selectRivalCharaId != GameDefine.INVALID_CHARA_ID)
            {
                var dressEnumerable = MasterDataManager.Instance.masterDressData.dictionary.Values.Where(c => c.CharaId == 0 || c.CharaId == _selectRivalCharaId).OrderBy(c => c.Id);
                var nameList = dressEnumerable.Select(c => c.Name).ToList();
                var dressDataList = dressEnumerable.ToList();
                var dressNameDropDownStringArray = nameList.ToArray();

                int dressIndex = 0;
                for (int i = 0, count = dressDataList.Count; i < count; i++)
                {
                    if (dressDataList[i].CharaId == _selectRivalCharaId)
                    {
                        dressIndex = i;
                        break;
                    }
                }

                obj = factory.AddDebugDropdown(_verticalLayoutRoot, dressIndex, dressNameDropDownStringArray, (index) =>
                {
                    _selectRivalDressId = dressDataList[index].Id;

                });
                obj.GetComponent<LayoutElement>().minWidth = 500;
                _list.Add(obj);
            }
        }

        /// <summary>
        /// デバッグUIの再生成
        /// 入力に合わせて再生成されます
        /// </summary>
        protected override void ReBuildMenu()
        {
            base.ReBuildMenu();

            for (int i = 0, count = _list.Count; i < count; i++)
            {
                GameObject.Destroy(_list[i]);
                _list[i] = null;
            }
            _list.Clear();

            BuildMenu();
        }

        private void Play()
        {
            switch (SceneManager.Instance.GetCurrentViewId())
            {
                case SceneDefine.ViewId.SingleModeRaceEntry:
                    {
                        SingleModeRaceEntryViewController viewController =  SceneManager.Instance.GetCurrentViewController<SingleModeRaceEntryViewController>();
                        var race = MasterDataManager.Instance.masterRace.Get(3001);
                        UpdateDispatcher.StartCoroutine( viewController.PlayRivalEntryCoroutine(_selectRivalCharaId, _selectRivalDressId, 5, race.Id, (RaceDefine.Grade)race.Grade, 10, viewController.DestroyRivalEntry) );
                    }
                    break;

                default:
                    break;
            }
        }
    }

}
#endif