using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 背景：画像：親
    /// </summary>
    [System.Serializable]
    public class StoryTimelineBgTrackData : StoryTimelineTrackData
    {
        #region abstract

        /// <summary>Trackの種別</summary>
        public override TrackType Type => TrackType.Bg;

        /// <summary>キー種別</summary>
        public override ClipKeyType KeyType => ClipKeyType.Frame;

        /// <summary>Update順</summary>
        public override int UpdatePriority => (int)TrackUpdatePriority.Bg;

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// Clipを追加する際に前のクリップをコピーしてくるかどうか
        /// </summary>
        public override bool IsCopyLastClip => true;

        /// <summary>
        /// ClipがLerpをサポートするか
        /// </summary>
        public override bool SupportLerp => true;

        /// <summary>
        /// タイムラインのツール上に表示される名前
        /// </summary>
        public override string TrackDispName => "背景";

        /// <summary>
        /// タイムラインのツール上に表示されるグループ名前
        /// </summary>
        public override string TrackGroupDispName => "背景";

        public override void FindUserDataError(System.Action<string> reportWarning, System.Action<string> reportError)
        {
            // ストーリーのみでチェック
            if (BlockData.TimelineData.SceneType == StoryTimelineData.SupportedSceneType.Story)
            {
                int clipCount = ClipList.Count;
                for (int i = 0; i < clipCount; i++)
                {
                    StoryTimelineBgClipData clip = ClipList[i] as StoryTimelineBgClipData;
                    clip.CheckBgTrackUsable(reportWarning);
                }
            }
        }
#endif

        #endregion abstract

        #region 作業中のデータ

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// どのツールで表示するか
        /// </summary>
        public override int DispToolFlag => (int)DispToolFlagType.StoryAndTutorial;

#endif

        #endregion 　作業中のデータ

        #region 子トラック
        /// <summary>
        /// 背景移動
        /// </summary>
        public StoryTimelineBgOffsetTrackData StoryTimelineBgOffsetTrackData = new StoryTimelineBgOffsetTrackData();
        #endregion 子トラック

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public StoryTimelineBgTrackData()
        {
            // 子
            StoryTimelineBgOffsetTrackData = new StoryTimelineBgOffsetTrackData();
        }

        /// <summary>
        /// 親子付け
        /// </summary>
        public override void SetRelation(StoryTimelineBlockData blockData, ref int index)
        {
            base.SetRelation(blockData, ref index);

            // 子供検索
            if (_childTrackList != null)
            {
                return;
            }

            // 子のリスト作成
            _childTrackList = new List<StoryTimelineTrackData>
            {
                StoryTimelineBgOffsetTrackData,
            };

            // 親登録 (BlockData.FindTrackIndexと結果が同じになるように0始まりのループにする)
            int count = _childTrackList.Count;
            for (int i = 0; i < count; i++)
            {
                _childTrackList[i].ParentTrack = this;
                _childTrackList[i].SetRelation(blockData, ref index);
            }
        }

        /// <summary>
        /// Clipが配置されていないときに呼び出されるUpdate
        /// </summary>
        protected override void UpdateNonClip(StoryTimelineClipData prevClip, int frameCount)
        {
            if (prevClip == null)
            {
                // 対応不要
                return;
            }

            // Skipされた時にも背景が切り替わるようにエディタ以外でも実行が必要
            prevClip.StartClipData(0, null, false);
        }
    }

    /// <summary>
    /// 背景：移動：子
    /// </summary>
    [System.Serializable]
    public class StoryTimelineBgOffsetTrackData : StoryTimelineTrackData
    {
        #region abstract

        /// <summary>Trackの種別</summary>
        public override TrackType Type => TrackType.BgOffset;

        /// <summary>キー種別</summary>
        public override ClipKeyType KeyType => ClipKeyType.Frame;

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// Clipを追加する際に前のクリップをコピーしてくるかどうか
        /// </summary>
        public override bool IsCopyLastClip => true;

        /// <summary>
        /// タイムラインのツール上に表示される名前
        /// </summary>
        public override string TrackDispName => "背景移動";

        public override void FindUserDataError(Action<string> reportWarning, Action<string> reportError)
        {
            // ストーリーのみでチェック
            if (BlockData.TimelineData.SceneType == StoryTimelineData.SupportedSceneType.Story)
            {
                int clipCount = ClipList.Count;
                for (int i = 0; i < clipCount; i++)
                {
                    ClipList[i].FindUserDataError(reportWarning, reportError);
                }
            }
        }
#endif
        #endregion abstract

        /// <summary>
        /// Clipが配置されていないときに呼び出されるUpdate
        /// </summary>
        protected override void UpdateNonClip(StoryTimelineClipData prevClip, int frameCount)
        {
            if (prevClip == null)
            {
                return;
            }

            prevClip.StartClipData(frameCount, null, false);
        }
    }
}
