#if UNITY_EDITOR
//#define BATCH_OUTPUT_MOTIONCAMERA_CUTT
using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Gallop.Live.Cutt
{
    //EditingKey種類
    enum EditKeyKind : int
    {
        Current = 0,
        Next,

        Max
    }

    //--------------------------------------------------------------------------------
    #region GUIContext class define
    //--------------------------------------------------------------------------------
    //OnGUIで引き回したいデータを入れるもの
    public class GUIContext
    {
        public CutTool tool;
        public Event e;
        public EditSheet curOnGUISheet;
        public LiveTimelineKey curOnGUIKey;

        public float keyframeBarX;
        public float keyframeBarWidth;
        public float keyframeBarHeight;
        public float keyframeAreaTop = 0f;

        public List<float> keyframeAreaTopList = new List<float>();
        public int requiredVertScrollEditLineIndex = -1;

        public float keyframeUnitMarkStartFrame;
        public float keyframeUnitMarkStartPos;
        public int kKeyframeUnitMarkVisibleNum;

#if true
        public bool requireRepaint;
#else
        bool _requireRepaint = false;
        public bool requireRepaint
        {
            get { return _requireRepaint; }
            set
            {
                _requireRepaint = value;
                if (_requireRepaint)
                {
                    Debug.Log("requireRepaint:" + _requireRepaint);
                }
            }
        }
#endif
        public bool requireCorrectEditKey;

        public int currentFrameBackup;//Context生成時点でのCurrentFrame
        public EditTimeline editingLineBackup;//Context生成時点での編集ライン

        public Rect windowRect;
        public Rect keyframeLabelAreaLastRect;
        public Rect keyframeAreaAboveRect;

        //MousePositionは基本的には使用する場所でctx.e.mousePositionして利用するものだが
        //それは実行したときのGUIローカル座標で取得されるらしいので、mousePositionを別階層で
        //共通の基準値として使用したい場合に、このctx.mousePositionを使う。
        public Vector2 mousePosition;

        //EditingKeyのOnGUIで選択状態にしたいBezierControlPointのIndexを入れる
        public int IndexOfBezierCPForSelect;

        public List<LiveTimelineKey> selectionKeysForMultiEdit = null;

        //-------------------------------------------
        #region GUI.colorスタック
        //-------------------------------------------
        public void GUIColorPush(Color c)
        {
            _guiColorStack.Push(GUI.color);
            GUI.color = c;
        }
        public void GUIColorPop()
        {
            if (_guiColorStack.Count > 0)
            {
                Color c = _guiColorStack.Pop();
                GUI.color = c;
            }
        }
        Stack<Color> _guiColorStack = new Stack<Color>();
        //-------------------------------------------
        #endregion GUI.colorスタック
        //-------------------------------------------

        //-------------------------------------------
        #region Event.modifierチェック
        //-------------------------------------------
        public bool CheckModifiers(EventModifiers check)
        {
            return CheckModifiers(e.modifiers, check);
        }
        public bool CheckModifiers(EventModifiers src, EventModifiers check)
        {
            return src == check;
        }
        public bool ContainModifiers(EventModifiers check)
        {
            return (e.modifiers & check) != 0;
        }
        //-------------------------------------------
        #endregion Event.modifierチェック
        //-------------------------------------------

        //-------------------------------------------
        #region Mouse押下チェック
        //-------------------------------------------
        public bool IsMouseLeft()
        {
            return e.button == 0;
        }
        public bool IsMouseRight()
        {
            return e.button == 1;
        }
        public bool IsMouseMiddle()
        {
            return e.button == 2;
        }
        public bool IsMouseDownLeft()
        {
            return e.type == EventType.MouseDown && IsMouseLeft();
        }
        public bool IsMouseDownRight()
        {
            return e.type == EventType.MouseDown && IsMouseRight();
        }
        public bool IsMouseUpLeft()
        {
            return e.type == EventType.MouseUp && IsMouseLeft();
        }
        public bool IsMouseDragLeft()
        {
            return e.type == EventType.MouseDrag && IsMouseLeft();
        }
        //-------------------------------------------
        #endregion Mouse押下チェック
        //-------------------------------------------

        //-------------------------------------------
        #region シングルキー関係
        //-------------------------------------------
        /// <summary>
        /// 編集不可のIntField
        /// </summary>
        /// <param name="label"></param>
        /// <param name="value"></param>
        public void LockedIntField(string label, int value)
        {
            bool isChangeEnabled = false;
            if (GUI.enabled)
            {
                GUI.enabled = false;
                isChangeEnabled = true;
            }
            EditorGUILayout.IntField(label, value);
            if (isChangeEnabled)
            {
                GUI.enabled = true;
            }
        }
        /// <summary>
        /// 編集不可のFloatField
        /// </summary>
        /// <param name="label"></param>
        /// <param name="value"></param>
        public void LockedFloatField(string label, float value)
        {
            bool isChangeEnabled = false;
            if (GUI.enabled)
            {
                GUI.enabled = false;
                isChangeEnabled = true;
            }
            EditorGUILayout.FloatField(label, value);
            if (isChangeEnabled)
            {
                GUI.enabled = true;
            }
        }
        /// <summary>
        /// 編集不可のVector3Field
        /// </summary>
        /// <param name="label"></param>
        /// <param name="value"></param>
        public void LockedVector3Field(string label, Vector3 value)
        {
            bool isChangeEnabled = false;
            if (GUI.enabled)
            {
                GUI.enabled = false;
                isChangeEnabled = true;
            }
            EditorGUIHelper.Vector3Field(label, value);
            if (isChangeEnabled)
            {
                GUI.enabled = true;
            }
        }

        /// <summary>
        /// 編集不可のTextField
        /// </summary>
        /// <param name="label"></param>
        /// <param name="text"></param>
        /// <returns></returns>
        public void LockedTextField(string label, string text)
        {
            bool isChangeEnabled = false;
            if (GUI.enabled)
            {
                GUI.enabled = false;
                isChangeEnabled = true;
            }
            EditorGUILayout.TextField(label, text);
            if (isChangeEnabled)
            {
                GUI.enabled = true;
            }
        }

        /// <summary>
        /// 編集不可のToggle
        /// </summary>
        /// <param name="label"></param>
        /// <param name="value"></param>
        public void LockedToggle(string label, bool value)
        {
            bool isChangeEnabled = false;
            if (GUI.enabled)
            {
                GUI.enabled = false;
                isChangeEnabled = true;
            }
            EditorGUILayout.Toggle(label, value);
            if (isChangeEnabled)
            {
                GUI.enabled = true;
            }
        }
        //-------------------------------------------
        #endregion シングルキー関係
        //-------------------------------------------

        //-------------------------------------------
        #region マルチキーエディット関係
        //-------------------------------------------
        public List<LiveTimelineKey> multiEditKeys = new List<LiveTimelineKey>();
#if false
        public static bool s_canEditKeyMulti = false;
        public bool canEditKeyMulti { get{ return tool.IsEditingSheet(curOnGUISheet) && s_canEditKeyMulti; } }
#else
        public bool canEditKeyMulti = false;
#endif

        public delegate void MultiKeyEditApplyDelegate<_TKey>(_TKey key) where _TKey : LiveTimelineKey;
        //複数編集可能対象キーリストへ編集結果反映処理を適用する
        public void MultiKeyEditApply<_TKey>(MultiKeyEditApplyDelegate<_TKey> handler, bool isForced = false) where _TKey : LiveTimelineKey
        {
            var applyKeys = new List<LiveTimelineKey>(multiEditKeys);
            applyKeys.Remove(curOnGUIKey);//編集中Keyは除く
            if (!keyPropetyGuiChangedGuard)
            {
                keyPropetyGuiChanged = true;
                keyPropetyGuiChangedGuard = true;
            }

            foreach (var k in applyKeys)
            {
                if (k is _TKey)
                {
                    if (!isForced && k.IsLockedLineOrKey())
                        continue;
                    //UndoようにApply前のClone
                    undoKeySnapshot.Add(k);
                    handler(k as _TKey);
                }
            }
        }

        /// <summary>
        /// 手動でまとめてUndo登録
        /// </summary>
        /// <param name="key"></param>
        public void ManualAddSnapShot( LiveTimelineKey key )
        {
            if(key == null)
            {
                return;
            }

            keyPropetyGuiChanged = true;
            undoKeySnapshot.Add(key);
        }

        //curOnGUIKeyが複数編集可能な状態かどうかを返す。
        bool CanEditKeyMulti(System.Type[] allowTypes)
        {
            if (multiEditKeys.Count == 0)
            {
                return false;
            }
            if (!canEditKeyMulti)
            {
                return false;
            }

            int arraySize = allowTypes != null ? allowTypes.Length : 0;
            arraySize += curOnGUIKey != null ? 1 : 0;
            if (arraySize == 0)
            {
                return false;
            }
            System.Type[] copiedAllowTypes = new System.Type[arraySize];
            if (allowTypes != null && allowTypes.Length > 0)
            {
                allowTypes.CopyTo(copiedAllowTypes, 0);
            }
            if (curOnGUIKey != null)
            {
                copiedAllowTypes[copiedAllowTypes.Length - 1] = curOnGUIKey.GetType();
            }

            foreach (System.Type allowType in copiedAllowTypes)
            {
                if (multiEditKeys.Any(x => !x.IsLockedLineOrKey() && (x.GetType() == allowType || x.GetType().IsSubclassOf(allowType))))
                {
                    //どれか一つでもallowTypeと継承関係にある場合にMultiEdit可能とする
                    return true;
                }
            }
            return false;
        }

        //-------------------------------------------
        #region マルチキーエディット関係 - GUI関数
        //-------------------------------------------
        public bool ME_EnumPopup(string label, ref System.Enum value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.EnumPopup("*" + label, value);
            }
            else
            {
                value = EditorGUILayout.EnumPopup(label, value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_EnumMask(string label, ref System.Enum value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
#if UNITY_2017_3_OR_NEWER
                value = EditorGUILayout.EnumFlagsField("*" + label, value);
#else
                value = EditorGUILayout.EnumMaskField("*" + label, value);
#endif
            }
            else
            {
#if UNITY_2017_3_OR_NEWER
                value = EditorGUILayout.EnumFlagsField(label, value);
#else
                value = EditorGUILayout.EnumMaskField(label, value);
#endif
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Popup(string label, ref int value, string[] options, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.Popup("*" + label, value, options);
            }
            else
            {
                value = EditorGUILayout.Popup(label, value, options);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Toolbar(string label, ref int value, string[] options, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.BeginHorizontal();
            if (CanEditKeyMulti(allowTypes))
            {
                EditorGUILayout.LabelField("*" + label, GUILayout.MaxWidth(100));
            }
            else
            {
                EditorGUILayout.LabelField(label, GUILayout.MaxWidth(100));
            }
            value = GUILayout.Toolbar(value, options);
            EditorGUILayout.EndHorizontal();
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Mask(string label, ref int value, string[] options, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.MaskField("*" + label, value, options);
            }
            else
            {
                value = EditorGUILayout.MaskField(label, value, options);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_IntField(string label, ref int value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.IntField("*" + label, value);
            }
            else
            {
                value = EditorGUILayout.IntField(label, value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_IntField(ref int value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            value = EditorGUILayout.IntField(value);
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_FloatField(string label, ref float value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.FloatField("*" + label, value);
            }
            else
            {
                value = EditorGUILayout.FloatField(label, value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_TextField(string label, ref string value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.TextField("*" + label, value);
            }
            else
            {
                value = EditorGUILayout.TextField(label, value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Slider(string label, ref float value, float min, float max, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.Slider("*" + label, value, min, max);
            }
            else
            {
                value = EditorGUILayout.Slider(label, value, min, max);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Vector4Field(string label, ref Vector4 value, System.Type[] allowTypes = null, Vector4 init_value = new Vector4())
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUIHelper.Vector4Field("*" + label, value, init_value);
            }
            else
            {
                value = EditorGUIHelper.Vector4Field(label, value, init_value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Vector3Field(string label, ref Vector3 value, System.Type[] allowTypes = null, Vector3 init_value = new Vector3())
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUIHelper.Vector3Field("*" + label, value, init_value);
            }
            else
            {
                value = EditorGUIHelper.Vector3Field(label, value, init_value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_RoundVector3Field(string label, ref Vector3 value, System.Type[] allowTypes = null, Vector3 init_value = new Vector3())
        {
            value = value.Round();
            return ME_Vector3Field(label, ref value, null, init_value);
        }
        public bool ME_Vector2Field(string label, ref Vector2 value, System.Type[] allowTypes = null, Vector2 init_value = new Vector2())
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUIHelper.Vector2Field("*" + label, value, init_value);
            }
            else
            {
                value = EditorGUIHelper.Vector2Field(label, value, init_value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_Toggle(string label, ref bool value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.Toggle("*" + label, value);
            }
            else
            {
                value = EditorGUILayout.Toggle(label, value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_ToggleLeft(string label, ref bool value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.ToggleLeft("*" + label, value);
            }
            else
            {
                value = EditorGUILayout.ToggleLeft(label, value);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_ToggleImage(string label, Texture2D image, ref bool value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                var context = new GUIContent("*" + label);
                context.image = image;
                value = GUILayout.Toggle(value, context);
            }
            else
            {
                var context = new GUIContent(label);
                context.image = image;
                value = GUILayout.Toggle(value, context);
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_ColorField(string label, ref Color value, System.Type[] allowTypes = null)
        {
            // ※Eyedropperで色を取得する際の誤爆を避けるため縦の最低サイズを設定する。
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.ColorField("*" + label, value, GUILayout.MinHeight(20));
            }
            else
            {
                value = EditorGUILayout.ColorField(label, value, GUILayout.MinHeight(20));
            }
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_CharaPosToggle(string label, ref LiveCharaPositionFlag value, System.Type[] allowTypes = null, int characterIndexMax = (int)LiveCharaPosition.CharacterMax)
        {
            EditorGUILayout.LabelField(CanEditKeyMulti(allowTypes) ? "*" + label : label);
            EditorGUI.BeginChangeCheck();
            EditorGUI.indentLevel++;

            int chrNum = tool.liveTimelineData.characterSettings.motionSequenceIndices.Length;
            var flags = value;
            System.Action<LiveCharaPositionFlag, LiveCharaPosition> fnToggle = (bit_, index_) =>
            {
                if ((int)index_ < chrNum)
                {
                    var lbl = LiveTimelineDefine.kLiveCharaPositionFlagDisplayOpts[(int)index_];
                    flags = EditorGUILayout.Toggle(lbl, (flags & bit_) == bit_) ? flags | bit_ : flags & ~bit_;
                }
            };
            {
                bool all = (flags & LiveCharaPositionFlag_Helper.Everyone) == LiveCharaPositionFlag_Helper.Everyone;
                if (EditorGUILayout.Toggle("ALL", all) != all)
                {
                    flags = !all ? LiveCharaPositionFlag_Helper.Everyone : 0;
                }
            }
            for (int i = 0; i <= characterIndexMax; i++)
            {
                fnToggle((LiveCharaPositionFlag)(1 << i), (LiveCharaPosition)i);
            }
            value = flags;

            EditorGUI.indentLevel--;
            return EditorGUI.EndChangeCheck();
        }
        public bool ME_ObjectField<T>(string label, ref UnityEngine.Object value, System.Type[] allowTypes = null)
        {
            EditorGUI.BeginChangeCheck();
            if (CanEditKeyMulti(allowTypes))
            {
                value = EditorGUILayout.ObjectField("*" + label, value, typeof(T), false);
            }
            else
            {
                value = EditorGUILayout.ObjectField(label, value, typeof(T), false);
            }
            return EditorGUI.EndChangeCheck();
        }
        //-------------------------------------------
        #endregion マルチキーエディット関係 - GUI関数
        //-------------------------------------------

        //-------------------------------------------
        #endregion マルチキーエディット関係
        //-------------------------------------------

        //-------------------------------------------
        #region Shuriken風折り畳み
        //-------------------------------------------
        /// <summary>
        /// Shuriken風折り畳み
        /// </summary>
        public static bool FoldOut(string title, bool display)
        {
            GUIStyle style = new GUIStyle("ShurikenModuleTitle");
            style.font = new GUIStyle(EditorStyles.label).font;
            style.border = new RectOffset(15, 7, 4, 4);
            style.fixedHeight = 22;
            style.contentOffset = new Vector2(20f, -2f);

            Rect rect = GUILayoutUtility.GetRect(16f, 22f, style);
            GUI.Box(rect, title, style);

            Event e = Event.current;

            Rect toggleRect = new Rect(rect.x + 4f, rect.y + 2f, 13f, 13f);
            if (e.type == EventType.Repaint)
            {
                EditorStyles.foldout.Draw(toggleRect, false, false, display, false);
            }
            else if (e.type == EventType.MouseDown)
            {
                if (rect.Contains(e.mousePosition))
                {
                    display = !display;
                    e.Use();
                }
            }

            return display;
        }

        /// <summary>
        /// Shuriken風折り畳み
        /// </summary>
        public static bool FoldOut(string title, bool display, Color backgroundColor, int heightBlank)
        {
            GUIStyle style = new GUIStyle("ShurikenModuleTitle");
            style.font = new GUIStyle(EditorStyles.label).font;
            style.border = new RectOffset(15, 7, heightBlank, heightBlank);
            style.fixedHeight = style.lineHeight + heightBlank * 2;
            style.contentOffset = new Vector2(20f, -2f);

            Rect rect = GUILayoutUtility.GetRect(16f, style.fixedHeight, style);
            Color backup = GUI.backgroundColor;
            GUI.backgroundColor = backgroundColor;
            GUI.Box(rect, title, style);
            GUI.backgroundColor = backup;

            Event e = Event.current;

            Rect toggleRect = new Rect(rect.x + 4f, rect.y - 3f + (float)heightBlank, style.lineHeight, style.lineHeight);
            if (e.type == EventType.Repaint)
            {
                EditorStyles.foldout.Draw(toggleRect, false, false, display, false);
            }
            else if (e.type == EventType.MouseDown)
            {
                if (rect.Contains(e.mousePosition))
                {
                    display = !display;
                    e.Use();
                }
            }

            return display;
        }
        //-------------------------------------------
        #endregion Shuriken風折り畳み
        //-------------------------------------------

        //-------------------------------------------
        #region Undo関係
        //-------------------------------------------
        public UndoKeySnapshot undoKeySnapshot = new UndoKeySnapshot();
        public bool keyPropetyGuiChanged = false;
        //毎回newで作成されリセットされるのでstaticで保持する必要がある
        public static bool keyPropetyGuiChangedGuard = false;
        //-------------------------------------------
        #endregion Undo関係
        //-------------------------------------------
    };
    //--------------------------------------------------------------------------------
    #endregion GUIContext class define
    //--------------------------------------------------------------------------------

    //--------------------------------------------------------------------------------
    #region UndoKeySnapshot
    //--------------------------------------------------------------------------------
    public class UndoKeySnapshot
    {
        public List<WeakReference> refKeys = new List<WeakReference>();
        public List<LiveTimelineKey> clonedKeys = new List<LiveTimelineKey>();

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void Add(LiveTimelineKey srcKey)
        {
            if (refKeys.Exists(x => x.Target == srcKey))
            {
                //すでに追加済みだったら無視する（editingKeyが入っている場合がある）
                return;
            }
            refKeys.Add(new WeakReference(srcKey));
            clonedKeys.Add(srcKey.Clone());
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void Remove(LiveTimelineKey removeRefKey)
        {
            var index = refKeys.FindIndex(x => x.IsAlive && x.Target == removeRefKey);
            if (index >= 0)
            {
                refKeys.RemoveAt(index);
                clonedKeys.RemoveAt(index);
            }
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void SetCapacity(int capa)
        {
            if (capa <= 0)
            {
                return;
            }
            if (capa < refKeys.Capacity)
            {
                //Debug.Log("SetCapa."+capa);
                refKeys.Capacity = capa;
            }
            if (capa < clonedKeys.Capacity)
            {
                clonedKeys.Capacity = capa;
            }
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void ApplyUndo()
        {
            for (int i = 0; i < refKeys.Count; i++)
            {
                if (refKeys[i].IsAlive)
                {
                    var target = refKeys[i].Target as LiveTimelineKey;
                    var src = clonedKeys[i];
                    var frame = target.frame;
                    src.Copy(target);
                    target.frame = frame;//frameはUndoしない
                }
            }
        }
    };
    //--------------------------------------------------------------------------------
    #endregion UndoKeySnapshot
    //--------------------------------------------------------------------------------

    //--------------------------------------------------------------------------------
    #region Drag and Drop Worker
    //--------------------------------------------------------------------------------
    public enum DragAndDropKind
    {
        TimelineKey,//タイムライン上のKey
        CharaMotKeyEnd,//CharaMotionSequenceTimelineのモーションEndIcon
        RectSelection,//KeyframeAreaの矩形選択
        DragKeyframeAreaAbove,//KeyframeArea上のFrame数とか表示している部分
        AutoPlayMarker,//AutoPlayMarker
        BookmarkIcon,
    }
    //Timeline上でのDragAndDropワーカー
    public class DragAndDropWorker
    {
        //Worker使用者の任意データ。ワーカー生成時に外部からセット
        public object _userData = null;

        public event Func<DragAndDropWorker, GUIContext, bool> OnDrag;
        public event Action<DragAndDropWorker, GUIContext> OnDrop;
        //コンストラクタで初期化
        public DragAndDropKind _kind = DragAndDropKind.TimelineKey;
        public Vector2 _startPos = Vector2.zero;
        public EventModifiers _startModifier = EventModifiers.None;
        //Updateで更新
        public Vector2 _currentPos = Vector2.zero;
        public int _currentFrame = 0;
        public bool _dragged = false;

        public DragAndDropWorker(DragAndDropKind kind_, GUIContext ctx)
        {
            _kind = kind_;
            //Event.current.mousePositionはどうやら呼び出し時点（GUI階層位置）で座標が変わる？
            //コンストラクタ時点と、Update時点で座標がずれていたのでその可能性が高い
            //そのため、ctx.mousePositionを使用する
            _currentPos = _startPos = ctx.mousePosition;
            _startModifier = ctx.e.modifiers;
        }

        public bool Update(EditSheet sheet, GUIContext ctx)
        {
            if (!UpdateModifier(ctx))
            {
                return false;//終了
            }
            var e = ctx.e;
            var mousePos = ctx.mousePosition;
            if (e.type == EventType.MouseDrag)
            {
                _dragged = true;
                _currentPos = mousePos;
                _currentFrame = sheet.GetFrameNumberFromPosAtGUI(ctx, _currentPos);
                if (OnDrag != null)
                {
                    if (!OnDrag(this, ctx))
                    {
                        return false;//終了
                    }
                }
                e.Use();
            }
            else if (e.rawType == EventType.MouseUp)
            {
                //_currentPos = mousePos;//_currentPosをDrag中の座標に限定するためコメントアウト（e.rawTypeなので）
                _currentFrame = sheet.GetFrameNumberFromPosAtGUI(ctx, _currentPos);
                if (_dragged)
                {//Dragを経由してないとDropできない
                    if (OnDrop != null)
                    {
                        OnDrop(this, ctx);
                    }
                    //Drop時のMouseUpでSelectionがClearされちゃったり面倒なのでUseする
                    //ただし、実際にDragしてない限りはClickの挙動にするため _dragged==true の時だけ
                    e.Use();
                }
                //参照が残ったら危険なので一応ここでもNull入れておく
                _userData = null;
                return false;//終了
            }
            else
            {
                _currentFrame = sheet.GetFrameNumberFromPosAtGUI(ctx, _currentPos);
            }
            return true;
        }

        bool UpdateModifier(GUIContext ctx)
        {
            //Modfierを離したら押してなかったことに
            var anyModfierPressed = _startModifier != EventModifiers.None;
            _startModifier = _startModifier & ctx.e.modifiers;
            if (anyModfierPressed && _startModifier == EventModifiers.None)
            {
                //全部のModifierが離されたらDraggを中止に
                return false;
            }
            return true;
        }
    }
    //--------------------------------------------------------------------------------
    #endregion
    //--------------------------------------------------------------------------------

    //--------------------------------------------------------------------------------
    #region ClipBoard
    //--------------------------------------------------------------------------------
    public enum ClipboardStatus
    {
        None,
        StoredCopyFrame,
        StoredCutFrame,
    }
    public static class ClipboardStatus_Helper
    {
        public static string GetLabel(this ClipboardStatus This)
        {
            switch (This)
            {
                case ClipboardStatus.None: return "None";
                case ClipboardStatus.StoredCopyFrame: return "Copy";
                case ClipboardStatus.StoredCutFrame: return "Cut";
                default: return "Unknown";
            }
        }
    }

    public class ClipBoardInfo
    {
        //Copy -> Remove -> Pasteに対応してみるため参照を保持する
        public int sheetID = CutTool.kInvalidEditLineID;
        public MultiLineCmdInput input = null;
        public ClipboardStatus status = ClipboardStatus.None;

        public ClipBoardInfo(int sheetID_, MultiLineCmdInput input_)
        {
            sheetID = sheetID_;
            input = input_;
        }
    }

    public class ClipBoardPasteInfo
    {
        public int frame = 0;
        public int sheetID = CutTool.kInvalidEditLineID;//Paste先
        public int editLineID = CutTool.kInvalidEditLineID;//Paste先

        public ClipBoardPasteInfo(int frame_, int sheetID_, int editLineID_)
        {
            frame = frame_;
            sheetID = sheetID_;
            editLineID = editLineID_;
        }
    }
    //--------------------------------------------------------------------------------
    #endregion ClipBoard
    //--------------------------------------------------------------------------------

    //--------------------------------------------------------------------------------
    #region キー選択ワーク
    //--------------------------------------------------------------------------------
    public class SelectionWork
    {
        class Line
        {
            public int editLineID = CutTool.kInvalidEditLineID;
            public List<LiveTimelineKey> _keys = new List<LiveTimelineKey>();
        }

        public int _editLineIDOfShiftBase = CutTool.kInvalidEditLineID;
        List<int> _editLineIDList = new List<int>();
        public List<int> editLineIDList { get { return _editLineIDList; } }

        EditSheet _sheet = null;

        //選択Keysリスト
        List<Line> _lineList = new List<Line>();
        //Shift選択の起点となるKey
        public LiveTimelineKey keyOfShiftBase { get { return _keyOfShiftBase; } set { _keyOfShiftBase = value; } }
        //MouseDown時にClickしたKeyが選択状態（Selection内）だったかどうか
        public bool lastMouseDownKeyIsSelected = false;

        public bool isTriggeredKeySetOrRemove = false;

        LiveTimelineKey _keyOfShiftBase = null;



        public SelectionWork(EditSheet sheet)
        {
            _sheet = sheet;
        }

        Line FindLine(int ID)
        {
            return _lineList.FirstOrDefault(x => x.editLineID == ID);
        }

        Line FindOrCreateLine(int ID)
        {
            var line = FindLine(ID);
            if (line == null)
            {
                line = new Line();
                line.editLineID = ID;
                _lineList.Add(line);
            }
            return line;
        }

        public MultiLineCmdInput CreateOpeCmdInput()
        {
            return CreateOpeCmdInput<MultiLineCmdInput>();
        }
        public _TInput CreateOpeCmdInput<_TInput>() where _TInput : MultiLineCmdInput, new()
        {
            var input = new _TInput();

            foreach (var line in _lineList)
            {
                var el = _sheet.QueryEditTimeline(line.editLineID);
                if (el != null && line._keys.Count > 0)
                {
                    input.Add(el, line._keys.ToArray());
                }
            }
            return input;
        }

        public void Clear()
        {
            _lineList = new List<Line>();
            _keyOfShiftBase = null;
            isTriggeredKeySetOrRemove = true;
        }

        //--------------------------------------------------------------
        #region Key
        //--------------------------------------------------------------
        public List<LiveTimelineKey> GetKeys(EditTimeline editLine)
        {
            if (editLine._editKeyList.IsLock)
                return null;

            var ret = FindLine(editLine.ID);

            return ret != null ? ret._keys.Where(x => !x.IsLock).ToList() : null;
        }
        public List<LiveTimelineKey> GetKeys(LiveTimelineKeyDataType typeFilter)
        {
            var ret = new List<LiveTimelineKey>();
            foreach (var line in _lineList)
            {
                var k = line._keys.FirstOrDefault();
                if (k != null && k.dataType == typeFilter)
                {
                    var keys = GetUnlockKeys(line);
                    if (keys == null)
                        continue;
                    ret.AddRange(keys);
                }
            }
            return ret;
        }
        public List<LiveTimelineKey> GetKeysAll()
        {
            var ret = new List<LiveTimelineKey>();
            foreach (var line in _lineList)
            {
                ret.AddRange(line._keys);
            }
            return ret;
        }

        private List<LiveTimelineKey> GetUnlockKeys(Line line)
        {
            var editTimeline = _sheet.QueryEditTimeline(line.editLineID);

            if (editTimeline == null ||
                editTimeline._editKeyList == null)
                return null;

            if (editTimeline._editKeyList.IsLock)
                return null;

            var keys = line._keys.Where(x => !x.IsLock).ToList();
            return keys;
        }

        public List<LiveTimelineKey[]> GetKeysByLine()
        {
            return _lineList.Select(x => x._keys.Where(y => !y.IsLockedLineOrKey()).ToArray()).ToList();
        }

        public List<EditTimeline> GetKeysEditLines()
        {
            return _lineList.Select(x => _sheet.QueryEditTimeline(x.editLineID)).Where(x => !x._editKeyList.IsLock).ToList();
        }

        public void AddKey(EditTimeline editLine, LiveTimelineKey key)
        {
            if (key == null)
            {
                Debug.LogError("key is null");
                Debug.Break();
            }
            var line = FindOrCreateLine(editLine.ID);
            key.IsWorkLineLock = editLine._editKeyList.IsLock;
            line._keys.Add(key);
        }

        public void SetKeys(EditTimeline editLine, LiveTimelineKey[] keys_)
        {
            foreach (var k in keys_)
            {
                if (k == null)
                {
                    Debug.LogError("key is null");
                    Debug.Break();
                }
                else
                {
                    k.IsWorkLineLock = editLine._editKeyList.IsLock;
                }
            }
            var line = FindOrCreateLine(editLine.ID);
            line._keys = new List<LiveTimelineKey>(keys_);
            isTriggeredKeySetOrRemove = true;
        }

        public void RemoveKey(EditTimeline editLine, LiveTimelineKey key)
        {
            var line = FindOrCreateLine(editLine.ID);
            if (line != null)
            {
                line._keys.Remove(key);
            }
            if (_keyOfShiftBase == key)
            {
                _keyOfShiftBase = null;
            }
            isTriggeredKeySetOrRemove = true;
        }

        public bool IsExistsKey(EditTimeline editLine, LiveTimelineKey key)
        {
            var line = FindLine(editLine.ID);
            if (line != null && line._keys.Contains(key))
            {
                return true;
            }
            return false;
        }

        public bool IsExistsAnyKey()
        {
            return _lineList.Any(x => x._keys.Count > 0);
        }
        //--------------------------------------------------------------
        #endregion Key
        //--------------------------------------------------------------

        //--------------------------------------------------------------
        #region Line
        //--------------------------------------------------------------
        public void ClearLine()
        {
            _editLineIDList.Clear();
        }

        public EditTimeline[] GetEditLineArray()
        {
            return _editLineIDList.Select(x => _sheet.QueryEditTimeline(x)).ToArray();
        }

        public EditTimeline[] GetEditLineArray(LiveTimelineKeyDataType filterType)
        {
            return _editLineIDList.Select(x => _sheet.QueryEditTimeline(x)).Where(x => (x.keyDataType == filterType) && !x._editKeyList.IsLock).ToArray();
        }

        public void SetLine(EditTimeline editLine)
        {
            _editLineIDOfShiftBase = CutTool.kInvalidEditLineID;
            ClearLine();
            ToggleLine(editLine);
        }

        public void AddLine(EditTimeline editLine)
        {
            if (editLine == null)
            {
                return;
            }
            if (!_editLineIDList.Contains(editLine.ID))
            {
                _editLineIDList.Add(editLine.ID);
            }
        }

        public void ToggleLine(EditTimeline editLine)
        {
            if (editLine == null)
            {
                return;
            }
            if (_editLineIDList.Contains(editLine.ID))
            {
                RemoveLine(editLine);
            }
            else
            {
                _editLineIDList.Add(editLine.ID);
            }
        }
        public void RemoveLine(EditTimeline editLine)
        {
            if (editLine == null)
            {
                return;
            }
            if (_editLineIDList.Contains(editLine.ID))
            {
                _editLineIDList.Remove(editLine.ID);
            }
            if (editLine.ID == _editLineIDOfShiftBase)
            {
                _editLineIDOfShiftBase = CutTool.kInvalidEditLineID;
            }
        }

        public bool IsExistsLine(EditTimeline editLine)
        {
            return _editLineIDList.Contains(editLine.ID);
        }

        public int GetEditLineArrayNum(LiveTimelineKeyDataType type)
        {
            int result = 0;
            for (int i = 0; i < _editLineIDList.Count; i++)
            {
                var timeline = _sheet.QueryEditTimeline(_editLineIDList[i]);
                if (timeline.keyDataType == type)
                {
                    result++;
                }
            }
            return result;
        }

        //--------------------------------------------------------------
        #endregion Line
        //--------------------------------------------------------------
    }
    //--------------------------------------------------------------------------------
    #endregion キー選択ワーク
    //--------------------------------------------------------------------------------

    //--------------------------------------------------------------------------------
    #region Editor helper
    //--------------------------------------------------------------------------------
    /// <summary>
    /// UnityEditorのPlay前（Stop状態）にTimelineEditorが起動していた場合に、Playした瞬間にTimelineEditorを
    /// Closeして、Playが終わった時に自動でReOpenするための機構
    /// </summary>
    [InitializeOnLoad]
    class EditorPlayMode
    {
        static EditorPlayMode()
        {
            //Debug.Log("cnstructor s_requireRelaunch:"+EditorPrefs.GetBool(kPrefsKeyName));
#if UNITY_2017_3_OR_NEWER
            EditorApplication.playModeStateChanged += (state) =>
#else
            EditorApplication.playmodeStateChanged += () =>
#endif
            {
                if (EditorApplication.isPlayingOrWillChangePlaymode)
                {
                    //Debug.Log("playmodeStateChanged isPlayingOrWillChangePlaymode");
                    if (CutTool.instance != null)
                    {
                        CutTool.instance.Close();
                        EditorPrefs.SetBool(LiveTimelineDefine.kPrefsKeyName_ToolLaunchControl, true);//static変数だとPlay時のInitializeでResetされるのでPrefs
                        //Debug.Log("s_requireRelaunch:"+EditorPrefs.GetBool(kPrefsKeyName));
                    }
                }
                if (EditorApplication.isPlaying)
                {
                    //Debug.Log("playmodeStateChanged isPlaying. s_requireRelaunch:"+EditorPrefs.GetBool(kPrefsKeyName));
                    if (CutTool.instance == null && EditorPrefs.GetBool(LiveTimelineDefine.kPrefsKeyName_ToolLaunchControl))
                    {
                        EditorPrefs.SetBool(LiveTimelineDefine.kPrefsKeyName_ToolLaunchControl, false);
                        CutTool.Open();
                    }
                }
            };
        }
    }

    static class LiveTimelineKeyDataListHelper
    {
        /// <summary>
        /// [UNITY_EDITOR Only]
        /// Frame数の昇順になるようにKeyをInsertする
        /// </summary>
        /// <param name="list"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static bool InsertKeyframeConsiderOrder(this ILiveTimelineKeyDataList list, LiveTimelineKey key)
        {
            int i = 0;
            foreach (var k in list)
            {
                if (key.frame == k.frame)
                {
                    return false;
                }
                if (key.frame < k.frame)
                {
                    break;
                }
                i++;
            }
            list.Insert(i, key);
            return true;
        }

        /// <summary>
        /// [UNITY_EDITOR Only]
        /// Frame数がちゃんと昇順になっているかチェック
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static bool CheckKeyframeOrder(this ILiveTimelineKeyDataList list)
        {
            int frame = -1;
            foreach (var k in list)
            {
                if (frame >= k.frame)
                {
                    Debug.LogError("Keyframe order had error. Frames NOT ascending.");
                    return false;
                }
                frame = k.frame;
            }
            return true;
        }

        /// <summary>
        /// [UNITY_EDITOR Only]
        /// Frame数がちゃんと昇順になっていない場合、犯人候補をメッセージにして返します
        /// </summary>
        public static string GetMessageForCriminalFrame(this ILiveTimelineKeyDataList list)
        {
            int frame = -1;
            foreach (var k in list)
            {
                if (frame >= k.frame)
                {
                    return string.Format("{0}frameと{1}frameのkeyに問題があります。", frame, k.frame);
                }
                frame = k.frame;
            }
            return "";
        }
    }

    public static class EditorGUIHelper
    {
        //Resetボタン付きVector3Field
        public static Vector4 Vector4Field(string label, Vector4 value, Vector4 init_value = new Vector4())
        {
            value = EditorGUILayout.Vector4Field(label, value);
            var lr = GUILayoutUtility.GetLastRect();
            var btnr = lr;
            btnr.x = lr.x + (lr.width - 22);
            btnr.width = 20;
            btnr.height = 15;
            if (GUI.Button(btnr, "R"))
            {
                value = init_value;
            }
            return value;
        }
        //Resetボタン付きVector3Field
        public static Vector3 Vector3Field(string label, Vector3 value, Vector3 init_value = new Vector3())
        {
            value = EditorGUILayout.Vector3Field(label, value);
            var lr = GUILayoutUtility.GetLastRect();
            var btnr = lr;
            btnr.x = lr.x + (lr.width - 22);
            btnr.width = 20;
            btnr.height = 15;
            if (GUI.Button(btnr, "R"))
            {
                value = init_value;
            }
            return value;
        }
        //Resetボタン付きVector2Field
        public static Vector2 Vector2Field(string label, Vector2 value, Vector2 init_value = new Vector2())
        {
            value = EditorGUILayout.Vector2Field(label, value);
            var lr = GUILayoutUtility.GetLastRect();
            var btnr = lr;
            btnr.x = lr.x + (lr.width - 22);
            btnr.width = 20;
            btnr.height = 15;
            if (GUI.Button(btnr, "R"))
            {
                value = init_value;
            }
            return value;
        }

        //独自Preset対応CurveField
        public static AnimationCurve CurveField(GUIContext ctx, string label, AnimationCurve value, Color color, Rect rect, int cidHint)
        {
            bool enabled = GUI.enabled;

            GUILayout.BeginHorizontal();
            EditorGUILayout.PrefixLabel(label);

            //CurveFieldは挙動が特殊で、これとCurvePresetWindowの動作を成立させるためにいろいろワークアラウンドしている

            var isOpenedPresetWindow = CutTool_CurvePresetWindow.instance != null;
            if (enabled)
            {
                GUI.enabled = !isOpenedPresetWindow;
            }

            var controlID = GUIUtility.GetControlID(cidHint, FocusType.Passive, GUILayoutUtility.GetLastRect());
            if (isOpenedPresetWindow && CutTool_CurvePresetWindow.instance.IsEditFor(controlID))
            {
                value = CutTool_CurvePresetWindow.instance.GetEditCorve();
            }

            if (isOpenedPresetWindow)
            {
                EditorGUILayout.CurveField(new AnimationCurve(), color, rect);//Dummy for GetLastRect()
            }
            else
            {
                //PresetWindow側でCurveField使っている時に、ここでも使ってしまうと
                //ここのCurveFieldが（アイコンを押下してないのに）カーブエディタを乗っ取ってしまう。
                //なので、PresetWindow出てない時はSwatchでダミー描画
                value = EditorGUILayout.CurveField(value, color, rect);
            }

            GUI.enabled = enabled;

            if (isOpenedPresetWindow)
            {
                EditorGUIUtility.DrawCurveSwatch(GUILayoutUtility.GetLastRect(), value, null, Color.green, Color.black);
            }
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            {
                GUILayout.FlexibleSpace();
                GUILayout.Label("Curve");
                if (GUILayout.Button("Save", EditorStyles.miniButtonLeft, GUILayout.Width(50)))
                {
                    var addCurve = new AnimationCurve(value.keys.ToArray());
                    CutTool.instance.curvePreset.AddCurve(addCurve, "");
                }
                if (GUILayout.Button("Load", EditorStyles.miniButtonRight, GUILayout.Width(50)))
                {
                    GUIUtility.keyboardControl = 0;
                    CutTool_CurvePresetWindow.Open(CutTool.instance, value, controlID);
                }
                if (ctx.canEditKeyMulti)
                {
                    GUILayout.Space(2);
                    if (GUILayout.Button("*Apply", EditorStyles.miniButton, GUILayout.Width(60)))
                    {
                        ctx.MultiKeyEditApply<LiveTimelineKeyWithInterpolate>(x =>
                        {
                            x.curve = new AnimationCurve(value.keys);
                        });
                        ctx.requireRepaint = true;
                    }
                }
            }
            GUILayout.EndHorizontal();
            return value;
        }

        /// <summary>
        /// 変更箇所のみコピーする
        /// </summary>
        /// <param name="dst"></param>
        /// <param name="oldValue"></param>
        /// <param name="newValue"></param>
        public static void SetChangeValue(ref Vector3 dst,ref Vector3 oldValue, ref Vector3 newValue)
        {
            bool x = Math.IsFloatEqual(oldValue.x, newValue.x);
            bool y = Math.IsFloatEqual(oldValue.y, newValue.y);
            bool z = Math.IsFloatEqual(oldValue.z, newValue.z);

            if (!x) dst.x = newValue.x;
            if (!y) dst.y = newValue.y;
            if (!z) dst.z = newValue.z;
        }
    }
    //--------------------------------------------------------------------------------
    #endregion
    //--------------------------------------------------------------------------------

    //--------------------------------------------------------------------------------
    #region MusicProxy
    //--------------------------------------------------------------------------------
    //UnityPackage化しやすいようにApp依存を分離
    public interface IMusicProxy
    {
        bool isAvailable { get; }
        bool isPlaying { get; }
        bool isMusicAvailable { get; }
        float musicTime { get; }
        void StopMusic();
        void PlayMusic(float time);
    }

    class MusicProxyDummy : IMusicProxy
    {
        public bool isAvailable { get { return false; } }
        public bool isPlaying { get { return false; } }
        public bool isMusicAvailable { get { return false; } }
        public float musicTime { get { return 0f; } }
        public void StopMusic() { }
        public void PlayMusic(float time) { }
    }

    #region xxxxxソースを参考に残す
    //#if xxxxx//ProjectStage用
    //    class MusicProxyStage : IMusicProxy
    //    {
    //        public bool isAvailable { get{return AudioManager.IsInstanceEmpty();} }
    //        public bool isPlaying { get{return AudioManager.Instance.IsPlayingSong();} }
    //        public bool isMusicAvailable { get{return AudioManager.Instance.IsSongAvailable();} }
    //        public float musicTime { get{return AudioManager.Instance.songTime;} }
    //        public void StopMusic() { AudioManager.Instance.StopSong(); }
    //        public void PlayMusic(float time) { AudioManager.Instance.PlaySong(time); }
    //    }
    //#endif//#if xxxxx
    #endregion xxxxxソースを参考に残す

    class MusicProxyGallop : IMusicProxy
    {
        public bool isAvailable { get { return true; } }
        public bool isPlaying
        {
            get
            {
                switch (SheetType)
                {
                    case LiveTimelineDefine.SheetIndex.PreLiveSkit:
                    case LiveTimelineDefine.SheetIndex.AfterLiveSkit:
                        {
                            string soundName = Director.GetSkitSoundName(SheetType == LiveTimelineDefine.SheetIndex.PreLiveSkit ? true : false);
                            return AudioManager.Instance.IsPlayBgm(soundName,0);
                        }

                    case LiveTimelineDefine.SheetIndex.MainLive:
                        return AudioManager.Instance.IsPlayingSong();
                }
                return false;
            }
        }

        public bool isMusicAvailable
        {
            get
            {
                switch (SheetType)
                {
                    case LiveTimelineDefine.SheetIndex.PreLiveSkit:
                    case LiveTimelineDefine.SheetIndex.AfterLiveSkit:
                        {
                            string soundName = Director.GetSkitSoundName(SheetType == LiveTimelineDefine.SheetIndex.PreLiveSkit ? true : false);
                            return AudioManager.IsAvailableCueSheet(soundName);
                        }

                    case LiveTimelineDefine.SheetIndex.MainLive:
                        return AudioManager.Instance.IsAvailableSongCueSheet();

                    case LiveTimelineDefine.SheetIndex.Master:
                        //どれか1つでも成功したら成功した扱いにする
                        if(AudioManager.IsAvailableCueSheet(Director.GetSkitSoundName(true)))
                        {
                            return true;
                        }
                        if (AudioManager.IsAvailableCueSheet(Director.GetSkitSoundName(false)))
                        {
                            return true;
                        }
                        return AudioManager.Instance.IsAvailableSongCueSheet();
                }
                return false;
            }
        }

        public float musicTime
        {
            get
            {
                const float MSecToSec = 1.0f / 1000.0f;
                switch (SheetType)
                {
                    case LiveTimelineDefine.SheetIndex.PreLiveSkit:
                    case LiveTimelineDefine.SheetIndex.AfterLiveSkit:
                        return StartTime + (AudioManager.Instance.BgmPlayback.Time * MSecToSec);

                    case LiveTimelineDefine.SheetIndex.MainLive:
                        return StartTime + AudioManager.Instance.SongPlaybackTime;
                }

                return float.MaxValue;
            }
        }

        public void StopMusic()
        {
            switch (SheetType)
            {
                case LiveTimelineDefine.SheetIndex.PreLiveSkit:
                case LiveTimelineDefine.SheetIndex.AfterLiveSkit:
                    AudioManager.Instance.StopBgm(0.0f);
                    AudioManager.Instance.StopVoiceAll();
                    break;

                case LiveTimelineDefine.SheetIndex.MainLive:
                    AudioManager.Instance.StopSong(0f);
                    break;
            }
        }
        public void PlayMusic(float time)
        {
            switch(SheetType)
            {
                case LiveTimelineDefine.SheetIndex.PreLiveSkit:
                case LiveTimelineDefine.SheetIndex.AfterLiveSkit:
                    {
                        string soundName = Director.GetSkitSoundName((SheetType == LiveTimelineDefine.SheetIndex.PreLiveSkit) ? true : false);
                        AudioManager.Instance.PlayBgmFromName(soundName, 0, isLoop:false,startTime:time,isCrossFade:false);
                    }
                    {
                        string soundName = Director.GetSkitSoundVoiceName((SheetType == LiveTimelineDefine.SheetIndex.PreLiveSkit) ? true : false);
                        if (string.IsNullOrEmpty(soundName))
                        {
                            AudioManager.Instance.StopVoiceAll();
                        }
                        else
                        {
                            AudioManager.Instance.PlayVoice(soundName, 0, startTime: time);
                        }
                    }
                    break;
                case LiveTimelineDefine.SheetIndex.MainLive:
                    AudioManager.Instance.PlaySong((null != Director.Instance) ? Director.Instance.SongPart : null, time);
                    break;
            }
        }

        public LiveTimelineDefine.SheetIndex SheetType { get; set; } = LiveTimelineDefine.SheetIndex.MainLive;

        public float StartTime { get; set; }
    }
    //--------------------------------------------------------------------------------
    #endregion MusicProxy
    //--------------------------------------------------------------------------------

    /// <summary>
    /// LiveTimelineエディタ
    /// </summary>
    public class CutTool : EditorWindow
    {
        public static CuttExporter exporter = new CuttExporter();

        //Cuttの外からKeyboardショートカットを任意に追加するためのイベント
        public static event System.Action<Event> On_OnGUI_KeyboardShortcut;

        //ツールがタイムラインをコントロールしているときTrue
        //そのうちAutoPlay時はMusic側がTimelineをコントロールする実装になると思われる
        public static bool isTimelineControlled
        {
            get { return instance != null && instance._liveTimelineControl != null; }
        }

        //------------ public properties
        public static CutTool instance { get; private set; }//Singletone
        public bool isAutoPlaying { get { return _isAutoPlaying; } }
        public float autoPlayTime
        {
            get { return _autoPlayTime; }
            set { _autoPlayTime = value; }
        }

        //------------ Convert color code
        public static Color GenColor(byte r, byte g, byte b, byte a = 255)
        {
            return new Color((float)r / 255f, (float)g / 255f, (float)b / 255f, (float)a / 255f);
        }
        public static Color GenColor(uint rgba)
        {
            var r = (byte)((rgba & 0xFF000000u) >> 24);
            var g = (byte)((rgba & 0x00FF0000u) >> 16);
            var b = (byte)((rgba & 0x0000FF00u) >> 8);
            var a = (byte)((rgba & 0x000000FFu));
            return new Color((float)r / 255f, (float)g / 255f, (float)b / 255f, (float)a / 255f);
        }
        public static Color GenColorRGBFromHash(int hash, int a = 255)
        {
            uint r = (((uint)hash & 0xFFE00000u) >> 21);
            uint g = (((uint)hash & 0x001FFC00u) >> 10);
            uint b = (((uint)hash & 0x000003FFu) >> 0);
            return new Color((float)r / (float)0x7FFu, (float)g / (float)0x7FFu, (float)b / (float)0x3FFu, (float)a / 255f);
        }

        //------------ constants
        //public static string kPathOfResourcesDirectory { get { return LiveTimelineDefine.kPathOfResourcesDirectory; } }

        public static Texture texKeyBirdsEye => LoadResource<Texture>("cutt_key_birds_eye_noA");
        public static Texture texKeyBirdsEyeAlpha => LoadResource<Texture>("cutt_key_birds_eye");
        public static Texture texKeyBirdsEyeWhite => LoadResource<Texture>("cutt_key_birds_eye_white_noA");
        public static Texture texKeyBirdsEyeWhiteAlpha => LoadResource<Texture>("cutt_key_birds_eye_white");
        public static Texture texKeyMotionEnd => LoadResource<Texture>("cutt_mot_end_key");
        public static Texture texMotionHeadMark => LoadResource<Texture>("cutt_mot_head_mark");
        public static Texture texDropTargetFrame => LoadResource<Texture>("cutt_drop_target_frame");
        public static Texture texIndLine => LoadResource<Texture>("cutt_indicator_line");
        public static Texture texNavPlay => LoadResource<Texture>("cutt_tex_nav_play");
        public static Texture texNavStop => LoadResource<Texture>("cutt_tex_nav_stop");
        public static Texture texSelectCamera => LoadResource<Texture>("cutt_video");
        public static Texture texSelectC1 => LoadResource<Texture>("cutt_editor_select_c-1");
        public static Texture texSelectC2 => LoadResource<Texture>("cutt_editor_select_c-2");
        public static Texture texSelectC3 => LoadResource<Texture>("cutt_editor_select_c-3");
        public static Texture texSelectC4 => LoadResource<Texture>("cutt_editor_select_c-4");
        public static Texture texSelectC12 => LoadResource<Texture>("cutt_editor_select_c-all");
        public static Texture texSelectP1 => LoadResource<Texture>("cutt_editor_select_p-1");
        public static Texture texSelectP2 => LoadResource<Texture>("cutt_editor_select_p-2");
        public static Texture texSelectP12 => LoadResource<Texture>("cutt_editor_select_p-12");
        public static Texture texEaseHelp => LoadResource<Texture>("cutt_editor_curve_icon");
        public static Texture texTrashBox => LoadResource<Texture>("cutt_trash");
        public static Texture texSettings => LoadResource<Texture>("cutt_settings");
        public static Texture texSave => LoadResource<Texture>("cutt_save");
        public static Texture texSheetList => LoadResource<Texture>("cutt_sheetlist");
        public static Texture texInterpArrow => LoadResource<Texture>("cutt_interp_arrow");
        public static Texture texPlus => LoadResource<Texture>("cutt_plus");
        public static Texture texMinus => LoadResource<Texture>("cutt_minus");
        public static Texture texOpenScratch => LoadResource<Texture>("cutt_openscratch");
        public static Texture texClipboard => LoadResource<Texture>("cutt_clipboard");
        public static Texture texBookmark => LoadResource<Texture>("cutt_bookmark");
        public static Texture texSearch => LoadResource<Texture>("cutt_search");
        public static Texture texRemove => LoadResource<Texture>("cutt_remove");

        public static GUISkin guiSkin => LoadResource<GUISkin>("LiveTimelineEditorSkin");

        public static readonly GUILayoutOption GUIEXPANDW = GUILayout.ExpandWidth(false);
        public static GUILayoutOption GUIW(float w) { return GUILayout.Width(w); }
        public static GUILayoutOption GUIH(float h) { return GUILayout.Height(h); }
        public const float kLeftColumnWidth = 200;
        public const float kKeyIconWidth = 8;
        public const float kVisibleKeyframeBarRatioDefault = 0.4f;
        public const int kVisibleFrameCountMin = 3 * 60;
        public const int kVisibleFrameCountMax = 60 * 60 * 2;
        public const int kVisibleFrameCountDefault = (int)((float)kVisibleFrameCountMax + ((float)kVisibleFrameCountMin - (float)kVisibleFrameCountMax) * kVisibleKeyframeBarRatioDefault);
        public const float kHightIndicatorFooter = 15f;
        public const float kInterpKeyConnectLineHeight = 3f;
        public const float kTimelineBorderSize = 2;
        const string kAssetPathOfBazierCPPrefab = LiveTimelineDefine.kCuttEditorDir + "/Prefab/TimelineEditor_BazierCP.prefab";
        const string kAssetPathOfCameraCPPrefab = LiveTimelineDefine.kCuttEditorDir + "/Prefab/TimelineEditor_CameraCP.prefab";
        public const int kInvalidEditLineID = -1;
        public const float kWindowHeight = 80;


        public static readonly Color kInterpKeyConnectLineColor = GenColor(109, 180, 206, 210);
        public static readonly Color kTimelineBackgroundColor1 = GenColor(230, 230, 230);
        public static readonly Color kTimelineBackgroundColor2 = GenColor(210, 210, 210);
        public static readonly Color kTimelineKeyDefaultColor = Color.white;
        public static readonly Color kTimelineKeySelectedColor = Color.yellow;
        public static readonly Color kTimelineKeySelectedBorderColor = Color.red;
        public static readonly Color kTimelineMotionBandColorA = GenColor(77, 122, 210, 128);
        public static readonly Color kTimelineMotionBandColorB = GenColor(77, 210, 122, 128);
        public static readonly Color kTimelineBorderColor = GenColor(247, 86, 86);
        public static readonly Color kTimelineSelectionRectColor = GenColor(153, 204, 255, 64);
        public static readonly Color kTimelineSelectionRectBorderColor = GenColor(112, 184, 255, 150);
        public static readonly Color kTimelineMarkingColor = GenColor(255, 0, 128, 150);
        private static readonly Color kCameraCPBazierColor = new Color(0.2f, 0.8f, 0.2f, 1f);
        public static readonly Color kBookmarkIconColor = new Color(0.1f, 0.1f, 0.9f);
        public static readonly Color kAutoPlayMarkerColor = new Color(0.9f, 0.1f, 0.1f);
        public static readonly Color kAutoPlayMarkerOutsideMaskColor = new Color(0, 0, 0, 0.5f);

        //------------ variables
        bool _initialized = false;
        public int targetFps
        {
            get { return LiveTimelineControl.kTargetFps; }
        }
        public int _currentFrame = 0;
        public int _totalFrameCount = 0;
        public float _timelineEndTime
        {
            get { return (float)_liveTimelineControl.data.timeLength; }
            set { _liveTimelineControl.data.timeLength = (int)value; }
        }
        LiveTimelineControl _liveTimelineControl = null;
        public LiveTimelineControl liveTimelineControl { get { return _liveTimelineControl; } }
        public LiveTimelineData liveTimelineData { get { return _liveTimelineControl != null ? _liveTimelineControl.data : null; } }

        List<EditSheetWindow> _editingSheetWindows = new List<EditSheetWindow>();
        List<EditSheet> _editingSheets = new List<EditSheet>();
        EditSheetWindow _editingSheetWindow = null;
        EditSheet _editingSheet = null;

        public ClipBoardInfo _clipBoardInfo = null;

        public Transform _cameraPositionLocatorsRoot = null;
        public Transform _cameraLookAtLocatorsRoot = null;
        //CameraLocatorGameObjectの生成、削除チェックの最小時間間隔（この秒数以内で連続しても無視）
        const float kCameraLocatorExistsCheckMinInterval = 0.1f;
        float _cameraLocatorExistsCheckLastTime = 0f;
        bool _cameraLocatorExistsCheckRequired = false;

        bool _isAutoPlaying = false;//自動Timeline送り中
        bool _autoPlayWithMusic = false;
        float _autoPlayTime = 0f;
        float _autoPlaySpeed = 1f;//AutoPlay時の再生スピード倍率
        public bool _autoPlayForceSmooth = false;

        GameObject _editorObjectRoot = null;//編集用GameObject類はこいつにぶら下げる

        //CameraControlPoint系
        public Dictionary<LiveTimelineKey, CPBundle> _cameraCPBundleDict = new Dictionary<LiveTimelineKey, CPBundle>();
        GameObject _bezierCPPrefab = null;
        GameObject _cameraCPPrefab = null;
        Stack<GameObject> _cameraCPGameObjectPool = new Stack<GameObject>();
        Stack<GameObject> _bezierCPGameObjectPool = new Stack<GameObject>();
        static int s_cameraCPSeqNo = 0;
        static int s_bezierCPSeqNo = 0;

        private Stack<GameObject> _formationCPGameObjectPool = new Stack<GameObject>();
        private Dictionary<LiveTimelineKey, FormationOffsetCPBundle> _formationOffsetCPBundleDict = new Dictionary<LiveTimelineKey, FormationOffsetCPBundle>();
        private static int s_formationOffsetCPSeqNo = 0;

        //Sheetの同変数にOnGUIでSync
        public float _visibleKeyframeBarRatio = 0;

        CutTool_SheetListWindow _sheetListWindow = null;
        CutTool_SettingsWindow _settingsWindow = null;
        CutTool_BookmarkWindow _bookmarkWindow = null;
        CutTool_SearchKeyWindow _searchKeyWindow = null;
        CutTool_ScratchWindow _scratchWindow = null;
        public CutTool_ScratchWindow scratchWindow { get { return _scratchWindow; } }

        //プレビューカメラ系
        private Camera[] _previewCameras = new Camera[(int)LiveTimelineDefine.kCameraMaxNum];
        private RenderTexture[] _previewCameraRTs = new RenderTexture[(int)LiveTimelineDefine.kCameraMaxNum];
        private static readonly Vector2 _previewResolution = new Vector2(150f, 150f * (1 / 1.6f));
        private bool[] _cameraPreviewStats = new bool[(int)LiveTimelineDefine.kCameraMaxNum];

        //音楽再生Interface
        #region xxxxxソースを参考に残す
        //#if xxxxx
        //        IMusicProxy _musicProxy = new MusicProxyStage();
        //#else xxxxx
        //        IMusicProxy _musicProxy = new MusicProxyDummy();
        //#endif xxxxx
        #endregion xxxxxソースを参考に残す
        IMusicProxy _musicProxy = new MusicProxyGallop();

        OperationCommander _opeCommander = new OperationCommander();
        public OperationCommander opeCommander { get { return _opeCommander; } }

        //BatchSave中か
        enum ToolMode
        {
            Normal,
            BatchSave,
            Reload,
        }
        ToolMode _toolMode = ToolMode.Normal;
        int _toolModeRno = 0;//ToolMode内RoutineNumber
        void ChangeToolMode(ToolMode mode)
        {
            _toolMode = mode;
            _toolModeRno = 0;
        }

        //フェイシャルノイズをAutoPlay時無効にするか
        public bool disableFaicialNoise = false;
        //フェイシャルノイズを常に有効にするか
        public bool alwaysEnableFacialNoise = false;
        //キャラモーションノイズをAutoPlay時無効にするか
        public bool disableCharaMotionNoise = false;
        //キャラモーションノイズを常に有効にするか
        public bool alwaysEnableCharaMotionNoise = false;

        //キャラのHotSwap中はCP -> Keyframeへの書き戻しをしたくないので、そのためのフラグ
        public bool nowHotSwap = false;

        //HotKey
        ToolHotKeyDefine _hotKeyDefine = null;
        public ToolHotKeyDefine hotKeyDefine { get { return _hotKeyDefine; } }

        //CurvePreset
        LiveTimelineCurvePreset _curvePreset = null;
        public LiveTimelineCurvePreset curvePreset { get { return _curvePreset; } }

        //Bookmark
        LiveTimelineUserPrefs _userPrefs = null;
        public LiveTimelineUserPrefs userPrefs { get { return _userPrefs; } }

        //OnProjectChangeコールバックでTrueになるフラグ：CuttPrefabのFindAsset用
        //何やらUnity5.1になってから（？）FindAssetsが激重になったのでProject変更があった時だけに限定するため用意
        bool _hadProjectChangeForFindPrefabs = false;
        //CuttPrefabのFindAsset結果
        string[] _findAssetsCacheForCuttPrefab = null;

        //Cuttデータリロード関係
        enum ReloadRoutine : int
        {
            WaitForToolClose = 0,
            DestroyControl,
            DestroyControl_Wait1,
            DestroyControl_Wait2,
            InstanciateAndToolOpen,
            Finish,
        }
        static ReloadRoutine s_ReloadRoutine = ReloadRoutine.WaitForToolClose;
        static LiveTimelineControl s_timelineControltForReload = null;
        static UnityEngine.Object s_prefabObjectForReload = null;
        static int s_currentFrameForReload = 0;

        //SheetのKeyframeArea描画に使用するWhilteTexture
        Texture2D _whiteTextureKeyBackground = null;
        public Texture2D whiteTextureKeyBackground
        {
            get
            {
                if (_whiteTextureKeyBackground == null)
                {
                    _whiteTextureKeyBackground = new Texture2D(1, 1);
                    _whiteTextureKeyBackground.SetPixel(0, 0, new Color(1f, 1f, 1f, 0.3f));
                    _whiteTextureKeyBackground.Apply();
                }
                return _whiteTextureKeyBackground;
            }
        }

        //ViewScaleの調整方法
        public bool viewScalingBasedOnCurrentFrame
        {
            get { return EditorPrefs.GetBool(LiveTimelineDefine.kPrefsKeyName_ViewScaleAdjustMethod, false); }
            set { EditorPrefs.SetBool(LiveTimelineDefine.kPrefsKeyName_ViewScaleAdjustMethod, value); }
        }

#if CYG_DEBUG
        //撮影用のカメラ調整
        private CameraControllerForRecording _cameraControllerForRecording = null;
#endif

        public void PlayMusic(float time)
        {
            float playTime = time;
            var gallopProxy = _musicProxy as MusicProxyGallop;
            gallopProxy.SheetType = _liveTimelineControl.GetCurrentSheetType();
            if (gallopProxy.SheetType == LiveTimelineDefine.SheetIndex.Master)
            {
                var startTime = _liveTimelineControl.GetCurrentWorkSheetStartTime(playTime);
                gallopProxy.SheetType = _liveTimelineControl.GetCurrentWorkSheetType(playTime);
                gallopProxy.StartTime = startTime;
                playTime = playTime - startTime;
            }
            else
            {
                gallopProxy.StartTime = 0.0f;
            }
            _musicProxy.PlayMusic(playTime);
        }

        #region アイコン用リソースの静的キャッシュ

        private static Dictionary<string, UnityEngine.Object> _resourceCache = new Dictionary<string, UnityEngine.Object>();
        private static T LoadResource<T>(string path) where T : UnityEngine.Object
        {
            if(_resourceCache.TryGetValue(path , out var obj)){
                return obj as T;
            }
            var newObj = Resources.Load<T>(path);
            _resourceCache.Add(path, newObj);
            return newObj;
        }

        #endregion

        //--------------------------------------------------------------------------------
        #region AutoPlayMarker
        //--------------------------------------------------------------------------------
        //ABループ再生のマーカーFrame（現状意味ないが、一応オブジェクトにしておく…）
        public class AutoPlayMarker
        {
            public int frame = 0;
        }
        public const int kAutoPlayMarkerCount = 2;
        //AutoPlayMarkerはframeの昇順に配列されているわけではないので注意
        AutoPlayMarker[] _autoPlayMarkers = new AutoPlayMarker[kAutoPlayMarkerCount];

        public AutoPlayMarker[] GetAutoPlayMarkerArray()
        {
            return _autoPlayMarkers;
        }
        //MarkerAは小さいFrame数のマーカーで、AutoPlay開始Frameとなる
        public AutoPlayMarker GetAutoPlayMarkerA()
        {
            return _autoPlayMarkers[0].frame < _autoPlayMarkers[1].frame ? _autoPlayMarkers[0] : _autoPlayMarkers[1];
        }
        //MarkerBは大きいFrame数のマーカーで、AutoPlay終了Frameとなる
        public AutoPlayMarker GetAutoPlayMarkerB()
        {
            return _autoPlayMarkers[0].frame > _autoPlayMarkers[1].frame ? _autoPlayMarkers[0] : _autoPlayMarkers[1];
        }
        //--------------------------------------------------------------------------------
        #endregion AutoPlayMarker
        //--------------------------------------------------------------------------------


        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public bool IsEditingSheet(EditSheet sheet)
        {
            return _editingSheet == sheet;
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public EditSheet GetEditingSheet()
        {
            return _editingSheet;
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public EditSheet[] GetEditSheetArray()
        {
            return _editingSheets.ToArray();
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public EditSheetWindow[] GetEditSheetWindowArray()
        {
            return _editingSheetWindows.ToArray();
        }
        public EditSheet GetEditSheet(LiveTimelineWorkSheet workSheet)
        {
            foreach(var editSheet in _editingSheets)
            {
                if(editSheet._data == workSheet)
                {
                    return editSheet;
                }
            }

            return null;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        [MenuItem("Cutt/Timeline Editor" + Gallop.EditorMenuShortcut.CtrlShiftT)]
        public static void Open()
        {
            Open(0);
        }
        //--------------------------------------------------------------------------------
        public static void Open(int initialFrame)
        {
            if (instance != null)
            {
                EditorWindow.FocusWindowIfItsOpen<CutTool>();
                return;
            }
            if (EditorApplication.isCompiling)
            {
                Debug.LogWarning("[ABORT CUTT-OPEN] Currently compiling scripts. please wait finish compile.");
                return;
            }
            //本当はEditorWindow.CreateInstanceが良い。でも動作は一緒
            var window = ScriptableObject.CreateInstance<CutTool>();
            window.minSize = new Vector2(600, kWindowHeight);
            window.Show();

            instance = window;
            window.titleContent = new GUIContent("Cutt");

            window.Init(initialFrame);
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void Init(int initialFrame = 0)
        {
            if (_liveTimelineControl == null)
            {
                _liveTimelineControl = FindObjectOfType<LiveTimelineControl>();
            }
            if (_liveTimelineControl == null)
            {
                return;
            }

            if (EditorApplication.isPlaying)
            {//SendMessageはRunning中のみ有効
                //Timelineのシーン側のドライバーを検索して、HITしたら初期化をリクエストする
                string[] driverObjectTypeNames = new string[] { "Director" };//一応タイトル側のクラス参照は死守しとく。…けどLiveControllerとか参照してるの思い出した…。
                for (int i = 0; i < driverObjectTypeNames.Length; i++)
                {
                    var typeName = driverObjectTypeNames[i];
                    var type = Type.GetType(typeName);
                    if (type == null)
                    {
                        continue;
                    }
                    var obj = FindObjectOfType(type);
                    var mono = obj as MonoBehaviour;
                    if (mono != null)
                    {
                        mono.SendMessage("OnTimelineControllerInitializeRequest", SendMessageOptions.DontRequireReceiver);
                        break;
                    }
                }
            }

            //HotKeyデータロード
            _hotKeyDefine = ToolHotKeyDefine.Load();
            //CurvePresetデータロード
            _curvePreset = LiveTimelineCurvePreset.Load();
            //UserPrefsデータロード
            _userPrefs = LiveTimelineUserPrefs.Load();

            //TimelineDataロード（↑ですでにロード済みならSKIP）
            _liveTimelineControl.LoadData();

            AttachCamLocatorScripts(_liveTimelineControl);

            _timelineEndTime = (float)_liveTimelineControl.data.timeLength;
            _currentFrame = initialFrame;
            _totalFrameCount = (int)_timelineEndTime * targetFps;

            //Markerは0Fと最終Fを初期位置にセット
            _autoPlayMarkers[0] = new AutoPlayMarker() { frame = 0 };
            _autoPlayMarkers[1] = new AutoPlayMarker() { frame = _totalFrameCount };

            Init_Data();

            _visibleKeyframeBarRatio = CutTool.kVisibleKeyframeBarRatioDefault;
            UpdateVisibleKeyframeBarRatio();

            _editorObjectRoot = new GameObject("Cutt Editor Object Root");

            //プレビューカメラ生成
            for (int i = 0; i < (int)LiveTimelineDefine.kCameraMaxNum; i++)
            {
                var cameraGameObj = new GameObject("Cutt Preview Camera " + (i + 1));
                _previewCameras[i] = cameraGameObj.AddComponent<Camera>();
                _previewCameras[i].enabled = false;
                var texw = Mathf.FloorToInt(_previewResolution.x);
                var texh = Mathf.FloorToInt(_previewResolution.y);
                _previewCameraRTs[i] = new RenderTexture(texw, texh, 24);
#if CYG_DEBUG
                _previewCameraRTs[i].name = "LiveTimelineEditor.Init._previewCameraRTs[" + i + "]";
#endif
                _previewCameraRTs[i].enableRandomWrite = false;
                _previewCameras[i].targetTexture = _previewCameraRTs[i];
                cameraGameObj.transform.SetParent(_editorObjectRoot.transform);
            }

            _initialized = true;

            SceneView.duringSceneGui += OnScene;

            Repaint();
        }

        //--------------------------------------------------------------------------------
        private void Init_Data()
        {
#if false
            var ws = liveTimelineData.GetWorkSheetList().FirstOrDefault();
            if(ws!=null) {
                StartEditSheet(ws);
            }
#else
            //全部のシートを開く
            LiveTimelineWorkSheet firstWS = null;
            bool isMasterSheet = false;
            foreach (var ws in liveTimelineData.GetWorkSheetList())
            {
                if (firstWS == null)
                {
                    firstWS = ws;
                }

                if(ws.IsMasterSheet)
                {
                    isMasterSheet = true;
                }

                if (ws != null)
                {
                    StartEditSheet(ws);
                    liveTimelineData.AddEditorSheet(ws);
                }
            }

            //マスターシートをチェックして読み込む
            if (!isMasterSheet)
            {
                string masterSheetPath = string.Format(LiveTimelineDefine.MASTER_SHEET_PATH, _liveTimelineControl.name, _liveTimelineControl.name);
                var masterSheet = AssetDatabase.LoadAssetAtPath<LiveTimelineWorkSheet>(masterSheetPath);
                if (masterSheet != null)
                {
                    string name = masterSheet.name;
                    masterSheet = ScriptableObject.Instantiate<LiveTimelineWorkSheet>(masterSheet);
                    masterSheet.SheetType = LiveTimelineDefine.SheetIndex.Master;
                    masterSheet.name = "*" + name;  //マスターには*を付ける
                    liveTimelineData.AddEditorSheet(masterSheet);
                    //MasterWorkSheetのnameHashなどを更新する
                    masterSheet.OnLoad(_liveTimelineControl);

                    _liveTimelineControl.SetupMasterWorkSheet(masterSheet);

                    firstWS = masterSheet;
                }
            }

            if (firstWS != null)
            {
                StartEditSheet(firstWS);//最初のやつをフォーカス
            }
#endif
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void StartEditSheet(LiveTimelineWorkSheet ws)
        {
            foreach (var editingSheetWindow in _editingSheetWindows)
            {
                if (ws == editingSheetWindow._editSheet._data)
                {
                    //すでに開いていた
                    editingSheetWindow.Focus();
                    return;
                }
            }
            var win = EditSheetWindow.Open(this, ws);
            if (win != null)
            {
                _editingSheetWindows.Add(win);
                _editingSheets.Add(win._editSheet);
                _editingSheet = win._editSheet;
            }
        }
        //--------------------------------------------------------------------------------
        public void FinishEditSheet(int ID)
        {

        }
        //--------------------------------------------------------------------------------
        public void SheetOnDestroyCallback(EditSheetWindow sheetWindow)
        {
            _editingSheetWindows.Remove(sheetWindow);
            _editingSheets.Remove(sheetWindow._editSheet);
            EditSheet.RemoveInstance(sheetWindow._editSheet.ID);
            if (_editingSheetWindow == sheetWindow)
            {
                _editingSheetWindow = _editingSheetWindows.FirstOrDefault();
                _editingSheet = _editingSheets.FirstOrDefault();
                if (_editingSheetWindow != null)
                {
                    _editingSheetWindow.Focus();
                }
            }
        }
        //--------------------------------------------------------------------------------
        public void UpdateTotalTimeLength()
        {
            //全体時間などを更新する
            if(_liveTimelineControl == null || _liveTimelineControl.data == null)
            {
                return;
            }

            if (_editingSheet != null)
            {
                if (_editingSheet._data.TotalTimeLength > 0)
                {
                    //フレーム変換時の小数点以下切り捨て
                    _totalFrameCount = (int)(_editingSheet._data.TotalTimeLength * targetFps);
                }
                else
                {
                    _totalFrameCount = _liveTimelineControl.data.timeLength * targetFps;
                }
                _editingSheet.OnFocus();
                _liveTimelineControl.SetCurrentSheetNo(_editingSheet._data);
            }
            else
            {
                _totalFrameCount = _liveTimelineControl.data.timeLength * targetFps;
                //シート番号が求められないので、直前の設定を使用する
            }

            if (_currentFrame > _totalFrameCount)
            {
                _currentFrame = _totalFrameCount;
            }
        }

        public void SheetOnFocusCallback(EditSheetWindow sheetWindow)
        {
            _editingSheetWindow = sheetWindow;
            _editingSheet = sheetWindow != null ? sheetWindow._editSheet : null;

            if (_editingSheet != null)
            {
                var gallopProxy = _musicProxy as MusicProxyGallop;
                gallopProxy.SheetType = _editingSheet._data.SheetType;
            }
            //全体時間などを更新する
            UpdateTotalTimeLength();
        }
        //--------------------------------------------------------------------------------
        public void SheetListWindowOnDestroyCallback()
        {
            _sheetListWindow = null;
        }
        //--------------------------------------------------------------------------------
        public void SettingsWindowOnDestroyCallback()
        {
            _settingsWindow = null;
        }
        //--------------------------------------------------------------------------------
        public void BookmarkWindowOnDestroyCallback()
        {
            _bookmarkWindow = null;
        }
        //--------------------------------------------------------------------------------
        public void SearchKeyWindowOnDestroyCallback()
        {
            _searchKeyWindow = null;
        }

        //--------------------------------------------------------------------------------
        public void ScratchWindowOnDestroyCallback()
        {
            _scratchWindow = null;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void UpdateVisibleKeyframeBarRatio()
        {
            foreach (var sheet in _editingSheets)
            {
                sheet.UpdateVisibleFrameCount();
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void AttachCamLocatorScripts(LiveTimelineControl timelineControl)
        {
            if (timelineControl == null)
            {
                return;
            }
            //CameraPositionLocatorへスクリプトアタッチ
            _cameraPositionLocatorsRoot = timelineControl.cameraPositionLocatorsRoot;
            if (_cameraPositionLocatorsRoot == null)
            {
                Debug.LogWarning("cameraPositionLocatorsRoot is null");
            }
            AttachScriptRecursivity<LiveTimelineCamPosLocator>(_cameraPositionLocatorsRoot);
            //CameraLookAtLocatorへスクリプトアタッチ
            _cameraLookAtLocatorsRoot = timelineControl.cameraLookAtLocatorsRoot;
            if (_cameraLookAtLocatorsRoot == null)
            {
                Debug.LogWarning("cameraLookAtLocatorsRoot is null");
            }
            AttachScriptRecursivity<LiveTimelineCamLookAtLocator>(_cameraLookAtLocatorsRoot);
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void AttachScriptRecursivity<TypeScript>(Transform t) where TypeScript : Component
        {
            if (t != null)
            {
                foreach (Transform c in t)
                {
                    if (c.gameObject.GetComponent<TypeScript>() == null)
                    {
                        c.gameObject.AddComponent<TypeScript>();
                    }
                    AttachScriptRecursivity<TypeScript>(c);
                }
            }
        }
        private void DetachScriptRecursivity<TypeScript>(Transform t) where TypeScript : Component
        {
            if (t != null)
            {
                foreach (Transform c in t)
                {
                    foreach (var component in c.gameObject.GetComponents<TypeScript>())
                    {
                        GameObject.DestroyImmediate(component);
                    }
                    DetachScriptRecursivity<TypeScript>(c);
                }
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void OnDestroy()
        {//Called when close
            if (instance == this)
            {
                if (_editorObjectRoot != null)
                {
                    if (Application.isPlaying)
                    {
                        Destroy(_editorObjectRoot);
                    }
                    else
                    {
                        DestroyImmediate(_editorObjectRoot);
                    }

                    _cameraCPBundleDict.Clear();

                    for (int i = 0; i < _previewCameras.Length; i++)
                    {
                        _previewCameras[i] = null;
                    }
                }

                //閉じる前に個人設定を保存
                {
                    if (curvePreset != null)
                    {
                        EditorUtility.SetDirty(curvePreset);
                    }

                    if (userPrefs != null)
                    {
                        EditorUtility.SetDirty(userPrefs);
                    }

                    if (_scratchWindow != null)
                    {
                        _scratchWindow.SetEntriesAsDirty();
                    }

                    AssetDatabase.SaveAssets();
                }

                SceneView.duringSceneGui -= OnScene;

                CloseAllSubWindows();

                instance = null;
            }
        }
        //--------------------------------------------------------------------------------
        private void CloseAllSubWindows()
        {
            var notEditingSheetWindows = _editingSheetWindows.Where(x => x != _editingSheetWindow).ToArray();
            foreach (var p in notEditingSheetWindows)
            {
                if (p != null)
                {
                    p.Close();
                }
            }
            if (_editingSheetWindow != null)
            {
                /* ここのコードの意味
                 * ・SheetWindowはOpen時にReflectionを駆使して無理やりAddTabしている（See. EditSheet.Open)
                 * ・SheetWindowが複数Tab状態でCutToolをCloseしてもレイアウト（WindowSize）が保存されないことが判明
                 * ・ILを追ってみると、EditorWindow.Closeの中でレイアウト保存しないパスへ行っている様子
                 * ・m_Parent.window.Close()する必要があるので、それをReflectionで無理やり実行
                 */
                var successClose = false;
                var fieldInfo_m_Parent = _editingSheetWindow.GetType().GetField("m_Parent", BindingFlags.Instance | BindingFlags.NonPublic);
                if (fieldInfo_m_Parent != null)
                {
                    var obj_m_Parent = fieldInfo_m_Parent.GetValue(_editingSheetWindow);
                    if (obj_m_Parent != null)
                    {
                        //Debug.Log(obj_m_Parent.GetType().Name);
                        if (obj_m_Parent.GetType().Name == "DockArea")
                        {
                            var propInfo_window = obj_m_Parent.GetType().GetProperty("window", BindingFlags.Instance | BindingFlags.GetProperty | BindingFlags.Public);
                            var obj_window = propInfo_window.GetValue(obj_m_Parent, null);
                            if (obj_window != null)
                            {
                                var methodInfo_Close = obj_window.GetType().GetMethod("Close");
                                if (methodInfo_Close != null)
                                {
                                    methodInfo_Close.Invoke(obj_window, null);
                                    successClose = true;
                                }
                                else
                                {
                                    Debug.Log("methodInfo_Close==null");
                                }
                            }
                            else
                            {
                                Debug.Log("obj_window==null");
                            }
                        }
                        else
                        {
                            Debug.Log("obj_m_Parent!=DockArea");
                        }
                    }
                    else
                    {
                        Debug.Log("obj_m_Parent==null");
                    }
                }
                else
                {
                    Debug.Log("fieldInfo_m_Parent==null");
                }
                if (!successClose)
                {
                    _editingSheetWindow.Close();
                }
                else
                {
                    if (_editingSheet != null)
                    {
                        //EditorWindow.Close内部のエミュレート
                        UnityEngine.Object.DestroyImmediate(_editingSheetWindow, true);
                    }
                }
                _editingSheetWindow = null;
                _editingSheet = null;
            }

            if (_sheetListWindow != null)
            {
                _sheetListWindow.Close();
                _sheetListWindow = null;
            }

            if (_settingsWindow != null)
            {
                _settingsWindow.Close();
                _settingsWindow = null;
            }

            if (_bookmarkWindow != null)
            {
                _bookmarkWindow.Close();
                _bookmarkWindow = null;
            }

            if (_searchKeyWindow != null)
            {
                _searchKeyWindow.Close();
                _searchKeyWindow = null;
            }

            if (_scratchWindow != null)
            {
                _scratchWindow.Close();
                _scratchWindow = null;
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void OnFocus()
        {//Called when got focus
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void OnLostFocus()
        {
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void OnHierarchyChange()
        {
            //カメラLocatorが増えた可能性があるのでスクリプトアタッチをリクエスト
            _cameraLocatorExistsCheckRequired = true;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void OnProjectChange()
        {
            _hadProjectChangeForFindPrefabs = true;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void OnChangeEditingLine(EditTimeline timeline)
        {
            if (timeline == null)
            {
                return;
            }
            //CameraCPをクリア（GameObjectは残す）
            ReleaseCameraCPAll();
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public int GetCurrentFrame()
        {
            return _currentFrame;
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public float GetTimelineCurrentTime()
        {
            return (float)_currentFrame / (float)targetFps;
        }
        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public float GetTimelineEndTime()
        {
            return _timelineEndTime;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private static void SetDirtyRecursiv(Transform tran)
        {
            EditorUtility.SetDirty(tran);
            foreach (Transform t in tran)
            {
                SetDirtyRecursiv(t);
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public static bool SaveData(LiveTimelineControl timelineControl)
        {
            Debug.Log("Save Timeline Data");

            if (timelineControl == null)
            {
                Debug.LogError("Save with null liveTimelineControl");
                return false;
            }
            if (timelineControl.data == null)
            {
                Debug.LogWarning("SaveData with null data");
                return false;
            }


            if (PrefabUtility.GetCorrespondingObjectFromSource(timelineControl.gameObject) == null)
            {
                EditorUtility.DisplayDialog(
                    "Prefab Apply Error",
                    "対応するPrefabが見つからないためPrefabへのApplyをSKIPします。data, sheetの保存のみ実行します",
                    "OK"
                );
            }

            void fnApplyPrefab()
            {
                if (PrefabUtility.GetCorrespondingObjectFromSource(timelineControl.gameObject) != null)
                {
                    SetDirtyRecursiv(timelineControl.transform);
                    //PrefabApply
                    PrefabUtility.SaveAsPrefabAssetAndConnect(timelineControl.gameObject, PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(timelineControl.gameObject), InteractionMode.AutomatedAction);
                }
            }

            fnApplyPrefab();

            AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
            if (timelineControl.data == null)
            {
                Debug.LogError("timelineControl.data == null");
                Debug.Break();
                return false;
            }
            timelineControl.data.Save(timelineControl);
            AssetDatabase.SaveAssets();

            fnApplyPrefab();//ここでもしないとdataがNullになっちゃうんだよなぁ…（PrefabをInspactorでみたとき）

            return true;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private List<string> GetPrefabAssetPathList()
        {
            var retList = new List<string>();
            if (_findAssetsCacheForCuttPrefab == null || _hadProjectChangeForFindPrefabs)
            {
                if (AssetDatabase.IsValidFolder(LiveTimelineDefine.kCuttResourceDir))
                {
                    _findAssetsCacheForCuttPrefab = AssetDatabase.FindAssets("t:LiveTimelineData", new string[] { LiveTimelineDefine.kCuttResourceDir });
                    var targetCuttPrefab = new List<string>();
                    //不要なものを除外したPrefabGUIDで管理しておく
                    if (_findAssetsCacheForCuttPrefab != null)
                    {
                        var foundAssets = _findAssetsCacheForCuttPrefab;
                        for (int iAsset = 0; iAsset < foundAssets.Length; iAsset++)
                        {
                            var asset = foundAssets[iAsset];
                            var assetPath = AssetDatabase.GUIDToAssetPath(asset);
                            var dirPath = System.IO.Path.GetDirectoryName(assetPath).Replace("\\","/");
                            var prefabPath = dirPath + "/" + System.IO.Path.GetFileName(dirPath) + ".prefab";
                            var prefabGuid = AssetDatabase.AssetPathToGUID(prefabPath);
                            targetCuttPrefab.Add(prefabGuid);
                        }
                        _findAssetsCacheForCuttPrefab = targetCuttPrefab.ToArray();
                    }
                }
            }

            if (_findAssetsCacheForCuttPrefab != null)
            {
                var foundAssets = _findAssetsCacheForCuttPrefab;
                for (int iAsset = 0; iAsset < foundAssets.Length; iAsset++)
                {
                    var asset = foundAssets[iAsset];
                    var assetPath = AssetDatabase.GUIDToAssetPath(asset);
                    retList.Add(assetPath);
                }
            }
            return retList;
        }

        //--------------------------------------------------------------------------------
        //すべてのCuttデータをセーブしなおす
        //--------------------------------------------------------------------------------
        private void BatchSaveData()
        {
#if true
            var prefabAssetPathList = GetPrefabAssetPathList();
#else
            //Convert test
            var pathlist = GetPrefabAssetPathList();
            var prefabAssetPathList = pathlist.Where(x => x.EndsWith("Cutt_Star.prefab"));
#endif
#if BATCH_OUTPUT_MOTIONCAMERA_CUTT
            var motFramesDict = new Dictionary<string, List<int>>();
#endif
            foreach (var prefabAssetPath in prefabAssetPathList)
            {
                var prefab = AssetDatabase.LoadAssetAtPath(prefabAssetPath, typeof(GameObject));
                if (prefab == null)
                {
                    Debug.LogWarning("Prefab load failed. " + prefabAssetPath);
                    continue;
                }
                var go = UnityEditor.PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                if (go == null)
                {
                    Debug.LogWarning("Prefab instanciate failed. " + prefabAssetPath);
                    continue;
                }
                var timelineControl = go.GetComponent<LiveTimelineControl>();
                if (timelineControl == null)
                {
                    Debug.LogWarning("Prefab is not have TimelineControl. " + prefabAssetPath);
                    continue;
                }
                timelineControl.Initialize();
#if BATCH_OUTPUT_MOTIONCAMERA_CUTT
                var motFrames = new List<int>();
                foreach(var sheet in timelineControl.data.GetWorkSheetList()) {
                    for(int i=0; i<sheet.cameraSwitcherKeys.Count; i++){
                        var key = sheet.cameraSwitcherKeys[i] as LiveTimelineKeyCameraSwitcherData;
                        if(key.cameraIndex == -1) {
                            motFrames.Add(key.frame);
                        }
                    }
                }
                if(motFrames.Count > 0) {
                    motFramesDict.Add(prefab.name, motFrames);
                }
#else
                SaveData(timelineControl);
#endif
                DestroyImmediate(timelineControl.gameObject);
            }

#if BATCH_OUTPUT_MOTIONCAMERA_CUTT
            var log = "**** Result ****";
            foreach(var pair in motFramesDict)
            {
                var s = string.Join(",", pair.Value.Select(x => x.ToString()+"("+((float)x/60f)+")").ToArray());
                log = string.Format("{0}\n{1}: {2}", log, pair.Key, s);
            }
            Debug.Log(log);
#endif
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void PerformToCameraKeys(System.Action<LiveTimelineKey> performFunc)
        {
            foreach (var sheet in _editingSheets)
            {
                sheet.PerformToCameraKeys(performFunc);
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void ToggleAutoPlay()
        {
            {
                //AutoPlayMarkerの間にCurrentFrameが無い場合は移動する
                int markerDiff = GetAutoPlayMarkerA().frame - GetAutoPlayMarkerB().frame;
                if ( Mathf.Abs(markerDiff) == 0)
                {
                    //Markerが重なっている場合NG
                    EditorUtility.DisplayDialog("再生エラー", "A-Bマーカーが重なっています", "OK");
                    return;
                }
                if (_currentFrame < GetAutoPlayMarkerA().frame)
                {
                    _currentFrame = GetAutoPlayMarkerA().frame;
                }
                else if (_currentFrame > GetAutoPlayMarkerB().frame)
                {
                    _currentFrame = GetAutoPlayMarkerA().frame;//Aからスタート
                }
            }

            if (_isAutoPlaying)
            {
                _isAutoPlaying = false;
                if (Application.isPlaying && _musicProxy.isAvailable)
                {
                    _musicProxy.StopMusic();
                }
                foreach (var sheetWindow in _editingSheetWindows)
                {
                    sheetWindow.RemoveNotification();
                }
#if CYG_DEBUG
                TransformRecorder.TransformRecorderUtility.EndRecording();
#endif
            }
            else
            {
                _isAutoPlaying = true;
                _autoPlayWithMusic = false;
                _autoPlayTime = GetTimelineCurrentTime();
                //連続再生の場合ここで再生時間調整と、その他もろもろ設定が必要
                if(_liveTimelineControl.IsSequencialPlay)
                {
                    //シートを切り替える
                    var sheet = _liveTimelineControl.GetCurrentWorkSheet();
                    if (sheet != null)
                    {
                        StartEditSheet(sheet);
                    }
                }

                var gallopProxy = _musicProxy as MusicProxyGallop;
                if (Application.isPlaying && _musicProxy.isAvailable)
                {
                    if (_musicProxy.isMusicAvailable)
                    {
                        _autoPlayWithMusic = true;
                        PlayMusic(_autoPlayTime);
                    }
                }
                foreach (var sheetWindow in _editingSheetWindows)
                {
                    sheetWindow.ShowNotification(new GUIContent("Auto Playing Now. Dont touch Timeline."));
                }
#if CYG_DEBUG
                TransformRecorder.TransformRecorderUtility.StartRecording();
#endif
            }
            RepaintAll();
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public CutTool_BookmarkWindow OpenBookmarkWindow(LiveTimelineWorkSheet sheet = null, int frame = -1)
        {
            if (_bookmarkWindow != null)
            {
                EditorWindow.FocusWindowIfItsOpen<CutTool_BookmarkWindow>();
            }
            else
            {
                if (sheet == null)
                {
                    if (_editingSheet == null)
                    {
                        return null;
                    }
                    sheet = _editingSheet._data;
                }
                _bookmarkWindow = CutTool_BookmarkWindow.Open(this, sheet, frame);
            }
            return _bookmarkWindow;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public CutTool_ScratchWindow OpenScratchWindow()
        {
            if (_scratchWindow != null)
            {
                EditorWindow.FocusWindowIfItsOpen<CutTool_ScratchWindow>();
            }
            else
            {
                _scratchWindow = CutTool_ScratchWindow.Open(this);
            }
            return _scratchWindow;
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void AddCurrentFrame(int add)
        {
            var newFrame = _currentFrame + add;
            if (newFrame < 0 || newFrame >= _totalFrameCount)
            {
                return;
            }
            _currentFrame = newFrame;
        }

        //--------------------------------------------------------------------------------
        //Called 100 times per second on all visible windows.　らしい
        //--------------------------------------------------------------------------------
        private void Update()
        {
            switch (_toolMode)
            {
                case ToolMode.Normal:
                    Update_Normal();
                    break;
                case ToolMode.BatchSave:
                    Update_BatchSave();
                    break;
                case ToolMode.Reload:
                    Update_Reload();
                    break;
            }
        }
        //--------------------------------------------------------------------------------
        //データ編集中の更新
        //--------------------------------------------------------------------------------
        private void Update_Normal()
        {
            if (!_initialized)
            {
                Init();
                return;
            }
            if (_liveTimelineControl == null)
            {
                //シーン破棄などでControlがいなくなったらSheetWindowを閉じる
                //SheetDataをEditSheetが保持し続けてMemoryに残ってしまう問題がでたので
                CloseAllSubWindows();
                //とりあえずMemoryに残ってやばそうな奴はここでクリア
                //本当はシーンから出るときにEditor.Close（からのReOpen）できたら手っ取り早い
                _clipBoardInfo = null;
                _opeCommander = new OperationCommander();
                Repaint();
                return;
            }

            //カメラLocatorGameObject生成、削除チェック
            if (_cameraLocatorExistsCheckRequired)
            {
                var timeDiff = Time.realtimeSinceStartup - _cameraLocatorExistsCheckLastTime;
                if (timeDiff >= kCameraLocatorExistsCheckMinInterval)
                {
                    _cameraLocatorExistsCheckLastTime = Time.realtimeSinceStartup;
                    _cameraLocatorExistsCheckRequired = false;
                    AttachCamLocatorScripts(liveTimelineControl);
                }
            }

            foreach (var sheet in _editingSheets)
            {
                sheet.AlterUpdate();
            }

            //TimelineカメラのパラメータをPreviewカメラへコピー
            for (int i = 0; i < (int)LiveTimelineDefine.kCameraMaxNum; i++)
            {
                if (!_cameraPreviewStats[i])
                {
                    continue;
                }
                var cacheCamera = liveTimelineControl.GetCamera(i);
                if (cacheCamera == null)
                {
                    continue;
                }
                var gameCamera = cacheCamera.camera;
                var previewCamera = _previewCameras[i];
                if (gameCamera == null || previewCamera == null)
                {
                    continue;
                }
                //CopyFromでパラメータコピーしていたが、CameraSwitcherでスイッチしたとき
                //PreviewCamera側の描画が更新されない現象が起こったので必要そうなのを手動でコピー
                previewCamera.clearFlags = gameCamera.clearFlags;
                previewCamera.backgroundColor = gameCamera.backgroundColor;
                previewCamera.cullingMask = gameCamera.cullingMask;
                previewCamera.fieldOfView = gameCamera.fieldOfView;
                previewCamera.nearClipPlane = gameCamera.nearClipPlane;
                previewCamera.farClipPlane = gameCamera.farClipPlane;
                previewCamera.depth = gameCamera.depth;
                previewCamera.useOcclusionCulling = gameCamera.useOcclusionCulling;
                previewCamera.allowHDR = gameCamera.allowHDR;
                previewCamera.transform.SetPositionAndRotation(gameCamera.transform.position, gameCamera.transform.rotation);
                previewCamera.transform.localScale = gameCamera.transform.localScale;
            }

            //AutoPlay制御
            if (_isAutoPlaying && EditorApplication.isPlaying)
            {
                bool isMovedByMusic = false;
                if (_musicProxy.isAvailable &&
                    _musicProxy.isPlaying &&
                    _autoPlayWithMusic &&
                    _autoPlayForceSmooth == false
                    )
                {
                    isMovedByMusic = true;
                }

                if (isMovedByMusic)
                {
                    //Live中のautoPlayTimeはランタイムからセットされる
                    _autoPlayTime = _musicProxy.musicTime;
                }
                else
                {
                    //それ以外のCutt単独利用
                    _autoPlayTime += (Time.smoothDeltaTime * _autoPlaySpeed);
                }

                //Masterの場合ステート切り替えが発生しないので、独自で曲の切り替えを行う
                if (_liveTimelineControl.GetCurrentSheetType() == LiveTimelineDefine.SheetIndex.Master)
                {
                    var gallopProxy = _musicProxy as MusicProxyGallop;
                    if (_liveTimelineControl.GetCurrentWorkSheetType(_autoPlayTime) != gallopProxy.SheetType)
                    {
                        PlayMusic(_autoPlayTime);
                    }
                }

                //Debug.Log("currentFrame:"+_currentFrame+", autoPlayTime:"+_autoPlayTime);
                _currentFrame = (int)(_autoPlayTime * (float)targetFps);
                if (_currentFrame > _totalFrameCount)
                {
                    _currentFrame = _totalFrameCount;
                }

                if (_currentFrame > GetAutoPlayMarkerB().frame)
                {
                    //マーカーBを超えたのでマーカーAのFrameに再生場所を戻す
                    _autoPlayTime = (1.0f / (float)targetFps) * (float)GetAutoPlayMarkerA().frame;
                    if (isMovedByMusic)
                    {
                        //音楽をかけなおす
                        PlayMusic(_autoPlayTime);
                    }
                }

                Repaint();
            }
        }
        //--------------------------------------------------------------------------------
        //全Cuttデータを一括して保存しなおす
        //--------------------------------------------------------------------------------
        private void Update_BatchSave()
        {
            switch (_toolModeRno)
            {
                case 0:
                    BatchSaveData();
                    _toolModeRno++;
                    break;
                case 1:
                    ChangeToolMode(ToolMode.Normal);
                    break;
            }
        }
        //--------------------------------------------------------------------------------
        //Cuttデータのリロード
        //--------------------------------------------------------------------------------
        private void Update_Reload()
        {
            switch (_toolModeRno)
            {
                case 0:
                    s_timelineControltForReload = _liveTimelineControl;
                    s_prefabObjectForReload = PrefabUtility.GetCorrespondingObjectFromSource(_liveTimelineControl);
                    s_currentFrameForReload = _currentFrame;
                    s_ReloadRoutine = ReloadRoutine.WaitForToolClose;
                    //CutToolのインスタンスメンバー初期化が煩わしいのでツールを閉じてReopenで対処
                    Close();
                    EditorApplication.update += OnEditorApplicationUpdate;
                    break;
                default:
                    break;
            }
        }

        //--------------------------------------------------------------------------------
        //Cuttデータリロードに使用するEditorApplication.updateのコールバック
        //一旦CuttEditor自体をCloseするため、EditorApplication.updateにて復帰を実行する
        //--------------------------------------------------------------------------------
        private static void OnEditorApplicationUpdate()
        {
            switch (s_ReloadRoutine)
            {
                case ReloadRoutine.WaitForToolClose:
                    if (CutTool.instance != null)
                    {
                        break;//wait for tool close
                    }
                    s_ReloadRoutine = ReloadRoutine.DestroyControl;
                    break;
                case ReloadRoutine.DestroyControl:
                    if (Application.isPlaying)
                    {
                        Destroy(s_timelineControltForReload.gameObject);
                    }
                    else
                    {
                        DestroyImmediate(s_timelineControltForReload.gameObject);
                    }
                    s_timelineControltForReload = null;
                    s_ReloadRoutine = ReloadRoutine.DestroyControl_Wait1;
                    break;
                case ReloadRoutine.DestroyControl_Wait1:
                    s_ReloadRoutine = ReloadRoutine.DestroyControl_Wait2;
                    break;
                case ReloadRoutine.DestroyControl_Wait2:
                    s_ReloadRoutine = ReloadRoutine.InstanciateAndToolOpen;
                    break;
                case ReloadRoutine.InstanciateAndToolOpen:
                    PrefabUtility.InstantiatePrefab(s_prefabObjectForReload);
                    s_prefabObjectForReload = null;
                    CutTool.Open(s_currentFrameForReload);
                    EditorApplication.update -= OnEditorApplicationUpdate;
                    s_ReloadRoutine = ReloadRoutine.Finish;
                    break;
                case ReloadRoutine.Finish:
                    break;
            }
        }

        //--------------------------------------------------------------------------------
        #region Controll Pointの共通化
        //--------------------------------------------------------------------------------
        //コントロールポイントは処理が似ている物が多いのである程度まとめる
        private GameObject GetOrCreateCPGameObject(string cpName, Stack<GameObject> gameObjectStack, ref int seqNo)
        {
            GameObject go;
            if (gameObjectStack.Count > 0)
            {
                go = gameObjectStack.Pop();
                go.SetActive(true);
                return go;
            }

            //アセットはカメラと共通
            if (_cameraCPPrefab == null)
            {
                _cameraCPPrefab = AssetDatabase.LoadAssetAtPath(kAssetPathOfCameraCPPrefab, typeof(GameObject)) as GameObject;
            }

            GameObject prefab = _cameraCPPrefab;
            go = Instantiate(prefab, Vector3.zero, Quaternion.identity) as GameObject;
            go.name = cpName + " " + (seqNo++);
            go.transform.SetParent(_editorObjectRoot.transform);
            return go;
        }

        private void ReleaseCP<T>(LiveTimelineKey key, Dictionary<LiveTimelineKey, T> cpBundleDictionatory, Stack<GameObject> gameObjectPool) where T : CPBundle
        {
            T cpBundle;
            if (cpBundleDictionatory.TryGetValue(key, out cpBundle))
            {
                ReleaseCP(key, cpBundle, cpBundleDictionatory, gameObjectPool);
            }
        }

        private void ReleaseCP<T>(LiveTimelineKey key, T cpBundle, Dictionary<LiveTimelineKey, T> cpBundleDictionatory, Stack<GameObject> gameObjectPool) where T : CPBundle
        {
            cpBundle.OnDisable(gameObjectPool);
            cpBundleDictionatory.Remove(key);
        }

        private void ReleaseCPAll<T>(Dictionary<LiveTimelineKey, T> cpBundleDictionatory, Stack<GameObject> gameObjectPool) where T : CPBundle
        {
            var keys = cpBundleDictionatory.Keys.ToArray();
            foreach (var key in keys)
            {
                ReleaseCP(key, cpBundleDictionatory, gameObjectPool);
            }
        }

        public void ReleaseCPAllByRefCount<T>(Dictionary<LiveTimelineKey, T> cpBundleDictionatory, Stack<GameObject> gameObjectPool) where T : CPBundle
        {
            var keys = cpBundleDictionatory.Keys.ToArray();
            foreach (var key in keys)
            {
                T cpBundle;
                if (cpBundleDictionatory.TryGetValue(key, out cpBundle))
                {
                    cpBundle._refCount--;
                    if (cpBundle._refCount < 0)
                    {
                        cpBundle._refCount = 0;
                        ReleaseCP(key, cpBundle, cpBundleDictionatory, gameObjectPool);
                    }
                }
            }
        }

        //--------------------------------------------------------------------------------
        #endregion Controll Pointの共通化
        //--------------------------------------------------------------------------------

        //--------------------------------------------------------------------------------
        #region カメラControlPoint
        //--------------------------------------------------------------------------------
        private GameObject GetOrCreateCameraCPGameObject()
        {
            if (_cameraCPGameObjectPool.Count > 0)
            {
                var go = _cameraCPGameObjectPool.Pop();
                go.SetActive(true);
                return go;
            }
            else
            {
                if (_cameraCPPrefab == null)
                {
                    _cameraCPPrefab = AssetDatabase.LoadAssetAtPath(kAssetPathOfCameraCPPrefab, typeof(GameObject)) as GameObject;
                }
                var go = Instantiate(_cameraCPPrefab, Math.VECTOR3_ZERO, Math.QUATERNION_IDENTITY, _editorObjectRoot.transform) as GameObject;
                s_cameraCPSeqNo++;
                go.name = "CameraCP " + s_cameraCPSeqNo.ToString();
                return go;
            }
        }
        //--------------------------------------------------------------------------------
        private GameObject GetOrCreateBezierCPGameObject()
        {
            if (_bezierCPGameObjectPool.Count > 0)
            {
                var go = _bezierCPGameObjectPool.Pop();
                go.SetActive(true);
                return go;
            }
            else
            {
                if (_bezierCPPrefab == null)
                {
                    _bezierCPPrefab = AssetDatabase.LoadAssetAtPath(kAssetPathOfBazierCPPrefab, typeof(GameObject)) as GameObject;
                }
                var go = Instantiate(_bezierCPPrefab, Math.VECTOR3_ZERO, Math.QUATERNION_IDENTITY, _editorObjectRoot.transform) as GameObject;
                s_bezierCPSeqNo++;
                go.name = "BezierCP " + s_bezierCPSeqNo.ToString();
                return go;
            }
        }
        //--------------------------------------------------------------------------------
        public CPBundle GetOrCreateCP(LiveTimelineKey keyForControl, LiveTimelineWorkSheet sheet)
        {
            if (!_cameraCPBundleDict.TryGetValue(keyForControl, out CPBundle cpBundle))
            {
                cpBundle = AddCameraCP(keyForControl);
                if (cpBundle == null)
                {
                    return null;
                }

                var context = new CPBundle.Context
                {
                    sheet = sheet,
                    currentFrame = _currentFrame,
                };
                cpBundle.Initialize(keyForControl, _liveTimelineControl, context);
            }
            return cpBundle;
        }
        //--------------------------------------------------------------------------------
        private CPBundle AddCameraCP(LiveTimelineKey keyForControl)
        {
            if (_cameraCPBundleDict.ContainsKey(keyForControl))
            {
                return null;
            }
            CPBundle cpBundle = null;

            System.Action fnSetupCameraPointHandles = () =>
            {
                var cameraCpBundle = cpBundle as CameraCPBundle;
                //カメラハンドル生成
                {
                    var go = GetOrCreateCameraCPGameObject();
                    cameraCpBundle.SetCP(go.GetComponent<LiveTimelineEditor_PointHandle>());
                    cameraCpBundle._cp.hideGizmo = true;
                }
                //ベジェ制御点ハンドル生成
                for (int i = 0; i < LiveTimelineDefine.BEZIER_CONTROL_POINT_MAX; i++)
                {
                    var go = GetOrCreateBezierCPGameObject();
                    cameraCpBundle.BezierPointHandleArray[i] = go.GetComponent<LiveTimelineEditor_PointHandle>();
                    cameraCpBundle.BezierPointHandleArray[i].hideGizmo = true;
                    cameraCpBundle.BezierPointHandleArray[i].text = (i + 1).ToString();
                }
                cameraCpBundle.SetColor(EditSheet.GetKeyPropertySectionColor(keyForControl.dataType), kCameraCPBazierColor);
            };

            System.Action fnSetupObjectPointHandles = () =>
            {
                //ハンドル生成
                {
                    var go = GetOrCreateCameraCPGameObject();//Cameraのやつ使い回す
                    cpBundle.SetCP(go.GetComponent<LiveTimelineEditor_PointHandle>());
                    cpBundle._cp.hideGizmo = true;
                }
                cpBundle.SetColor(EditSheet.GetKeyPropertySectionColor(keyForControl.dataType));
            };

            switch (keyForControl.dataType)
            {
                case LiveTimelineKeyDataType.CameraPos:
                case LiveTimelineKeyDataType.CameraLookAt:
                case LiveTimelineKeyDataType.MonitorCameraPos:
                case LiveTimelineKeyDataType.MonitorCameraLookAt:
                case LiveTimelineKeyDataType.MultiCameraPos:
                case LiveTimelineKeyDataType.MultiCameraLookAt:
                case LiveTimelineKeyDataType.EyeCameraPos:
                case LiveTimelineKeyDataType.EyeCameraLookAt:
                    cpBundle = new CameraCPBundle();
                    fnSetupCameraPointHandles();
                    break;

                case LiveTimelineKeyDataType.FacialEyeTrack:
                case LiveTimelineKeyDataType.Object:
                case LiveTimelineKeyDataType.Transform:
                case LiveTimelineKeyDataType.Audience:
                case LiveTimelineKeyDataType.VolumeLight:
                case LiveTimelineKeyDataType.Laser:
                case LiveTimelineKeyDataType.LightProjection:
                case LiveTimelineKeyDataType.MobControl:
                case LiveTimelineKeyDataType.CyalumeControl:
                    cpBundle = new ObjectCPBundle();
                    fnSetupObjectPointHandles();
                    break;

                default:
                    DebugUtils.Assert(false, "default break: " + keyForControl.dataType);
                    return null;
            }

            _cameraCPBundleDict[keyForControl] = cpBundle;
            return cpBundle;
        }
        //--------------------------------------------------------------------------------
        public void ReleaseCameraCP(LiveTimelineKey key)
        {
            CPBundle cpBundle;
            if (_cameraCPBundleDict.TryGetValue(key, out cpBundle))
            {
                ReleaseCameraCP(key, cpBundle);
            }
        }
        //--------------------------------------------------------------------------------
        private void ReleaseCameraCP(LiveTimelineKey key, CPBundle cpBundle)
        {
            if (cpBundle._cp != null)
            {
                var go = cpBundle._cp.gameObject;
                cpBundle._cp.text = string.Empty;
                go.SetActive(false);
                _cameraCPGameObjectPool.Push(go);
            }

            var cameraCpBundle = cpBundle as CameraCPBundle;
            if (cameraCpBundle != null)
            {
                foreach (var cp in cameraCpBundle.BezierPointHandleArray)
                {
                    cp.text = string.Empty;
                    var go = cp.gameObject;
                    go.SetActive(false);
                    _bezierCPGameObjectPool.Push(go);
                }
            }

            _cameraCPBundleDict.Remove(key);
        }
        //--------------------------------------------------------------------------------
        //全てのカメラ制御ハンドルオブジェクトをReleaseする
        //--------------------------------------------------------------------------------
        public void ReleaseCameraCPAll()
        {
            var keys = _cameraCPBundleDict.Keys.ToArray();
            foreach (var key in keys)
            {
                ReleaseCameraCP(key);
            }
        }
        //--------------------------------------------------------------------------------
        //全てのカメラ制御ハンドルオブジェクトを参照カウントをチェックしてReleaseする
        //--------------------------------------------------------------------------------
        public void ReleaseCameraCPAllByRefCount()
        {
            var keys = _cameraCPBundleDict.Keys.ToArray();
            foreach (var key in keys)
            {
                CPBundle cpBundle;
                if (_cameraCPBundleDict.TryGetValue(key, out cpBundle))
                {
                    cpBundle._refCount--;
                    if (cpBundle._refCount < 0)
                    {
                        cpBundle._refCount = 0;
                        ReleaseCameraCP(key, cpBundle);
                    }
                }
            }
        }
        //--------------------------------------------------------------------------------
        #endregion カメラControlPoint
        //--------------------------------------------------------------------------------

        #region FormationOffsetControlPoint

        private GameObject GetOrCreateFormationOffsetCPGameObject()
        {
            return GetOrCreateCPGameObject("FormationOffsetCP", _formationCPGameObjectPool, ref s_formationOffsetCPSeqNo);
        }
        //--------------------------------------------------------------------------------
        public FormationOffsetCPBundle GetOrCreateFormationCP(LiveTimelineKey keyForControl, LiveTimelineWorkSheet sheet, int formationIndex)
        {
            FormationOffsetCPBundle cpBundle;
            if (!_formationOffsetCPBundleDict.TryGetValue(keyForControl, out cpBundle))
            {
                cpBundle = new FormationOffsetCPBundle();

                //カメラハンドル生成
                {
                    var go = GetOrCreateFormationOffsetCPGameObject();
                    cpBundle.SetCP(go.GetComponent<LiveTimelineEditor_PointHandle>());
                    cpBundle._cp.hideGizmo = true;
                    cpBundle._cp.text = string.Empty;
                }

                {
                    var go = GetOrCreateFormationOffsetCPGameObject();
                    cpBundle.WorldCp = go.GetComponent<LiveTimelineEditor_PointHandle>();
                    cpBundle.WorldCp.hideGizmo = true;
                    cpBundle.WorldCp.text = string.Empty;
                }

                _formationOffsetCPBundleDict[keyForControl] = cpBundle;

                FormationOffsetCPBundle.FormationContext context = new FormationOffsetCPBundle.FormationContext();
                context.sheet = sheet;
                context.currentFrame = _currentFrame;
                context.formationIndex = formationIndex;
                cpBundle.SetColor(EditSheet.GetKeyPropertySectionColor(keyForControl.dataType));
                cpBundle.Initialize(keyForControl, _liveTimelineControl, context);
            }
            else
            {
                //毎回設定が必要
                cpBundle.SetFormationIndex(formationIndex);
            }
            cpBundle._refCount = 1; //使用中の制御ハンドルは参照カウントを上げる
            return cpBundle;
        }
        //--------------------------------------------------------------------------------
        public void ReleaseFormationOffsetCP(LiveTimelineKey key)
        {
            ReleaseCP(key, _formationOffsetCPBundleDict, _formationCPGameObjectPool);
        }
        //--------------------------------------------------------------------------------
        //全てのFormationOffset制御ハンドルオブジェクトをReleaseする
        //--------------------------------------------------------------------------------
        public void ReleaseFormationOffsetCPAll()
        {
            ReleaseCPAll(_formationOffsetCPBundleDict, _formationCPGameObjectPool);
        }
        //--------------------------------------------------------------------------------
        //全てのFormationOffset制御ハンドルオブジェクトを参照カウントをチェックしてReleaseする
        //--------------------------------------------------------------------------------
        public void ReleaseFormationOffsetCPAllByRefCount()
        {
            ReleaseCPAllByRefCount(_formationOffsetCPBundleDict, _formationCPGameObjectPool);
        }
        #endregion FormationOffsetControlPoint

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        public void RepaintAll()
        {
            Repaint();
            foreach (var sheet in _editingSheets)
            {
                sheet.RequestCorrectEditingKey();//Repaintを兼ねる
            }
            if (_scratchWindow != null)
            {
                _scratchWindow.Repaint();
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        void OnGUI()
        {
            if (!_initialized || _liveTimelineControl == null)
            {
                OnGUI_NoTimelineControl();
                return;
            }

            GUIContext ctx = new GUIContext();
            ctx.requireRepaint = false;
            ctx.e = Event.current;
            ctx.tool = this;
            ctx.windowRect = position;
            ctx.currentFrameBackup = _currentFrame;

            //GUIメイン
            try
            {
                EditorGUIUtility.labelWidth = 0;
                EditorGUIUtility.fieldWidth = 0;

                GUILayout.BeginVertical();
                {
                    EditorGUI.BeginChangeCheck();
                    OnGUI_Main(ctx);
                    if (EditorGUI.EndChangeCheck())
                    {
                        // キーボードショートカットの誤爆防止
                        ctx.e.Use();
                    }
                    OnGUI_KeyboardShortcut(ctx);
                }
                GUILayout.EndVertical();

                //この処理によって、UnityのInputFieldが無駄にねっちょりFocusを保持する問題が解決する。
                //起こっていた問題としては
                //・editingTextFieldがTrueになりっぱなしだと、CuttのKeyboardショートカットが使えない
                //・InputFieldのFocusが残っていると、GUI切り替えがうまくいかない（切り替わらない）現象が発生する
                if (ctx.e.type == EventType.MouseDown)
                {
                    EditorGUIUtility.editingTextField = false;
                    ctx.requireRepaint = true;
                }

                if (ctx.requireRepaint)
                {
                    RepaintAll();
                }
            }
            catch (System.Exception exception)
            {
                //OnGUIでエラーが出るとコントロールを失ってUnity自体をShutdownするほかなくなるので
                //例外キャッチしてBreakする
                if (exception is ExitGUIException)
                {
                    throw exception;//Unity独特の無害な奴はRethrow
                }
                else
                {
                    Debug.LogError(exception.ToString());
                    Debug.Break();
                }
            }
        }

        //--------------------------------------------------------------------------------
        //LiveTimelineControlがシーンにない場合のOnGUI
        //--------------------------------------------------------------------------------
        string _newCreateName = "NewData";
        Vector2 _scrollValueOfExistsPrefabList = Vector2.zero;
        bool _visibleDevelopOpe = false;
        void OnGUI_NoTimelineControl()
        {
            GUILayout.Label("シーンにTimelineControlオブジェクトが見つかりません");
            GUILayout.Space(10);

            _scrollValueOfExistsPrefabList = GUILayout.BeginScrollView(_scrollValueOfExistsPrefabList);

            GUILayout.BeginHorizontal();

            GUILayout.BeginVertical(GUILayout.MaxWidth(position.width / 2));
            {
                GUILayout.Label(string.Format("--------- 新規作成 ---------"));
                GUILayout.BeginHorizontal();
                _newCreateName = EditorGUILayout.TextField("データ名", _newCreateName);
                var dirPath = string.Format("{0}/Cutt_{1}", LiveTimelineDefine.kCuttResourceDir, _newCreateName);
                var savePath = string.Format("{0}/Cutt_{1}.prefab", dirPath, _newCreateName);
                if (GUILayout.Button("新規作成", GUILayout.Width(80)))
                {
                    if (System.IO.Directory.Exists(dirPath))
                    {
                        var allowOverwrite = EditorUtility.DisplayDialog(
                            "上書き確認",
                            "すでにファイルが存在します。上書きしますか？",
                            "OK", "キャンセル");
                        if (!allowOverwrite)
                        {
                            return;
                        }
                        AssetDatabase.DeleteAsset(dirPath);
                    }
                    System.IO.Directory.CreateDirectory(dirPath);
                    if (!AssetDatabase.CopyAsset(LiveTimelineDefine.kNewPrefabTemplatePath, savePath))
                    {
                        Debug.LogWarning("Create new prefab failed");
                        return;
                    }
                    AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
                    var prefabObj = AssetDatabase.LoadAssetAtPath(savePath, typeof(GameObject));
                    var gobj = PrefabUtility.InstantiatePrefab(prefabObj) as GameObject;
                    var timelineControl = gobj.GetComponent<LiveTimelineControl>();
                    //データ新規作成
                    timelineControl.data = LiveTimelineData.CreateNew(timelineControl);
                    if (timelineControl.data == null)
                    {
                        Debug.LogError("Create data failed");
                        Debug.Break();
                        return;
                    }
                    if (!SaveData(timelineControl))
                    {
                        Debug.LogError("Save data failed");
                        Debug.Break();
                        return;
                    }

                    var immOpen = EditorUtility.DisplayDialog(
                        "確認",
                        "今作ったCuttデータをシーンに設置しますか？",
                        "Yes", "No");
                    if (!immOpen)
                    {
                        DestroyImmediate(gobj);
                        Repaint();
                        return;
                    }
                    Init();
                }
                GUILayout.EndHorizontal();
                GUILayout.Label(string.Format("Prefabは {0} へ作成されます", savePath));

                _visibleDevelopOpe = GUILayout.Toggle(_visibleDevelopOpe, "Developer's Option");
                if (_visibleDevelopOpe)
                {
                    //Cuttデータをバッチ保存
                    if (GUILayout.Button("Batch Save"))
                    {
                        ChangeToolMode(ToolMode.BatchSave);
                    }
                }
            }
            GUILayout.EndVertical();

            GUILayout.BeginVertical(GUILayout.MaxWidth(position.width / 2));
            {
                GUILayout.Label(string.Format("--------- 作成済みのやつを設置 ---------"));
                var prefabAssetPathList = GetPrefabAssetPathList();
                foreach (var assetPath in prefabAssetPathList)
                {
                    GUILayout.BeginHorizontal();
                    GUILayout.Label(assetPath);
                    if (GUILayout.Button("Load", GUILayout.Width(80)))
                    {
                        var prefabObj = AssetDatabase.LoadAssetAtPath(assetPath, typeof(GameObject));
                        PrefabUtility.InstantiatePrefab(prefabObj);
                        Init();
                    }
                    if (GUILayout.Button("素材撮影にコピー", GUILayout.Width(120)))
                    {
                        var prefabObj = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                        CopyToRecordingFolder(prefabObj);
                    }
                    GUILayout.EndHorizontal();
                }
            }
            GUILayout.EndVertical();

            GUILayout.EndHorizontal();

            GUILayout.EndScrollView();
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private bool _isFocusLayout = false;
        void OnGUI_KeyboardShortcut(GUIContext ctx)
        {
            if (this != EditorWindow.focusedWindow)
            {
                _isFocusLayout = false;
                return;
            }

            if (ctx.e.type == EventType.MouseDown)
            {
                _isFocusLayout = true;
            }

            if (EditorGUIUtility.editingTextField && !_isFocusLayout)
            {
                return;
            }
            if (_editingSheet == null)
            {
                return;
            }

            if (On_OnGUI_KeyboardShortcut != null)
            {
                On_OnGUI_KeyboardShortcut(ctx.e);
            }

            if (ctx.e.type == EventType.KeyDown)
            {
                switch (ctx.e.keyCode)
                {
                    case KeyCode.Space:
                        // 他のUIがフォーカスされて操作が妨害されるのを防ぐ。
                        GUI.FocusControl(string.Empty);
                        ToggleAutoPlay();
                        break;
                }
            }
        }

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        private void OnGUI_Main(GUIContext ctx)
        {
            using (new EditorGUILayout.HorizontalScope())
            {
                //Save
                using (new EditorGUILayout.HorizontalScope())
                {
                    if (GUILayout.Button(new GUIContent(texSave, "Cuttデータを保存"), GUIW(30), GUIH(20)))
                    {
                        if (IsValidationA2U())
                        {
                            SaveData(_liveTimelineControl);
                        }
                    }
                    EditorGUILayout.ObjectField(PrefabUtility.GetCorrespondingObjectFromSource(_liveTimelineControl.gameObject), typeof(GameObject), false);
                }

                //SettingsWindow
                if (GUILayout.Button(new GUIContent(texSettings, "設定ウィンドウを開く"), GUIW(30), GUIH(20)))
                {
                    if (_settingsWindow != null)
                    {
                        EditorWindow.FocusWindowIfItsOpen<CutTool_SettingsWindow>();
                    }
                    else
                    {
                        _settingsWindow = CutTool_SettingsWindow.Open(this);
                    }
                }

                //SheetListWindow
                if (GUILayout.Button(new GUIContent(texSheetList, "シートリストウィンドウを開く"), GUIW(30), GUIH(20)))
                {
                    if (_sheetListWindow != null)
                    {
                        EditorWindow.FocusWindowIfItsOpen<CutTool_SheetListWindow>();
                    }
                    else
                    {
                        _sheetListWindow = CutTool_SheetListWindow.Open(this);
                    }
                }

                //BookmarkWindow
                if (GUILayout.Button(new GUIContent(texBookmark, "ブックマークウィンドウを開く"), GUIW(30), GUIH(20)))
                {
                    OpenBookmarkWindow();
                }

                //SearchWindow
                if (GUILayout.Button(new GUIContent(texSearch, "キー検索ウィンドウを開く"), GUIW(30), GUIH(20)))
                {
                    if (_searchKeyWindow != null)
                    {
                        EditorWindow.FocusWindowIfItsOpen<CutTool_SearchKeyWindow>();
                    }
                    else
                    {
                        _searchKeyWindow = CutTool_SearchKeyWindow.Open(this);
                    }
                }

                //ScratchWindow
                if (GUILayout.Button(new GUIContent(texOpenScratch, "スクラッチウィンドウを開く"), GUIW(30), GUIH(20)))
                {
                    OpenScratchWindow();
                }

                GUILayout.FlexibleSpace();

                //カメラを選択状態にするボタン
                if (GUILayout.Button(new GUIContent(texSelectCamera, "アクティブカメラをシーン上で選択"), GUIW(30), GUIH(20)))
                {
                    if (_liveTimelineControl != null && _editingSheet != null)
                    {
                        var cam = _liveTimelineControl.GetCamera(_editingSheet._data.targetCameraIndex);
                        if (cam != null)
                        {
                            Selection.activeGameObject = cam.camera.gameObject;
                        }
                    }
                }

                GUILayout.Space(5);

                //Easeのヘルプを開く
                if (GUILayout.Button(new GUIContent(texEaseHelp, "Easeのデモページを開く"), GUIW(30), GUIH(20)))
                {
                    Application.OpenURL("http://easings.net/ja");
                }

                GUILayout.FlexibleSpace();

                if (isAutoPlaying)
                {
                    if (GUILayout.Button(texNavStop, GUIW(40), GUIH(20)))
                    {
                        ToggleAutoPlay();
                    }
                }
                else
                {
                    if (GUILayout.Button(texNavPlay, GUIW(40), GUIH(20)))
                    {
                        ToggleAutoPlay();
                    }
                }
                GUILayout.Label("PlaySpeed");
                _autoPlaySpeed = GUILayout.HorizontalSlider(_autoPlaySpeed, 0, 1, GUIW(50));
                GUILayout.Label(_autoPlaySpeed.ToString("F2"));

                GUILayout.FlexibleSpace();

                GUILayout.Label("View Scale");
                {
                    EditorGUI.BeginChangeCheck();
                    _visibleKeyframeBarRatio = GUILayout.HorizontalSlider(_visibleKeyframeBarRatio, 0, 1, GUIW(200));
                    if (EditorGUI.EndChangeCheck())
                    {
                        UpdateVisibleKeyframeBarRatio();
                    }
                }
            }

            using (new EditorGUILayout.HorizontalScope())
            {
                using (new EditorGUILayout.HorizontalScope(GUIW(kLeftColumnWidth)))
                {
                    if (GUILayout.Button("<<<", EditorStyles.miniButtonLeft))
                    {
                        // 先頭へ
                        _currentFrame = 0;
                        ctx.requireRepaint = true;
                        _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                    }
                    if (GUILayout.Button("<<", EditorStyles.miniButtonMid))
                    {
                        //１個前のKeyをフォーカス
                        if (_editingSheet != null && _editingSheet.JampEditingKey(false))
                        {
                            ctx.requireRepaint = true;
                            _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                        }
                    }
                    if (GUILayout.Button("<", EditorStyles.miniButtonMid))
                    {
                        //Frame１個前に
                        AddCurrentFrame(-1);
                        ctx.requireRepaint = true;
                        _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                    }
                    if (GUILayout.Button(">", EditorStyles.miniButtonMid))
                    {
                        //Frame１個次に
                        AddCurrentFrame(1);
                        ctx.requireRepaint = true;
                        _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                    }
                    if (GUILayout.Button(">>", EditorStyles.miniButtonMid))
                    {
                        //１個後のKeyをフォーカス
                        if (_editingSheet != null && _editingSheet.JampEditingKey(true))
                        {
                            ctx.requireRepaint = true;
                            _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                        }
                    }
                    if (GUILayout.Button(">>>", EditorStyles.miniButtonRight))
                    {
                        // 最後へ
                        _currentFrame = _totalFrameCount - 1;
                        ctx.requireRepaint = true;
                        _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                    }
                }
                using (new EditorGUILayout.HorizontalScope())
                {
                    ctx.GUIColorPush(Color.yellow);
                    var currentFrameBk = _currentFrame;
                    _currentFrame = EditorGUILayout.IntSlider(_currentFrame, 0, _totalFrameCount);
                    if (_currentFrame != currentFrameBk)
                    {
                        RepaintAll();
                        _editingSheet.ScrollKeyframeAreaWithFrame(_currentFrame);
                    }
                    if (GUILayout.Button("Copy", EditorStyles.miniButton, GUIW(36), GUIH(16)))
                    {
                        GUIUtility.systemCopyBuffer = _currentFrame.ToString();
                    }
                    float currentSec = (float)_currentFrame / (float)targetFps;
                    GUILayout.Label(string.Format("{0:F3}秒", currentSec), GUIEXPANDW);
                    ctx.GUIColorPop();
                }
            }

            GUILayout.Space(7);

            using (new EditorGUILayout.HorizontalScope())
            {
                //Undo/Redo
                {
                    GUI.enabled = opeCommander.CanUndo();
                    if (GUILayout.Button("Undo", EditorStyles.miniButtonLeft, GUIW(40), GUIH(20)))
                    {
                        opeCommander.Undo();
                        RepaintAll();
                    }

                    GUI.enabled = opeCommander.CanRedo();
                    if (GUILayout.Button("Redo", EditorStyles.miniButtonMid, GUIW(40), GUIH(20)))
                    {
                        opeCommander.Redo();
                        RepaintAll();
                    }
                    GUI.enabled = true;
                    if (GUILayout.Button("CLR", EditorStyles.miniButtonRight, GUIW(40), GUIH(20)))
                    {
                        opeCommander.Clear();
                        RepaintAll();
                    }
                }

                //Key追加、削除ショートカットボタン
                GUILayout.FlexibleSpace();
                if (_editingSheet != null)
                {
                    _editingSheet.OnGUI_KeyToggleButtons(ctx, false);
                }

                GUILayout.FlexibleSpace();
                //Cuttデータ再読み込み
                if (GUILayout.Button("Reload", GUIH(20)))
                {
                    if (!isAutoPlaying)
                    {
                        if (EditorUtility.DisplayDialog(
                            "確認",
                            "Cuttデータをリロードしますか？保存されていない編集中データは破棄されます",
                            "Yes", "No"))
                        {
                            ChangeToolMode(ToolMode.Reload);
                        }
                    }
                }
                GUILayout.Space(10);
                //アニメーションリダクション情報出力
                if (GUILayout.Button("アニメーションリダクション情報出力", GUIH(20)))
                {
                    if (null != _editingSheet)
                    {
                        _editingSheet.OutputAnimationKeyReductionInfo();
                    }
                }
            }

            using (new EditorGUILayout.HorizontalScope())
            {
                _liveTimelineControl.IsSequencialPlay = GUILayout.Toggle(_liveTimelineControl.IsSequencialPlay, "寸劇再生(シート連続再生)");
                if(GUILayout.Button("シート操作ウインドウ"))
                {
                    GetWindow<CutTool_SheetControl>();
                }

#if CYG_DEBUG
                if (GUILayout.Button("Transform Recorder"))
                {
                    TransformRecorder.TransformRecorderWindow.Open();
                }

                var enableCameraOverride = EditorGUILayout.ToggleLeft("撮影用",(_cameraControllerForRecording?.Enabled == true), GUILayout.MaxWidth(60));
                if (_cameraControllerForRecording != null)
                {
                    _cameraControllerForRecording.SetEnable(enableCameraOverride);
                    _cameraControllerForRecording.OnGUI();
                }
                else if (enableCameraOverride && (Director.Instance.CheckAlive()?.CameraObjects.Length > 0))
                {
                    _cameraControllerForRecording = new CameraControllerForRecording();
                    _cameraControllerForRecording.Initialize(Director.Instance.CameraObjects);
                }
#endif
            }

            using (new EditorGUILayout.HorizontalScope())
            {
                using (new EditorGUILayout.HorizontalScope(GUI.skin.box))
                {
                    //ClipBoard情報を表示
                    if (_clipBoardInfo == null)
                    {
                        EditorGUILayout.LabelField("[ClipBoard] Empty");
                    }
                    else
                    {
                        EditorGUILayout.LabelField(string.Format("[ClipBoard] {0}", _clipBoardInfo.status.GetLabel()));
                        //ClipBoardクリアボタン
                        if (GUILayout.Button("Clear"))
                        {
                            _clipBoardInfo = null;
                        }
                    }
                }
                GUILayout.Space(10);

                //Undo/Redo情報を表示
                GUILayout.Label(opeCommander.GetInfo());

                GUILayout.FlexibleSpace();
            }
        }

        /// <summary>
        /// A2Uのデータが正しい設定になっているか
        /// </summary>
        /// <returns></returns>
        private bool IsValidationA2U()
        {
            bool error = false;
            System.Text.StringBuilder description = new System.Text.StringBuilder();

            for (int i = 0; i < _liveTimelineControl.data.GetWorkSheetList().Count; i++)
            {
                int num = _liveTimelineControl.data.GetWorkSheet(i).a2uList.Count;
                for (int j = 0; j < num; j++)
                {
                    var a2uSheet = _liveTimelineControl.data.GetWorkSheet(i).a2uList[j];
                    if (!A2U.IsPrefab(a2uSheet.name))
                    {
                        description.AppendLine("A2U:" + a2uSheet.name + ":Cutt/Settingsに登録されていないNameです");
                        error = true;
                    }
                }
            }

            if (error)
            {
                description.AppendLine("タイムラインのA2U Timeline Propertyを確認してください");
                EditorUtility.DisplayDialog(
                    string.Format("A2Uエラー"),
                    description.ToString(),
                    "OK"
                );

                return false;
            }

            return true;
        }

        //--------------------------------------------------------------------------------
        #region CutTools Window（SceneViewで表示されるウィンドウ）
        //--------------------------------------------------------------------------------
        /// <summary>
        /// CutTools Window
        /// </summary>
        private const int _cuttToolsButtonDispCount = 3;
        private const int _cuttToolsButtonMaxCount = 7;
        private const float _cuttToolsButtonWidth = 35f;
        private const float _cuttToolsScrollBarHeight = 12f;
        private const float _cuttToolsWindowBaseWidth = 85f;
        private const float _cuttToolsWindowInitSizeX = _cuttToolsWindowBaseWidth + (_cuttToolsButtonWidth * _cuttToolsButtonDispCount);
        private const float _cuttToolsWindowInitSizeY = 140f + ((_cuttToolsButtonDispCount < _cuttToolsButtonMaxCount) ? _cuttToolsScrollBarHeight : 0f);
        private const float _cuttToolsWindowFoldoutSizeY = 90f;//Foldout部分を展開した場合の拡張サイズ
        private const string SETTING_TOOLS_WINDOW_SIZE_X = "LiveTimelineEditor.CuttTools_WindowSize_X";
        private const string SETTING_TOOLS_WINDOW_SIZE_Y = "LiveTimelineEditor.CuttTools_WindowSize_Y";

        private Rect _cuttToolsWindowRect = new Rect(10f, 20f, _cuttToolsWindowInitSizeX, _cuttToolsWindowInitSizeY);
        private bool _cuttToolsWindowSettingFoldout = false;
        private bool _cuttToolsWindowToggleFoldout = false;
        private Vector2 _cuttToolsScroll = Vector2.zero;

        private static float GetSettingsFloat(string name, float defaultValue)
        {
            var result = EditorUserSettings.GetConfigValue(name);
            if (string.IsNullOrEmpty(result))
            {
                return defaultValue;
            }
            if (!float.TryParse(result, out var value))
            {
                return defaultValue;
            }
            return value;
        }

        private static void SetSettingsFloat(string name, float value)
        {
            EditorUserSettings.SetConfigValue(name, value.ToString());
        }

        private void CuttToolsWindowUpdate(int windowId)
        {
            using (var scrollView = new EditorGUILayout.ScrollViewScope(_cuttToolsScroll))
            {
                _cuttToolsScroll = scrollView.scrollPosition;

                _cuttToolsWindowSettingFoldout = EditorGUILayout.Foldout(_cuttToolsWindowSettingFoldout, "設定");
                if (_cuttToolsWindowSettingFoldout)
                {
                    EditorGUI.indentLevel++;

                    var windowSize = new Vector2(_cuttToolsWindowRect.width, _cuttToolsWindowRect.height);
                    EditorGUILayout.LabelField("Size");
                    EditorGUI.indentLevel++;
                    windowSize.x = EditorGUILayout.DelayedFloatField("Width", windowSize.x);
                    windowSize.y = EditorGUILayout.DelayedFloatField("Height", windowSize.y);
                    // 初期サイズより小さくならないようにする。
                    windowSize.x = Mathf.Max(windowSize.x, _cuttToolsWindowInitSizeX);
                    windowSize.y = Mathf.Max(windowSize.y, _cuttToolsWindowInitSizeY);
                    EditorGUI.indentLevel--;

                    if ((_cuttToolsWindowRect.width != windowSize.x) ||
                        (_cuttToolsWindowRect.height != windowSize.y))
                    {
                        _cuttToolsWindowRect.width = windowSize.x;
                        _cuttToolsWindowRect.height = windowSize.y;

                        //ウインドウサイズを保存する
                        SetSettingsFloat(SETTING_TOOLS_WINDOW_SIZE_X, windowSize.x);
                        SetSettingsFloat(SETTING_TOOLS_WINDOW_SIZE_Y, windowSize.y);
                    }

                    EditorGUI.indentLevel--;
                }

                //カメラControlPoint系の操作
                if ((_editingSheet != null) && (_editingSheet._data != null))
                {
                    CuttToolsWindowUpdate_CameraCP("Pos", _editingSheet._data.cameraPosKeys);
                    CuttToolsWindowUpdate_CameraCP("LookAt", _editingSheet._data.cameraLookAtKeys);

                    int count = Mathf.Min(_editingSheet._data.monitorCameraPosKeys.Count, _editingSheet._data.monitorCameraLookAtKeys.Count);
                    for (int index = 0; index < count; ++index)
                    {
                        CuttToolsWindowUpdate_CameraCP("MonPos:" + index, _editingSheet._data.monitorCameraPosKeys[index].keys);
                        CuttToolsWindowUpdate_CameraCP("MonLookAt:" + index, _editingSheet._data.monitorCameraLookAtKeys[index].keys);
                    }

                    int multiNum = Mathf.Max(_editingSheet._data.multiCameraPosKeys.Count, _editingSheet._data.multiCameraLookAtKeys.Count);
                    for (int i = 0; i < multiNum; i++)
                    {
                        if (_editingSheet._data.multiCameraPosKeys.Count > i)
                        {
                            CuttToolsWindowUpdate_CameraCP("MultiPos:" + i, _editingSheet._data.multiCameraPosKeys[i].keys);
                        }
                        if (_editingSheet._data.multiCameraLookAtKeys.Count > i)
                        {
                            CuttToolsWindowUpdate_CameraCP("MultiLookAt:" + i,
                                                           _editingSheet._data.multiCameraLookAtKeys[i].keys);
                        }
                    }

                    count = Mathf.Min(_editingSheet._data.EyeCameraPosList.Count, _editingSheet._data.EyeCameraLookAtList.Count);
                    for (int i = 0; i < count; ++i)
                    {
                        CuttToolsWindowUpdate_CameraCP("EyePos:" + i, _editingSheet._data.EyeCameraPosList[i].keys);
                        CuttToolsWindowUpdate_CameraCP("EyeLookAt:" + i, _editingSheet._data.EyeCameraLookAtList[i].keys);
                    }
                }
                else
                {
                    CuttToolsWindowUpdate_CameraCP("Pos", null);
                    CuttToolsWindowUpdate_CameraCP("LookAt", null);
                }

                if (_editingSheet._editingLine != null)
                {
                    var keyDataType = _editingSheet._editingLine.keyDataType;
                    var timeline = _editingSheet._editingLine as EditTimeline;
                    string label = string.Empty;
                    if (timeline != null)
                    {
                        label = timeline.name;
                    }
                    //複数選択されている場合はそれっぽいマークを付ける
                    if (_editingSheet._selectionWork.GetEditLineArrayNum(keyDataType) >= 2)
                    {
                        label = "M*" + label;
                    }
                    switch (keyDataType)
                    {
                        case LiveTimelineKeyDataType.FormationOffset:
                            //指定キャラクター操作用
                            CuttToolsWindowUpdate_FormationOffsetCP(label, timeline._editKeyList);
                            break;
                        case LiveTimelineKeyDataType.FacialEyeTrack:
                        case LiveTimelineKeyDataType.Object:
                        case LiveTimelineKeyDataType.Transform:
                        case LiveTimelineKeyDataType.Audience:
                        case LiveTimelineKeyDataType.VolumeLight:
                        case LiveTimelineKeyDataType.Laser:
                        case LiveTimelineKeyDataType.LightProjection:
                        case LiveTimelineKeyDataType.MobControl:
                        case LiveTimelineKeyDataType.CyalumeControl:
                            //Object操作用
                            CuttToolsWindowUpdate_ObjectCP(_editingSheet, keyDataType);
                            break;
                    }
                }

                //いろいろ（Expandableにして隠す）
                _cuttToolsWindowToggleFoldout = EditorGUILayout.Foldout(_cuttToolsWindowToggleFoldout, "Functions");
                if (_cuttToolsWindowToggleFoldout)
                {
                    disableFaicialNoise = GUILayout.Toggle(disableFaicialNoise, "FacialNoise OFF (AutoPlay)");
                    disableCharaMotionNoise = GUILayout.Toggle(disableCharaMotionNoise, "MotionNoise OFF (AutoPlay)");
                    alwaysEnableFacialNoise = GUILayout.Toggle(alwaysEnableFacialNoise, "FacialNoise ON (Always)");
                    alwaysEnableCharaMotionNoise = GUILayout.Toggle(alwaysEnableCharaMotionNoise, "MotionNoise ON (Always)");

                    float lastRectMaxY = 0f;
                    using (new EditorGUILayout.HorizontalScope())
                    {
                        for (int i = 0; i < LiveTimelineDefine.kCameraMaxNum; i++)
                        {
                            var label = "C" + (i + 1);
                            _cameraPreviewStats[i] = GUILayout.Toggle(_cameraPreviewStats[i], label);
                            _previewCameras[i].enabled = _cameraPreviewStats[i];
                            lastRectMaxY = GUILayoutUtility.GetLastRect().yMax;
                            GUILayout.Space(5f);
                        }
                        GUILayout.FlexibleSpace();
                    }
                    using (new EditorGUILayout.HorizontalScope())
                    {
                        float x = 5f;
                        float y = lastRectMaxY + 3f;
                        for (int i = 0; i < LiveTimelineDefine.kCameraMaxNum; i++)
                        {
                            if (_cameraPreviewStats[i])
                            {
                                if (_previewCameraRTs[i])
                                {
                                    var texRect = new Rect(x, y, _previewResolution.x, _previewResolution.y);
                                    GUI.DrawTexture(texRect, _previewCameraRTs[i]);
                                    x += _previewResolution.x + 5f;
                                }
                            }
                            GUILayout.Space(5f);
                        }
                        GUILayout.FlexibleSpace();
                    }
                }
            }

            GUI.DragWindow();
        }

        public static void AddSelectPointHandle(MonoBehaviour[] addCPs_)
        {
            if (Event.current == null)
            {
                return;
            }

            //Ctrl押しながらだったら選択リストへ追加
            if (!Event.current.control)
            {
                Selection.activeGameObject = null;
            }
            var selectionObjs = new List<UnityEngine.Object>();
            foreach (var cp_ in addCPs_)
            {
                if (cp_ == null)
                {
                    continue;
                }
                if (!Selection.Contains(cp_.GetInstanceID()))
                {
                    selectionObjs.Add(cp_.gameObject);
                }
            }
            if (selectionObjs.Count > 0)
            {
                selectionObjs.AddRange(Selection.objects);
                Selection.objects = selectionObjs.ToArray();//これがやたら重たい…けどこれ以外方法がない
            }

            // 選択時に自動でフォーカスするテスト <2017/08/01:miyashita>
            if (addCPs_.Length > 0)
            {
                SceneView.lastActiveSceneView.LookAt(Selection.activeGameObject.transform.position);
            }
        }

        #region ControlPointの登録

        /// <summary>
        /// ControlPointの追加
        /// </summary>
        /// <param name="addCPs"></param>
        private void AddCP(MonoBehaviour[] addCPs)
        {
            //Ctrl押しながらだったら選択リストへ追加
            if (!Event.current.control)
            {
                Selection.activeGameObject = null;
            }
            var selectionObjs = new List<UnityEngine.Object>();
            foreach (var cp_ in addCPs)
            {
                if (cp_ == null)
                {
                    continue;
                }
                if (!Selection.Contains(cp_.GetInstanceID()))
                {
                    selectionObjs.Add(cp_.gameObject);
                }
            }
            if (selectionObjs.Count > 0)
            {
                selectionObjs.AddRange(Selection.objects);
                Selection.objects = selectionObjs.ToArray();//これがやたら重たい…けどこれ以外方法がない
            }
        }

        /// <summary>
        /// キャラクター操作用
        /// </summary>
        /// <param name="label"></param>
        /// <param name="keys"></param>
        private void CuttToolsWindowUpdate_FormationOffsetCP(string label, ILiveTimelineKeyDataList keys)
        {
            LiveTimelineKey curKey = null;
            LiveTimelineKey nextKey = null;
            if (keys != null)
            {
                LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, keys, _currentFrame);
            }

            FormationOffsetCPBundle curBundle = null;
            FormationOffsetCPBundle nextBundle = null;

            if (curKey != null)
            {
                _formationOffsetCPBundleDict.TryGetValue(curKey, out curBundle);
            }
            if (nextKey != null)
            {
                _formationOffsetCPBundleDict.TryGetValue(nextKey, out nextBundle);
            }

            GUILayout.BeginHorizontal();
            {
                GUILayout.Label(label, GUILayout.Width(70));

                var layoutOpt = new GUILayoutOption[] { GUIW(30), GUIH(20) };
                //制御点１を選択
                GUI.enabled = curBundle != null;
                if (GUILayout.Button(texSelectP1, layoutOpt))
                {
                    AddCP(new UnityEngine.MonoBehaviour[] { curBundle._cp });
                }
                //制御点２を選択
                GUI.enabled = nextBundle != null;
                if (GUILayout.Button(texSelectP2, layoutOpt))
                {
                    AddCP(new UnityEngine.MonoBehaviour[] { nextBundle._cp });
                }
                //カメラ制御点１、２を選択
                GUI.enabled = curBundle != null && nextBundle != null;
                if (GUILayout.Button(texSelectP12, layoutOpt))
                {
                    AddCP(new UnityEngine.MonoBehaviour[] { curBundle._cp, nextBundle._cp });
                }

                //ワールド原点制御1を選択
                if (curKey is LiveTimelineKeyFormationOffsetData )
                {
                    var curKeyData = curKey as LiveTimelineKeyFormationOffsetData;
                    GUI.enabled = curBundle != null && curKeyData.IsWorldSpace;
                    if (GUILayout.Button(texSelectC1, layoutOpt))
                    {
                        AddCP(new UnityEngine.MonoBehaviour[] { curBundle.WorldCp });
                    }
                }

                //ワールド原点制御2を選択
                if (nextKey is LiveTimelineKeyFormationOffsetData)
                {
                    var nextKeyData = nextKey as LiveTimelineKeyFormationOffsetData;
                    GUI.enabled = curBundle != null && nextKeyData.IsWorldSpace;
                    if (GUILayout.Button(texSelectC2, layoutOpt))
                    {
                        AddCP(new UnityEngine.MonoBehaviour[] { nextBundle.WorldCp });
                    }
                }

                //ワールド原点制御1、2を選択
                if (curKey is LiveTimelineKeyFormationOffsetData && nextKey is LiveTimelineKeyFormationOffsetData)
                {
                    var curKeyData = curKey as LiveTimelineKeyFormationOffsetData;
                    var nextKeyData = nextKey as LiveTimelineKeyFormationOffsetData;
                    GUI.enabled = curBundle != null && nextKeyData.IsWorldSpace && curKeyData.IsWorldSpace;
                    if (GUILayout.Button(texSelectC12, layoutOpt))
                    {
                        AddCP(new UnityEngine.MonoBehaviour[] { curBundle.WorldCp, nextBundle.WorldCp });
                    }
                }
            }
            GUILayout.EndHorizontal();
        }

        private void CuttToolsWindowUpdate_ObjectCP(EditSheet editSheet, LiveTimelineKeyDataType keyType)
        {
            System.Action<List<LiveTimelineKey>[]> fnAddSelect = (addKeyListArray) =>
            {
                var pointHandles = new List<MonoBehaviour>();
                foreach (var addKeyList in addKeyListArray)
                {
                    foreach (var key in addKeyList)
                    {
                        CPBundle cpBundle = null;
                        if (!_cameraCPBundleDict.TryGetValue(key, out cpBundle))
                            continue;
                        pointHandles.Add(cpBundle._cp);
                    }
                }

                //Ctrl押しながらだったら選択リストへ追加
                if (!Event.current.control)
                {
                    Selection.activeGameObject = null;
                }
                var selectionObjs = new List<UnityEngine.Object>();
                foreach (var cp_ in pointHandles)
                {
                    if (cp_ == null)
                    {
                        continue;
                    }
                    if (!Selection.Contains(cp_.GetInstanceID()))
                    {
                        selectionObjs.Add(cp_.gameObject);
                    }
                }
                if (selectionObjs.Count > 0)
                {
                    selectionObjs.AddRange(Selection.objects);
                    Selection.objects = selectionObjs.ToArray();//これがやたら重たい…けどこれ以外方法がない
                }
            };

            var curKeys = new List<LiveTimelineKey>();
            var nextKeys = new List<LiveTimelineKey>();

            //編集中タイムラインのCurrentKey, NextKeyを編集対象に追加
            var selectedEditLines = editSheet._selectionWork.GetEditLineArray(keyType);
            foreach (var line in selectedEditLines)
            {
                LiveTimelineKey curKey, nextKey;
                LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, line._editKeyList, editSheet.Compat.currentFrame);
                if (curKey != null)
                {
                    curKeys.Add(curKey);
                }
                if (nextKey != null)
                {
                    nextKeys.Add(nextKey);
                }
            }

            GUILayout.BeginHorizontal();
            {
                var multiEdit = selectedEditLines.Length > 1;
                GUILayout.Label((multiEdit ? "M*" : "") + keyType.ToString(), GUILayout.Width(70));

                var layoutOpt = new GUILayoutOption[] { GUIW(30), GUIH(20) };
                //カメラ制御点１を選択
                GUI.enabled = curKeys.Count > 0;
                if (GUILayout.Button(texSelectP1, layoutOpt))
                {
                    fnAddSelect(new[] { curKeys });
                }
                //カメラ制御点２を選択
                GUI.enabled = nextKeys.Count > 0;
                if (GUILayout.Button(texSelectP2, layoutOpt))
                {
                    fnAddSelect(new[] { nextKeys });
                }
                //カメラ制御点１、２を選択
                GUI.enabled = curKeys.Count > 0 || nextKeys.Count > 0;
                if (GUILayout.Button(texSelectP12, layoutOpt))
                {
                    fnAddSelect(new[] { curKeys, nextKeys });
                }

                GUI.enabled = true;
            }
            GUILayout.EndHorizontal();
        }

        #endregion

        //--------------------------------------------------------------------------------
        //--------------------------------------------------------------------------------
        void CuttToolsWindowUpdate_CameraCP(string label, ILiveTimelineKeyDataList keys)
        {
            LiveTimelineKey curKey = null;
            LiveTimelineKey nextKey = null;
            if (keys != null)
            {
                LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, keys, _currentFrame);
            }

            CPBundle curBundle = null;
            CPBundle nextBundle = null;

            if (curKey != null)
            {
                _cameraCPBundleDict.TryGetValue(curKey, out curBundle);
            }
            if (nextKey != null)
            {
                _cameraCPBundleDict.TryGetValue(nextKey, out nextBundle);
            }

            GUILayout.BeginHorizontal();
            {
                GUILayout.Label(label, GUILayout.Width(70));

                var layoutOpt = new GUILayoutOption[] { GUIW(30), GUIH(20) };
                //カメラ制御点１を選択
                GUI.enabled = curBundle != null;
                if (GUILayout.Button(texSelectP1, layoutOpt))
                {
                    AddSelectPointHandle(new[] { curBundle._cp });
                }
                //カメラ制御点２を選択
                GUI.enabled = nextBundle != null;
                if (GUILayout.Button(texSelectP2, layoutOpt))
                {
                    AddSelectPointHandle(new[] { nextBundle._cp });
                }
                //カメラ制御点１、２を選択
                GUI.enabled = curBundle != null && nextBundle != null;
                if (GUILayout.Button(texSelectP12, layoutOpt))
                {
                    AddSelectPointHandle(new[] { curBundle._cp, nextBundle._cp });
                }

                var nextCameraBundle = nextBundle as CameraCPBundle;
                if (nextCameraBundle != null)
                {
                    var bazier = nextCameraBundle.BezierPointHandleArray;
                    //曲線制御点１を選択
                    GUI.enabled = bazier != null && bazier[0].hideGizmo == false;
                    if (GUILayout.Button(texSelectC1, layoutOpt))
                    {
                        AddSelectPointHandle(new[] { bazier[0] });
                    }
                    //曲線制御点２を選択
                    GUI.enabled = bazier != null && bazier[1].hideGizmo == false;
                    if (GUILayout.Button(texSelectC2, layoutOpt))
                    {
                        AddSelectPointHandle(new[] { bazier[1] });
                    }
                    //曲線制御点３を選択
                    GUI.enabled = bazier != null && bazier[2].hideGizmo == false;
                    if (GUILayout.Button(texSelectC3, layoutOpt))
                    {
                        AddSelectPointHandle(new[] { bazier[2] });
                    }

                    //曲線制御点１、２、３を選択
                    GUI.enabled = bazier != null && bazier[0].hideGizmo == false && bazier[1].hideGizmo == false && bazier[2].hideGizmo == false;
                    if (GUILayout.Button(texSelectC12, layoutOpt))
                    {
                        AddSelectPointHandle(new[] { bazier[0], bazier[1], bazier[2] });
                    }

                }
                GUI.enabled = true;
            }
            GUILayout.EndHorizontal();
        }

        /// <summary>
        /// SceneView.onSceneGUIDelegateに登録される関数
        /// </summary>
        /// <param name="sceneView"></param>
        void OnScene(SceneView sceneView)
        {
            if (_editingSheet == null)
            {
                return;
            }

            Handles.BeginGUI();
            {
                //CutToolsウィンドウ
                float addHeight = 0f;
                float winWidth = 5f;
                for (int i = 0; i < LiveTimelineDefine.kCameraMaxNum; i++)
                {
                    if (_cameraPreviewStats[i] && (_previewCameraRTs[i] != null))
                    {
                        addHeight = _previewResolution.y + 5f;
                        winWidth += _previewResolution.x + 5f;
                    }
                }
                //項目追加分
                if (_editingSheet._data.monitorCameraPosKeys.Count > 0)
                {
                    addHeight += 21.0f;
                }
                if (_editingSheet._data.monitorCameraLookAtKeys.Count > 0)
                {
                    addHeight += 21.0f;
                }
                if (_editingSheet._data.multiCameraPosKeys.Count > 0)
                {
                    addHeight += 21.0f;
                }
                if (_editingSheet._data.multiCameraLookAtKeys.Count > 0)
                {
                    addHeight += 21.0f;
                }
                if (_editingSheet._data.EyeCameraPosList.Count > 0)
                {
                    addHeight += 21.0f;
                }
                if (_editingSheet._data.EyeCameraLookAtList.Count > 0)
                {
                    addHeight += 21.0f;
                }

                //選択されているラインによって追加で項目を表示する
                if (_editingSheet._editingLine != null)
                {
                    switch (_editingSheet._editingLine.keyDataType)
                    {
                        case LiveTimelineKeyDataType.FacialEyeTrack:
                        case LiveTimelineKeyDataType.Object:
                        case LiveTimelineKeyDataType.FormationOffset:
                        case LiveTimelineKeyDataType.Transform:
                        case LiveTimelineKeyDataType.Audience:
                        case LiveTimelineKeyDataType.VolumeLight:
                        case LiveTimelineKeyDataType.Laser:
                        case LiveTimelineKeyDataType.LightProjection:
                        case LiveTimelineKeyDataType.MobControl:
                        case LiveTimelineKeyDataType.CyalumeControl:
                            addHeight += 21.0f;
                            break;
                    }
                }

                winWidth += 40.0f;
                _cuttToolsWindowRect.height = _cuttToolsWindowInitSizeY + addHeight;
                if (_cuttToolsWindowToggleFoldout)
                {
                    _cuttToolsWindowRect.height += _cuttToolsWindowFoldoutSizeY;
                }
                _cuttToolsWindowRect.width = Mathf.Max(winWidth, _cuttToolsWindowInitSizeX);

                //ウインドウサイズを保存する
                _cuttToolsWindowRect.width = GetSettingsFloat(SETTING_TOOLS_WINDOW_SIZE_X, _cuttToolsWindowRect.width);
                _cuttToolsWindowRect.height = GetSettingsFloat(SETTING_TOOLS_WINDOW_SIZE_Y, _cuttToolsWindowRect.height);

                _cuttToolsWindowRect = GUI.Window(0, _cuttToolsWindowRect, CuttToolsWindowUpdate, "Cutt Tools");
            }
            Handles.EndGUI();

            OnSceneGUI_CameraPosInterpolateLine(_liveTimelineControl);
            OnSceneGUI_CameraLookAtLine(_liveTimelineControl);
            OnSceneGUI_SelectedTransform(_liveTimelineControl);
            OnSceneGUI_MonitorCameraPosInterpolateLine(_liveTimelineControl);
            OnSceneGUI_MonitorCameraLookAtLine(_liveTimelineControl);
            OnSceneGUI_MultiCameraPosInterpolateLine(_liveTimelineControl);
            OnSceneGUI_MultiCameraLookAtLine(_liveTimelineControl);
            OnSceneGUI_EyeCameraPosInterpolateLine(_liveTimelineControl);
            OnSceneGUI_EyeCameraLookAtLine(_liveTimelineControl);

            OnSceneGUI_DrawGlobalLightDir(_liveTimelineControl);

            if (_editingSheet._editingLine != null)
            {
                switch (_editingSheet._editingLine.keyDataType)
                {
                    case LiveTimelineKeyDataType.FormationOffset:
                        {
                            var formationOffset = _editingSheet._editingLine as EditTimelineFormationOffset;
                            OnSceneGUI_FormationOffsetPosInterpolateLine(_liveTimelineControl, formationOffset._editKeyList);
                        }
                        break;
                }
            }
        }

        /// <summary>
        /// モニターカメラ用のkey座標補間ライン描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_MonitorCameraPosInterpolateLine(LiveTimelineControl timelineControl)
        {
            for (int index = 0; index < _editingSheet._data.monitorCameraPosKeys.Count; ++index)
            {
                OnSceneGUI_CameraPosInterpolateLine(_liveTimelineControl, _editingSheet._data.monitorCameraPosKeys[index].keys);
            }
        }

        /// <summary>
        /// モニターカメラ用LookAtのデバッグ描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_MonitorCameraLookAtLine(LiveTimelineControl timelineControl)
        {
            for (int index = 0; index < _editingSheet._data.monitorCameraLookAtKeys.Count; ++index)
            {
                OnSceneGUI_CameraLookAtLine(timelineControl, _editingSheet._data.monitorCameraLookAtKeys[index].keys,
                    (out Vector3 cameraPos, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                    {
                        liveTimelineControl.CalculateMonitorCameraPos(out cameraPos, _editingSheet._data, currentFrame, index);
                    },

                    (out Vector3 cameraLookAt, out float cameraLocalScale, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                    {
                        liveTimelineControl.CalculateMonitorCameraLookAt(out cameraLookAt, out cameraLocalScale, _editingSheet._data, currentFrame, index);
                    }
                );
            }
        }

        /// <summary>
        /// マルチカメラ用のkey座標補間ライン描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_MultiCameraPosInterpolateLine(LiveTimelineControl timelineControl)
        {
            if (_editingSheet._data.multiCameraPosKeys.Count > 0)
            {
                for (int i = 0; i < _editingSheet._data.multiCameraPosKeys.Count; i++)
                {
                    OnSceneGUI_CameraPosInterpolateLine(_liveTimelineControl,
                                                        _editingSheet._data.multiCameraPosKeys[i].keys);
                }
            }
        }

        /// <summary>
        /// マルチカメラ用LookAtのデバッグ描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_MultiCameraLookAtLine(LiveTimelineControl timelineControl)
        {
            if ((_editingSheet._data.multiCameraLookAtKeys.Count > 0) && (_editingSheet._data.multiCameraPosKeys.Count > 0))
            {
                for (int i = 0; i < _editingSheet._data.multiCameraLookAtKeys.Count; i++)
                {
                    OnSceneGUI_CameraLookAtLine(timelineControl, _editingSheet._data.multiCameraLookAtKeys[i].keys,
                                                (out Vector3 cameraPos, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                                                {
                                                    liveTimelineControl.CalculateMultiCameraPos(
                                                        out cameraPos, _editingSheet._data, currentFrame, i);
                                                },
                                                (out Vector3 cameraLookAt, out float cameraLocalScale, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                                                {
                                                    liveTimelineControl.CalculateMultiCameraLookAt(
                                                        out cameraLookAt, out cameraLocalScale, _editingSheet._data, currentFrame, i);
                                                });
                }
            }
        }

        /// <summary>
        /// 瞳の映り込みカメラ用のkey座標補間ライン描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_EyeCameraPosInterpolateLine(LiveTimelineControl timelineControl)
        {
            for (int index = 0; index < _editingSheet._data.EyeCameraPosList.Count; ++index)
            {
                OnSceneGUI_CameraPosInterpolateLine(_liveTimelineControl, _editingSheet._data.EyeCameraPosList[index].keys);
            }
        }

        /// <summary>
        /// 瞳の映り込みカメラ用LookAtのデバッグ描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_EyeCameraLookAtLine(LiveTimelineControl timelineControl)
        {
            for (int index = 0; index < _editingSheet._data.EyeCameraLookAtList.Count; ++index)
            {
                OnSceneGUI_CameraLookAtLine(timelineControl, _editingSheet._data.EyeCameraLookAtList[index].keys,
                    (out Vector3 cameraPos, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                    {
                        liveTimelineControl.CalculateEyeCameraPos(out cameraPos, _editingSheet._data, currentFrame, index);
                    },

                    (out Vector3 cameraLookAt, out float cameraLocalScale, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                    {
                        liveTimelineControl.CalculateEyeCameraLookAt(out cameraLookAt, out cameraLocalScale, _editingSheet._data, currentFrame, index);
                    }
                );
            }
        }

        /// <summary>
        /// グローバルライトの向きを描画する
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_DrawGlobalLightDir(LiveTimelineControl timelineControl)
        {
            if (timelineControl == null)
            {
                return;
            }
            if (timelineControl.data == null)
            {
                return;
            }

            int lightCount = _editingSheet._data.globalLightDataLists.Count;
            var backupColor = Gizmos.color;
            Handles.color = Color.red;
            Vector3 lightDir;
            Vector3 sliderPos = new Vector3(0.0f, 2.0f, 2.0f);
            if (lightCount == 0)
            {
                //共通設定を使用する
                if (Director.Instance != null)
                {
                    var director = Director.Instance;
                    if (director.GlobalLightTransform != null)
                    {
                        lightDir = director.GlobalLightTransform.rotation.eulerAngles.normalized;
                        lightDir = lightDir * -1.0f;
                        Handles.Slider(sliderPos, lightDir);
                    }
                }
            }
            else
            {
                for (int i = 0; i < lightCount; i++)
                {
                    var lightSheetKeys = _editingSheet._data.globalLightDataLists[i].keys;

                    LiveTimelineKey curKey = null;
                    LiveTimelineKey nextKey = null;
                    LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, lightSheetKeys, _currentFrame);
                    var curData = curKey as LiveTimelineKeyGlobalLightData;
                    var nextData = nextKey as LiveTimelineKeyGlobalLightData;
                    if (curData == null)
                    {
                        return;
                    }

                    Quaternion lightDirRotation = Quaternion.identity;
                    if (curData.cameraFollow)
                    {
                        var camera = timelineControl.GetCamera(_editingSheet._data.targetCameraIndex);
                        if (camera != null)
                        {
                            lightDirRotation = camera.cacheTransform.rotation;
                        }
                    }

                    if ((nextData != null) && nextData.IsInterpolateKey())
                    {
                        //補間処理
                        float t = LiveTimelineControl.CalculateInterpolationValue(curData, nextData, _currentFrame);
                        var curRotation = Quaternion.Euler(curData.lightDir);
                        var nextRotation = Quaternion.Euler(nextData.lightDir);
                        var rotation = Quaternion.Lerp(curRotation, nextRotation, t);
                        lightDir = (rotation * lightDirRotation) * Vector3.forward;
                    }
                    else
                    {
                        //CurrentKeyの値に設定
                        Quaternion rotation = Quaternion.Euler(curData.lightDir);
                        lightDir = (rotation * lightDirRotation) * Vector3.forward;
                    }
                    lightDir = lightDir * -1.0f;
                    Handles.Slider(sliderPos, lightDir.normalized);
                }
            }
            Handles.color = backupColor;
        }

        /// <summary>
        /// FormationOffsetKey座標補間ラインの描画
        /// </summary>
        /// <param name="timelineControl"></param>
        /// <param name="keys"></param>
        private void OnSceneGUI_FormationOffsetPosInterpolateLine(LiveTimelineControl timelineControl, ILiveTimelineKeyDataList keys)
        {
            //選択されているキャラクター位置を求める必要がある
            int formationIndex = -1;
            var keysArray = _editingSheet._data.formationOffsetSet.GetKeyListArray();
            for (int i = 0; i < keysArray.Length; i++)
            {
                if (keysArray[i] == keys)
                {
                    formationIndex = i;
                    break;
                }
            }
            if (formationIndex == -1)
                return;

            LiveTimelineKey curKey = null;
            LiveTimelineKey nextKey = null;
            if (keys != null)
            {
                LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, keys, _currentFrame);
            }
            var curData = curKey as LiveTimelineKeyFormationOffsetData;
            var nextData = nextKey as LiveTimelineKeyFormationOffsetData;
            if (curData == null || nextData == null)
                return;

            var initialPosition = timelineControl.liveCharactorLocators[formationIndex].liveCharaInitialPosition;
            Vector3 curPos, nextPos;
            curPos.x = curData.Position.x;
            curPos.y = curData.Position.y;
            curPos.z = curData.Position.z;

            nextPos.x = nextData.Position.x;
            nextPos.y = nextData.Position.y;
            nextPos.z = nextData.Position.z;

            Handles.color = Color.yellow;
            Handles.DrawLine(curPos + initialPosition, nextPos + initialPosition);

            if (curData.IsWorldSpace)
            {
                Handles.color = Color.cyan;
                Handles.DrawLine(curData.WorldSpaceOrigin, nextData.WorldSpaceOrigin);
            }
        }

        /// <summary>
        /// Key座標補間ラインの描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_CameraPosInterpolateLine(LiveTimelineControl timelineControl)
        {
            OnSceneGUI_CameraPosInterpolateLine(_liveTimelineControl, _editingSheet._data.cameraPosKeys);
        }

        /// <summary>
        /// Key座標補間ラインの描画
        /// </summary>
        /// <param name="timelineControl"></param>
        /// <param name="keys"></param>
        private void OnSceneGUI_CameraPosInterpolateLine(LiveTimelineControl timelineControl, ILiveTimelineKeyDataList keys)
        {
            if (timelineControl == null)
            {
                return;
            }
            if (timelineControl.data == null)
            {
                return;
            }
            LiveTimelineKey curKey = null;
            LiveTimelineKey nextKey = null;
            LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, keys, _currentFrame);
            var curData = curKey as LiveTimelineKeyCameraPositionData;
            var nextData = nextKey as LiveTimelineKeyCameraPositionData;
            if (curData == null || nextData == null)
            {
                return;
            }

            var drawDotPath = !nextData.IsInterpolateKey();//補間の時だけ実線
            var dotWidth = 2;
            var curPos = curData.GetValue(timelineControl);
            var nextPos = nextData.GetValue(timelineControl);

            if (nextData.HasBezier())
            {
                const int kCurveStep = 60;
                const float tStep = 1f / (float)kCurveStep;
                var quadratic = nextData.GetBezierPointCount() == 1;
                int bezierCount = nextData.GetBezierPointCount();

                if (nextData.NecessaryToUseNewBezierCalcMethod)
                {
                    var bezierCalcWork = BezierCalcWork.CameraPosLine;
                    bezierCalcWork.Set(curPos, nextPos, bezierCount);
                    bezierCalcWork.UpdatePoints(nextData, timelineControl);
                    bezierCalcWork.DrawLine(bezierCount, Color.yellow, kCurveStep);
                    bezierCalcWork.DrawDottedLine(bezierCount, Color.green, dotWidth);
                    Handles.color = Color.white;
                    Handles.DrawLine(curPos, nextPos);
                }
                else
                {
                    var cp1 = nextData.GetBezierPoint(0, timelineControl);
                    var cp2 = quadratic ? Math.VECTOR3_ZERO : nextData.GetBezierPoint(1, timelineControl);
                    var cp3 = quadratic ? Math.VECTOR3_ZERO : nextData.GetBezierPoint(2, timelineControl);
                    var cp4 = quadratic ? Math.VECTOR3_ZERO : nextData.GetBezierPoint(3, timelineControl);

                    Handles.color = Color.yellow;
                    float t = tStep;
                    Vector3 prevPos = curPos;
                    for (int i = 0; i < kCurveStep; i++)
                    {
                        Vector3 position = Math.VECTOR3_ZERO;
                        switch (bezierCount)
                        {
                            case 1:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, t, out position);
                                break;
                            case 2:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, ref cp2, t, out position);
                                break;
                            case 3:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, ref cp2, ref cp3, t, out position);
                                break;
                            case 4:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, ref cp2, ref cp3, ref cp4, t, out position);
                                break;
                        }
                        Handles.DrawLine(prevPos, position);
                        prevPos = position;
                        t += tStep;
                    }

                    Handles.color = Color.white;
                    Handles.DrawLine(curPos, nextPos);

                    // 緑線の描画
                    Handles.color = Color.green;
                    Handles.DrawDottedLine(curPos, cp1, dotWidth);
                    switch (bezierCount)
                    {
                        case 1:
                            Handles.DrawDottedLine(cp1, nextPos, dotWidth);
                            break;
                        case 2:
                            Handles.DrawDottedLine(cp1, cp2, dotWidth);
                            Handles.DrawDottedLine(cp2, nextPos, dotWidth);
                            break;
                        case 3:
                            Handles.DrawDottedLine(cp1, cp2, dotWidth);
                            Handles.DrawDottedLine(cp2, cp3, dotWidth);
                            Handles.DrawDottedLine(cp3, nextPos, dotWidth);
                            break;
                        case 4:
                            Handles.DrawDottedLine(cp1, cp2, dotWidth);
                            Handles.DrawDottedLine(cp2, cp3, dotWidth);
                            Handles.DrawDottedLine(cp3, cp4, dotWidth);
                            Handles.DrawDottedLine(cp4, nextPos, dotWidth);
                            break;
                    }
                }
            }
            else
            {
                Handles.color = Color.yellow;
                if (drawDotPath)
                {
                    Handles.DrawDottedLine(curPos, nextPos, dotWidth);
                }
                else
                {
                    Handles.DrawLine(curPos, nextPos);
                }
            }
        }

        /// <summary>
        /// カメラ位置計算のコールバック
        /// </summary>
        /// <param name="cameraPos"></param>
        /// <param name="currentFrame"></param>
        public delegate void OnCalculateCameraPos(out Vector3 cameraPos, LiveTimelineControl timelineControl, int currentFrame);

        /// <summary>
        /// カメラ注視点のコールバック
        /// </summary>
        /// <param name="cameraLookAt"></param>
        /// <param name="currentFrame"></param>
        public delegate void OnCalculateCameraLookAt(out Vector3 cameraLookAt, out float cameraLocalScale, LiveTimelineControl timelineControl, int currentFrame);

        /// <summary>
        /// LookAtのデバッグ描画
        /// </summary>
        /// <param name="timelineControl"></param>
        private void OnSceneGUI_CameraLookAtLine(LiveTimelineControl timelineControl)
        {
            OnSceneGUI_CameraLookAtLine(timelineControl, _editingSheet._data.cameraLookAtKeys,
                (out Vector3 cameraPos, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                {
                    liveTimelineControl.CalculateCameraPos(out cameraPos, _editingSheet._data, currentFrame);
                },
                (out Vector3 cameraLookAt, out float cameraLocalScale, LiveTimelineControl liveTimelineControl, int currentFrame) =>
                {
                    liveTimelineControl.CalculateCameraLookAt(out cameraLookAt, out cameraLocalScale, _editingSheet._data, currentFrame);
                }
            );
        }

        /// <summary>
        /// LookAtのデバッグ描画
        /// </summary>
        /// <param name="timelineControl"></param>
        /// <param name="keys"></param>
        /// <param name="calcCameraPos"></param>
        /// <param name="calcCameraLookAt"></param>
        private void OnSceneGUI_CameraLookAtLine(LiveTimelineControl timelineControl, ILiveTimelineKeyDataList keys, OnCalculateCameraPos calcCameraPos, OnCalculateCameraLookAt calcCameraLookAt)
        {
            if (timelineControl == null)
            {
                return;
            }
            if (timelineControl.data == null)
            {
                return;
            }

            var cameraPos = Math.VECTOR3_ZERO;
            calcCameraPos(out cameraPos, timelineControl, _currentFrame);

            var cameraLookAt = Math.VECTOR3_ZERO;
            var cameraLocalScale = 1f;
            calcCameraLookAt(out cameraLookAt, out cameraLocalScale, timelineControl, _currentFrame);

            Handles.color = Color.cyan;
            Handles.DrawLine(cameraPos, cameraLookAt);

            LiveTimelineKey curKey = null;
            LiveTimelineKey nextKey = null;
            LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, keys, _currentFrame);
            var curData = curKey as LiveTimelineKeyCameraLookAtData;
            var nextData = nextKey as LiveTimelineKeyCameraLookAtData;
            if (curData == null || nextData == null)
            {
                return;
            }

            var dotWidth = 2;
            if (nextData.HasBezier())
            {
                const int kCurveStep = 60;
                const float tStep = 1f / (float)kCurveStep;
                var quadratic = nextData.GetBezierPointCount() == 1;

                var curPos = curData.GetValue(timelineControl);
                var nextPos = nextData.GetValue(timelineControl);
                int bezierCount = nextData.GetBezierPointCount();

                if (nextData.NecessaryToUseNewBezierCalcMethod)
                {
                    var bezierCalcWork = BezierCalcWork.CameraLookAtLine;
                    bezierCalcWork.Set(curPos, nextPos, bezierCount);
                    bezierCalcWork.UpdatePoints(nextData, timelineControl);
                    bezierCalcWork.DrawLine(bezierCount, Color.yellow, kCurveStep);
                    bezierCalcWork.DrawDottedLine(bezierCount, Color.green, dotWidth);
                    Handles.color = Color.white;
                    Handles.DrawLine(curPos, nextPos);
                }
                else
                {
                    var cp1 = nextData.GetBezierPoint(0, timelineControl);
                    var cp2 = quadratic ? Math.VECTOR3_ZERO : nextData.GetBezierPoint(1, timelineControl);
                    var cp3 = quadratic ? Math.VECTOR3_ZERO : nextData.GetBezierPoint(2, timelineControl);
                    var cp4 = quadratic ? Math.VECTOR3_ZERO : nextData.GetBezierPoint(3, timelineControl);

                    Handles.color = Color.yellow;
                    float t = tStep;
                    Vector3 prevPos = curPos;
                    for (int i = 0; i < kCurveStep; i++)
                    {
                        Vector3 position = Math.VECTOR3_ZERO;
                        switch (bezierCount)
                        {
                            case 1:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, t, out position);
                                break;
                            case 2:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, ref cp2, t, out position);
                                break;
                            case 3:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, ref cp2, ref cp3, t, out position);
                                break;
                            case 4:
                                BezierUtil.Calc(ref curPos, ref nextPos, ref cp1, ref cp2, ref cp3, ref cp4, t, out position);
                                break;
                        }
                        Handles.DrawLine(prevPos, position);
                        prevPos = position;
                        t += tStep;
                    }

                    Handles.color = Color.white;
                    Handles.DrawLine(curPos, nextPos);

                    // 緑線の描画
                    Handles.color = Color.green;
                    Handles.DrawDottedLine(curPos, cp1, dotWidth);
                    switch (bezierCount)
                    {
                        case 1:
                            Handles.DrawDottedLine(cp1, nextPos, dotWidth);
                            break;
                        case 2:
                            Handles.DrawDottedLine(cp1, cp2, dotWidth);
                            Handles.DrawDottedLine(cp2, nextPos, dotWidth);
                            break;
                        case 3:
                            Handles.DrawDottedLine(cp1, cp2, dotWidth);
                            Handles.DrawDottedLine(cp2, cp3, dotWidth);
                            Handles.DrawDottedLine(cp3, nextPos, dotWidth);
                            break;
                        case 4:
                            Handles.DrawDottedLine(cp1, cp2, dotWidth);
                            Handles.DrawDottedLine(cp2, cp3, dotWidth);
                            Handles.DrawDottedLine(cp3, cp4, dotWidth);
                            Handles.DrawDottedLine(cp4, nextPos, dotWidth);
                            break;
                    }
                }
            }
        }

        private void OnSceneGUI_SelectedTransform(LiveTimelineControl timelineControl)
        {
            LiveTimelineKey curKey = null;
            LiveTimelineKey nextKey = null;
            LiveTimelineControl.FindTimelineKey(out curKey, out nextKey, _editingSheet._editingLine._editKeyList, _currentFrame);
            var curData = curKey as LiveTimelineKeyTransformData;
            var nextData = nextKey as LiveTimelineKeyTransformData;
            if (curData == null)
            {
                return;
            }
            Vector3 transPos = Math.VECTOR3_ZERO;
            if ((nextData != null) && nextData.IsInterpolateKey())
            {
                //補間処理
                float t = LiveTimelineControl.CalculateInterpolationValue(curData, nextData, _currentFrame);
                transPos = curData.position + (nextData.position - curData.position) * t;
            }
            else
            {
                //CurrentKeyの値に設定
                transPos = curData.position;
            }
            Handles.color = Color.red;
            Handles.DrawLine(curData.position, transPos);
            Handles.Label(transPos, _editingSheet._editingLine.name);
        }
        //--------------------------------------------------------------------------------
        #endregion CutTools Window（SceneViewで表示されるウィンドウ）
        //--------------------------------------------------------------------------------
        private void CreateFolder(string folder)
        {
            //あったら何もしない
            if (AssetDatabase.IsValidFolder(folder))
                return;

            //親もあるのか見る
            var parentFolder = System.IO.Directory.GetParent(folder).ToString();
            CreateFolder(parentFolder);
            AssetDatabase.CreateFolder(parentFolder,System.IO.Path.GetFileName(folder));
        }

        private void CopyToRecordingFolder(GameObject srcPrefab)
        {
            var fileName = srcPrefab.name;
            var folder = ResourcePath.LIVERECORDING_CUTT_FOLDER + $"/{fileName}";
            CreateFolder(folder);

            //出力するファイル名の取得
            var srcFolder = System.IO.Path.GetDirectoryName(AssetDatabase.GetAssetPath(srcPrefab));
            {
                {
                    var guidArray = AssetDatabase.FindAssets("t:Prefab t:LiveTimelineData", new string[] { srcFolder });
                    foreach (var guid in guidArray)
                    {
                        var srcFileName = AssetDatabase.GUIDToAssetPath(guid);
                        var dstFileName = System.IO.Path.GetFileName(srcFileName);
                        AssetDatabase.CopyAsset(srcFileName, folder + $"/{dstFileName}");
                    }
                }
                //ワークシートはバックアップが作られるので必要なものだけをコピーする
                {
                    var guidArray = AssetDatabase.FindAssets("t:LiveTimelineData", new string[] { srcFolder });
                    foreach (var guid in guidArray)
                    {
                        var srcTimelineDataPath = AssetDatabase.GUIDToAssetPath(guid);
                        var srcTimelineData = AssetDatabase.LoadAssetAtPath<LiveTimelineData>(srcTimelineDataPath);

                        var workSheetList = srcTimelineData.GetWorkSheetList();
                        for(int i=0;i<workSheetList.Count;i++)
                        {
                            var sheet = workSheetList[i];
                            var srcFileName = AssetDatabase.GetAssetPath(sheet);
                            var dstFileName = System.IO.Path.GetFileName(srcFileName);
                            AssetDatabase.CopyAsset(srcFileName, folder + $"/{dstFileName}");
                        }
                    }
                }
                AssetDatabase.Refresh();
            }

            //参照を解決する
            {
                var prefabPath = ResourcePath.GetLiveRecordingCuttPrefabPath(fileName);
                var timelinePrefab = PrefabUtility.LoadPrefabContents(prefabPath);
                var timelineController = timelinePrefab.GetComponent<LiveTimelineControl>();
                var dataName = timelineController.data.name;
                timelineController.data = AssetDatabase.LoadAssetAtPath<LiveTimelineData>(folder + "/" + dataName + ".asset");
                timelineController.data.SwapWorkSeet(folder);
                EditorUtility.SetDirty(timelineController.data);
                EditorUtility.SetDirty(timelineController);
                PrefabUtility.SaveAsPrefabAsset(timelinePrefab, prefabPath);
                PrefabUtility.UnloadPrefabContents(timelinePrefab);
            }
            AssetDatabase.Refresh();
        }

        /// <summary>
        /// [Menu項目]
        /// メモリ上から使われてないアセットをUnloadする
        /// ToolがCrashするなどして編集状態のCuttデータがメモリに残り続ける問題への対策
        /// </summary>
        [MenuItem("Cutt/Clear memory")]
        public static void Menu_ClearMemory()
        {
            EditorUtility.UnloadUnusedAssetsImmediate();
        }
        [MenuItem("Cutt/Test/SaveAssets")]
        public static void Menu_SaveAssets()
        {
            AssetDatabase.SaveAssets();
        }
        [MenuItem("Cutt/Test/CheckPersistent")]
        public static void Menu_CheckPersistent()
        {
            var tool = CutTool.instance;
            if (tool == null || tool.liveTimelineControl == null || tool.liveTimelineData == null)
            {
                return;
            }
            Debug.Log("liveTimelineData: " + EditorUtility.IsPersistent(tool.liveTimelineData));
            Debug.Log("curvePreset: " + EditorUtility.IsPersistent(tool.curvePreset));
        }

        static void TestSetDirty()
        {
            var tool = CutTool.instance;
            EditorUtility.SetDirty(tool.liveTimelineData);
            EditorUtility.SetDirty(tool.curvePreset);
            var sheets = tool.liveTimelineData.GetWorkSheetList();
            foreach (var sheet in sheets)
            {
                EditorUtility.SetDirty(sheet);
            }
        }
        [MenuItem("Cutt/Test/SetDirty")]
        public static void Menu_SetDirty()
        {
            var tool = CutTool.instance;
            if (tool == null || tool.liveTimelineControl == null || tool.liveTimelineData == null)
            {
                return;
            }
            TestSetDirty();
            Debug.Log("DONE");
        }
        [MenuItem("Cutt/Test/SetDirty And Refresh")]
        public static void Menu_SetDirtyAndRefresh()
        {
            var tool = CutTool.instance;
            if (tool == null || tool.liveTimelineControl == null || tool.liveTimelineData == null)
            {
                return;
            }
            TestSetDirty();
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
            Debug.Log("DONE");
        }
        [MenuItem("Cutt/Test/SetDirty And SaveAssets")]
        public static void Menu_SetDirtyAndSaveAssets()
        {
            var tool = CutTool.instance;
            if (tool == null || tool.liveTimelineControl == null || tool.liveTimelineData == null)
            {
                return;
            }
            TestSetDirty();
            AssetDatabase.SaveAssets();
            Debug.Log("DONE");
        }
    }
}//namespace Gallop.Live.Cutt
#endif//UNITY_EDITOR
