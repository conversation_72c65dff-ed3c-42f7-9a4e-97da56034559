using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Gallop.Live;
using Gallop.Cyalume;
using MobCyalumeUpdateInfo = Gallop.Live.Cutt.MobCyalumeUpdateInfo;
using UnityEngine;

namespace Gallop
{
    namespace CutIn
    {
        using Cutt;

        /// <summary>
        /// カットイン用背景モデル
        /// </summary>
        public class CutInBgModel : MonoBehaviour, IMirrorReflectionController
        {
            // サイリウムのオフセットポジション.
            [SerializeField, Tooltip("サイリウムの位置のオフセット値")]
            private Vector3 _cyalumeOffsetPosition = Math.VECTOR3_ZERO;

            [SerializeField, Tooltip("観客オブジェクト")]
            private GameObject[] _audienceObjectArray = null;
            private AudienceController[] _audienceControllerArray;
            private Vector4[] _audienceWorldPositionArray;
#if UNITY_EDITOR
            private string[] _audienceObjectNameArray = null;
#endif

            // 観客モデルアニメーション
            [SerializeField]
            public AnimationClip[] AudienceAnimationArray = null;

            private CutInTimelineController _timelineController;
            private CutInCyalumeController _cyalumeController;
            private bool _isCyalumeController;    //nullの場合があるのでチェック用
            private MobShadowController _mobController = null;
            private bool _isMobConroller;

            private MaterialPropertyBlock _materialPropertyBlock;
            public MaterialPropertyBlock MaterialPropertyBlock => _materialPropertyBlock;
            private MeshRenderer[] _meshRenderers;
            private AnimatorOverrideController _overrideController;

            public Animator Animator { get; private set; }
            public MirrorReflection[] MirrorReflectionArray { get; private set; } = new MirrorReflection[0];
            public Vector3 DefaultPosition { get; private set; }
            public Vector3 OffsetPosition { get; private set; } = Math.VECTOR3_ZERO;
            public Quaternion DefaultRotation { get; private set; }
            public Color DefaultMulColor { get; set; } = GameDefine.COLOR_WHITE;

            /// <summary>
            /// Cleanup時にCutInTimelineControllerからDestroyしてもらうか
            /// </summary>
            public bool IsRequireDestroyOnCleanup = true;
            /// <summary>
            /// Position,Rotation,Scaleを設定するか。
            /// </summary>
            public bool IsSetTransform = true;

            private void OnDestroy()
            {
                Animator = null;
                _meshRenderers = null;
                _overrideController = null;

                if (_timelineController != null)
                {
                    _timelineController.OnUpdateChoreographyCyalume -= UpdateChoreographyCyalume;
                    _timelineController.OnUpdateAudience -= UpdateAudience;
                    _timelineController.OnUpdateCyalumeControl -= UpdateCyalumeControl;
                    _timelineController.OnUpdateMobControl -= UpdateMobControl;
                    _timelineController.OnPostUpdateAllTimeline -= PostUpdateAllTimeline;
                }
            }

            public void AlterLateUpdate()
            {
                for( int i = 0; i < MirrorReflectionArray.Length; i++ )
                {
                    MirrorReflectionArray[i].AlterLateUpdate();
                }
            }

            public void Setup(Camera camera)
            {
                DefaultPosition = transform.position;
                DefaultRotation = transform.rotation;
                _materialPropertyBlock = new MaterialPropertyBlock();
                _meshRenderers = GetComponentsInChildren<MeshRenderer>(true);
                Animator = GetComponent<Animator>();
                if (Animator == null)
                {
                    Animator = gameObject.AddComponent<Animator>();
                }
                if (Animator.runtimeAnimatorController == null)
                {
                    Animator.runtimeAnimatorController =
                        ResourceManager.LoadOnScene<RuntimeAnimatorController>(ResourcePath.CutInAnimatorPath);
                }

                MirrorReflectionArray = GetComponentsInChildren<MirrorReflection>();
                for (var i = 0; i < MirrorReflectionArray.Length; i++)
                {
                    // ミラーの更新は自前でやらないとタイムラインの更新タイミングとズレるので、自前でLateUpdateして、PreRender前にマトリクス計算させる
                    MirrorReflectionArray[i].Initialize(camera, false, false);
                    MirrorReflectionArray[i].SetActive(true);
                }
                SetMulColor(DefaultMulColor);
            }

            /// <summary>
            /// サイリウム関連のオブジェクト初期化
            /// </summary>
            /// <param name="timelineController"></param>
            public void SetupStageObjects(CutInTimelineController timelineController)
            {
                _timelineController = timelineController;

                // モブ影
                _mobController = gameObject.AddComponent<MobShadowController>();
                _isMobConroller = (_mobController != null);

                // 3Dサイリウムがあった場合はオフセットポジションを渡しておく.
                _cyalumeController = gameObject.GetComponentInChildren<CutInCyalumeController>();
                _isCyalumeController = (_cyalumeController != null);
                if (_isCyalumeController)
                {
                    _cyalumeController.Initialize(_cyalumeOffsetPosition, _mobController,
                        (TimelineKeyChoreographyCyalumeDataList) timelineController.GetActiveWorkSheet().CheckAlive()
                            ?.ChoreographyCyalumeList.ElementAtOrDefault(0)?.Keys);
                }

                SetupAudienceObject();

                _timelineController.OnUpdateChoreographyCyalume += UpdateChoreographyCyalume;
                _timelineController.OnUpdateAudience += UpdateAudience;
                _timelineController.OnUpdateCyalumeControl += UpdateCyalumeControl;
                _timelineController.OnUpdateMobControl += UpdateMobControl;
                _timelineController.OnPostUpdateAllTimeline += PostUpdateAllTimeline;
            }

            /// <summary>
            /// 観客リソースのセットアップ
            /// </summary>
            private void SetupAudienceObject()
            {
                AudienceController.CyalumeController3D = _cyalumeController;

                _audienceControllerArray = null;

                if ((_audienceObjectArray == null) || (_audienceObjectArray.Length <= 0))
                {
                    return;
                }

                // 観客のタイムラインの本数分、AudienceControllerを作成する。
                var parentTransform = this.transform;
                var audienceControllerList = new List<AudienceController>();
                var sheetList = _timelineController.Data.GetWorkSheetList();
                var sheetCount = sheetList.Count;
                for (int i = 0; i < sheetCount; i++)
                {
                    var sheet = sheetList[i];
                    var timelineCount = sheet.AudienceList.Count;
                    for (int j = 0; j < timelineCount; j++)
                    {
                        var audienceController = new AudienceController();

                        var audienceData = sheet.AudienceList[j];
                        if ((audienceData.ObjectIndex >= 0) && (audienceData.ObjectIndex < _audienceObjectArray.Length))
                        {
                            audienceController.Initialize(
                                _audienceObjectArray[audienceData.ObjectIndex],
                                parentTransform,
                                AudienceAnimationArray?.Length > 0 ? AudienceAnimationArray : null,
                                isSetLayer: false);
                            audienceController.GameObject.SetLayerRecursively(
                                GraphicSettings.GetLayer(GraphicSettings.LayerIndex.LayerBG));
                        }
#if CYG_DEBUG
                        else
                        {
                            Debug.LogWarningFormat("Invalid ObjectIndex {0} (max:{1})", audienceData.ObjectIndex,
                                _audienceObjectArray.Length - 1);
                        }
#endif

                        audienceControllerList.Add(audienceController);
                    }
                }

                _audienceControllerArray = audienceControllerList.ToArray();

                _audienceWorldPositionArray = new Vector4[_audienceControllerArray.Length];

#if UNITY_EDITOR
                // GCAllocを避けるため_audienceObjects配列のオブジェクト名を取得しておく。
                _audienceObjectNameArray = new string[_audienceObjectArray.Length];
                for (int i = 0; i < _audienceObjectArray.Length; i++)
                {
                    _audienceObjectNameArray[i] = _audienceObjectArray[i].name;
                }
#endif
            }

            /// <summary>
            /// 全タイムライン更新直後に実行される処理
            /// </summary>
            private void PostUpdateAllTimeline(float currentTime)
            {
                // サイリウムのマテリアルに観客モデルのワールド座標配列を設定する。
                if (_isCyalumeController)
                {
                    _cyalumeController.AlterUpdate(currentTime);
                    _cyalumeController.SetAudienceWorldPositionArray(_audienceWorldPositionArray);
                    _cyalumeController.FlushGroupMatrix();
                }

                if (_isMobConroller)
                {
                    _mobController.FlushGroupMatrix();
                }
            }

            #region ChoreographyCyalume

            private void UpdateChoreographyCyalume(int keyIndex)
            {
            }

            #endregion ChoreographyCyalume

            #region Audience

            /// <summary>
            /// 観客の更新
            /// </summary>
            /// <param name="updateInfo"></param>
            private void UpdateAudience(ref AudienceUpdateInfo updateInfo)
            {
                if ((_audienceControllerArray == null) || (_audienceControllerArray.Length <= 0))
                {
                    return;
                }

                var index = updateInfo.timelineIndex;

                if ((index >= 0) && (index < _audienceControllerArray.Length))
                {
                    _audienceWorldPositionArray[index] = Math.VECTOR4_ZERO;

                    var audienceController = _audienceControllerArray[index];
                    if (audienceController != null)
                    {
                        audienceController.UpdateInfo(ref updateInfo);

                        _audienceWorldPositionArray[index] = audienceController.GetWorldPosition();
                    }
                }
            }

#if UNITY_EDITOR
            public string GetAudienceObjectName(int index)
            {
                const string UNKNOWN_OBJECT_NAME = "Unknown Object";
                if ((_audienceObjectNameArray == null) || (_audienceObjectNameArray.Length <= 0) || (index < 0) ||
                    (index >= _audienceObjectNameArray.Length))
                {
                    return UNKNOWN_OBJECT_NAME;
                }

                return _audienceObjectNameArray[index];
            }
#endif

            #endregion // Audience

            #region MobControl

            private void UpdateMobControl(ref MobCyalumeUpdateInfo info)
            {
                if (_isMobConroller)
                {
                    _mobController.SetGroupTRS(info.GroupIndex, ref info.Position, ref info.Rotation, ref info.Scale);
                }
            }

            #endregion

            #region CyalumeControl

            private void UpdateCyalumeControl(ref MobCyalumeUpdateInfo info)
            {
                if (_isCyalumeController)
                {
                    _cyalumeController.SetGroupTRS(info.GroupIndex, ref info.Position, ref info.Rotation,
                        ref info.Scale);
                }
            }

            #endregion

            /// <summary>
            /// モデルのUVスクロールを達成するために、現在の時間をマテリアルに流す
            /// </summary>
            /// <param name="time"></param>
            public void SetTime( float time )
            {
                if (_materialPropertyBlock == null)
                {
                    _materialPropertyBlock = new MaterialPropertyBlock();
                }

                _materialPropertyBlock.SetFloat(ShaderManager.GetPropertyId( ShaderManager.PropertyId._AppTime ), time);

                ApplyPropertyBlock();
            }

            /// <summary>
            /// Clipを指定する
            /// </summary>
            /// <param name="clip"></param>
            public void SetClip(AnimationClip clip)
            {
                if (_overrideController == null)
                {
                    _overrideController = new AnimatorOverrideController();
#if UNITY_EDITOR || CYG_DEBUG
                    _overrideController.name = "CutInBgModel.SetClip";
#endif
                    _overrideController.runtimeAnimatorController = Animator.runtimeAnimatorController;
                    Animator.runtimeAnimatorController = _overrideController;
                }
                _overrideController[Cutt.CutInTimelineController.OVERRIDE_TARGET_CLIP_NAME] = clip;
            }

            /// <summary>
            /// 現在指定しているClipを取得する
            /// </summary>
            /// <returns></returns>
            public AnimationClip GetClip()
            {
                if (_overrideController == null)
                {
                    _overrideController = new AnimatorOverrideController();
#if UNITY_EDITOR || CYG_DEBUG
                    _overrideController.name = "CutInBgModel.GetClip";
#endif
                }
                return _overrideController[Cutt.CutInTimelineController.OVERRIDE_TARGET_CLIP_NAME];
            }

            public float MotionHeadTime { get; set; } = 0f;
            public float PlaySpeed { get; set; } = 1f;

            /// <summary>
            /// 乗算カラーを設定
            /// </summary>
            /// <param name="color"></param>
            public void SetMulColor(Color color)
            {
                _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MulColor0), color);
                _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._Color), color);
                _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._AmbientColor), color);
                ApplyPropertyBlock();
            }

            /// <summary>
            /// 加算カラーを設定
            /// </summary>
            /// <param name="color"></param>
            public void SetAddColor(Color color)
            {
                _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._AddColor), color);
                ApplyPropertyBlock();
            }

            /// <summary>
            /// MirrorReflection.MirrorReflectionRateを設定
            /// </summary>
            public void SetMirrorClipPlaneOffset(float mirrorClipPlaneOffset)
            {
                for (var i = 0; i < MirrorReflectionArray.Length; i++)
                {
                    MirrorReflectionArray[i].MirrorClipPlaneOffset = mirrorClipPlaneOffset;
                }
            }

            /// <summary>
            /// MirrorReflection.MirrorReflectionRateを設定
            /// </summary>
            public void SetMirrorReflectionRate(float mirrorReflectionRate)
            {
                for (var i = 0; i < MirrorReflectionArray.Length; i++)
                {
                    MirrorReflectionArray[i].MirrorReflectionRate = mirrorReflectionRate;
                }
            }

            /// <summary>
            /// ReplacementShaderを使うかを設定
            /// </summary>
            /// <param name="isEnable">ReplacementShaderを使うか</param>
            public void SetLightMirrorShader(bool isEnable)
            {
                for (var i = 0; i < MirrorReflectionArray.Length; i++)
                {
                    MirrorReflectionArray[i].SetLightMirrorShader(isEnable);
                }
            }

            /// <summary>
            /// ProjectionMatrixの更新をPreRenderで行うフラグを設定
            /// </summary>
            /// <param name="isUpdatePreRender">PreRenderで更新するならtrue</param>
            public void SetUpdateProjectionMatrixPreRender(bool isUpdatePreRender)
            {
                for (var i = 0; i < MirrorReflectionArray.Length; i++)
                {
                    MirrorReflectionArray[i].SetUpdateProjectionMatrixPreRender(isUpdatePreRender);
                }
            }

            /// <summary>
            /// MirrorReflectionの更新をMonoBehaviourで行うフラグを設定
            /// </summary>
            /// <param name="enabled">MonoBehaviourで更新するならtrue</param>
            public void SetEnabledMonoBehaviourUpdate(bool enabled)
            {
                for (var i = 0; i < MirrorReflectionArray.Length; i++)
                {
                    MirrorReflectionArray[i].SetEnabledMonoBehaviourUpdate(enabled);
                }
            }

            /// <summary>
            /// 現在再生中のクリップの時間を取得する
            /// </summary>
            /// <returns></returns>
            private float GetClipLength()
            {
                if (Animator == null)
                {
                    return 0f;
                }
                if (Animator.runtimeAnimatorController == null)
                {
                    return 0f;
                }

                if (!Animator.isInitialized)
                {
                    return 0f;
                }

                Animator.GetCurrentAnimatorClipInfo(0, GallopUtil.animatorClipInfoList);
                if (GallopUtil.animatorClipInfoList.Count <= 0)
                {
                    return 0f;
                }
                var clip = GallopUtil.animatorClipInfoList[0].clip;
                GallopUtil.animatorClipInfoList.Clear();
                if (clip == null)
                {
                    return 0f;
                }
                return clip.length;
            }

            public void CrossFade(int frame, int targetFps, float currentTime)
            {
                var startTime = frame / (float)targetFps;
                var setTime = currentTime - startTime - MotionHeadTime;
                var clipLength = GetClipLength();
                if (Math.IsFloatEqual(clipLength, 0f)) return;
                var normalizeTime = (setTime * PlaySpeed) / GetClipLength();
                CrossFade(normalizeTime);
            }

            /// <summary>
            /// 特定のフレーム（normalizeTime）の位置にアニメーションを進める
            /// </summary>
            /// <param name="normalizeTime"></param>
            public void CrossFade(float normalizeTime)
            {
                if (Animator == null)
                {
                    return;
                }

                if (Animator.runtimeAnimatorController == null)
                {
                    return;
                }

                var stateInfo = Animator.GetCurrentAnimatorStateInfo(0);
                if (stateInfo.shortNameHash == 0)
                {
                    return;
                }
                Animator.CrossFade(stateInfo.shortNameHash, 0f, 0, normalizeTime);
            }

            /// <summary>
            /// PropertyBlockを全てのRendererに渡す（パラメータを変更するたびに必要）
            /// </summary>
            private void ApplyPropertyBlock()
            {
                for (var i = 0; i < _meshRenderers.Length; i++)
                {
                    _meshRenderers[i].SetPropertyBlock(_materialPropertyBlock);
                }
            }

            private const string TARGET_CHILD_NAME = "_billboard";

            /// <summary>
            /// コンポーネントからビルボードを生成する
            /// </summary>
            /// <returns></returns>
            private CourseBillboardController.BillboardInfo[] GetBillboardTargetFromBillboardData()
            {
                var billboardDataArray = transform.GetComponentsInChildren<CutInBillboardData>();
                if (billboardDataArray == null || billboardDataArray.Length == 0)
                    return null;

                var billInfoArray = new CourseBillboardController.BillboardInfo[billboardDataArray.Length];
                for(int i=0;i< billboardDataArray.Length;i++)
                {
                    billInfoArray[i] = billboardDataArray[i].CreateBillboardInfo();
                }
                return billInfoArray;
            }

            public CourseBillboardController.BillboardInfo[] GetBillboardTargets()
            {
                var targetRoot = transform.Find(TARGET_CHILD_NAME);
                if (targetRoot == null)
                {
                    return GetBillboardTargetFromBillboardData();
                }

                var childArray = targetRoot.GetChildren(true);
                var billboardInfoList = new List<CourseBillboardController.BillboardInfo>(childArray.Length);
                foreach (var child in childArray)
                {
                    var info = new CourseBillboardController.BillboardInfo();
                    if (child.GetComponent<Renderer>() != null)
                    {
                        info.Initialize(child.transform, false, CourseBillboardController.RotationType.Y_Only);
                        billboardInfoList.Add(info);
                    }
                }
                return billboardInfoList.ToArray();
            }
        }//end class CutInBgModel
    }
}
