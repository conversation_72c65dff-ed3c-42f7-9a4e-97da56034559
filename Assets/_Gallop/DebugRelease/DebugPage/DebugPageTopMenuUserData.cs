#if !CYG_PRODUCT
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using Cute.Core;
using Gallop.BugTrackHelper;
using Gallop.BugTrackHelper.Slack;

namespace Gallop
{
    /// <summary>
    /// トップページ
    /// </summary>
    [DebugPageAttribute(typeof(DebugPageTopMenuRoot))]
    public class DebugPageTopMenuUserData : DebugPageButtonMenuBase
    {
        /// <summary>
        /// リストメニュー
        /// </summary>
        private static readonly Dictionary<string, DebugButtonDetail> DEFAULT_MENU_DIC = new Dictionary<string, DebugButtonDetail>()
        {
            {"ViewIDコピー",new DebugButtonDetail(() =>
            {
                var viewerId = Certification.ViewerId;
                Cute.Core.NativePluginWrapper.SetStringToClipboard(viewerId.ToString());
                UIManager.Instance.ShowNotification($"クリップボードに {viewerId} をコピーしました");
            })},
            {"アカウント情報送信",new DebugButtonDetail(() =>
            {
                SendAccountInfo();
            })},
            {"ユーザーデータ削除", new DebugButtonDetail(ClearUserData)},
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList() { return DEFAULT_MENU_DIC; }

        /// <summary>
        /// アカウント関連情報をSlackに送信する
        /// </summary>
        private static void SendAccountInfo()
        {
            string infoText = TicketManager.GetEnvironmentInfoString();

            PostMessageData data = new PostMessageData()
            {
                token = BugTrackHelper.Slack.Environment.SlackApiToken,
                username = "Unityちゃん",
                icon_url = "xxxxxxxxxx/v1/240x240/r/icon/*********/f2289c9b27f2ea7f9b7d838ca6b3dbd9.png",
                channel = "#notify_trace_log",
                text = infoText
            };

            var debugSaveData = SaveDataManager.Instance.GetDebugSettingSaveData();
            if (!string.IsNullOrEmpty(debugSaveData.SlackUserID))
            {
                data.channel = $"@{debugSaveData.SlackUserID}";
            }

            // slack投稿
            var routine = BugTrackHelper.Slack.SlackAPI.PostMessage(data);
            Cute.Core.UpdateDispatcher.StartCoroutine(routine);
        }

        /// <summary>
        /// ユーザーデータ削除
        /// </summary>
        private static void ClearUserData()
        {
            SaveDataManager.RemoveSavedata();
            var _data = new DialogCommon.Data();
            _data.SetSimpleOneButtonMessage("データ削除", "データを削除しました。\nタイトルへ戻ります。",
            (dialog) =>
            {
                PushNotificationManager.IsNeedRefresh = true;
                GameSystem.Instance.SoftwareReset();
            });
            DialogManager.PushSystemDialog(_data);
        }
    }
}
#endif  // !CYG_PRODUCT //