#if !CYG_PRODUCT
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using Cute.Core;

namespace Gallop
{
    /// <summary>
    /// トップページ
    /// </summary>
    [DebugPageAttribute(typeof(DebugPageTopMenuRoot))]
    public class DebugPageTopMenuGame : DebugPageButtonMenuBase
    {
        private const int BUTTON_SPACE_SIZE = 75;
        private const string GO_TO_TITLE_BUTTON_NAME = "タイトルへ";
        private const string CLOSE_MENU_BUTTON_NAME = "閉じる";

        /// <summary>
        /// 初期メニュー
        /// </summary>
        private static readonly Dictionary<string, DebugButtonDetail> DEFAULT_MENU_DIC = new Dictionary<string, DebugButtonDetail>()
        {
#if CYG_DEBUG
            {"シーン移動", new DebugButtonDetail(OpenChangeSceneMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"オプション", new DebugButtonDetail(OpenOption, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ライブ", new DebugButtonDetail(OpenLiveMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton) },
            {"ストーリー", new DebugButtonDetail(OpenStoryMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"シングルモード", new DebugButtonDetail(OpenSingleModeMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"育成開始", new DebugButtonDetail(OpenSingleStartMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"育成リザルト", new DebugButtonDetail(OpenSingleResultMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"チーム競技場", new DebugButtonDetail(OpenTeamStadiumMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ミニ演出", new DebugButtonDetail(OpenMiniMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"強化編成", new DebugButtonDetail(OpenCharacterMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ライブシアター", new DebugButtonDetail(OpenLiveTheaterMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"サークル", new DebugButtonDetail(OpenCircleMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"チャンピオンズ", new DebugButtonDetail(OpenChampions, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ミニゲーム", new DebugButtonDetail(OpenMinigameMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"チュートリアル", new DebugButtonDetail(OpenTutorialMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"WEBビュー確認", new DebugButtonDetail(OpenWebViewMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"デバッグ設定", new DebugButtonDetail(OpenDebugSetting, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ホーム", new DebugButtonDetail(OpenHomeMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ミッション", new DebugButtonDetail(OpenMissionMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"ルーレットダービー", new DebugButtonDetail(OpenRouletteDerbyMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"アウトゲーム", new DebugButtonDetail(OpenOutGameMenu, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
#endif
            {"ロード画面", new DebugButtonDetail(() => { DebugManager.Instance.DebugPageController.OpenPage<DebugPageLoading>(); },
                                                         DebugPageContentFactory.DebugButtonType.HierarchicalButton)},

        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList() { return DEFAULT_MENU_DIC; }

        /// <summary>
        /// 一部のボタンの間に空白を挟む対応
        /// </summary>
        private bool _initializedSpacingObject = false;        // 一度設定すればいい

        private GameObject _spacingBehindGoToTitle = null;
        private GameObject _spacingBehindClose = null;
        
        
        /// <summary>
        /// ボタンの生成処理
        /// </summary>
        protected override void BuildButtons()
        {
            base.BuildButtons();
            
            // 閉じるボタンとタイトルの誤タップを防ぐため、これら2ボタンの間に空白を挟む
            GameObject spacingObject = new GameObject("Spacing");
            spacingObject.AddComponent<LayoutElement>();

            _initializedSpacingObject = false;
            _spacingBehindGoToTitle = GameObject.Instantiate(spacingObject, _buttonGrid.transform);
            _spacingBehindClose = GameObject.Instantiate(spacingObject, _buttonGrid.transform);
        }        

        // =====================================================
        //  ボタン押下時コールバック
        // =====================================================
#if CYG_DEBUG

        /// <summary>
        /// シーンジャンプメニューを開く
        /// </summary>
        private static void OpenChangeSceneMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageChangeScene>();
        }
        
        /// <summary>
        /// オプションを開く
        /// </summary>
        private static void OpenOption()
        {
            UpdateDispatcher.StartCoroutine(OpenOptionCoroutine());
        }

        private static IEnumerator OpenOptionCoroutine()
        {
            //横持なら縦向きに変える
            bool isVertical = Screen.IsVertical;
            if (!isVertical)
            {
                yield return Screen.ChangeScreenOrientationPortraitAsync();
            }
            var dialog = DialogOptionHome.Open();
            //ダイアログが削除されたら元に戻す
            dialog.DialogData.DestroyCallBack = () =>
            {
                if (!isVertical)
                {
                    UpdateDispatcher.StartCoroutine(Screen.ChangeScreenOrientationLandscapeAsync());
                }
            };
        }

        /// <summary>
        /// WEBビュー確認
        /// </summary>
        private static void OpenWebViewMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageWebView>();
        }

        /// <summary>
        /// デバッグ設定
        /// </summary>
        private static void OpenDebugSetting()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageDebugSettings>();
        }

        /// <summary>
        /// ルーレットダービー
        /// </summary>
        private static void OpenRouletteDerbyMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageRouletteDerby>();
        }

        /// <summary>
        /// ホーム
        /// </summary>
        private static void OpenHomeMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageHome>();
        }

        /// <summary>
        /// ミッション
        /// </summary>
        private static void OpenMissionMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageMission>();
        }

        /// <summary>
        /// ストーリーメニューを開く
        /// </summary>
        private static void OpenStoryMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageStoryTop>();
        }

        /// <summary>
        /// ライブメニューを開く
        /// </summary>
        private static void OpenLiveMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageLiveTop>();
        }

        /// <summary>
        /// シングルモードメニューを開く
        /// </summary>
        private static void OpenSingleModeMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageSingleModeTop>();
        }

        /// <summary>
        /// 育成開始メニューを開く
        /// </summary>
        private static void OpenSingleStartMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageSingleModeStart>();
        }

        private static void OpenSingleResultMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageSingleModeResult>();
        }

        /// <summary>
        /// チーム競技場メニューを開く
        /// </summary>
        private static void OpenTeamStadiumMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageTeamStadiumTop>();
        }

        /// <summary>
        /// ミニ演出デバコマ
        /// </summary>
        private static void OpenMiniMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageMiniCharaTop>();
        }

        /// <summary>
        /// 強化編成デバコマ
        /// </summary>
        private static void OpenCharacterMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageCharacterTop>();
        }

        /// <summary>
        /// ライブシアターデバコマ
        /// </summary>
        private static void OpenLiveTheaterMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageLiveTheaterTop>();
        }

        /// <summary>
        /// サークルデバコマ
        /// </summary>
        private static void OpenCircleMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageCircleTop>();
        }

        /// <summary>
        /// チャンミデバコマ
        /// </summary>
        private static void OpenChampions()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageChampionsTop>();
        }

        /// <summary>
        /// ミニゲームデバコマ
        /// </summary>
        private static void OpenMinigameMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageMiniGameTop>();
        }

        /// チュートリアルデバコマ
        /// </summary>
        private static void OpenTutorialMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageTutorialTop>();
        }
        
        /// <summary>
        /// その他アウトゲーム
        /// </summary>
        private static void OpenOutGameMenu()
        {
            DebugManager.Instance.DebugPageController.OpenPage<DebugPageOutGameTop>();
        }
#endif
        /// <summary>
        /// デバッグメニューを閉じる
        /// </summary>
        public static void CloseDebugMenu()
        {
            DebugManager.Instance.DebugPageController.SetEnable(false);
        }

        /// <summary>
        /// 一度構築されたページの再構築
        /// 表示する時の条件に応じてメニューを入れ替えたい場合はこの関数を継承し、実装します。
        /// </summary>
        public override void UpdateMenu()
        {
            if (!_initializedSpacingObject)
            {
                // Spacing用の矩形を作成して設定する
                RectTransform spacingBehindGoToTitleRectTransform = _spacingBehindGoToTitle.transform as RectTransform;
                RectTransform spacingBehindCloseRectTransform = _spacingBehindClose.transform as RectTransform;
//                spacingBehindGoToTitleRectTransform.rect.Set(
//                    spacingBehindGoToTitleRectTransform.rect.x, spacingBehindGoToTitleRectTransform.rect.y,
//                    spacingBehindGoToTitleRectTransform.rect.width, 50.0f);
//                spacingBehindCloseRectTransform.rect.Set(
//                    spacingBehindCloseRectTransform.rect.x, spacingBehindCloseRectTransform.rect.y,
//                    spacingBehindCloseRectTransform.rect.width, 50.0f);

                Vector2 newSize = spacingBehindGoToTitleRectTransform.sizeDelta;
                newSize.y = 50.0f;
                spacingBehindGoToTitleRectTransform.sizeDelta = newSize;
                spacingBehindCloseRectTransform.sizeDelta = newSize;
            
                // タイトルへ　ボタン
                //var goToTitleButton = _buttonList[(int)ButtonOrder.Footer][GO_TO_TITLE_BUTTON_NAME];
                //int goToTitleInsertIndex = goToTitleButton.transform.GetSiblingIndex();

                // 閉じる　ボタン
                //var closeButton = _buttonList[(int)ButtonOrder.Footer][CLOSE_MENU_BUTTON_NAME];
                //int closeInsertIndex = closeButton.transform.GetSiblingIndex();

                //spacingBehindGoToTitleRectTransform.SetSiblingIndex(goToTitleInsertIndex);
                //spacingBehindCloseRectTransform.SetSiblingIndex(closeInsertIndex + 1);

                _initializedSpacingObject = true;
            }
        }
    }
}
#endif  // !CYG_PRODUCT //