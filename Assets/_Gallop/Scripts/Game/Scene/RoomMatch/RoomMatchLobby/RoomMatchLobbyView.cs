using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using UnityEngine;
using System;

namespace Gallop
{
    #region View

    /// <summary>
    /// ルームマッチ：待機ルームView
    /// </summary>
    public class RoomMatchLobbyView : ViewBase
    {
        /// <summary>
        /// ルーム名テキスト
        /// </summary>
        [field: SerializeField, RenameField]
        public TextCommon RoomNameText { get; private set; } = null;

        /// <summary>
        /// ルームコメントテキスト
        /// </summary>
        [field: SerializeField, RenameField]
        public TextCommon RoomCommentText { get; private set; } = null;

        /// <summary>
        /// 詳細ボタン
        /// </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon DetailButton { get; private set; } = null;

        /// <summary>
        /// レースID親オブジェクト
        /// </summary>
        [field: SerializeField, RenameField]
        public GameObject RaceIdInfoParentObject { get; private set; } = null;

        /// <summary>
        /// レースID
        /// </summary>
        [field: SerializeField, RenameField]
        public TextCommon RaceIdNumText { get; private set; } = null;

        /// <summary>
        /// コピーボタン
        /// </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon CopyButton { get; private set; } = null;

        /// <summary>
        /// シェアボタン
        /// </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon ShareButton { get; private set; } = null;

        /// <summary>
        /// 参加キャラ一覧
        /// </summary>
        [field: SerializeField, RenameField]
        public PartsRoomMatchEntryTrainedCharaList PartsTrainerList { get; private set; } = null;

        /// <summary>
        /// カウントダウンテキスト
        /// </summary>
        [field: SerializeField, RenameField]
        public TextCommon CountdownText { get; private set; } = null;

        /// <summary>
        /// レースボタン
        /// </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon RaceButton { get; private set; } = null;

        /// <summary>
        /// 繰り上げ出走ボタン
        /// </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon StartRaceEarlierButton { get; private set; } = null;

        /// <summary>
        /// 観戦ボタン
        /// </summary>
        [field: SerializeField, RenameField]
        public ObjectSwitchButton AudienceButton { get; private set; } = null;

        /// <summary>
        /// 公開設定
        /// </summary>
        [field: SerializeField, RenameField]
        public PartsOnOffToggleSwitch AllowDisplayToggle { get; private set; } = null;

        /// <summary>
        /// 公開設定ColorSender
        /// </summary>
        [field: SerializeField, RenameField]
        public ColorSender AllowDisplayToggleColorSender { get; private set; } = null;

        /// <summary>
        /// 3D表示領域
        /// </summary>
        [field: SerializeField, RenameField]
        public MiniDirectorUI DirectorUI { get; private set; } = null;

        [field: SerializeField, RenameField]
        public RectTransform EntryNumTweenRoot { get; private set; } = null;
        
        [field: SerializeField, RenameField]
        public RectTransform IdInfoTweenRoot { get; private set; } = null;

        [field: SerializeField, RenameField]
        public RectTransform AllowDisplayTweenRoot { get; private set; } = null;

        [field: SerializeField, RenameField]
        public RectTransform BottomPartsTweenRoot { get; private set; } = null;
    }
    
    #endregion

    /// <summary>
    /// ルームマッチ: キャラ選択
    /// ViewInfo
    /// </summary>
    public class RoomMatchLobbyViewInfo : IViewInfo
    {
        public int RoomId { get; private set; }

        public bool IsCreateImmediate { get; private set; } = false;

        public RoomMatchLobbyViewInfo(int roomId, bool isCreateImmediate = false)
        {
            RoomId = roomId;
            IsCreateImmediate = isCreateImmediate;
        }
    }

    /// <summary>
    /// ルームマッチ：待機ルーム
    /// Viewコントローラー
    /// </summary>
    public partial class RoomMatchLobbyViewController : ViewControllerBase<RoomMatchLobbyView>
    {
        #region Const

        /// <summary> ボタンエフェクトサイズ </summary>
        private static readonly Vector2 TARGET_RACE_BUTTON_EFFECT_SIZE = new Vector2(478, 232);
        /// <summary> ボタンエフェクトパーティクル位置 </summary>
        private static readonly Vector3 TARGET_RACE_BUTTON_EFFECT_POSITION = new Vector3(0, 12, 0);

        #endregion

        #region Member, Property

        private RoomMatchLobby3D _lobby3d = null;

        /// <summary> レース開始までの残り時間 </summary>
        private long _remainingTime = long.MaxValue;
        /// <summary> レース開始可能時間か </summary>
        private bool CanStartRaceTime => _remainingTime < 0;
        /// <summary> 繰り上げ出走時間の可否 </summary>
        private bool CanForceRaceStartTime => _remainingTime > ServerDefine.RoomMatchForceStartLimitSecond;

        /// <summary> エントリー不可時間経過のUI更新フラグ </summary>
        private bool _isUpdateUICantEntry = false;
        /// <summary> 初期化フラグ </summary>
        private bool _isInitializing = false;

        /// <summary> ポーリング用タイムカウント </summary>
        private float _pollingCoolTime = 0;
        /// <summary> ポーリングが可能かどうか </summary>
        private bool _enablePolling = false;
        /// <summary> ポーリング中かどうか </summary>
        private bool _isPolling = false;

        /// <summary> ミニキャラ配列 </summary>
        private List<RoomMatchLobbyMiniCharaData> _miniCharaList = new List<RoomMatchLobbyMiniCharaData>();
        /// <summary> ミニキャラ配列長 </summary>
        private int _miniCharaListCount;

        /// <summary> ミニキャラ用ユニークID </summary>
        private long _miniCharaUniqueId = 0;

        private GameObject _raceButtonEffect = null;

        #endregion

        #region ViewControllerBase

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // ボタンエフェクト
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_TOP_BUTTON_RACE_EFFECT_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_TOP_BUTTON_RACE_EFFECT_PARTICLE_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_TOP_BUTTON_TRAINING_EFFECT_PATH);

            RoomMatchLobby3D.RegisterDownload(register);
        }

        /// <summary>
        /// 初期化（HubViewが読み込まれた時に呼ばれる）
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            // ボタンコールバック
            _view.RaceButton.SetOnClick(GotoPaddock);
            _view.AudienceButton.SetOnClick(GotoPaddock);

            _view.CopyButton.SetOnClick(OnClickCopyButton);
            _view.ShareButton.SetOnClick(OnClickShareButton);
            _view.StartRaceEarlierButton.SetOnClick(() => StartRaceEarlier(TextId.RoomMatch0058.Text()));

            _view.AllowDisplayToggle.Setup(false);
            _view.AllowDisplayToggle.Button.SetOnClick(OnClickAllowDisplayToggle);

            yield return base.InitializeView();
        }

        /// <summary>
        /// 初期化（HubView使用時にViewが遷移する度に呼ばれる）
        /// </summary>
        public override IEnumerator InitializeEachPlayIn()
        {
            _isPolling = false;
            _enablePolling = false;
            _isUpdateUICantEntry = false;
            _miniCharaUniqueId = 0;
            _isInitializing = true;

            var prevViewId = SceneManager.Instance.GetPrevViewId();
            if (prevViewId != SceneDefine.ViewId.RoomMatchCharacterEntry)
            {
                // キャラ選択には戻れない
                TempData.Instance.RoomMatchData.PrevViewId = prevViewId;
            }

            var viewInfo = GetViewInfo<RoomMatchLobbyViewInfo>();

            if (viewInfo == null)
            {
                Debug.LogWarning("ルームIDが指定されていません。");
                yield break;
            }
            else
            {
                // 部屋を作った直後は待ちリクエストしなくて良い.
                if (!viewInfo.IsCreateImmediate)
                {
                    // Work更新
                    yield return LoadLobbyEnterRequest(viewInfo.RoomId);
                }
            }

            // レスポンス依存のリソースをロード
            yield return RegisterDownlodFromServerData();

            _pollingCoolTime = WorkDataManager.Instance.RoomMatchData.PollingInterval;
            _remainingTime = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.StartUnixTime - TimeUtil.GetServerTimeStamp();

            // たづなさんが居たら消す
            ExhibitionRaceCharacterBgController.GetModelController().SetActiveWithCheck(false);

            Initialize3d();
            Initialize2d();

            yield return base.InitializeEachPlayIn();
            _isInitializing = false;
        }

        /// <summary>
        /// 画面In再生
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PlayInView()
        {
            yield return base.PlayInView();
            
            // UIイリ
            // TODO: @takahashi_reo: もっときれいなやり方があれば差替え
            // RectTransformの位置が正しくとれないので1フレ待機
            var seq = CreateAnimation(false);
            seq.Complete();
            yield return null;
            yield return CreateAnimation(true);

            if (!CanStartRace())
            {
                _enablePolling = true;
            }

            OpenForceStartDialogWithEntryNumIfNeed();
        }

        /// <summary>
        /// 画面Out再生
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PlayOutView()
        {
            _enablePolling = false;
            
            // UIハケ
            var outSeq = CreateAnimation(false);
            yield return base.PlayOutView();
            
            yield return outSeq.WaitForCompletion();
        }

        /// <summary>
        /// 更新
        /// </summary>
        public override void UpdateView()
        {
            UpdateRemainingTime();

            UpdatePolling();

            if (_lobby3d != null)
            {
                _lobby3d.UpdateView();
            }
            base.UpdateView();
        }
        public override void LateUpdateView()
        {
            if (_lobby3d != null)
            {
                _lobby3d.LateUpdateView();
            }
            base.LateUpdateView();
        }

        /// <summary>
        /// 終了（HubView使用時にViewが遷移する度に呼ばれる）
        /// </summary>
        /// <returns></returns>
        public override IEnumerator EndView()
        {
            _view.PartsTrainerList.EndView();
            Finalize3d();

            if (_raceButtonEffect != null)
            {
                UnityEngine.Object.Destroy(_raceButtonEffect);
                _raceButtonEffect = null;
            }

            yield return base.EndView();
        }

        /// <summary>
        /// 終了処理(NowLoading表示完了後)
        /// </summary>
        public override IEnumerator FinalizeView()
        {
            yield return base.FinalizeView();
        }

        public override void UpdateViewBeforeLoadingOut()
        {
            UpdateView();
        }
        public override void UpdateViewBeforeLoadingIn()
        {
            UpdateView();
        }

        #endregion ViewControllerBase

        #region UI

        /// <summary>
        /// 2D初期化
        /// </summary>
        private void Initialize2d()
        {
            var workRoomMatchData = WorkDataManager.Instance.RoomMatchData;

            _view.ShareButton.SetNotificationMessage(string.Empty);
            _view.CopyButton.SetNotificationMessage(string.Empty);
            _view.StartRaceEarlierButton.SetNotificationMessage(string.Empty);
            _view.ShareButton.SetInteractable(true);
            _view.CopyButton.SetInteractable(true);
            SetAllowDisplayToggleInteractive(true);

            _view.RaceIdInfoParentObject.SetActiveWithCheck(true);

            var raceType = RoomMatchUtil.GetCurrentRoomRaceType();
            if (raceType == ExhibitionRaceDefine.RoomRaceType.Host && !CanStartRaceTime)
            {
                // 繰り上げ発走可能なホスト
                SetupRaceButton();

                _view.StartRaceEarlierButton.SetActiveWithCheck(true);
                _view.RaceButton.SetActiveWithCheck(false);
                _view.AudienceButton.SetActiveWithCheck(false);

                _view.StartRaceEarlierButton.SetInteractable(CanForceRaceStartTime);
            }
            else if (raceType == ExhibitionRaceDefine.RoomRaceType.Host || raceType == ExhibitionRaceDefine.RoomRaceType.Guest)
            {
                SetupRaceButton();

                _view.StartRaceEarlierButton.SetActiveWithCheck(false);
                _view.RaceButton.SetActiveWithCheck(true);
                _view.AudienceButton.SetActiveWithCheck(false);
                SetAllowDisplayToggleInteractive(false);
            }
            else if (raceType == ExhibitionRaceDefine.RoomRaceType.Audience)
            {
                _view.StartRaceEarlierButton.SetActiveWithCheck(false);
                _view.RaceButton.SetActiveWithCheck(false);
                _view.AudienceButton.SetActiveWithCheck(true);
                SetAllowDisplayToggleInteractive(false);
            }

            // ルーム詳細（開催キャンセルはこのダイアログ内にある）
            var dialogType = DialogRoomMatchRaceDetail.DetailType.LobbyHost;
            if (raceType == ExhibitionRaceDefine.RoomRaceType.Guest)
            {
                dialogType = DialogRoomMatchRaceDetail.DetailType.LobbyEntry;
            }
            else if(raceType == ExhibitionRaceDefine.RoomRaceType.Audience)
            {
                dialogType = DialogRoomMatchRaceDetail.DetailType.LobbyWatch;
            }
            _view.DetailButton.SetOnClick(
                () => DialogRoomMatchRaceDetail.Open(
                    WorkDataManager.Instance.RoomMatchData.CurrentRoomData,
                    () =>
                    {
                        // 編集結果を即時反映
                        _view.RoomNameText.text = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomName;
                        _view.RoomCommentText.text = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.Message;
                    },
                    dialogType
                    )
                );

            _view.RoomNameText.text = workRoomMatchData.CurrentRoomData.RoomName;
            _view.RoomCommentText.text = workRoomMatchData.CurrentRoomData.Message;
            _view.RaceIdNumText.text = RoomMatchUtil.GetRoomIdText(workRoomMatchData.CurrentRoomData.RoomId);
            _view.AllowDisplayToggle.Apply(workRoomMatchData.CurrentRoomData.AllowDisplay);

            _view.PartsTrainerList.Setup(
                workRoomMatchData.CurrentRoomData.PrivateSetting,
                workRoomMatchData.CurrentRoomUserList,
                workRoomMatchData.CurrentRoomData.EntryNum,
                workRoomMatchData.CurrentRoomData.PrivateEntryNum,
                true
                );
            _view.PartsTrainerList.ResetNormalizedPosition();

            SetUIWithCanRaceStart();
        }

        /// <summary>
        /// レース開始ボタンのセットアップ
        /// </summary>
        private void SetupRaceButton()
        {
            var trainedChara = GetMyLeaderChara();
            SingleModeUtils.SetupButtonCharaPetit(trainedChara.CharaId, trainedChara.GetRaceDressId(), _view.RaceButton, ResourcePath.CharaPetitCatetory.SingleTopTargetRaceOff, ResourcePath.CharaPetitCatetory.SingleTopTargetRaceOn);
        }

        /// <summary>
        /// 残り時間計算
        /// </summary>
        private void UpdateRemainingTime()
        {
            if (CanStartRaceTime || _isInitializing)
            {
                return;
            }

            _remainingTime = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.StartUnixTime - TimeUtil.GetServerTimeStamp();

            if (!_isUpdateUICantEntry && _remainingTime <= ServerDefine.RoomMatchEntryLimitMinute * TimeUtil.MINUTE_SECOND)
            {
                SetUICantEntry();
            }

            if (_remainingTime < 0)
            {
                SetUIWithCanRaceStart();
            }
            else
            {
                if (!CanForceRaceStartTime && _view.StartRaceEarlierButton.IsInteractable())
                {
                    // 繰り上げ発走不可
                    _view.StartRaceEarlierButton.SetInteractable(false);
                    _view.StartRaceEarlierButton.SetNotificationMessage(TextId.RoomMatch0061.Text());
                }

                _view.CountdownText.text = TextUtil.Format(
                    TextId.RoomMatch0055.Text(),
                    _remainingTime / TimeUtil.HOUR_SECOND,
                    (_remainingTime % TimeUtil.HOUR_SECOND) / TimeUtil.MINUTE_SECOND,
                    _remainingTime % TimeUtil.MINUTE_SECOND
                    );
            }
        }

        /// <summary>
        /// レースの状態に応じたUIを設定
        /// </summary>
        private void SetUIWithCanRaceStart()
        {
            // 初期化の都合上2回以上通る場合がある
            if (_raceButtonEffect != null)
            {
                UnityEngine.Object.Destroy(_raceButtonEffect);
                _raceButtonEffect = null;
            }

            if (CanStartRace())
            {
                // レース参加者でレース開始可能
                _view.RaceButton.SetInteractable(true);
                _view.AudienceButton.SetInteractable(true);

                _view.RaceButton.SetNotificationMessage(string.Empty);
                _view.AudienceButton.SetNotificationMessage(string.Empty);

                _view.CountdownText.text = TextId.RoomMatch0056.Text();
                _view.StartRaceEarlierButton.SetActiveWithCheck(false);

                SetUICantEntry();

                var raceType = RoomMatchUtil.GetCurrentRoomRaceType();
                if (raceType != ExhibitionRaceDefine.RoomRaceType.Audience)
                {
                    _view.RaceButton.SetActiveWithCheck(true);
                    _raceButtonEffect = CreateRaceButtonEffect(_view.RaceButton.transform);
                }
                else
                {
                    _raceButtonEffect = CreateRaceButtonEffect(_view.AudienceButton.transform);
                }

                _enablePolling = false;// ポーリングは止めておく
            }
            else if (CanStartRaceTime)
            {
                // レース観戦でシミュレート待ち
                _view.AudienceButton.SetInteractable(false);
                _view.AudienceButton.SetNotificationMessage(TextId.RoomMatch0193.Text());

                _view.CountdownText.text = TextId.RoomMatch0192.Text();

                SetUICantEntry();
            }
            else
            {
                // カウントダウンあり
                _view.RaceButton.SetInteractable(false);
                _view.AudienceButton.SetInteractable(false);
                _view.RaceButton.SetNotificationMessage(TextId.RoomMatch0062.Text());
                _view.AudienceButton.SetNotificationMessage(TextId.RoomMatch0063.Text());

                UpdateRemainingTime();
            }
        }

        /// <summary>
        /// ボタンエフェクトの作成
        /// </summary>
        /// <param name="parent"></param>
        /// <returns></returns>
        private GameObject CreateRaceButtonEffect(Transform parent)
        {
            var effPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_TOP_BUTTON_RACE_EFFECT_PATH);
            var raceButtonEffect = UnityEngine.Object.Instantiate(effPrefab, parent);
            raceButtonEffect.transform.SetAsFirstSibling();
            raceButtonEffect.GetComponent<TweenAnimationTimelineComponent>().PlayLoop();
            var effectRect = raceButtonEffect.transform as RectTransform;
            effectRect.sizeDelta = TARGET_RACE_BUTTON_EFFECT_SIZE;
            var effImageArray = raceButtonEffect.GetComponentsInChildren<ImageCommon>();
            var effSprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Single, AtlasSpritePath.SingleMode.EFF_CMD_00);
            foreach (var imageCommon in effImageArray)
            {
                imageCommon.sprite = effSprite;
            }

            // パーティクル
            var particlePrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_TOP_BUTTON_RACE_EFFECT_PARTICLE_PATH);
            var particleObject = UnityEngine.Object.Instantiate(particlePrefab, raceButtonEffect.transform);
            particleObject.transform.localPosition = TARGET_RACE_BUTTON_EFFECT_POSITION;
            return raceButtonEffect;
        }

        /// <summary>
        /// ポーリングでUIを更新
        /// </summary>
        private void UpdateUIWithPolling()
        {
            _view.RoomNameText.text = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomName;
            _view.RoomCommentText.text = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.Message;
            _view.AllowDisplayToggle.Apply(WorkDataManager.Instance.RoomMatchData.CurrentRoomData.AllowDisplay);
        }

        /// <summary>
        /// エントリー不可時間になった時のUI更新処理
        /// </summary>
        private void SetUICantEntry()
        {
            _view.ShareButton.SetInteractable(false);
            _view.CopyButton.SetInteractable(false);
            _view.ShareButton.SetNotificationMessage(TextId.RoomMatch0064.Text());
            _view.CopyButton.SetNotificationMessage(TextId.RoomMatch0064.Text());
            SetAllowDisplayToggleInteractive(false);

            _isUpdateUICantEntry = true;
        }

        /// <summary>
        /// 公開設定トグルのアクティブ切り替え
        /// </summary>
        /// <param name="isInteractive"></param>
        private void SetAllowDisplayToggleInteractive(bool isInteractive)
        {
            _view.AllowDisplayToggleColorSender.SendColor = isInteractive ? ButtonCommon.DEFAULT_COLOR_WHITE : ButtonCommon.NO_INTERACTERABLE_COLOR;
            _view.AllowDisplayToggle.Button.SeType = isInteractive ? ButtonCommon.ButtonSeType.DecideM01 : ButtonCommon.ButtonSeType.CancelM02;
        }

        /// <summary>
        /// 人数が最大になった時に強制的に繰り上げ出走ダイアログを表示
        /// </summary>
        private void OpenForceStartDialogWithEntryNumIfNeed()
        {
            var roomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
            if (TempData.Instance.RoomMatchData.IsOpenForceStartDialog(roomData.RoomId)
                && roomData.CurrentEntryNum == roomData.EntryNum
                && CanForceRaceStart())
            {
                // 人数最大かつ繰り上げ出走可能なら繰り上げ出走可能確認ダイアログを表示
                TempData.Instance.RoomMatchData.AddOpenForceStartDialogRoom(WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomId);
                StartRaceEarlier(TextId.RoomMatch0186.Text());
            }
        }

        /// <summary>
        /// イリハケアニメーション開始
        /// </summary>
        /// <param name="isPlayIn"></param>
        /// <returns></returns>
        private Sequence CreateAnimation(bool isPlayIn)
        {
            var seq = DOTween.Sequence();
            
            // ボタン
            _view.BottomPartsTweenRoot.anchoredPosition = Math.VECTOR2_ZERO;
            seq.Join(TweenAnimationBuilder.CreateSequence(
                _view.BottomPartsTweenRoot.gameObject,
                isPlayIn ? TweenAnimation.PresetType.PartsInMoveAndFade : TweenAnimation.PresetType.PartsOutMoveAndFade));

            // 出走人数
            _view.EntryNumTweenRoot.anchoredPosition = Math.VECTOR2_ZERO;
            seq.Join(TweenAnimationBuilder.CreateSequence(
                _view.EntryNumTweenRoot.gameObject,
                isPlayIn ? TweenAnimation.PresetType.BottomLeftPartsIn : TweenAnimation.PresetType.PartsOutFade));

            // レースIDなどの部分
            _view.IdInfoTweenRoot.anchoredPosition = Math.VECTOR2_ZERO;
            seq.Join(TweenAnimationBuilder.CreateSequence(
                _view.IdInfoTweenRoot.gameObject,
                isPlayIn ? TweenAnimation.PresetType.PartsInFade : TweenAnimation.PresetType.PartsOutFade));

            // 公開設定
            _view.AllowDisplayTweenRoot.anchoredPosition = Math.VECTOR2_ZERO;
            seq.Join(TweenAnimationBuilder.CreateSequence(
                _view.AllowDisplayTweenRoot.gameObject,
                isPlayIn ? TweenAnimation.PresetType.PartsInFade : TweenAnimation.PresetType.PartsOutFade));

            // リスト
            seq.Join(_view.PartsTrainerList.PlayUIAnimation(isPlayIn));
            
            return seq;
        }

        #endregion

        #region ボタンコールバック

        /// <summary>
        /// 繰り上げ出走
        /// </summary>
        private void StartRaceEarlier(string message)
        {
            if (!CanForceRaceStart())
            {
                // 繰り上げ出走不可
                return;
            }

            DialogExhibitionRaceRaceConfirm.Open(
                TextId.RoomMatch0057.Text(),
                message,
                WorkDataManager.Instance.RoomMatchData.CurrentRoomData,
                _ =>
                {
                    var req = new RoomMatchForceRaceStartRequest()
                    {
                        room_id = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomId,
                    };
                    SendRequestInLobby(req, (RoomMatchForceRaceStartResponse res) =>
                    {
                        WorkDataManager.Instance.RoomMatchData.UpdateCurrentRoomData(res.data.room_info);

                        // UI更新
                        _view.StartRaceEarlierButton.SetActiveWithCheck(false);
                        _view.RaceButton.SetActiveWithCheck(true);
                        _view.RaceButton.SetInteractable(false);
                        _view.RaceButton.SetNotificationMessage(TextId.RoomMatch0062.Text());

                        DialogManager.RemoveAllDialog(() =>
                        {
                            // 出走完了ダイアログ
                            DialogManager.PushDialog(new DialogCommon.Data().SetSimpleOneButtonMessage(
                                TextId.RoomMatch0059.Text(),
                                TextId.RoomMatch0060.Text()
                                ));
                        });
                    });
                }
                );
        }

        /// <summary>
        /// パドックへ遷移
        /// </summary>
        private void GotoPaddock()
        {
            if (!CanStartRace())
            {
                return;
            }

            // SendRequestInLobbyと同じ処理を入れる
            Connecting.Instance.Show();
            UIManager.Instance.LockGameCanvas();

            var currentRoomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;

            TempData.Instance.RoomMatchData.CurrentRaceRoomId = currentRoomData.RoomId;
            
            RoomMatchUtil.RoomMatchRaceStartCommunication(currentRoomData.RoomId, currentRoomData.RaceInstanceId, (raceInfo) =>
            {
                UIManager.Instance.UnlockGameCanvas();
                Connecting.Instance.Hide();

                // リプレイの時は終了APIを呼ばないようにする為にフラグで制御。初期化（false）にする
                WorkDataManager.Instance.RoomMatchData.UpdateReplayFlag(false);

                var viewInfo = new RoomMatchPaddockViewInfo(raceInfo, SceneDefine.ViewId.RoomMatchTop);
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.RoomMatchPaddock, viewInfo);
            },
            false,
            (Cute.Http.ErrorType errorType, int code) =>
            {
                Connecting.Instance.Hide();
                if (UIManager.Instance.IsLockGameCanvas())
                {
                    // エラー処理で既に外れている場合がある
                    UIManager.Instance.UnlockGameCanvas();
                }
            });
        }

        /// <summary>
        /// コピーボタン押下
        /// </summary>
        private void OnClickCopyButton()
        {
            // レースIDをコピー
            GUIUtility.systemCopyBuffer = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomId.ToString();
            DialogManager.PushDialog(new DialogCommon.Data().SetSimpleOneButtonMessage(
                TextId.RoomMatch0065.Text(),
                TextId.RoomMatch0066.Text()
            ));
        }
        
        /// <summary>
        /// コールバック：シェアボタン押下
        /// </summary>
        private void OnClickShareButton()
        {
            var currentRoomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
            DialogRoomMatchLobbyShare.Open(currentRoomData);
        }

        /// <summary>
        /// コールバック：公開設定トグル
        /// </summary>
        private void OnClickAllowDisplayToggle()
        {
            var type = RoomMatchUtil.GetCurrentRoomRaceType();
            if (type != ExhibitionRaceDefine.RoomRaceType.Host)
            {
                // 変更可能なのはホストのみ
                UIManager.Instance.ShowNotification(TextId.RoomMatch0197.Text());
                return;
            }

            if (_isUpdateUICantEntry)
            {
                // 参加可能時間=編集可能時間を超えた
                UIManager.Instance.ShowNotification(TextId.RoomMatch0198.Text());
                return;
            }

            var roomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
            var req = new RoomMatchChangeAllowDisplayRequest()
            {
                room_id = roomData.RoomId,
                is_allow_display = roomData.AllowDisplay ? 0 : 1,
            };
            SendRequestInLobby(req, (RoomMatchChangeAllowDisplayResponse res) =>
            {
                roomData.UpdateRoomInfo(res.data.room_info);
                _view.AllowDisplayToggle.Apply(roomData.AllowDisplay);
            }, stallOneSecond: true);
        }

        /// <summary>
        /// バックボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {
            Back();
            base.OnClickBackButton();
        }

        /// <summary>
        /// OSバックボタン押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            Back();
            base.OnClickOsBackKey();
        }

        /// <summary>
        /// 戻る処理
        /// </summary>
        private void Back()
        {
            if (!SceneManager.Instance.BackUsingStack())
            {
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.RoomMatchTop);
            }
        }

        #endregion

        #region 通信関連

        /// <summary>
        /// 入室
        /// </summary>
        /// <param name="roomId"></param>
        /// <returns></returns>
        private IEnumerator LoadLobbyEnterRequest(int roomId)
        {
            var isLoaded = false;
            new RoomMatchEnterWaitingRoomRequest()
            {
                room_id = roomId,
            }.Send(res =>
            {
                WorkDataManager.Instance.RoomMatchData.ApplyRoomEnter(res.data);
                isLoaded = true;
            });

            yield return new WaitUntil(() => isLoaded);
        }

        /// <summary>
        /// ポーリング用Update
        /// </summary>
        private void UpdatePolling()
        {
            if (!_enablePolling || _isPolling 
                || Cute.Http.HttpManager.Instance.IsConnecting || DialogManager.DispDialogCount > 0)
            {
                return;
            }

            _pollingCoolTime -= Time.deltaTime;
            if (_pollingCoolTime < 0)
            {
                Polling();
            }
        }

        /// <summary>
        /// ポーリング
        /// </summary>
        private void Polling()
        {
            _isPolling = true;
            new RoomMatchPollingRequest()
            {
                room_id = WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomId,
            }.Send(res =>
            {
                if (!_enablePolling)
                {
                    return;
                }

                WorkDataManager.Instance.RoomMatchData.UpdateWithPolling(res.data);

                // ルーム情報が変わった
                if (WorkDataManager.Instance.RoomMatchData.ChangeRoomDataWithPolling)
                {
                    UpdateUIWithPolling();

                    // シミュレート済みになった
                    if (WorkDataManager.Instance.RoomMatchData.CurrentRoomData.IsRaceSimulateDone)
                    {
                        SetUIWithCanRaceStart();
                    }
                }

                // 人の入れ替えがあった
                if (WorkDataManager.Instance.RoomMatchData.ChangeUserWithPolling)
                {
                    UpdateMiniCharaDataWithPolling();
                    _view.PartsTrainerList.UpdateList(WorkDataManager.Instance.RoomMatchData.CurrentRoomUserList);

                    // 最大人数なら繰り上げ出走確認を表示
                    OpenForceStartDialogWithEntryNumIfNeed();
                    var roomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
                    if (roomData.CurrentEntryNum != roomData.EntryNum)
                    {
                        // 繰り上げ出走確認ダイアログ用フラグを落としておく
                        TempData.Instance.RoomMatchData.RemoveOpenForceStartDialogRoom(roomData.RoomId);
                    }
                }

                _pollingCoolTime = WorkDataManager.Instance.RoomMatchData.PollingInterval;
                _isPolling = false;
            }, controlConnectingUI: false, disableClick: false);
        }

        #endregion

        #region 3d周り

        private void CreateMiniCharaList()
        {
            var workRoomMatchData = WorkDataManager.Instance.RoomMatchData;

            // リスト初期化
            _miniCharaList.Clear();
            foreach (var user in workRoomMatchData.CurrentRoomUserList)
            {
                var trainedCharaCount = user.TraindCharaList.Count();
                for (var i = 0; i < trainedCharaCount; i++)
                {
                    _miniCharaList.Add(new RoomMatchLobbyMiniCharaData(_miniCharaUniqueId, user.ViewerId, user.UserName, user.TraindCharaList[i]));
                    _miniCharaUniqueId++;
                }
            }
            _miniCharaListCount = _miniCharaList.Count();
        }

        /// <summary>
        /// ポーリングによるミニキャラ入退室
        /// </summary>
        private void UpdateMiniCharaDataWithPolling()
        {
            // 退室処理
            if (WorkDataManager.Instance.RoomMatchData.CurrentRoomLeaveUserList.Any())
            {
                var leaveUserList = WorkDataManager.Instance.RoomMatchData.CurrentRoomLeaveUserList;

                var leaveCharaMinIndex = 0;
                for (var i = 0; i < _miniCharaListCount; i++)
                {
                    if (leaveUserList.Any(u => u.ViewerId == _miniCharaList[i].ViewerId))
                    {
                        // 退出キャラの最小インデックスを求める
                        leaveCharaMinIndex = i;
                        break;
                    }
                }

                // 退出させる
                foreach (var leaveData in WorkDataManager.Instance.RoomMatchData.CurrentRoomLeaveUserList)
                {
                    _miniCharaList.RemoveAll(data => data.ViewerId == leaveData.ViewerId);
                }
                _miniCharaListCount = _miniCharaList.Count;

                // ユニークIDを別にしてキャラをつめる
                for (var i = leaveCharaMinIndex; i < _miniCharaListCount; i++)
                {
                    _miniCharaList[i] = new RoomMatchLobbyMiniCharaData(_miniCharaUniqueId, _miniCharaList[i]);
                    _miniCharaUniqueId++;
                }
            }
            // 入室処理
            if (WorkDataManager.Instance.RoomMatchData.CurrentRoomJoinUserList.Any())
            {
                var joinUserList = WorkDataManager.Instance.RoomMatchData.CurrentRoomJoinUserList;
                foreach (var user in joinUserList)
                {
                    var trainedCharaCount = user.TraindCharaList.Count();
                    for (var i = 0; i < trainedCharaCount; i++)
                    {
                        _miniCharaList.Add(new RoomMatchLobbyMiniCharaData(_miniCharaUniqueId, user.ViewerId, user.UserName, user.TraindCharaList[i]));
                        _miniCharaUniqueId++;
                    }
                }
                _miniCharaListCount = _miniCharaList.Count;
            }
        }

        /// <summary>
        /// 3d周りの初期化。
        /// </summary>
        private void Initialize3d()
        {
            CreateMiniCharaList();

            var scene = GetSceneController().GetSceneTransform().gameObject;
            if (scene != null)
            {
                _lobby3d = scene.GetComponent<RoomMatchLobby3D>();
                if (_lobby3d == null)
                {
                    _lobby3d = scene.AddComponent<RoomMatchLobby3D>();
                }
                var context = new RoomMatchLobby3D.Context();
                context.DirectorUI = _view.DirectorUI;
                context.GetCharaNumFunc = () => _miniCharaListCount;
                context.GetCharaDataFunc = i =>
                {
                    if (i < 0 || _miniCharaListCount < i)
                    {
                        Debug.LogWarning("ミニキャラ取得のindexが不正です。: " + i);
                        return null;
                    }

                    return _miniCharaList[i];
                };
                context.OnClickFunc = miniCharaData =>
                {
                    if (miniCharaData is RoomMatchLobbyMiniCharaData data)
                    {
                        RoomMatchUtil.OpenDialogTrainedCharaDetail(data.TrainedChara, data.TrainerName);
                    }
                };
                // UI初期化時に有効化してないといけないのでこのタイミングだけ強制的に有効化
                var viewActive = _view.gameObject.activeSelf;
                _view.gameObject.SetActive(true);
                _lobby3d.OnInitialize(context);
                _view.gameObject.SetActive(viewActive);
                _lobby3d.SetVisible(true);
            }
        }
        /// <summary>
        /// 3d周りの終了処理。
        /// </summary>
        private void Finalize3d()
        {
            if (_lobby3d != null)
            {
                _lobby3d.OnFinalize();
                GameObject.Destroy(_lobby3d);
                _lobby3d = null;
            }
        }
        #endregion 3d

        /// <summary>
        /// リクエストに依存したRegisterDownlod
        /// </summary>
        /// <returns></returns>
        private IEnumerator RegisterDownlodFromServerData()
        {
            var isEndDownLoad = false;
            var register = DownloadManager.GetNewRegister();

            // レース開始ボタンのキャラ画像
            if (RoomMatchUtil.GetCurrentRoomRaceType() != ExhibitionRaceDefine.RoomRaceType.Audience)
            {
                var trainedChara = GetMyLeaderChara();
                if (trainedChara != null)
                {
                    register.RegisterPath(ResourcePath.GetCharaPetitIconPath(trainedChara.CharaId, trainedChara.GetRaceDressId(), ResourcePath.CharaPetitCatetory.SingleTopRaceOff));
                    register.RegisterPath(ResourcePath.GetCharaPetitIconPath(trainedChara.CharaId, trainedChara.GetRaceDressId(), ResourcePath.CharaPetitCatetory.SingleTopRaceOn));
                }
            }

            // SNS共有周りのリソースDL
            var currentRoomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
            DialogRoomMatchLobbyShare.RegisterDownload(currentRoomData, register);

            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                isEndDownLoad = true;
            });

            yield return new WaitUntil(() => isEndDownLoad);
        }

        /// <summary>
        /// 自分のリーダーキャラを取得
        /// </summary>
        /// <returns></returns>
        private WorkTrainedCharaData.TrainedCharaData GetMyLeaderChara()
        {
            return WorkDataManager.Instance.RoomMatchData.CurrentRoomUserList
                .FirstOrDefault(user => user.ViewerId == WorkDataManager.Instance.UserData.ViewerId)?.TraindCharaList.First();
        }

        /// <summary>
        /// 繰り上げ出走ができるか
        /// </summary>
        /// <returns></returns>
        private bool CanForceRaceStart()
        {
            return CanForceRaceStartTime && WorkDataManager.Instance.RoomMatchData.CurrentRoomData.HostUser.ViewerId == WorkDataManager.Instance.UserData.ViewerId;
        }

        /// <summary>
        /// レース開始できるか
        /// </summary>
        /// <returns></returns>
        private bool CanStartRace()
        {
            var raceType = RoomMatchUtil.GetCurrentRoomRaceType();

            if (raceType != ExhibitionRaceDefine.RoomRaceType.Audience)
            {
                return CanStartRaceTime;
            }

            return WorkDataManager.Instance.RoomMatchData.CurrentRoomData.IsRaceSimulateDone;
        }

        /// <summary>
        /// 61782 ポーリング中はボタンが連打できるので明示的にUIをロックする
        /// </summary>
        /// <typeparam name="TRequestType"></typeparam>
        /// <typeparam name="TResponseType"></typeparam>
        /// <param name="req"></param>
        /// <param name="onSuccess"></param>
        /// <param name="onError"></param>
        /// <param name="disableClick"></param>
        /// <param name="shelveError"></param>
        /// <param name="stallOneSecond"></param>
        /// <param name="onErrorWithHeader"></param>
        public static void SendRequestInLobby<TRequestType, TResponseType>(
            TRequestType req,
            Action<TResponseType> onSuccess,
            Action<Cute.Http.ErrorType, int> onError = null,
            bool disableClick = true,
            bool shelveError = false,
            bool stallOneSecond = false,
            Action<Cute.Http.ErrorType, int, DataHeader> onErrorWithHeader = null)
            where TRequestType : RequestBase<TResponseType>
            where TResponseType : ResponseCommon
        {
            Connecting.Instance.Show();
            UIManager.Instance.LockGameCanvas();
            Cute.Http.HttpManager.Instance.Send(
                req,
                (TResponseType res) =>
                {
                    UIManager.Instance.UnlockGameCanvas();
                    Connecting.Instance.Hide();
                    onSuccess?.Invoke(res);
                },
                (Cute.Http.ErrorType errorType, int code) =>
                {
                    Connecting.Instance.Hide();
                    if (UIManager.Instance.IsLockGameCanvas())
                    {
                        // エラー処理で既に外れている場合がある
                        UIManager.Instance.UnlockGameCanvas();
                    }
                    onError?.Invoke(errorType, code);
                },
                false, disableClick, shelveError, stallOneSecond, onErrorWithHeader);
        }
    }
}
