using UnityEngine;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// ルームマッチ：レースリザルト3D背景
    /// </summary>
    public sealed class PartsRoomMatchRaceResultBackground : MonoBehaviour
    {
        private const string BODY_MOTION_NAME = "_overRun02_loop";
        private const string FACE_EAR_MOTION_NAME = "overRun02_loop";
        private const int COURSE_ID = 10006; // 東京
        private const GameDefine.BgSeason COURSE_SEASON = GameDefine.BgSeason.Spring;
        private const RaceDefine.Weather COURSE_WEATHER = RaceDefine.Weather.Sunny;
        private const RaceDefine.Rotation COURSE_ROTATION = RaceDefine.Rotation.Right;

        #region SerializeField
        [Header("キャラ")]
        [SerializeField]
        private int _charaDressId = 1;
        [SerializeField]
        private int _charaId = 1001;
        [SerializeField]
        private string _motionName = BODY_MOTION_NAME;
        [SerializeField]
        private string _faceEarMotionName = FACE_EAR_MOTION_NAME;
        [SerializeField]
        private float _charaSpeed = 6.0f;
        [SerializeField]
        private Vector3 _charaEyeTracePosition = Math.VECTOR3_ZERO;

        [Header("競馬場")]
        [SerializeField]
        private int _courseId = 10006;
        [SerializeField]
        private GameDefine.BgSeason _courseSeason = GameDefine.BgSeason.Spring;
        [SerializeField]
        private RaceDefine.Weather _courseWeather = RaceDefine.Weather.Sunny;
        [SerializeField]
        private RaceDefine.Rotation _courseRotation = RaceDefine.Rotation.Right;

        [Header("カメラ")]
        [SerializeField]
        private Camera _camera = null;
        [SerializeField]
        private float _cameraRotationY = 45;
        [SerializeField]
        private float _cameraHeight = 1.16f;
        [SerializeField]
        private float _cameraLookAtHeight = 1.16f;
        [SerializeField]
        private float _cameraDistance = 2.3f;

        [Header("ライト")]
        [SerializeField]
        private float _lightRotationX = 45;
        [SerializeField]
        private float _lightRotationY = 90;

        #endregion

        #region 変数

        private GameObject _courseObject;
        private SimpleModelController _characterModelController;
        private CourseLaneAnim _courseLaneAnim;
        private CourseBg _courseBg;
        private float _distance;
        private GallopCharacterImageEffect _imageEffect;

        public bool EnableUpdate { get; set; } = true;
        public bool EnableCameraUpdate { get; set; } = true;

        #endregion

        private void OnDestroy()
        {
            if (_courseBg != null)
            {
                _courseBg.Exit();
                _courseBg = null;
            }
        }

        #region public 関数

        /// <summary>
        /// 競馬場やキャラクターを生成する
        /// </summary>
        public void CreateCourseAndCharacter(int charaId, int dressId)
        {
            _courseId = COURSE_ID;
            _courseSeason = COURSE_SEASON;
            _courseWeather = COURSE_WEATHER;
            _courseRotation = COURSE_ROTATION;
            _charaId = charaId;
            _charaDressId = dressId;
            CreateCourseAndCharacter();
        }

        /// <summary>
        /// キャラクター表示のon/off
        /// </summary>
        public void SetCharacterVisible(bool visible)
        {
            _characterModelController.SetVisible(visible);
        }

        #endregion

        #region private 関数

        /// <summary>
        /// 競馬場やキャラクターを生成する
        /// </summary>
        [ContextMenu("CreateCourseAndCharacter")]
        private void CreateCourseAndCharacter()
        {
            var courseId = _courseId;
            var courseSeason = _courseSeason;
            var courseWeather = _courseWeather;
            var courseTime = RaceDefine.Time.Daytime;
            CourseEnvParam envParam = null;

            // ライト
            DirectionalLightManager.Instance.SetEnable(true);

            // LowResolutionCamera対応
            var lowResoCamera = _camera.gameObject.AddComponent<LowResolutionCamera>();
            lowResoCamera.InitializeVerticalWithBackGround();
            lowResoCamera.ChangeDirection(LowResolutionCameraUtil.DrawDirection.Vertical);

            // 競馬場ロード
            var loader = new RaceLoaderManager();
            {
                // 環境設定ロード
                {
                    var envDefineList = MasterDataManager.Instance.masterRaceEnvDefine.GetListWithRaceTrackIdOrderByIdAsc(courseId);
                    var envDefine = envDefineList.FirstOrDefault(e => e.Season == (int)courseSeason && e.Weather == (int)courseWeather && e.Timezone == (int)courseTime);
                    int resourceId = (null != envDefine) ? (int)envDefine.Resource : 0;
                    loader.LoaderCourseEnvParam.Load(courseId, resourceId);
                    envParam = loader.CourseEnvParam;
                }

                var context = new RaceLoaderManager.CourseContext();
                context.InitBase(courseId, courseSeason, courseTime);
                context.InitGoalGate(RaceDefine.GoalGateType.Common, RaceDefine.GoalGateType.Common);
                context.InitGoalFlower(RaceDefine.GoalGateType.Common, RaceDefine.GoalGateType.Common);
                context.InitSkydome(ResourcePath.CourseCommonPartsID, courseSeason, courseWeather, courseTime);
                context.InitAudience(RaceDefine.Audience.Few, GameDefine.BgSeason.Summer, courseWeather);
                context.InitTree(0, 0);
                context.RotationCategory = _courseRotation;

                context.lightProbeId = envParam.lightProbeId;
                context.materialTexturePairs = envParam.swapTextures;
                context.materialSubTexturePairs = envParam.swapSubTextures;

                loader.LoadCourseSync(context);
            }
            // 競馬場生成
            {
                if (_courseBg != null)
                {
                    _courseBg.Exit();
                    _courseBg = null;
                }
                if (_courseObject != null)
                {
                    Destroy(_courseObject);
                    _courseObject = null;
                }
                
                _courseObject = new GameObject("CourseObjectRoot");
                _courseObject.transform.SetParent(gameObject.transform);
                _courseBg = _courseObject.AddComponent<CourseBg>();
                var context = new CourseBg.Context(loader, null, Math.VECTOR3_ZERO, Math.QUATERNION_IDENTITY);
                context._isInitializeSync = true;
                _courseBg.Init(ref context);
                _courseBg.StartGate.SetVisible(false);
                _courseBg.GoalGate.SetVisible(false);
            }
            // キャラクター生成
            {
                if (_characterModelController != null)
                {
                    Destroy(_characterModelController.gameObject);
                    _characterModelController = null;
                }
                var buildInfo = new CharacterBuildInfo(0, _charaId, _charaDressId, ModelLoader.ControllerType.Simple);
                var modelObject = ModelLoader.CreateModel(buildInfo);
                modelObject.transform.SetParent(gameObject.transform);
                _characterModelController = modelObject.GetComponent<SimpleModelController>();
                _characterModelController.SetCullingMode(AnimatorCullingMode.AlwaysAnimate);
                _characterModelController.ResetCyspring();
                _characterModelController.ReserveWarmingUpCySpring();

                _characterModelController.LightProbeColor = envParam.DirectionalLightParam.LightProbeColor;
                _characterModelController.EyeTraceController.IsEnable = false; //目線は不要

                // ポストフィルムパラメータを特定
                var postFilmGroup = ResourceManager.LoadOnScene<CoursePostFilmSetGroup>(ResourcePath.GetPostFilmGroupPath()).FilmParamGroup;
                CoursePostFilmSetGroup.CharacterParam charaParam = null;
                int num = postFilmGroup.Length;
                for (int i = num - 1; i >= 0; i--)
                {
                    //後ろにあるものほど優先度が高い
                    var filmParam = postFilmGroup[i];
                    if (CoursePostFilmSetGroup.Check(filmParam, courseSeason, courseTime, courseWeather))
                    {
                        charaParam = filmParam.CharacterParam;
                        _characterModelController.SetRimParameter(charaParam.RimColor, charaParam.RimStep, charaParam.RimFeather, charaParam.RimSpecRate);
                        break;
                    }
                }
                _characterModelController.UpdateGraphicSettings(0);

                // モーション設定
                SetCharacterMotion();
            }
            // オーバーランアニメ
            {
                string overRunLaneName = ResourcePath.GetRoundLanePath(courseId, RaceDefine.GroundType.Turf, RaceDefine.CoursePathType.NormalRace);
                _courseLaneAnim = ResourceManager.LoadOnScene<CourseLaneAnim>(overRunLaneName);
            }
            // カメラ
            _camera.farClipPlane = RaceCameraManager.DEFAULT_FARCLIP;

            if (_imageEffect == null)
            {
                _imageEffect = _camera.gameObject.AddComponent<GallopCharacterImageEffect>();
                _imageEffect.Initialize(); //パラメータ系の初期化
                _imageEffect.ColorCorrectionParam.Initialize();
                _imageEffect.LoadShader();
                _imageEffect.CheckResources();
                _imageEffect.ModelController = _characterModelController;
            }

            // イメージエフェクトとキャラクター色彩
            var imageEffectParameter = ResourceManager.LoadOnView<GallopCharacterImageEffectParameter>(ResourcePath.SINGLE_MODE_RESULT_ENV_PATH);
            if (_imageEffect != null)
            {
                imageEffectParameter.CopyTo(_imageEffect); // イメージエフェクトに適用
                _imageEffect.UpdateCharacterColor();
            }
        }

        /// <summary>
        /// キャラクターモーション設定
        /// </summary>
        [ContextMenu("SetCharacterMotion")]
        private void SetCharacterMotion()
        {
            // 体
            {
                var path = GetBodyMotionPath(_characterModelController.GetDressId(), _characterModelController.GetRaceOverRunPersonalityType(), _motionName, _courseRotation);
                var clip = ResourceManager.LoadOnScene<AnimationClip>(path);
                _characterModelController.PlaySingleAnimation(clip);
            }
            // 顔
            {
                var path = ResourcePath.GetCharacterRaceOverRunMotionFacialPath(_faceEarMotionName, _courseRotation);
                var drivenKey = ResourceManager.LoadOnScene<DrivenKeyAnimation>(path);
                _characterModelController.PlayDrivenKey(drivenKey);
            }
            // 耳
            {
                var path = ResourcePath.GetCharacterRaceOverRunMotionEarPath(_faceEarMotionName, _courseRotation);
                var drivenKey = ResourceManager.LoadOnScene<DrivenKeyAnimation>(path);
                _characterModelController.PlayEarDrivenKey(drivenKey);
            }
        }

        public static void RegisterDownload(
            int dressId,
            ModelLoader.RacePersonalityType racePersonalityType,
            DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_RESULT_ENV_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.GetEnvParamPath(COURSE_ID, COURSE_SEASON, COURSE_WEATHER, RaceDefine.Time.Daytime));
            var loader = new RaceLoaderManager();
            {
                // 環境設定ロード
                CourseEnvParam envParam = null;
                {
                    int resourceId = CourseEnvParam.MakeID(COURSE_SEASON, COURSE_WEATHER, RaceDefine.Time.Daytime);
                    loader.LoaderCourseEnvParam.Load(COURSE_ID, resourceId);
                    envParam = loader.CourseEnvParam;
                }

                var context = new RaceLoaderManager.CourseContext();
                context.InitBase(COURSE_ID, COURSE_SEASON, RaceDefine.Time.Daytime);
                context.InitGoalGate(RaceDefine.GoalGateType.Common, RaceDefine.GoalGateType.Common);
                context.InitGoalFlower(RaceDefine.GoalGateType.Common, RaceDefine.GoalGateType.Common);
                context.InitSkydome(ResourcePath.CourseCommonPartsID, COURSE_SEASON, COURSE_WEATHER, RaceDefine.Time.Daytime);
                context.InitAudience(RaceDefine.Audience.Few, GameDefine.BgSeason.Summer, COURSE_WEATHER);
                context.InitTree(0, 0);
                context.RotationCategory = RaceDefine.Rotation.Left;

                context.lightProbeId = envParam.lightProbeId;
                context.materialTexturePairs = envParam.swapTextures;
                context.materialSubTexturePairs = envParam.swapSubTextures;
                context.CourseStartGateBaseId = envParam.CourseStartGateBaseResourceId;

                loader.RegisterPath(context, register);

                register.RegisterPathWithoutInfo(ResourcePath.GetPostFilmGroupPath());
                register.RegisterPathWithoutInfo(ResourcePath.GetWeatherDataPath());
            }
            // 体
            {
                var path = GetBodyMotionPath(dressId, racePersonalityType, BODY_MOTION_NAME, COURSE_ROTATION);
                register.RegisterPathWithoutInfo(path);
            }
            // 顔・耳
            register.RegisterPathWithoutInfo(ResourcePath.GetCharacterRaceOverRunMotionFacialPath(FACE_EAR_MOTION_NAME, COURSE_ROTATION));
            register.RegisterPathWithoutInfo(ResourcePath.GetCharacterRaceOverRunMotionEarPath(FACE_EAR_MOTION_NAME, COURSE_ROTATION));
        }

        private static string GetBodyMotionPath(int dressId, ModelLoader.RacePersonalityType racePersonalityType, string motionName, RaceDefine.Rotation rotation)
        {
            return ResourcePath.GetCharacterRaceOverRunMotionPath(racePersonalityType, RaceModelController.RaceCategory, motionName, rotation);
        }

        /// <summary>
        /// キャラクター更新
        /// </summary>
        private void UpdateCharacter()
        {
            if (_courseLaneAnim == null || _characterModelController == null) return;

            _distance += Time.deltaTime * _charaSpeed;
            _distance %= _courseLaneAnim.Distance; // オーバーラン一周したら元に戻るように。コースパスで始点と終点が一致していればこれで周回可能。

            float distanceTmp = _courseRotation == RaceDefine.Rotation.Right ? _courseLaneAnim.Distance - _distance : _distance;
            float animTime = _courseLaneAnim.Distance2Time(distanceTmp);
            _courseLaneAnim.Sample(out var pos, out var rot, animTime);
            if (_courseRotation == RaceDefine.Rotation.Right)
            {
                rot *= Quaternion.Euler(CourseLane.ROTATION_FIX_RIGHT);
            }
            _characterModelController.SetTransform(pos, rot);

            // キャラ目線
            var eyeTrace = _characterModelController.EyeTraceController;
            eyeTrace.TargetTransform.localPosition = _charaEyeTracePosition;
        }

        /// <summary>
        /// ライト更新
        /// </summary>
        private void UpdateLight()
        {
            if (_characterModelController == null) return;
            var rotationY = _lightRotationY + _characterModelController.GetRotation().eulerAngles.y;
            DirectionalLightManager.Instance.SetRotation(Quaternion.Euler(_lightRotationX, rotationY, 0));
        }

        /// <summary>
        /// カメラ更新
        /// </summary>
        private void UpdateCamera()
        {
            if (_characterModelController == null || EnableCameraUpdate == false) return;
            float heightScale = CameraController.Get3DCharaHeightScale(_characterModelController.GetHeight(),
                _characterModelController.GetCharaID(), _characterModelController.GetDressId());
            var targetTrans = _characterModelController.transform;
            var targetPosition = targetTrans.position;

            var dir = Quaternion.Euler(0, _cameraRotationY, 0) * targetTrans.forward;//カメラの方向
            var cameraOffset = (dir * _cameraDistance + Vector3.up * _cameraHeight) * heightScale;
            var lookAtOffset = (Vector3.up * _cameraLookAtHeight) * heightScale;
            //カメラ位置更新

            _camera.transform.position = targetPosition + cameraOffset;
            _camera.transform.LookAt(targetPosition + lookAtOffset);
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void Update()
        {
            if (EnableUpdate == false) return;
            UpdateCharacter();
            UpdateCamera();
            UpdateLight();

#if UNITY_EDITOR
            // エディター時のみ、調整できるようにリアルタイム更新する

            // イメージエフェクト更新
            if (_imageEffect != null)
            {
                // キャラクター色彩更新
                _imageEffect.UpdateCharacterColor();
            }
#endif
        }

        #endregion
    }
}
