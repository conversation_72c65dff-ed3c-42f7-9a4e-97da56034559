using System.Collections.Generic;
using System;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// ルームマッチUtil
    /// </summary>
    public static class RoomMatchUtil
    {
        /// <summary>
        /// 殿堂入りウマ娘詳細を開く
        /// </summary>
        /// <param name="trained<PERSON><PERSON>"></param>
        /// <param name="userName"></param>
        public static void OpenDialogTrainedCharaDetail(WorkTrainedCharaData.TrainedCharaData trainedChara, string userName)
        {
            TeamStadiumUtil.OpenDialogTrainedCharacterDetail(trainedChara, userName, trainedChara.ViewerId == WorkDataManager.Instance.UserData.ViewerId);
        }

        /// <summary>
        /// 参加状態の取得
        /// </summary>
        /// <returns></returns>
        public static ExhibitionRaceDefine.RoomRaceType GetCurrentRoomRaceType()
        {
            var workRoomMatchData = WorkDataManager.Instance.RoomMatchData;
            var raceType = ExhibitionRaceDefine.RoomRaceType.Audience;
            var ownViewerId = WorkDataManager.Instance.UserData.ViewerId;
            if (ownViewerId == workRoomMatchData.CurrentRoomData.HostUser.ViewerId)
            {
                raceType = ExhibitionRaceDefine.RoomRaceType.Host;
            }
            else
            {
                var user = workRoomMatchData.CurrentRoomUserList.FirstOrDefault(u => u.ViewerId == ownViewerId);
                raceType = user != null ? ExhibitionRaceDefine.RoomRaceType.Guest : ExhibitionRaceDefine.RoomRaceType.Audience;
            }

            return raceType;
        }

        /// <summary>
        /// 参加状態の取得（RaceInfo.RaceHorse版）
        /// レース初期化周り、パドック等レース周りで使用
        /// </summary>
        /// <returns></returns>
        public static ExhibitionRaceDefine.RoomRaceType GetPlayingRaceType()
        {
        #if CYG_DEBUG
            // RaceDirectでルームマッチのjsonを読み込んだ時にWorkDataでnullアクセスするのでHostを返却する。
            if (SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.RaceDirect)
            {
                return ExhibitionRaceDefine.RoomRaceType.Host;
            }
        #endif
            
            var workRoomMatchData = WorkDataManager.Instance.RoomMatchData;
            var raceType = ExhibitionRaceDefine.RoomRaceType.Audience;
            var ownViewerId = WorkDataManager.Instance.UserData.ViewerId;
            if (ownViewerId == workRoomMatchData.CurrentRoomData.HostUser.ViewerId)
            {
                raceType = ExhibitionRaceDefine.RoomRaceType.Host;
            }
            else
            {
                var user = RaceManager.RaceInfo.RaceHorse.FirstOrDefault(u => u.ViewerId == ownViewerId);
                raceType = user != null ? ExhibitionRaceDefine.RoomRaceType.Guest : ExhibitionRaceDefine.RoomRaceType.Audience;
            }
            return raceType;
        }

        /// <summary>
        /// レースIDとして表示するテキストに変換
        /// (1234567→1234 567)
        /// </summary>
        /// <param name="roomId"></param>
        /// <returns></returns>
        public static string GetRoomIdText(int roomId)
        {
            const int ROOM_ID_SPLIT_COUNT = 4;
            var roomIdString = roomId.ToString();

            if (roomIdString.Length < ROOM_ID_SPLIT_COUNT)
            {
                // 原則デバッグ期間のみのはず
                Debug.LogWarning("レースIDの桁数が短すぎます。");
                return roomIdString;
            }

            return TextUtil.Format(TextId.Common0252.Text(), roomIdString.Substring(0, ROOM_ID_SPLIT_COUNT), roomIdString.Substring(ROOM_ID_SPLIT_COUNT, roomIdString.Length - ROOM_ID_SPLIT_COUNT));
        }

        /// <summary>
        /// ルームマッチレース開始の通信
        /// </summary>
        /// <param name="roomId"></param>
        /// <param name="raceInstanceId"></param>
        /// <param name="onComplete"></param>
        /// <param name="useConnecting">UIロックとConnecting表示を入れるかどうか</param>
        public static void RoomMatchRaceStartCommunication(int roomId, int raceInstanceId, Action<RaceInitializer.LoadRaceInfo> onComplete, bool useConnecting = true, Action<Cute.Http.ErrorType, int> onError = null)
        {
            var req = new RoomMatchRaceStartRequest();
            req.room_id = roomId;
            req.Send(res =>
            {
                WorkDataManager.Instance.RoomMatchData.ApplyRaceStart(res.data);

                var raceResult = WorkDataManager.Instance.RoomMatchData.RaceResultInfo;

                // race_scenarioがあればrace_scenarioを使う
                if (raceResult.RoomId == roomId && !string.IsNullOrEmpty(raceResult.RaceScenario))
                {
                    // シミュレート済みデータからRaceInfoを作成
                    var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam();
                    buildParam.raceInstanceId = raceInstanceId;
                    buildParam.raceHorseDataList = raceResult.RaceHorseDataArray;
                    buildParam.randomSeed = raceResult.RandomSeed;
                    buildParam.raceType = RaceDefine.RaceType.RoomMatch;
                    buildParam.season = (GameDefine.BgSeason)raceResult.Season;
                    buildParam.weather = (RaceDefine.Weather)raceResult.Weather;
                    buildParam.groundCondition = (RaceDefine.GroundCondition)raceResult.GroundCondition;
                    buildParam.simDataBase64 = raceResult.RaceScenario;

                    var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
                    RaceInitializer.CreateRaceInfo(loadRaceInfo);

                    // サーバーから受け取った育成情報をこのタイミングでRaceInfoに入れる(レース結果から).
                    var trainedCharaArray = WorkDataManager.Instance.RoomMatchData.RaceResultInfo.TrainedCharaArray;
                    RaceUtil.SetTrainedCharaData(trainedCharaArray);

                    onComplete?.Invoke(loadRaceInfo);
                }
                else
                {
                    // TODO:kamizato_seiya:クライアントシミュレート
                }

            }, onError: onError, controlConnectingUI: useConnecting, disableClick: useConnecting);
        }

        /// <summary>
        /// パドックへ遷移（リプレイ）
        /// </summary>
        public static void ReplayRaceGotoPaddock()
        {
            var resultInfo = WorkDataManager.Instance.RoomMatchData.SavedRaceResultInfo;

            if (!string.IsNullOrEmpty(resultInfo.RaceScenario))
            {
                // シミュレート済みデータからRaceInfoを作成
                var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam();
                buildParam.raceInstanceId = resultInfo.RaceResult.race_instance_id;
                buildParam.raceHorseDataList = resultInfo.RaceHorseDataArray;
                buildParam.randomSeed = resultInfo.RandomSeed;
                buildParam.raceType = RaceDefine.RaceType.RoomMatch;
                buildParam.season = (GameDefine.BgSeason)resultInfo.Season;
                buildParam.weather = (RaceDefine.Weather)resultInfo.Weather;
                buildParam.groundCondition = (RaceDefine.GroundCondition)resultInfo.GroundCondition;
                buildParam.simDataBase64 = resultInfo.RaceScenario;

                var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
                RaceInitializer.CreateRaceInfo(loadRaceInfo);

                // サーバーから受け取った育成情報をこのタイミングでRaceInfoに入れる(保存結果から).
                var trainedCharaArray = WorkDataManager.Instance.RoomMatchData.SavedRaceResultInfo.TrainedCharaArray;
                RaceUtil.SetTrainedCharaData(trainedCharaArray);

                // リプレイの時は終了APIを呼ばないようにする為にフラグで制御。trueにする
                WorkDataManager.Instance.RoomMatchData.UpdateReplayFlag(true);

                // パドックへ遷移
                var viewInfo = new RoomMatchPaddockViewInfo(loadRaceInfo, SceneDefine.ViewId.RoomMatchTop);
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.RoomMatchPaddock, viewInfo);
            }
        }

        /// <summary>
        /// レースのフォーカス対象のウマ娘（RaceHorseIndex）取得
        /// </summary>
        /// <param name="horseDataArray"></param>
        /// <returns></returns>
        public static int GetFocusRaceHorseIndex(RaceHorseData[] horseDataArray)
        {
            var myTeamId = RoomMatchUtil.GetCurrentRaceMyTeamId();
            var horseIndex = 0;

            for (int i = 0; i < horseDataArray.Length; i++)
            {
                var horseData = horseDataArray[i];
                // 自チームのエースを初期選択キャラにする。
                if ((horseData.team_id == myTeamId) &&
                    (horseData.team_member_id == 1))
                {
                    horseIndex = i;
                }
            }
            return horseIndex;
        }

        /// <summary>
        /// 保存リストの更新
        /// </summary>
        public static void GetSavedRaceResultListAPI(Action onComplete = null)
        {
            // 保存リスト更新.
            var req = new RoomMatchGetSavedRaceResultListRequest();
            req.Send(res =>
            {
                WorkDataManager.Instance.RoomMatchData.SaveRoomDictionary.Clear();
                for (int i = 0; i < res.data.race_result_array.Length; i++)
                {
                    RoomMatchUser hostUser = null;
                    long hostId = res.data.race_result_array[i].host_viewer_id;
                    for (int j = 0; j < res.data.room_user_array.Length; j++)
                    {
                        // ホストの割り出し.
                        if (hostId == res.data.room_user_array[j].viewer_id)
                        {
                            hostUser = res.data.room_user_array[j];
                        }
                    }
                    if (hostUser == null)
                    {
                        continue;
                    }

                    WorkRoomMatchData.RoomData roomData = new WorkRoomMatchData.RoomData(res.data.race_result_array[i], hostUser);
                    var key = System.Tuple.Create(res.data.race_result_array[i].saved_room_id, res.data.race_result_array[i].register_id);
                    ExhibitionRaceDefine.RoomRaceType raceType = (ExhibitionRaceDefine.RoomRaceType)res.data.race_result_array[i].own_join_type;
                    var value = new Tuple<WorkRoomMatchData.RoomData, ExhibitionRaceDefine.RoomRaceType>(roomData, raceType);
                    WorkDataManager.Instance.RoomMatchData.SaveRoomDictionary.Add(key, value);
                }
                onComplete?.Invoke();
            });
        }

        /// <summary>
        /// レース再生で使う自身のチームIDを取得
        /// </summary>
        /// <returns></returns>
        public static int GetCurrentRaceMyTeamId()
        {
            var myTeamId = 0;

            if (RaceManager.RaceInfo != null && RaceManager.RaceInfo.RaceHorse != null)
            {
                var raceHorses = RaceManager.RaceInfo.RaceHorse;

                if (GetPlayingRaceType() == ExhibitionRaceDefine.RoomRaceType.Audience)
                {
                    // 観戦者ならルームホストのTeamIdを使う
                    var currentRoom = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
                    var hostHorse = raceHorses.FirstOrDefault(x => x.ViewerId == currentRoom.HostUser.ViewerId);
                    if (hostHorse != null)
                    {
                        myTeamId = hostHorse.TeamId;
                    }
                }
                else
                {
                    myTeamId = RaceManager.RaceInfo.RaceHorse.FirstOrDefault(x => x.ViewerId == Certification.ViewerId).TeamId;
                }
            }

            return myTeamId;
        }

        /// <summary>
        /// 保存済レース結果の取得
        /// </summary>
        /// <param name="onComplete"></param>
        public static void GetSavedRaceResultAPI(int saveRoomId, ulong registerId, Action onComplete = null, Action<int> onError = null)
        {
            var req = new RoomMatchGetSavedRaceResultRequest();
            req.saved_room_id = saveRoomId;
            req.register_id = registerId;
            req.Send(res =>
            {
                // ルーム作成時にランダムの場合は確定後の情報が入ってくる
                WorkDataManager.Instance.RoomMatchData.ApplySavedRaceResult(res.data);

                onComplete?.Invoke();
            },
            (errorType, resultCode) =>
            {
                if (errorType == Cute.Http.ErrorType.ResultCode)
                {
                    onError?.Invoke(resultCode);
                }
            });
        }

        /// <summary>
        /// 再生するレース種別とルームIDを取得
        /// </summary>
        /// <returns>ルームID</returns>
        public static int GetPlayRaceCurrentRoomId()
        {
            var currentRoomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
            return currentRoomData.RoomId;
        }


        /// <summary>
        /// 指定したルームIDが「やる気画面スキップリスト」に存在しているか
        /// </summary>
        /// <param name="roomId"></param>
        /// <returns>やる気演出スキップ対象なら: true, やる気演出再生するなら: false</returns>
        public static bool ExistInSkipMotivationRaceList(int roomId)
        {
            if (roomId == 0)
            {
                return false;
            }

            var existsRace = WorkDataManager.Instance.RoomMatchData.SkipMotivationList.Exists(x => (x == roomId));
            return existsRace;
        }

        /// <summary>
        /// ルーム内の指定したフレンドの情報取得
        /// </summary>
        /// <param name="viewerId"></param>
        public static UserInfoAtFriend GetRoomUserFriendInfoByViewerId(long viewerId)
        {
            var userInfo = WorkDataManager.Instance.RoomMatchData.RoomUserFriendInfoList.FirstOrDefault(x => x.viewer_id == viewerId);
            return userInfo;
        }

        #region ルームマッチ外から呼び出す機能

        /// <summary>
        /// 通知バッジを表示するかどうか
        /// </summary>
        /// <returns></returns>
        public static bool IsShowRoomMatchNotifyBadge()
        {
            if (!IsReleasedContent())
            {
                return false;
            }    

            return WorkDataManager.Instance.RoomMatchData.NeedNotifyRoom;
        }

        /// <summary>
        /// 機能解放の条件を満たしているかどうか
        /// </summary>
        public static bool IsReleasedContent()
        {
            var userData = WorkDataManager.Instance.UserData;
            var bestTeamEvaluationRank = TeamStadiumUtil.GetTeamRank(userData.BestTeamEvaluationPoint);
            return (int)bestTeamEvaluationRank >= ServerDefine.NeedTeamRankPlayRoomMatch;
        }

        #endregion
    }
}
