using UnityEngine;
using System;
using DG.Tweening;

namespace Gallop
{
    public class PartsRoomMatchHostEntryRoomInfoAndSetting : PartsRoomMatchRoomInfoAndSetting
    {
        #region SerializeField, Member

        [field: Header("開催時の設定")]

        /// <summary> 出走人数 </summary>
        [SerializeField]
        private TextCommon _entryNum = null;

        /// <summary> 開始時間 </summary>
        [SerializeField]
        private TextCommon _startTime = null;

        /// <summary> ホスト情報のルート(非表示用) </summary>
        [SerializeField]
        private GameObject _hostRoot = null;

        /// <summary> 出走人数変更ボタン </summary>
        [SerializeField]
        private ButtonCommon _changeEntryNumButton = null;

        /// <summary> ルーム設定ボタン </summary>
        [SerializeField]
        private ButtonCommon _changeRoomSettingButton = null;

        /// <summary> レース設定変更ボタン </summary>
        [SerializeField]
        private ButtonCommon _changeRaceSettingButton = null;

        /// <summary> エントリー情報 </summary>
        private RoomMatchHostEntrySettingInfo _info = null;

        #endregion

        #region Method

        /// <summary>
        /// 初期化
        /// </summary>
        public override void Initialize(Action<ExhibitionRaceRaceSettingInfo> onChange)
        {
            base.Initialize(onChange);

            // 不要なオブジェクトを非表示
            _hostRoot.SetActive(false);

            // ボタンコールバック
            _changeEntryNumButton.SetOnClick(OnClickEntryNumButton);
            _changeRoomSettingButton.SetOnClick(() => DialogRoomMatchHostEntryRoomSetting.Open(_info, i => UpdateInfo(i)));
            _changeRaceSettingButton.SetOnClick(() => DialogExhibitionRaceChangeRaceSetting.OpenForRoomMatch(_info, i => UpdateInfo(i)));

            // テキストフィールド
            SetupTextField(
                roomName =>
                {
                    _info.RoomName = roomName;
                    UpdateInfo(_info);
                },
                message =>
                {
                    _info.Message = message;
                    UpdateInfo(_info);
                }
                );
        }

        /// <summary>
        /// 情報の更新
        /// </summary>
        /// <param name="info"></param>
        public override void UpdateInfo(ExhibitionRaceRaceSettingInfo info)
        {
            _info = info as RoomMatchHostEntrySettingInfo;
            if (_info == null)
            {
                return;
            }

            UpdateTextField(_info.RoomName, _info.Message);

            _startTime.text = _info.StartTime.GetEnumDisplayName();
            _entryNum.text = TextUtil.Format(TextId.Common0270.Text(), _info.EntryNum);

            // 共通の設定
            SetupRoomSetting(new RoomSettingParam()
            {
                CanWatch = _info.CanWatch,
                PrivateSetting = _info.PrivateSetting,
                PrivateEntryNum = _info.PrivateEntryNum,
                Season = _info.Season,
                Weather = _info.Weather,
                GroundCondition = _info.GroundCondition,
                Motivation = _info.Motivation,
                RankRestriction = (int)_info.RankRestriction,
                RankRestrictionType = _info.RankRestrictionType,
            });

            _onChange?.Invoke(_info);
        }

        /// <summary>
        /// 出走人数変更ボタンコールバック
        /// </summary>
        private void OnClickEntryNumButton()
        {
            if (!_info.CourseSetting.SelectedRaceId.HasValue)
            {
                Debug.LogWarning("レースIDが決定していません。");// ここには通常来ないはず
                return;
            }
            
            var maxNum = MasterDataManager.Instance.masterRace.Get(_info.CourseSetting.SelectedRaceId.Value)?.EntryNum;

            if (maxNum.HasValue)
            {
                ExhibitionRaceUtil.OpenMemberNumSelectDialog(ServerDefine.RoomMatchMinEntryNum, maxNum.Value, n =>
                {
                    _info.EntryNum = n;

                    if (_info.EntryNum < _info.PrivateEntryNum)
                    {
                        _info.PrivateEntryNum = _info.EntryNum;
                    }

                    UpdateInfo(_info);
                }, TextId.CustomRace0043.Text(), _info.EntryNum);
            }
            else
            {
                Debug.LogWarning("RaceIdに対応するMasterRaceがありません: " + _info.CourseSetting.SelectedRaceId.Value);
            }
        }

        #endregion
    }
}
