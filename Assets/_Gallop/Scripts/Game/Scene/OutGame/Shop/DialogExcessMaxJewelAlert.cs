using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 最大ジュエル超過警告ダイアログ
    /// </summary>
    public class DialogExcessMaxJewelAlert : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// 購入前ジュエル
        /// </summary>
        [SerializeField]
        private TextCommon _beforeJewel;

        /// <summary>
        /// 購入後ジュエル
        /// </summary>
        [SerializeField]
        private TextCommon _afterJewel;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// 開く
        /// </summary>
        public static void Open(int beforeJewel, int afterJewel)
        {
            var content = LoadAndInstantiatePrefab<DialogExcessMaxJewelAlert>(ResourcePath.DIALOG_EXCESS_MAX_JEWEL_ALERT);
            var data = content.CreateDialogData();
            content.Setup(beforeJewel, afterJewel);

            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Shop0090.Text();
            data.CenterButtonText = TextId.Common0007.Text();

            return data;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="beforeJewel"></param>
        /// <param name="afterJewel"></param>
        private void Setup(int beforeJewel, int afterJewel)
        {
            _beforeJewel.text = beforeJewel.ToCommaSeparatedString();
            _afterJewel.text = afterJewel.ToCommaSeparatedString();
        }

        #endregion
    }
}
