using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// カード交換の実行確認ダイアログ
    /// (既に所持済みと交換)
    /// </summary>
    public class DialogItemExchangeConversionConfirmCard : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// 変換されるアイテムのアイコン
        /// </summary>
        [Header("ピースアイコン")]
        [SerializeField]
        private ItemIcon _itemIcon;

        /// <summary>
        /// テキスト
        /// </summary>
        [Header("メッセージ")]
        [SerializeField]
        private TextCommon _messageText;

        /// <summary>
        /// 所持汎用ピース
        /// </summary>
        [Header("所持")]
        [SerializeField]
        private PartsBeforeAfterCount _havingItemCount;
        
        /// <summary>
        /// 消費アイテム
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _payItemCount;

        /// <summary>
        /// 消費アイテムアイコン
        /// </summary>
        [SerializeField]
        private ImageCommon _payItemIcon;

        /// <summary>
        /// 消費アイテムアイコン
        /// </summary>
        [SerializeField]
        private RawImageCommon _payItemIconRawImage;

        /// <summary>
        /// 残り交換回数
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _exchangeCount;

        /// <summary>
        /// 警告テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _alertText;

        #endregion

        #region Member

        /// <summary>
        /// 交換マスタ
        /// </summary>
        private MasterItemExchange.ItemExchange _itemExchange;

        /// <summary>
        /// ダイアログ
        /// </summary>
        private DialogCommon _dialogCommon;

        /// <summary>
        /// 交換完了時のコールバック
        /// </summary>
        private Action<ItemExchangeResult> _onComplete;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="itemExchange"></param>
        /// <param name="onComplete"></param>
        public static void Open(MasterItemExchange.ItemExchange itemExchange, Action<ItemExchangeResult> onComplete)
        {
            const string PATH = ResourcePath.DIALOG_ITEM_EXCHANGE_CONVERSION_CONFIRM_CARD;
            var content = LoadAndInstantiatePrefab<DialogItemExchangeConversionConfirmCard>(PATH);
            var dialogData = content.CreateDialogData();

            content.SetupCallback(dialogData);
            var dialog = DialogManager.PushDialog(dialogData);
            content.Setup(itemExchange, dialog, onComplete);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Shop0040.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Shop0043.Text();
            dialogData.AutoClose = false;

            return dialogData;
        }

        /// <summary>
        /// コールバックを設定
        /// </summary>
        /// <param name="dialogData"></param>
        private void SetupCallback(DialogCommon.Data dialogData)
        {
            dialogData.LeftButtonCallBack = dialog => dialog.Close();
            dialogData.RightButtonCallBack = ExecuteExchange;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="itemExchange"></param>
        /// <param name="dialogCommon"></param>
        /// <param name="onComplete"></param>
        public void Setup(
            MasterItemExchange.ItemExchange itemExchange,
            DialogCommon dialogCommon,
            Action<ItemExchangeResult> onComplete)
        {
            _itemExchange = itemExchange;
            _dialogCommon = dialogCommon;
            _onComplete = onComplete;

            SetupConversionItemIcon();
            SetupPayItemCount();
            SetupExchangeCount();
            SetupMessageText();
        }

        /// <summary>
        /// 変換されるアイテムアイコンをセットアップ
        /// </summary>
        private void SetupConversionItemIcon()
        {
            var masterCardData = MasterDataManager.Instance.masterCardData.Get(_itemExchange.ChangeItemId);
            if (masterCardData == null)
                return;

            var rarity = masterCardData.DefaultRarity;
            var conversionArray = MasterDataManager.Instance.masterGachaPiece.dictionary.Values
                .Where(conversion => conversion.Rarity == rarity)
                .ToArray();

            var item = conversionArray
                .FirstOrDefault(conversion => conversion.PieceType == (int) GachaDefine.GachaPieceType.Common);
            SetupItemIcon(item);
            SetupItemCount(item);
        }

        /// <summary>
        /// 汎用アイテムアイコンをセットアップ
        /// </summary>
        /// <param name="item"></param>
        private void SetupItemIcon(MasterGachaPiece.GachaPiece item)
        {
            if (item == null)
            {
                _itemIcon.SetActiveWithCheck(false);
                return;
            }

            _itemIcon.SetActiveWithCheck(true);
            _itemIcon.SetData(
                item.ItemCategory,
                item.ItemId,
                item.PieceNum,
                isInfoPop: true);
        }

        /// <summary>
        /// 汎用ピース所持数をセットアップ
        /// </summary>
        /// <param name="item"></param>
        private void SetupItemCount(MasterGachaPiece.GachaPiece item)
        {
            if (item == null)
            {
                _havingItemCount.SetActiveWithCheck(false);
                return;
            }

            _havingItemCount.SetActiveWithCheck(true);

            var itemCategory = (GameDefine.ItemCategory) item.ItemCategory;
            var beforeNum = GallopUtil.GetHaveItemNum(itemCategory, item.ItemId);
            var afterNum = beforeNum + item.PieceNum;
            _havingItemCount.Setup(beforeNum.ToCommaSeparatedString(), afterNum.ToCommaSeparatedString());
        }
        
        /// <summary>
        /// 消費アイテムをセットアップ
        /// </summary>
        private void SetupPayItemCount()
        {
            var beforeNum = WorkDataManager.Instance.ItemData.GetHaveItemNum(_itemExchange.PayItemId);
            ;
            var afterNum = beforeNum - _itemExchange.PayItemNum;
            _payItemCount.Setup(beforeNum.ToCommaSeparatedString(), afterNum.ToCommaSeparatedString());

            //アイテムアイコン
            var payItemCategory = _itemExchange.PayItemCategory.ToItemCategory();
            GallopUtil.SetUseIconImage(payItemCategory, _itemExchange.PayItemId , _payItemIcon , _payItemIconRawImage);
        }

        /// <summary>
        /// 警告テキストをセットアップ
        /// </summary>
        private void SetupMessageText()
        {
            var itemCategory = _itemExchange.PayItemCategory.ToItemCategory();
            var itemName = GallopUtil.GetItemName(itemCategory, _itemExchange.PayItemId);
            _messageText.text = TextUtil.Format(TextId.Shop0059.Text(), itemName);
            _alertText.SetTextWithCustomTag(TextId.Gacha0068.Text());
        }

        /// <summary>
        /// 交換回数をセットアップ
        /// </summary>
        private void SetupExchangeCount()
        {
            if (_itemExchange.ChangeItemLimitNum > 0)
            {
                var exchangeCount = WorkDataManager.Instance.Exchange.GetExchangeCount(_itemExchange.Id);
                var remain = _itemExchange.ChangeItemLimitNum - exchangeCount;
                _exchangeCount.Setup(string.Format(TextId.Shop0033.Text(), remain.ToString()));
                _exchangeCount.SetBeforeFontColor(FontColorType.Emphasis);
            }
            else
            {
                _exchangeCount.Setup(TextId.Common0099.Text());
                _exchangeCount.SetBeforeFontColor(FontColorType.Brown);
            }
        }

        /// <summary>
        /// 交換を実行する
        /// </summary>
        /// <param name="dialog"></param>
        private void ExecuteExchange(DialogCommon dialog)
        {
            ItemExchanger.Exchange(_itemExchange, 1, OnExchangeComplete);
        }

        /// <summary>
        /// 交換完了時
        /// </summary>
        /// <param name="result"></param>
        private void OnExchangeComplete(ItemExchangeResult result)
        {
            _dialogCommon.Close();
            _onComplete?.Invoke(result);
        }

        #endregion
    }
}
