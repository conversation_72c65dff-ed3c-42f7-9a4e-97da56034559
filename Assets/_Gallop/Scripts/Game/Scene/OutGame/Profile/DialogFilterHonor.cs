using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 称号絞り込みダイアログ
    /// </summary>
    public class DialogFilterHonor : DialogInnerBase
    {
        #region DialogInnerBase
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }
        #endregion


        #region 定義

        /// <summary>
        /// 取得状況
        /// </summary>
        public enum GetStatus
        {
            AcquiredOnly,
            NotAcquiredOnly,
            All,
        }

        /// <summary>
        /// フィルタリング設定
        /// </summary>
        public class FilterSetting
        {
            /// <summary>
            /// 取得状況
            /// </summary>
            public GetStatus Status;
        }

        #endregion
        

        #region SerializeField
        [SerializeField]
        private ToggleCommon _toggleAcquired = null;

        [SerializeField]
        private ToggleCommon _toggleNotAcquired = null;

        #endregion
        

        #region メソッド

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="prevSetting"></param>
        /// <param name="onExecuteFilter"></param>
        public static void Open(FilterSetting prevSetting, Action<FilterSetting> onExecuteFilter)
        {
            GameObject clone = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_FILTER_HONOR_PATH));
            var component = clone.GetComponent<DialogFilterHonor>();
            component.Setup(prevSetting);

            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = clone;
            dialogData.Title = Localize.Get(TextId.Outgame0015);
            dialogData.LeftButtonText = Localize.Get(TextId.Common0004);
            dialogData.RightButtonText = Localize.Get(TextId.Common0003);
            dialogData.RightButtonCallBack = (dialog) =>
            {
                var setting = new FilterSetting();
                bool isOnAcquired = component._toggleAcquired.isOn;
                bool isOnNotAcquired = component._toggleNotAcquired.isOn;
                if (isOnAcquired && isOnNotAcquired)
                {
                    // 全て
                    setting.Status = GetStatus.All;
                }
                else if (isOnAcquired)
                {
                    // 取得済みのみ
                    setting.Status = GetStatus.AcquiredOnly;
                }
                else if (isOnNotAcquired)
                {
                    // 未取得のみ
                    setting.Status = GetStatus.NotAcquiredOnly;
                }
                else
                {
                    // 何も選択されてない場合はすべて表示
                    setting.Status = GetStatus.All;
                }
                onExecuteFilter?.Invoke(setting);
            };

            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="prevSetting"></param>
        private void Setup(FilterSetting prevSetting)
        {
            if (prevSetting == null) { return; }

            switch (prevSetting.Status)
            {
                case GetStatus.AcquiredOnly:
                    _toggleAcquired.isOn = true;
                    _toggleNotAcquired.isOn = false;
                    break;
                case GetStatus.NotAcquiredOnly:
                    _toggleAcquired.isOn = false;
                    _toggleNotAcquired.isOn = true;
                    break;
                default:
                    _toggleAcquired.isOn = true;
                    _toggleNotAcquired.isOn = true;
                    break;
            }
        }
        #endregion
    }
}
