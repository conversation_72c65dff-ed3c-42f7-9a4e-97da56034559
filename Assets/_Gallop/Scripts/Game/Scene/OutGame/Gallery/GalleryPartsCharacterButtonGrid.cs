using UnityEngine;
using System;
using System.Collections.Generic;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// キャラクターボタンを縦横等間隔に並べるクラス
    /// ボタンが多くなると下へ伸びて行く
    /// </summary>
    public class GalleryPartsCharacterButtonGrid : MonoBehaviour
    {
        /// <summary>
        /// 複製元となるボタン
        /// </summary>
        [SerializeField]
        private CharacterButton _originalButton = null;

        /// <summary>
        /// 1行の最大ボタン数
        /// </summary>
        [SerializeField, Min(1)]
        private int _maxButtonInRow = 5;

        /// <summary>
        /// ボタンの実体
        /// </summary>
        private List<CharacterButton> _buttons = new List<CharacterButton>();
        public List<CharacterButton> Buttons => _buttons;

        /// <summary>
        /// 行数
        /// </summary>
        private int _rowNum = 1;
        public int RowNum => _rowNum;

        /// <summary>
        /// 全ボタン更新
        /// </summary>
        public void Refresh(List<CharacterButtonInfo> buttonInfoList, Func<string, Texture> loadTextureFastFunc = null, Action<CharacterButton> charaButtonAdditionalSetup = null)
        {
            // 複製元となるボタンは複製の瞬間以外は非アクティブ
            _originalButton.SetActiveWithCheck(false);

            // ボタンの実体が足りない場合は追加
            if (_buttons.Count < buttonInfoList.Count)
            {
                var addNum = buttonInfoList.Count - _buttons.Count;
                for (int i = 0; i < addNum; ++i)
                {
                    AddButton();
                }
            }

            // 実体のあるボタンの数だけ回す
            for (int i = 0; i < _buttons.Count; ++i)
            {
                _buttons[i].gameObject.SetActive(true);
                _buttons[i].transform.localScale = Math.VECTOR3_ONE;

                var myButton = _buttons[i].MyButton;
                var canvasGroup = myButton.CanvasGroup;

                // CharacterButtonInfoがある場合は表示
                if (i < buttonInfoList.Count)
                {
                    if (canvasGroup != null)
                    {
                        canvasGroup.alpha = 1f;
                    }
                    if (myButton != null)
                    {
                        myButton.enabled = true;
                    }

                    if (loadTextureFastFunc != null)
                    {
                        _buttons[i].LoadTextureFastFunc = loadTextureFastFunc;
                    }
                    var buttonInfo = buttonInfoList[i];
                    _buttons[i].Setup(buttonInfo);
                }
                // CharacterButtonInfoが無い場合は非表示
                else
                {
                    _buttons[i].gameObject.SetActive(false);
                }

                // キャラクターボタンに対して追加で行いたいセットアップ処理があるなら実行
                charaButtonAdditionalSetup?.Invoke(_buttons[i]);
            }

            // 行数計算
            _rowNum = (_buttons.Count / _maxButtonInRow) + 1;
            _rowNum = Mathf.Max(_rowNum, 0);
        }

        /// <summary>
        /// ボタンを追加
        /// </summary>
        private void AddButton()
        {
            // ボタンの実体を生成
            _originalButton.SetActiveWithCheck(true);
            {
                var newButton = Instantiate(_originalButton, transform); // 複製
                newButton.transform.SetParent(this.transform);
                newButton.SetActiveWithCheck(false);
                _buttons.Add(newButton);
            }
            _originalButton.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 全ボタンのCanvasGroupの濃度をセット
        /// </summary>
        public void SetButtonCanvasGroupAlpha(float alpha)
        {
            if (_buttons == null || _buttons.Count <= 0)
                return;

            foreach (var button in _buttons)
            {
                button.CanvasGroup.alpha = alpha;
            }
        }

        /// <summary>
        /// 指定の行にアニメを再生させます
        /// </summary>
        public void PlayAnimationsRow(Sequence sequence, int rowIndex, TweenAnimation.PresetType presetType, float delay)
        {
            // ボタンが無いなら何もしない
            if (_buttons == null || _buttons.Count <= 0)
                return;

            // 指定された行が存在しないなら何もしない
            if (rowIndex >= _rowNum)
                return;

            var startButtonIndex = rowIndex * _maxButtonInRow;
            var endButtonIndex = startButtonIndex + _maxButtonInRow - 1;
            endButtonIndex = Mathf.Min(endButtonIndex, (_buttons.Count - 1));
            for (int i = startButtonIndex; i <= endButtonIndex; ++i)
            {
                var charaButtonObj = _buttons[i].gameObject;
                sequence.Join(TweenAnimationBuilder.CreateSequence(charaButtonObj, presetType).SetDelay(delay));
            }
        }
    }
}
