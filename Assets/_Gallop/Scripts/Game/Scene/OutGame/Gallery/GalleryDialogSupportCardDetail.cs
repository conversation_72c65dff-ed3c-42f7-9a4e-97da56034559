using Gallop;
using System;
using System.Collections;
using System.Collections.Generic;
using Cute.UI;
using UnityEngine;
using System.Linq;

namespace Gallop
{
    public class GalleryDialogSupportCardDetailModel
    {
        public int SupportCardId { get; set; }

        private MasterSupportCardData.SupportCardData _supportCardData = null;
        public MasterSupportCardData.SupportCardData SupportCardData
        {
            get
            {
                if(_supportCardData == null)
                {
                    _supportCardData = MasterDataManager.Instance.masterSupportCardData.Get(SupportCardId);
                }
                return _supportCardData;
            }
        }

        private List<MasterSingleModeStoryData.SingleModeStoryData> _supportCardStoryDataList = null;
        public List<MasterSingleModeStoryData.SingleModeStoryData> SupportCardStoryDataList
        {
            get
            {
                if(_supportCardStoryDataList == null)
                {
                    _supportCardStoryDataList = CreateGallerySupportCardStoryList();
                }
                return _supportCardStoryDataList;
            }
        }

        private List<MasterSingleModeStoryData.SingleModeStoryData> CreateGallerySupportCardStoryList()
        {
            //(newしないとリスト操作でMasterData側のキャッシュに影響を与える)
            //サポカに紐づくものとサポカキャラに紐づくものをそれぞれ抜き出し
            var supportCardStoryDataList = new List<MasterSingleModeStoryData.SingleModeStoryData>(MasterDataManager.Instance.masterSingleModeStoryData.GetListWithSupportCardIdOrderByStoryIdAsc(SupportCardData.Id));
            var supportCardCharaStoryDataList =  new List<MasterSingleModeStoryData.SingleModeStoryData>(MasterDataManager.Instance.masterSingleModeStoryData.GetListWithSupportCharaIdOrderByStoryIdAsc(SupportCardData.CharaId));
            //キャラに紐づく→サポカに紐づくイベントの順
            supportCardCharaStoryDataList.AddRange(supportCardStoryDataList);

            if(!supportCardCharaStoryDataList.Any())
            {
                return null;
            }

            //ギャラリーに並べるフラグを抜き出し
            var gallerySupportCardStoryDataList = supportCardCharaStoryDataList.FindAll(n => n.GalleryFlag == (int)GalleryPartsEventListItem.DialogType.SupportCard);

            //GalleryListIdの値ででソート
            gallerySupportCardStoryDataList.Sort((a, b) => a.GalleryListId - b.GalleryListId);

            //解放未解放分類して合成
            var storyDataList = new List<MasterSingleModeStoryData.SingleModeStoryData>();
            var closeStoryDataList = new List<MasterSingleModeStoryData.SingleModeStoryData>();
            foreach (var supportCardEvent in gallerySupportCardStoryDataList)
            {
                if (WorkDataManager.Instance.GalleryData.IsReadEventData(supportCardEvent.StoryId))
                {
                    if (storyDataList.Find(n => n.StoryId == supportCardEvent.StoryId) == null)
                    {
                        storyDataList.Add(supportCardEvent);
                    }
                }
                else
                {
                    if (closeStoryDataList.Find(n => n.StoryId == supportCardEvent.StoryId) == null)
                    {
                        closeStoryDataList.Add(supportCardEvent);
                    }
                }
            }
            storyDataList.AddRange(closeStoryDataList);

            return storyDataList;
        }

        public void CalcNewBadge()
        {
            // isNewフラグチェック
            WorkDataManager.Instance.GalleryData.CheckAndChangeNewFlag(SupportCardStoryDataList);
        }
    }


    public class GalleryDialogSupportCardDetail : DialogInnerBase
    {
        #region MyRegion
        /// <summary>
        /// ヘッダー背景の画像の色。α値が40%になる
        /// </summary>
        private static readonly Color CARD_BG_COLOR = new Color(1.0f, 1.0f, 1.0f, 40 / 100f);
        #endregion

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        #region member

        [SerializeField]
        private RawImageCommon _headerRarityBg = null;
        [SerializeField]
        private RawImageCommon _headerSupportBg = null;
        [SerializeField]
        private RawImageCommon _headerFrontBg = null;

        [SerializeField]
        private LoopScroll _eventListScroll = null;

        [SerializeField]
        private CharacterButton _characterButton = null;

        [SerializeField]
        private TextCommon _nickName = null;

        [SerializeField]
        private TextCommon _characterName = null;

        [SerializeField]
        private TextCommon _openEventCount = null;

        [SerializeField]
        private TextCommon _allEventCount = null;

        [SerializeField]
        private ScrollRectCommon _scrollRectCommon = null;


        private GalleryDialogSupportCardDetailModel _galleryDialogSupportCardDetailModel = null;

        private System.Action<MasterSingleModeStoryData.SingleModeStoryData, float> _onClickPlayStory = null;

        #endregion

        #region method

        /// <summary>
        /// イベントタブの初回設定
        /// </summary>
        /// <param name="supporCardId"></param>
        private void SetUpEventTab(int openEventNum, int allEventNum)
        {
            var supportCardStoryDataList = _galleryDialogSupportCardDetailModel.SupportCardStoryDataList;
            if (_eventListScroll != null)
            {
                if (supportCardStoryDataList.Any())
                {
                    _eventListScroll.Setup<GalleryPartsEventListItem>(supportCardStoryDataList.Count, (item) => OnUpdateEventListItem(item));
                }
            }

            if (allEventNum == 0)
            {
                allEventNum = supportCardStoryDataList.Count;
            }

            _openEventCount.text = openEventNum.ToString();
            _allEventCount.text = allEventNum.ToString();
        }

        private void OnUpdateEventListItem(GalleryPartsEventListItem item)
        {
            var supportCardStoryDataList = _galleryDialogSupportCardDetailModel.SupportCardStoryDataList;
            item.Init(supportCardStoryDataList[item.ItemIndex], OnClickPlayStoryButton);
        }

        private void OnClickPlayStoryButton(MasterSingleModeStoryData.SingleModeStoryData singleModeStoryData)
        {
            _onClickPlayStory?.Invoke(singleModeStoryData, _scrollRectCommon.verticalNormalizedPosition);
        }

        /// <summary>
        /// サポカ情報追加
        /// </summary>
        /// <param name="cardData"></param>
        private void SetUpSupportCardInfo(WorkSupportCardData.SupportCardData supportCardData, DialogCommon.Data dialogData)
        {
            //ヘッダー
            var rarityBgPath = ResourcePath.GetSupportCardDetailRarityBgPath(supportCardData.GetRarity());
            if (string.IsNullOrEmpty(rarityBgPath))
            {
                _headerRarityBg.gameObject.SetActive(false);
            }
            else
            {
                _headerRarityBg.texture = ResourceManager.LoadOnHash<Texture2D>(rarityBgPath, dialogData.DialogHash);
                _headerRarityBg.gameObject.SetActive(true);
            }
            _headerFrontBg.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.SUPPORTCARD_DETAIL_FRONT_BG_PATH, dialogData.DialogHash);

            _headerSupportBg.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.GetSupportCardTexTexturePath(supportCardData.GetSupportCardId()), dialogData.DialogHash);
            _headerSupportBg.color = CARD_BG_COLOR;

            //位置やサイズ、回転調整などは画像毎に設定する
            var masterSupportCard = supportCardData.GetMasterSupportCard();
            var supportBgTransform = _headerSupportBg.rectTransform;

            //位置
            var setPos = new Vector2(Math.MasterInt2Float(masterSupportCard.DetailPosX), Math.MasterInt2Float(masterSupportCard.DetailPosY));
            supportBgTransform.anchoredPosition = setPos;

            //サイズはスケールをいじって調整（プランナーからこちらで調整したいとの要望）
            var baseScale = supportBgTransform.localScale;
            baseScale.x = Math.MasterInt2Float(masterSupportCard.DetailScale);
            baseScale.y = Math.MasterInt2Float(masterSupportCard.DetailScale);
            supportBgTransform.localScale = baseScale;

            //角度
            var setRotation = new Vector3(0f, 0f, Math.MasterInt2Float(masterSupportCard.DetailRotZ));
            supportBgTransform.eulerAngles = setRotation;

            var charaButtonInfo = new CharacterButtonInfo
            {
                Id = supportCardData.SupportCardId,
                IdType = CharacterButtonInfo.IdTypeEnum.Support,
                IconSizeType = IconBase.SizeType.SupportCardNormal,
                LimitBreakCount = supportCardData.LimitBreakCount,
                Level = supportCardData.Level,
                Rarity = supportCardData.GetRarity(),
                EnableRarity = true,
                EnableButton = false,
                EnableObtain = true
            };
            _characterButton.Setup(charaButtonInfo);

            _characterName.text = supportCardData.GetCharaName();
            _nickName.text = supportCardData.GetTitleName();
        }

        /// <summary>
        /// DialogDataを作成
        /// </summary>
        private static DialogCommon.Data GalleryCreateDialogData(GalleryDialogSupportCardDetailModel model, int openEventCount, int allEventCount, float scrollPos, Action<MasterSingleModeStoryData.SingleModeStoryData, float> onClickPlayStory, Action onClose = null)
        {
            var galleryDialogSupportCardDetail = LoadAndInstantiatePrefab<GalleryDialogSupportCardDetail>(ResourcePath.DIALOG_GALLERY_SUPPORTCARD_DETAIL_PATH);
            var dialogData = galleryDialogSupportCardDetail.CreateDialogData();

            // Model保存
            galleryDialogSupportCardDetail._galleryDialogSupportCardDetailModel = model;


            //キャラ情報設定
            var charaWorkData = WorkDataManager.Instance.SupportCardData.GetSupportCardData(model.SupportCardId);
            galleryDialogSupportCardDetail.SetUpSupportCardInfo(charaWorkData, dialogData);

            // イベント設定
            galleryDialogSupportCardDetail._onClickPlayStory = onClickPlayStory;

            // リスト表示設定
            galleryDialogSupportCardDetail.SetUpEventTab(openEventCount, allEventCount);

            //ダイアログ設定
            dialogData.ContentsObject = galleryDialogSupportCardDetail.gameObject;
            dialogData.Title = TextId.Gallery0006.Text();
            dialogData.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.BeginOpenCallback = (dialog) =>
            {
                //開けた瞬間にスクロール位置設定
                galleryDialogSupportCardDetail._scrollRectCommon.verticalNormalizedPosition = scrollPos;
            };
            dialogData.DestroyCallBack = () =>
            {
                onClose?.Invoke();
            };
            return dialogData;
        }

        /// <summary>
        /// ダイアログオープン(サポカ)
        /// </summary>
        /// <param name="onClose"></param>
        public static DialogCommon OpenSupportCard(int supportCardId, int openEventCount, int allEventCount, float scrollPos, Action<MasterSingleModeStoryData.SingleModeStoryData, float> onClickPlayStory, Action onClose = null)
        {
            //WorkData取得
            var galleryData = WorkDataManager.Instance.GalleryData;
            //ギャラリー側にサポカ情報渡す
            galleryData.OverrideSupportCardID = supportCardId;

            // Model作成
            var model = new GalleryDialogSupportCardDetailModel();
            model.SupportCardId = supportCardId;

            var dialogData = GalleryCreateDialogData(model, openEventCount, allEventCount, scrollPos, onClickPlayStory, onClose);
            var dialog = DialogManager.PushDialog(dialogData);

            return dialog;
        }

        #endregion

    }
}