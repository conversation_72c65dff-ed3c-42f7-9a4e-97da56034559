using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    public class CharacterCardLimitBreakCutView : ViewBase
    {
        [field: SerializeField, RenameField]
        public Transform LimitBreakCutRoot = null;

        [field: SerializeField, RenameField]
        public ButtonCommon <PERSON>on = null;
    }

    public class CharacterCardLimitBreakCutViewInfo : IViewInfo
    {
        public int CardId = GameDefine.INVALID_CARD_ID;
        public SceneDefine.ViewId NextViewId = SceneDefine.ViewId.None;
        public IViewInfo NextViewInfo = null;

        public CharacterCardLimitBreakCutViewInfo(int cardId, SceneDefine.ViewId viewId = SceneDefine.ViewId.None, IViewInfo viewInfo = null)
        {
            CardId = cardId;
            NextViewId = viewId;
            NextViewInfo = viewInfo;
        }
    }

    public class CharacterCardLimitBreakCutViewController : ViewControllerBase<CharacterCardLimitBreakCutView>
    {
        private CharacterCardLimitBreakCutViewInfo _info = null;
        private LimitBreakCut _limitBreakCut;

        public override void RegisterDownload(DownloadPathRegister register)
        {
            base.RegisterDownload(register);

            LimitBreakCut.RegisterDownload(register);
        }
        public override IEnumerator InitializeView()
        {
            _info = GetViewInfo<CharacterCardLimitBreakCutViewInfo>();

            return base.InitializeView();
        }

        public override void UpdateView()
        {
            base.UpdateView();
            _limitBreakCut?.AlterUpdate();
        }

        public override void LateUpdateView()
        {
            base.LateUpdateView();
            _limitBreakCut?.AlterLateUpdate();
        }

        public override void UpdateViewBeforeLoadingOut()
        {
            base.UpdateViewBeforeLoadingOut();
            _limitBreakCut?.AlterUpdate();
        }
        public override void LateUpdateViewBeforeLoadingOut()
        {
            base.LateUpdateViewBeforeLoadingOut();
            _limitBreakCut?.AlterLateUpdate();
        }

        public override IEnumerator PlayInView()
        {
            FadeManager.Instance.FadeOut(GameDefine.COLOR_WHITE, 0f);
            var info = new LimitBreakCut.Info(new WorkCardData.CardData(_info.CardId, GameDefine.DRESS_CHANGE_RARITY), 1);
            info.OnEnd = OnEnd;
            var cut = LimitBreakCut.Create(info, _view.LimitBreakCutRoot);
            cut.Play();
            _limitBreakCut = cut;
            return base.PlayInView();
        }

        /// <summary>
        /// 終了時コールバック
        /// </summary>
        private void OnEnd()
        {
            _limitBreakCut = null;
            if (_info.NextViewId == SceneDefine.ViewId.None)
            {
                // 指定が無ければ前の画面に戻る
                SceneManager.Instance.BackUsingStack();
            }
            else
            {
                SceneManager.Instance.ChangeView(_info.NextViewId, _info.NextViewInfo, forceChange: true);
            }
        }
    }
}