using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cute.Cri;
namespace Gallop
{
    public class ContentOptionSound : ContentOptionBase
    {
        #region SerializeField
        // メンテコスト軽減のため、出来る限り表示される順番で項目を追加してください
        [SerializeField]
        private OptionSoundVolumeSlider _volumeBGM = null;
        [SerializeField]
        private OptionSoundVolumeSlider _volumeSE = null;
        [SerializeField]
        private OptionSoundVolumeSlider _volumeVoice = null;
        [SerializeField]
        private OptionSoundVolumeSlider _volumeJikkyo = null;
        [SerializeField]
        private OptionSoundVolumeSlider _volumeLive = null;
        [SerializeField]
        private ButtonCommon _buttonVolumeDefault = null;
        [SerializeField]
        private ToggleGroupCommon _toggleVoiceType = null;
        [SerializeField]
        private ToggleGroupCommon _toggleLiveSE = null;
        #endregion

        #region CurrentPlaybackInfo
        //BGM復帰用
        private string _currentBGMCueSheetName;
        private string _currentBGMCueName;
        private AudioPlayback _currentVoicePlayback;
        private AudioPlayback _previewMusicPlayback;
        #endregion

        #region const
        //音量のプレビューで使用するSEやボイスのキューシートおよびキュー情報

        private const string JIKKYO_CUESHEET_NAME_FEMALE = "snd_voi_chara_jky_01_1001";
        private const string JIKKYO_CUE_NAME_FEMALE = "snd_voi_race_jky_chara_100103";

        private const string JIKKYO_CUESHEET_NAME_MALE = "snd_voi_chara_jky_02_1001";
        private const string JIKKYO_CUE_NAME_MALE = "snd_voi_race_jky_chara_100103";

        private const string VOICE_CUESHEET_NAME = "snd_voi_title";
        private const string VOICE_CUE_NAME = "snd_voi_title_1001";

        private const string LIVE_CUESHEET_NAME = "1006/snd_bgm_live_1006_preview_02";
        private const string LIVE_CUE_NAME = "snd_bgm_live_1006_preview_02";
        #endregion

        #region InitCueSheet
        /// <summary>
        /// 音量プレビューで使用するCueSheetのDLを登録する
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownloadPreviewSoundCueSheets(DownloadPathRegister register)
        {
            var cueSheetsJikkyo = new List<string>()
            {
                JIKKYO_CUESHEET_NAME_MALE,
                JIKKYO_CUESHEET_NAME_FEMALE
            };
            var cueSheetsLive = new List<string>()
            {
                LIVE_CUESHEET_NAME
            };
            AudioManager.Instance.RegisterDownloadByCueSheets(register, cueSheetsJikkyo, AudioManager.SubFolder.Jikkyo);
            AudioManager.Instance.RegisterDownloadByCueSheets(register, cueSheetsLive, AudioManager.SubFolder.Live);
        }

        /// <summary>
        /// DLされたCueSheetを読み込む
        /// </summary>
        public static void AddCueSheet()
        {
            AudioManager.Instance.AddCueSheet(JIKKYO_CUESHEET_NAME_MALE,AudioManager.SubFolder.Jikkyo);
            AudioManager.Instance.AddCueSheet(JIKKYO_CUESHEET_NAME_FEMALE,AudioManager.SubFolder.Jikkyo);
            AudioManager.Instance.AddSongCueSheet(0,new []{LIVE_CUESHEET_NAME});
        }
        #endregion

        #region ContentOptionBase

        public override void Setup()
        {
            // 自動で保存させない
            _autoPhysicalSave = false;

            var save = SaveDataManager.Instance.SaveLoader;
            _volumeBGM.Setup(AudioManager.Category.BGM, save.BgmVolume, !save.IsUseBgm);
            _volumeSE.Setup(AudioManager.Category.SE, save.SeVolume, !save.IsUseSe);
            _volumeVoice.Setup(AudioManager.Category.VOICE, save.VoiceVolume, !save.IsUseVoice);
            _volumeJikkyo.Setup(AudioManager.Category.JIKKYO, save.JikkyoVolume, !save.IsUseJikkyo);
            _volumeLive.Setup(AudioManager.Category.LIVE, save.LiveVolume, !save.IsUseLive);
            _buttonVolumeDefault.SetOnClick(OnClickDefaultButton);
            _toggleVoiceType.SetToggleOnFromNumber((int)save.JikkyoVoice);

            var bgmPlayBack = AudioManager.Instance.BgmPlayback;
            
            //BGMがないシーンでLiveのサンプルを再生するとLiveBGMのPlaybackが残ってしまうため
            if(bgmPlayBack.CueSheetName != LIVE_CUESHEET_NAME)
            {
                _currentBGMCueSheetName = bgmPlayBack.CueSheetName;
                _currentBGMCueName = bgmPlayBack.CueName;
            }
            SetupCallback();
            ApplyToggleFromBool(_toggleLiveSE, save.IsLiveCall);
        }


        /// <summary>
        /// 各オプション変更時のコールバックを登録
        /// </summary>
        private void SetupCallback()
        {
            // ライブ楽曲
            if (SceneManager.Instance.GetCurrentSceneId() != SceneDefine.SceneId.LiveTheater) // ライブシアターではプレビューを聴きながらボリューム調整できるのでサンプル再生はしない
            {
                // サンプルを再生
                _volumeLive.SetupSliderTouchCallback(()=>
                {
                    _previewMusicPlayback = AudioManager.Instance.PlaySe(LIVE_CUESHEET_NAME, LIVE_CUE_NAME);
                    AudioManager.Instance.PauseBgm();
                },
                ()=>
                {
                    AudioManager.Instance.ResumeBgm();
                    AudioManager.Instance.StopSe(_previewMusicPlayback);
                });
            }

            //SE
            _volumeSE.SetupOnSliderChanged(() =>
            {
                AudioManager.Instance.PlaySe(AudioId.SFX_UI_DECIDE_S_01);
            });

            //実況
            _volumeJikkyo.SetupSliderTouchCallback(null, ()=>
            {
                PlayJikkyoSampleVoiceByGender(_toggleVoiceType.GetOnIndex());
            });

            //実況者の性別
            _toggleVoiceType.SetOnSelectCallback((index) =>
            {
                PlayJikkyoSampleVoiceByGender(index);
            });

            //ボイス
            _volumeVoice.SetupSliderTouchCallback(null, PlayVoiceSample);
        }

        /// <summary>
        /// 実況者の性別に応じてサンプルを再生する
        /// indexが0ならば男性
        /// </summary>
        /// <param name="index"></param>
        private void PlayJikkyoSampleVoiceByGender(int index)
        {
            if(AudioManager.IsPlay(_currentVoicePlayback))
            {
                AudioManager.Stop(_currentVoicePlayback);
            }
            var isMale = index == 0;
            _currentVoicePlayback = AudioManager.Instance.PlayVoice(
                isMale ? JIKKYO_CUESHEET_NAME_MALE :JIKKYO_CUESHEET_NAME_FEMALE ,
                isMale ? JIKKYO_CUE_NAME_MALE : JIKKYO_CUE_NAME_FEMALE
            );
        }

        /// <summary>
        /// ボイスのサンプルを再生する
        /// </summary>
        private void PlayVoiceSample()
        {
            if(AudioManager.IsPlay(_currentVoicePlayback))
            {
                AudioManager.Stop(_currentVoicePlayback);
            }
            _currentVoicePlayback = AudioManager.Instance.PlayVoice(VOICE_CUESHEET_NAME,VOICE_CUE_NAME);
        }

        public override void Save()
        {
            // ContentOptionSound が非アクティブ設定な場合は保存しない（ホームオプションでのライブページなど）
            if (!gameObject.activeSelf)
                return;

            var save = SaveDataManager.Instance.SaveLoader;
            
            // 複数ページのオプションではサウンド以外のページでも保存できる
            // 非表示になっていても、アクティブ設定な項目なら保存する（ホームオプションでのサウンドページなど）
            if (_volumeBGM.gameObject.activeSelf)
            {
                save.BgmVolume = _volumeBGM.Volume;
                save.IsUseBgm = _volumeBGM.IsOn;
            }
            if (_volumeSE.gameObject.activeSelf)
            {
                save.SeVolume = _volumeSE.Volume;
                save.IsUseSe = _volumeSE.IsOn;
            }
            if (_volumeVoice.gameObject.activeSelf)
            {
                save.VoiceVolume = _volumeVoice.Volume;
                save.IsUseVoice = _volumeVoice.IsOn;
            }
            if (_volumeJikkyo.gameObject.activeSelf)
            {
                save.JikkyoVolume = _volumeJikkyo.Volume;
                save.IsUseJikkyo = _volumeJikkyo.IsOn;
            }
            if (_volumeLive.gameObject.activeSelf)
            {
                save.LiveVolume = _volumeLive.Volume;
                save.IsUseLive = _volumeLive.IsOn;
            }

            if (_toggleVoiceType.gameObject.activeSelf)
                save.JikkyoVoice = (SaveDataManager.JikkyoVoiceType)_toggleVoiceType.GetOnIndex();

            if (_toggleLiveSE.gameObject.activeSelf)
                save.IsLiveCall = GetBoolFromToggle(_toggleLiveSE);

            AudioManager.ApplyVolumeSettings();

            base.Save();
        }
        #endregion

        /// <summary>
        /// 音量を初期設定に戻す
        /// </summary>
        public void OnClickDefaultButton()
        {
            if (_volumeBGM.gameObject.activeSelf)
                _volumeBGM.Default();
            if (_volumeSE.gameObject.activeSelf)
                _volumeSE.Default();
            if (_volumeVoice.gameObject.activeSelf)
                _volumeVoice.Default();
            if (_volumeJikkyo.gameObject.activeSelf)
                _volumeJikkyo.Default();
            if (_volumeLive.gameObject.activeSelf)
                _volumeLive.Default();
        }

        /// <summary>
        /// 音量をSetup時の設定に戻す
        /// </summary>
        public void OnClickCancel()
        {
            if (_volumeBGM.gameObject.activeSelf)
                _volumeBGM.Cancel();
            if (_volumeSE.gameObject.activeSelf)
                _volumeSE.Cancel();
            if (_volumeVoice.gameObject.activeSelf)
                _volumeVoice.Cancel();
            if (_volumeJikkyo.gameObject.activeSelf)
                _volumeJikkyo.Cancel();
            if (_volumeLive.gameObject.activeSelf)
                _volumeLive.Cancel();
        }
    }
}
