using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// ガチャ演出：リスト子要素
    /// </summary>
    public class PartsGachaMovieListItem : MonoBehaviour
    {
        private const int NONE = 0;

        [SerializeField]
        private RawImageCommon _thumbnailImage = null;
        [SerializeField]
        private TextCommon _titleText = null;
        [SerializeField]
        private TextCommon _nameText = null;
        [SerializeField]
        private ButtonCommon _button = null;

        private GameDefine.CardType _cardType;
        private int _charaId = NONE;
        private int _cardId = NONE;
        
        public void SetCardId(int cardId)
        {
            var cardData = MasterDataManager.Instance.masterCardData.Get(cardId);
            if (cardData == null)
                return;

            _cardType = GameDefine.CardType.Card;
            _thumbnailImage.texture = ResourceManager.LoadOnView<Texture>(ResourcePath.GetNoteActGachaCharacterCardTexturePath(cardId));
            _titleText.text = cardData.Titlename;
            _nameText.text = cardData.Charaname;

            _charaId = cardData.CharaId;
            _cardId = cardId;
            
            _button.SetOnClick(OnClick);
        }

        public void SetSupportCardId(int supportId)
        {
            var supportData = MasterDataManager.Instance.masterSupportCardData.Get(supportId);
            if (supportData == null)
                return;

            _cardType = GameDefine.CardType.SupportCard;
            _thumbnailImage.texture = ResourceManager.LoadOnView<Texture>(ResourcePath.GetNoteActGachaSupportCardTexturePath(supportId));
            _titleText.text = supportData.Titlename;
            _nameText.text = supportData.Charaname;

            _charaId = supportData.CharaId;
            _cardId = supportId;

            _button.SetOnClick(OnClick);
        }

        private void OnClick()
        {
            var gachaList = MasterDataManager.Instance.masterGachaAvailable.dictionary.Values;
            var cardId = new MasterDataUtil.CardId();
            cardId._cardId = _cardId;
            cardId._cardType = _cardType;

            var result = new GachaCardResult(cardId._cardId, 0, true, cardId._cardType);
            var resultList = new List<GachaCardResult>();
            resultList.Add(result);
            TempData.Instance.GachaResult.Clear();
            var gachaConsumption = new GachaConsumption(GachaDefine.GachaConsumableItemType.Stone, GameDefine.CALOTTE_STONE_ID, 0, GachaDefine.GachaDrawType.Normal);

            TempData.Instance.GachaResult.ExecutionResult = new GachaExecutionResult(
                new GachaExecutableUnit(
                    GachaDefine.IGNORE_GACHA_ID,
                    1,
                    gachaConsumption,
                    GachaGuarantee.CreateEmpty(),
                    false,
                    false,
                    false,
                    false,
                    0
                ),
                GachaExecutionResult.Execution.Note,
                cardType: _cardType
            );
            TempData.Instance.GachaResult.ResultSet = new GachaResultSet(resultList.ToArray());
            TempData.Instance.GachaData.TargetGachaId = GachaDefine.IGNORE_GACHA_ID;

            DialogManager.RemoveAllDialog(()=> {
                var viewInfo = new CharacterNoteMainViewController.ViewInfo(_charaId, CharacterNoteMainViewController.ViewInfo.BeginDialogType.Gacha);
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.GachaMain, new GachaMainViewInfo(SceneDefine.ViewId.CharacterNoteMain, viewInfo));
            });
        }
        
        
    }
}
