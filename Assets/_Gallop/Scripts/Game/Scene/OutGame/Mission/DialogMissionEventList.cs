using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// イベント限定ミッションダイアログ
    /// （ミッションのあるイベントの一覧ダイアログ）
    /// </summary>
    public class DialogMissionEventList : DialogInnerBase
    {
        /// <summary>スクロールビュー</summary>
        [SerializeField]
        private LoopScroll _loopScroll = null;

        public override DialogCommon.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(List<PartsMissionEventButton.EventInfo> eventInfoList, Action onDestroy, Action onCloseMissionDialog)
        {
            var component = LoadAndInstantiatePrefab<DialogMissionEventList>(ResourcePath.DIALOG_MISSION_EVENT_LIST);
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.Outgame0601.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();

            var dialog = DialogManager.PushDialog(dialogData);
            dialog.AddDestroyCallBack(onDestroy);
            component.Setup(eventInfoList, dialog, onCloseMissionDialog);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(List<PartsMissionEventButton.EventInfo> eventInfoList, DialogCommon dialog, Action onCloseMissionDialog)
        {
            // 項目のセットアップ用パラメータを作成
            var itemSetupParamList = new List<PartsMissionEventListItem.SetupParameter>();
            foreach (var eventInfo in eventInfoList)
            {
                // 項目を表示可能か、バッジが表示可能かを更新
                eventInfo.Update();

                if (!eventInfo.IsShowable)
                {
                    continue;
                }

                var param = new PartsMissionEventListItem.SetupParameter(eventInfo);
                itemSetupParamList.Add(param);
            }

            // EndDateが早い順にソート
            itemSetupParamList.OrderByDescending(param => param.EventInfo.EndDate);

            // スクロールビュー作成
            _loopScroll.Setup(itemSetupParamList.Count, (item) =>
            {
                var missionEventListItem = item as PartsMissionEventListItem;
                if (missionEventListItem != null)
                {
                    missionEventListItem.UpdateItem(itemSetupParamList[missionEventListItem.ItemIndex],
                        onChangeView: () =>
                        {
                            dialog.Close();
                        },
                        onCloseMissionDialog: onCloseMissionDialog
                        );
                }
            });
        }
    }
}