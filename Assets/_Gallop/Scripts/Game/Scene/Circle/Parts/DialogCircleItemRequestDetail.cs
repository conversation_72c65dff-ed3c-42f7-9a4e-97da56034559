using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// アイテムリクエスト：寄付数確認ダイアログ
    /// </summary>
    public class DialogCircleItemRequestDetail : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Root;
        }

        /// <summary>
        /// ダイアログ開く
        /// </summary>
        /// <param name="itemRequest"></param>
        public static void PushDialog(WorkCircleChatData.ItemRequestInfo itemRequest)
        {
            if (itemRequest == null)
                return;

            if (itemRequest.IsEnd)
            {
                //受け取りAPI
                SendRecieveAPI(itemRequest, () => PushDialogInternal(itemRequest));
            }
            else
            {
                //共有リストのリスト取得APIを実行する
                DialogCircleShareItemList.SendGetListApi(DialogCircleShareItemList.TabStatus.Item, list =>
                {
                    //最新取得の結果、終了していた場合は受け取りへ流す
                    if (itemRequest.IsEnd)
                    {
                        SendRecieveAPI(itemRequest, () => PushDialogInternal(itemRequest));
                    }
                    else
                    {
                        PushDialogInternal(itemRequest);
                    }
                });
            }
        }

        //アイテム受け取りAPI実行
        private static void SendRecieveAPI(WorkCircleChatData.ItemRequestInfo itemRequest, System.Action onSuccess)
        {
            var req = new CircleItemRequestReceiveRequest();
            req.Send(res => {
                if (res == null || res.data == null)
                    return;

                var presentNum = GallopUtil.GetInPresentNum(res);
                var rewardList = WorkDataUtil.SetRewardSummaryInfo(res.data.reward_summary_info);

                //受け取れた合計数を出す
                var totalNum = 0;
                for (int i = 0; i < rewardList.Count; i++)
                    totalNum += rewardList[i].ItemNum;

                if (presentNum > 0)
                {
                    //プレゼントに送られたものがあればその旨を表示した後にonSuccess
                    GallopUtil.OpenDialogRewardOver(totalNum == 0, onSuccess);
                }
                else
                {
                    onSuccess();
                }

                //寄付内容の更新
                WorkDataManager.Instance.CircleChatData.UpdateChatUserData(res.data.circle_chat_user_array);
                itemRequest.UpdateDonateInfoList(res.data.circle_item_donate_array);

                //アイテムリクエストデータ削除
                WorkDataManager.Instance.CircleChatData.RemoveItemRequestData(itemRequest.RequestId);
            });
        }

        private static void PushDialogInternal(WorkCircleChatData.ItemRequestInfo itemRequest)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CIRCLE_ITEM_REQUEST_DETAIL_PATH));
            var component = instance.GetComponent<DialogCircleItemRequestDetail>();
            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = instance;
            dialogData.Title = TextId.Circle0233.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();

            DialogManager.PushDialog(dialogData);
            component.Initialize(itemRequest);
        }
        
        [SerializeField]
        private TextCommon _textDescription = null;
        [SerializeField]
        private ItemDescription _itemDescription = null;
        [SerializeField]
        private TextCommon _textProgress = null;
        [SerializeField]
        private ImageCommon _imageProgress = null;
        [SerializeField]
        private GameObject _prefabCircleItemRequestUserInfo = null;
        [SerializeField]
        private Transform _transformScrollViewContents = null;
        [SerializeField]
        private GameObject _objRemainTime = null;
        [SerializeField]
        private TextCommon _textRemainTime = null;
        [SerializeField]
        private TextCommon _textEnd = null;
        [SerializeField]
        private GameObject _emptyObj = null;
        
        private float _progressWidth = 0f;


        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="itemRequestInfo"></param>
        private void Initialize(WorkCircleChatData.ItemRequestInfo itemRequestInfo)
        {
            _progressWidth = _imageProgress.rectTransform.sizeDelta.x;
            
            _itemDescription.Setup(GameDefine.ItemCategory.TRAINING, itemRequestInfo.ItemId, false);

            _textProgress.text = TextId.Common0177.Format(itemRequestInfo.GetDonatedNum(), ServerDefine.MaxDonateNumTotal);

            var size = _imageProgress.rectTransform.sizeDelta;
            size.x = _progressWidth * itemRequestInfo.GetDonatedNum() / ServerDefine.MaxDonateNumTotal;
            _imageProgress.rectTransform.sizeDelta = size;
            

            if(itemRequestInfo.IsEnd)
            {
                // 終わってる
                _textDescription.text = TextId.Circle0256.Format(itemRequestInfo.GetDonatedNum());
            }
            else
            {
                _textDescription.text = TextId.Circle0259.Text();
                _textRemainTime.text = itemRequestInfo.GetRemainTimeString();
            }

            _objRemainTime.SetActive(!itemRequestInfo.IsEnd);
            _textEnd.gameObject.SetActive(itemRequestInfo.IsEnd);

            //Maxの時は文言変更
            if(itemRequestInfo.IsMax)
            {
                _textEnd.TextId = TextId.Circle0352;
            }

            GenerateUserInfoView(itemRequestInfo.ItemDonateInfoList);
        }

        /// <summary>
        /// アイテムくれた人の情報を表示
        /// </summary>
        private void GenerateUserInfoView(List<WorkCircleChatData.ItemDonateInfo> itemDonateInfoList)
        {
            if(itemDonateInfoList == null || itemDonateInfoList.Count == 0)
            {
                _emptyObj.SetActive(true);
                return;
            }

            _emptyObj.SetActive(false);
            foreach (var donateInfo in itemDonateInfoList)
            {
                var obj = Instantiate(_prefabCircleItemRequestUserInfo, _transformScrollViewContents);
                obj.GetComponent<CircleItemRequestUserInfo>().Setup(donateInfo);
            }
        }
        
    }
}
