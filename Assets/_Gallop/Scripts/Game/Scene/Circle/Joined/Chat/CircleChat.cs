using Cute.Http;
using System;
using System.Linq;
using System.Collections.Generic;
using Cute.UI;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// チャット機能
    /// </summary>
    public class CircleChat : MonoBehaviour
    {
        #region 定義

        /// <summary>ポーリング停止の場合のポーリング間隔</summary>
        private const int INVALID_POLLING_TIME = 0;
        /// <summary>デフォルトのポーリング間隔</summary>
        private const int DEFAULT_POLLING_TIME = 3;
        /// <summary>チャット送信後この期間はチャットを送れない、到着にムラがあるためサーバー側で設定されてるクールタイムより少し長めにしてある</summary>
        private const float CHAT_INTERVAL_TIME = 1.1f;

        /// <summary>デフォルトのメッセージID</summary>
        private const int DEFAULT_MESSAGE_ID = 0;

        private const int ITEM_REQUEST_MENU_INDEX = 0;

        private static readonly Vector2 MENU_ICON_SIZE = new Vector2(88, 88);

        private static readonly Vector2 ITEM_BADGE_POS = new Vector2(30, 48);
        private const int BADGE_SORT_OFFSET = 200;

        //古いUIとの座標調整
        //アイテム
        private static readonly Vector3 ITEM_LOCAL_POS_BEFORE = new Vector3(-469f,72f, 0);
        private static readonly Vector3 ITEM_LOCAL_POS_AFTER = new Vector3(-363f, 72f, 0);
        private static readonly Vector3 ITEM_ANCHORED_POS_BEFORE = new Vector3(70f,72f, 0);
        private static readonly Vector3 ITEM_ANCHORED_POS_AFTER = new Vector3(177f, 72f, 0);
        //スタンプ
        private static readonly Vector3 STAMP_LOCAL_POS_BEFORE = new Vector3(-363f,72f, 0);
        private static readonly Vector3 STAMP_LOCAL_POS_AFTER = new Vector3(-257f, 72f, 0);
        private static readonly Vector3 STAMP_ANCHORED_POS_BEFORE = new Vector3(177f,72f, 0);
        private static readonly Vector3 STAMP_ANCHORED_POS_AFTER = new Vector3(283f, 72f, 0);
        
        //Input
        private static readonly Vector3 INPUT_LOCAL_POS_BEFORE = new Vector3(10f,76f, 0);
        private static readonly Vector3 INPUT_LOCAL_POS_AFTER = new Vector3(-67.5f, 76f, 0);
        private static readonly Vector3 INPUT_ANCHORED_POS_BEFORE = new Vector3(10f,76f, 0);
        private static readonly Vector3 INPUT_ANCHORED_POS_AFTER = new Vector3(67.5f, 76f, 0);
        private static readonly Vector3 INPUT_SIZE_DELTA_POS_BEFORE = new Vector3(-462f,86f, 0);
        private static readonly Vector3 INPUT_SIZE_DELTA_POS_AFTER = new Vector3(-577f, 86f, 0);
        
        //フィルター
        public enum FilterMode
        {
            [EnumDisplayName(TextId.Circle0356)] All = 0,
            [EnumDisplayName(TextId.Circle0358)] Message = 1,
            [EnumDisplayName(TextId.Circle0357)] ItemRequest = 2,
            [EnumDisplayName(TextId.RoomMatch0001)] RoomMatchInvite = 3,
            [EnumDisplayName(TextId.Circle249007)] PracticePartner = 4,
            [EnumDisplayName(TextId.None)] Max,
        }

        #endregion

        #region SerailizeField

        /// <summary>ClubChatControllerインスタンス</summary>
        [SerializeField]
        private CircleChatScrollUI _chatScrollUI = null;
        /// <summary>スタンプボタン</summary>
        [SerializeField]
        private ButtonCommon _stampButton = null;
        /// <summary>入力フィールド</summary>
        [SerializeField]
        private InputFieldCommon _input = null;
        /// <summary>投稿ボタン</summary>
        [SerializeField]
        private ButtonCommon _postButton = null;
        [SerializeField]
        protected ButtonCommon _itemRequestButton;
        //投稿完了まで次のメッセージを打てないようにするためのブロック
        [SerializeField]
        protected GameObject _inputBlock;
        //フィルターボタン
        [SerializeField]
        private ButtonCommon _buttonFilter;
        //練習パートナー共有
        [SerializeField]
        private ButtonCommon _sharePartnerButton;
        

        #endregion

        #region 変数

        /// <summary>Chat表示中か</summary>
        private bool _isChatVisible = false;
        /// <summary>ポーリング間隔カウントアップ用</summary>
        private float _pollingInterval = 0;
        /// <summary>チャット送信可能時刻</summary>
        private float _canSendTime = 0.0f;
        /// <summary>初回生成かどうか</summary>
        private bool _isFirstCreate = false;

        /// <summary>ポーリングが許可されているか</summary>
        private bool _isPollingEnable = false;

        /// <summary>ApiManager</summary>
        private HttpManager _httpMgr = null;
        /// <summary>ClubTempData</summary>
        private WorkCircleChatData _chatWorkData = null;
        //ユーザーが最後に出したアイテムリクエスト
        private WorkCircleChatData.ItemRequestInfo _itemReqInfoCache = null;
        //アイテムリクエスト新着バッジ
        private FlashPlayer _itemReqBadge = null;
        //フィルター
        private FilterMode _filterMode = FilterMode.All;

        // サークル情報
        private WorkCircleData _workCircleData = null;
        #endregion

        #region Create

        /// <summary>
        /// チャットに必要なリソースをリストに登録
        /// </summary>
        /// <returns></returns>
        public static void AddChatResourcesToList(DownloadPathRegister register)
        {
            DialogStampSelect.RegisterDownload(register);
        }

        /// <summary>
        /// 初期化処理
        /// </summary>
        public void Initialize()
        {
            // アクティブ
            gameObject.SetActive(true);

            _chatWorkData = WorkDataManager.Instance.CircleChatData;
            _httpMgr = HttpManager.Instance;
            _workCircleData = WorkDataManager.Instance.CircleData;

            // ポーリング可能フラグ
            _isPollingEnable = true;

            // ポーリングタイム設定
            _chatWorkData.ChatWaitTime = DEFAULT_POLLING_TIME;

            // ボタン
            var rectTransformItem = _itemRequestButton.gameObject.GetComponent<RectTransform>();
            var stampTransformItem = _stampButton.gameObject.GetComponent<RectTransform>();
            var inputTransformItem = _input.gameObject.GetComponent<RectTransform>();
            
            _sharePartnerButton.gameObject.SetActive(true);
            rectTransformItem.localPosition = ITEM_LOCAL_POS_AFTER;
            rectTransformItem.anchoredPosition = ITEM_ANCHORED_POS_AFTER;
                
            stampTransformItem.localPosition = STAMP_LOCAL_POS_AFTER;
            stampTransformItem.anchoredPosition = STAMP_ANCHORED_POS_AFTER;
                
            inputTransformItem.localPosition = INPUT_LOCAL_POS_AFTER;
            inputTransformItem.anchoredPosition = INPUT_ANCHORED_POS_AFTER;
            inputTransformItem.sizeDelta = INPUT_SIZE_DELTA_POS_AFTER;

            _stampButton.SetActiveWithCheck(true);
            _stampButton.SetOnClick(OnClickStamp);
            _postButton.interactable = false;
            _postButton.SetOnClick(RequestChatPostMessage);
            _itemRequestButton.SetOnClick(() => OnClickItemRequest());
            SettingPartnerShareButton();

            //バッジ作成
            _itemReqBadge = UIUtil.CreateNotifyBadgeIconFlash(_itemRequestButton.transform, BADGE_SORT_OFFSET);
            _itemReqBadge.transform.localPosition = ITEM_BADGE_POS;

            // InputFieldCommonのonChangeValueを呼ばない
            _input.ChangeTextFlag = false;
            _input.SetOnEndEdit(OnChangeInputText);
#if DMM || UNITY_EDITOR
            _input.ChangeTextCallEndcallback = true;
#endif
            _postButton.SetNotificationMessage(TextId.Circle0231.Text());
            SetChatInputBlockActive(false);

            // 初期通信
            _canSendTime = 0.0f;

            _isFirstCreate = true;

            //フィルター初期化
            _filterMode = (FilterMode)(int)SaveDataManager.Instance.SaveLoader.CircleChatFilter;
            _buttonFilter.TargetText.text = _filterMode.GetEnumDisplayName();
            _buttonFilter.SetOnClick(OnClickFilter);

            ApplyChatView();

            OpenChat();
        }

        /// <summary>
        /// パートナー共有設定ボタン
        /// </summary>
        private void SettingPartnerShareButton()
        {
            if (_workCircleData.PostPracticePartnerCountDay >= ServerDefine.CircleChatPostPartnerDailyMaxCount)
            {
                _sharePartnerButton.SetOnClick(null);
                _sharePartnerButton.SetNotificationMessage(TextUtil.Format(TextId.PracticeRace249028.Text(), ServerDefine.CircleChatPostPartnerDailyMaxCount));
                _sharePartnerButton.SetInteractable(false);
            }
            else
            {
                _sharePartnerButton.SetInteractable(true);
                _sharePartnerButton.SetNotificationMessage(string.Empty);
                _sharePartnerButton.SetOnClick(OnClickSharePartner);
            }
        }

        /// <summary>
        /// ユーザーのアイテムリクエストの状態に応じて見た目を更新する
        /// </summary>
        private void UpdateItemRequestStatus()
        {
            if (_itemReqBadge == null)
                return;

            //自分のアイテムリクエストが積まれて無いか
            //全箇所でチェックするのが面倒なのでUpdateで監視、サークル人数分しかたまらないし、、
            _itemReqInfoCache = WorkDataManager.Instance.CircleChatData.GetItemRequestMessageInfo(Certification.ViewerId);
            if (_itemReqInfoCache == null)
            {
                if (_itemReqBadge.gameObject.activeSelf)
                {
                    _itemReqBadge.SetActiveWithCheck(false);
                }
                return;
            }

            _itemReqBadge.SetActiveWithCheck(_itemReqInfoCache.IsEnd);
        }

        //チャット入力ブロックの有効無効切り替え
        private void SetChatInputBlockActive(bool active)
        {
            if (_inputBlock != null)
            {
                _inputBlock.SetActive(active);
            }
        }

#endregion

#region Monobehavior

        /// <summary>
        /// 更新処理
        /// </summary>
        void Update()
        {
            //ポーリング更新
            UpdatePolling();
            //アイテムリクエストの状態更新
            UpdateItemRequestStatus(); 
        }

        //ポーリング更新
        private void UpdatePolling()
        {
            if (!_isPollingEnable || !_isChatVisible || HttpManager.Instance.IsConnecting || !WorkDataManager.Instance.CircleData.IsJoin() || DialogManager.DispDialogCount > 0)
            {
                return;
            }

            // ポーリング待機時間（ms）
            _pollingInterval += Time.deltaTime;
            if (_chatWorkData.ChatWaitTime <= _pollingInterval)
            {
                RequestChatPolling();
            }
        }

        /// <summary>
        /// Polingを停止する
        /// </summary>
        public void StopPolling()
        {
            _isPollingEnable = false;
        }

#endregion

#region callback

        /// <summary>
        /// 閉ボタン押下時
        /// </summary>
        public void OnClickOpenButton()
        {
            OpenChat();
        }

        /// <summary>
        /// 閉ボタン押下時
        /// </summary>
        public void OnClickCloseButton()
        {
            CloseChat();
        }

        /// <summary>
        /// 開く
        /// </summary>
        private void OpenChat()
        {
            if (!_isChatVisible)
            {
                _isChatVisible = true;
            }
        }

        /// <summary>
        /// 閉じる
        /// </summary>
        private void CloseChat(bool soon = false)
        {
            if (_isChatVisible)
            {
                // 非表示に
                _isChatVisible = false;
            }
        }

        /// <summary>
        /// アイテムリクエスト押下
        /// </summary>
        private void OnClickItemRequest()
        {
            if (_itemReqInfoCache == null)
            {
                //出しているアイテムリクエストが無いので新規リクエスト画面へ
                DialogCircleItemRequest.PushDialog();
                return;
            }

            //確認or結果
            DialogCircleItemRequestDetail.PushDialog(_itemReqInfoCache);
        }

        /// <summary>
        /// スタンプボタンを押下時
        /// </summary>
        public void OnClickStamp()
        {
            OpenStampDialog();
        }
        
        /// <summary>
        /// 練習パートナー共有ボタン
        /// </summary>
        private void OnClickSharePartner()
        {
            DialogTrainedCharaList.OpenForSharePracticePartner(onDecide: selectTrainedCharaId=>
            {
                ConfirmPostPracticePartner(selectTrainedCharaId);
            });
        }
        
        /// <summary>
        /// 練習パートナー投稿確認
        /// </summary>
        /// <param name="dialog"></param>
        private void ConfirmPostPracticePartner(int selectTrainedCharaId)
        {
            var trainedCharacterData = WorkDataManager.Instance.TrainedCharaData.Get(selectTrainedCharaId);
            DialogCircleSharePracticeCharacterConfirm.Open(trainedCharacterData, (comment) =>
            {
                //前に開いているダイアログも非表示
                DialogManager.RemoveAllDialog();
                //チャットに送信API叩く
                RequestChatPostPracticePartner(trainedCharacterData, comment);
            });
        }

        /// <summary>
        /// 入力テキスト変更時
        /// </summary>
        public void OnChangeInputText()
        {
            var text = _input.text;

            // 絵文字とか半角カナとか除去
            text = GallopUtil.GetModifiedString(text, _input);

            //nullなら強制空文字
            if (string.IsNullOrEmpty(text))
            {
                text = "";
            }

            //改行・空白のみの入力は避ける
            var replacedText = text
                .Replace(" ", "")
                .Replace("　", "");

            var isValid = replacedText.Length > 0;
            _postButton.interactable = isValid;
            _postButton.SetNotificationMessage(isValid ? "" : TextId.Circle0231.Text());

            _input.text = text;
        }

        /// <summary>
        /// スタンプ一覧表示
        /// </summary>
        private void OpenStampDialog()
        {
            DialogStampSelect.PushDialog(RequestChatPostStamp, false);
        }

        /// <summary>
        /// フィルター実行
        /// </summary>
        private void OnClickFilter()
        {
            if (_buttonFilter == null)
                return;

            _filterMode++;

            if(_filterMode >= FilterMode.Max)
            {
                _filterMode = FilterMode.All;
            }

            _buttonFilter.TargetText.text = _filterMode.GetEnumDisplayName();
            ApplyChatView();
            _chatScrollUI.SetPositionBottom();  //最下段に設定
        }

        /// <summary>
        /// 画面終了時
        /// </summary>
        public void OnEndView()
        {
            //ポーリングの停止
            StopPolling();

            //フィルターの保存
            SaveDataManager.Instance.SaveLoader.CircleChatFilter = (int)_filterMode;
            SaveDataManager.Instance.Save();
        }

#endregion

#region 通信
        
        /// <summary>
        /// チャットUI反映
        /// </summary>
        private void ApplyChatView()
        {
            _chatScrollUI.CreateList
            (
                WorkDataManager.Instance.CircleChatData.ChatDataList,
                _isFirstCreate,
                SaveDataManager.Instance.SaveLoader.CircleChatReadMessageId,
                _filterMode
            );
            _isFirstCreate = false;
        }

        /// <summary>
        /// チャットのメッセージ登録
        /// </summary>
        /// <param name="response"></param>
        public void SetChatMessages(CircleChatSendMessageResponse.CommonResponse response)
        {
            var list = _chatWorkData.UpdateChatData(response);
            OnRecieveNewMessages(list);
            ApplyChatView();
        }
        public void SetChatMessages(CircleChatSendStampResponse.CommonResponse response)
        {
            var list = _chatWorkData.UpdateChatData(response);
            OnRecieveNewMessages(list);
            ApplyChatView();
        }
        public void SetChatMessages(CircleChatSendItemRequestResponse.CommonResponse response)
        {
            var list = _chatWorkData.UpdateChatData(response);
            OnRecieveNewMessages(list);
            ApplyChatView();
        }
        
        public void SetChatMessages(CircleChatPostPartnerResponse.CommonResponse response)
        {
            var list = _chatWorkData.UpdateChatData(response);
            OnRecieveNewMessages(list);
            ApplyChatView();
        }
        
        public void SetChatMessages(CircleChatPollingResponse.CommonResponse response)
        {
            var list = _chatWorkData.UpdateChatData(response);
            OnRecieveNewMessages(list);
            ApplyChatView();
        }

        //ポーリングで受け取った新規チャットメッセージ受け取り時処理
        private void OnRecieveNewMessages(List<WorkCircleChatData.ChatMessageInfo> list)
        {
            var num = list.Count;
            var stampId = 0;
            var myViewerId = Certification.ViewerId;
            for(int i = 0; i < num; i++)
            {
                var message = list[i];
                if (message == null)
                    continue;
                if(message.ViewerId == myViewerId)
                {
                    SetChatInputBlockActive(false); //自分の打ったメッセージが帰ってきたら入力OK
                }
                switch (message.Type)
                {
                    case WorkCircleChatData.MessageType.SYSTEM_MESSAGE_VARIABLE:
                        switch (message.SystemMessageId)
                        {
                            //サークル名変更
                            case WorkCircleChatData.SYSTEM_MESSAGE_ID.NAME_CHANGE:
                                WorkDataManager.Instance.CircleData.CircleName = message.MessageParam;
                                break;
                        }
                        break;
                    case WorkCircleChatData.MessageType.STAMP:
                        stampId = message.StampId; 
                        break;
                }
            }
            if(stampId != 0)
            {
                //一度に大量のスタンプのSEが鳴るのを防ぐため、最後に打たれたスタンプのみSE再生
                AudioManager.Instance.PlaySe_Stamp(stampId, true);
            }
        }

        /// <summary>
        /// チャットポーリング通信
        /// </summary>
        private void RequestChatPolling()
        {
            //通信前にリセット(通信中はポーリング待機時間を加算しない)
            _pollingInterval = 0;
            var req = new CircleChatPollingRequest();
            Action<CircleChatPollingResponse> onSuccess = param =>
            {
                //通信中にポーリングが停止している可能性がある
                if (_isPollingEnable == false)
                    return;
                // ワークデータ更新
                SetChatMessages(param.data);
            };

            // 通信中アイコン非表示、通信中クリック許可
            _httpMgr.Send(req, onSuccess, (type, resultCode) => {

                { OnErrorChatApiRequest(); }
            }, false, false);
        }

        /// <summary>
        /// チャットメッセージ、スタンプを送信
        /// </summary>
        private void RequestChatPostMessage()
        {
            if (_canSendTime > Time.time)
            {
                // 送信不能時間内は送信できない
                Debug.LogWarning("連打禁止 enableTime: " + _canSendTime + ", current:" + Time.time);
                return;
            }

            if (string.IsNullOrEmpty(_input.text))
            {
                if(_postButton.interactable)
                {
                    //空文字なのに有効になってる場合は強制的に無効にしてNotification出す
                    OnChangeInputText();   
                    UIManager.Instance.ShowNotification(TextId.Circle0231.Text());
                }
                return;
            }

            _canSendTime = Time.time + CHAT_INTERVAL_TIME;

            var req = new CircleChatSendMessageRequest();
            req.message = _input.text;

            Action<CircleChatSendMessageResponse> onSuccess = (res) =>
            {
                // 送信した文字は消す.
                _input.text = string.Empty;
                OnChangeInputText();
                SetChatMessages(res.data);
            };

            SetChatInputBlockActive(true);  //打った内容が反映されるまで入力NG
            _httpMgr.Send(req, onSuccess, (errorType, resultCode) => { OnErrorChatApiRequest(); });
        }

        /// <summary>
        /// スタンプを送信
        /// </summary>
        private void RequestChatPostStamp(int stampId)
        {
            if (_canSendTime > Time.time)
            {
                // 送信不能時間内は送信できない
                return;
            }

            var saveLoader = SaveDataManager.Instance.SaveLoader;

            _canSendTime = Time.time + CHAT_INTERVAL_TIME;

            var req = new CircleChatSendStampRequest();
            req.stamp_id = stampId;

            Action<CircleChatSendStampResponse> onSuccess = (res) =>
            {
                SetChatMessages(res.data);
            };

            SetChatInputBlockActive(true);  //スタンプの内容が帰ってくるまで入力許可しない
            _httpMgr.Send(req, onSuccess, (errorType, resultCode) => { OnErrorChatApiRequest(); });
        }
        
        /// <summary>
        /// 共有パートナーを送信
        /// </summary>
        private void RequestChatPostPracticePartner(WorkTrainedCharaData.TrainedCharaData trainedCharaData,string commentData)
        {
            if (_canSendTime > Time.time)
            {
                // 送信不能時間内は送信できない
                return;
            }

            _canSendTime = Time.time + CHAT_INTERVAL_TIME;

            var req = new CircleChatPostPartnerRequest();
            req.trained_chara_id = trainedCharaData.Id;
            req.comment = commentData;

            Action<CircleChatPostPartnerResponse> onSuccess = (res) =>
            {
                var chat = CircleUtil.GetChatInstance();
                if (chat != null)
                {
                    chat.SetChatMessages(res.data);
                    //投稿したカウントを1つ増やす
                    _workCircleData.UpdatePostPartnerCountDay(_workCircleData.PostPracticePartnerCountDay + 1);
                    //ボタンチェック
                    SettingPartnerShareButton();
                }
            };

            SetChatInputBlockActive(true);  //スタンプの内容が帰ってくるまで入力許可しない
            _httpMgr.Send(req, onSuccess, onError:(errorType, resultCode) =>
            {
                if (resultCode == GallopResultCode.CIRCLE_OVER_DAILY_POST_PARTNER_COUNT_LIMIT)
                {
                    var dialogData = new DialogCommon.Data();
                    var errorHeader = MasterDataManager.Instance.masterString.GetText(MasterString.Category.ErrorHeader, GallopResultCode.CIRCLE_OVER_DAILY_POST_PARTNER_COUNT_LIMIT);
                    var errorMessage = MasterDataManager.Instance.masterString.GetText(MasterString.Category.ErrorMessage, GallopResultCode.CIRCLE_OVER_DAILY_POST_PARTNER_COUNT_LIMIT);

                    dialogData.SetSimpleOneButtonMessage(
                        errorHeader,
                        errorMessage,
                        dialog =>
                        {
                            OnErrorChatApiRequest();
                        });

                    DialogManager.PushDialog(dialogData);
                }
            });
        }

        /// <summary>
        /// APIエラー時の共通のエラー処理
        /// TimeOut, WwwErrorなど、エラー種別がリザコ起因でない場合はそもそもここに来ない
        /// その場合ここでキャッチしポーリングを停止とかできないので、Updateでエラーを含むダイアログ開いてたら通信しないようにしてる
        /// </summary>
        private void OnErrorChatApiRequest()
        {
            SetChatInputBlockActive(false); //NGワードエラーなどの場合は再入力
        }
#endregion

#region 更新

        public void UpdateItems()
        {
            _chatScrollUI.UpdateItems();
        }

#endregion
    }
}
