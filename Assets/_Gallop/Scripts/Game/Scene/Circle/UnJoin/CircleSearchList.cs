using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.Http;
using System;
using static Gallop.CircleUtil;

namespace Gallop
{
    /// <summary>
    /// サークル一覧UI
    /// </summary>
    public class CircleSearchList : Mono<PERSON><PERSON>aviour
    {
        
        #region 定義、SerializeField、変数

        //-----------------------------------------------------------------------------------------
        // SerializeField
        //-----------------------------------------------------------------------------------------

        [SerializeField]
        private TextCommon _textPopulation = null;
        [SerializeField]
        private TextCommon _textPolicy = null;
        [SerializeField]
        private TextCommon _textApproval = null;

        [SerializeField]
        private FlickToggleGroupCommon _flickToggleGroupCommon = null;
        /// <summary>
        /// 検索条件による絞り込みボタン
        /// </summary>
        [SerializeField]
        private ButtonCommon _buttonRefine = null;

        [SerializeField]
        private PartsCircleList _circleList = null;
        [SerializeField]
        private PartsCircleList _scoutList = null;

        //アニメーション対象
        [SerializeField] private GameObject _animBottom;
        [SerializeField] private GameObject _animRight;

        #endregion

        //-----------------------------------------------------------------------------------------
        // 定義
        //-----------------------------------------------------------------------------------------

        /// <summary>
        /// タブ
        /// </summary>
        private enum TabType
        {
            Invalid = -1,
            CircleList = 0,   // サークル一覧
            Scout,      // 勧誘一覧
        }

        //-----------------------------------------------------------------------------------------
        // 変数
        //-----------------------------------------------------------------------------------------

        private TabType _tabType;                                       // クラブ検索画面でのタブ状態

        private List<PartsCircleList.ListData> _circleDataListRecommend = new List<PartsCircleList.ListData>();
        private List<PartsCircleList.ListData> _circleDataListRequest = new List<PartsCircleList.ListData>();
        private List<PartsCircleList.ListData> _circleDataListScout = new List<PartsCircleList.ListData>();

        private List<PartsCircleList.ListData> _circleDataListSearch = new List<PartsCircleList.ListData>();

        private CircleSearchParam _circleSearchParam = new CircleSearchParam();

        private List<ViewBase.InOutAnimationData> _animList = new List<ViewBase.InOutAnimationData>();

        #region ビュー関数

        //-----------------------------------------------------------------------------------------
        // function
        //-----------------------------------------------------------------------------------------
        
        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize(CircleSearchParam? param = null, Action<CircleListResponse.CommonResponse> onComplete = null)
        {
            _tabType = TabType.Invalid;
            if(param == null)
            {
                _circleSearchParam.LoadDefault();
            }
            else
            {
                _circleSearchParam = (CircleSearchParam)param;
            }
            
            ApplyTexts();
            _buttonRefine.SetOnClick(OnClickRefineButton);
            _flickToggleGroupCommon.SetOnSelectCallback(ChangeTab);
            
            ReloadList(res =>
            {
                //一覧表示
                ChangeTab(TabType.CircleList);
                //アニメーションリストの生成
                UIUtil.CreateInAnimationDataList(_animList, new GameObject[] { _circleList.ScrollRect.verticalScrollbar.gameObject }, _animBottom, _circleList.ScrollRect);
                onComplete?.Invoke(res);
            });
        }
        
        /// <summary>
        /// テキスト反映
        /// </summary>
        private void ApplyTexts()
        {
            _textPopulation.text = _circleSearchParam.population.Text();
            _textPolicy.text = _circleSearchParam.policy.Text();
            _textApproval.text = _circleSearchParam.approval.Text();
        }

        /// <summary>
        /// 画面入り
        /// </summary>
        public void PlayIn(System.Action onComplete)
        {
            UIUtil.PlayInViewParts(_animList, _animRight, onComplete);
        }

        /// <summary>
        /// 画面捌け
        /// </summary>
        /// <param name="onComplete"></param>
        public void PlayOut(System.Action onComplete)
        {
            switch (_tabType)
            {
                case TabType.CircleList:
                    UIUtil.CreateOutAnimationDataList(_animList, new GameObject[] { _circleList.ScrollRect.verticalScrollbar.gameObject }, _animBottom, _circleList.ScrollRect);
                    break;
                case TabType.Scout:
                    UIUtil.CreateOutAnimationDataList(_animList, new GameObject[] { _scoutList.ScrollRect.verticalScrollbar.gameObject }, _animBottom, _scoutList.ScrollRect);
                    break;
            }

            UIUtil.PlayOutViewParts(_animList, _animRight, onComplete, UIUtil.VIEW_OUT_PARTS_INTERVAL_ONE_FRAME, true);
        }

        #endregion

        #region ボタン反応

        //-----------------------------------------------------------------------------------------
        // ボタン反応
        //-----------------------------------------------------------------------------------------
        
        private void ChangeTab(int tabIndex)
        {
            if (_tabType == (TabType)tabIndex)
            {
                return;
            }
            _tabType = (TabType)tabIndex;

            // 絞り込みボタンの出しわけ
            _buttonRefine.gameObject.SetActive(_tabType == TabType.CircleList);

            // 表示するサークルリストの更新
            _circleList.SetActiveWithCheck(_tabType == TabType.CircleList);
            _scoutList.SetActiveWithCheck(_tabType == TabType.Scout);
        }
        
        private void ChangeTab(TabType tabType)
        {
            ChangeTab((int)tabType);
        }

        /// <summary>
        /// サークル検索絞り込みボタン
        /// </summary>
        public void OnClickRefineButton()
        {
            DialogCircleSearchRefine.PushDialog(_circleSearchParam, OnSearchComplete);
        }

        /// <summary>
        /// 検索終了
        /// </summary>
        /// <param name="param"></param>
        private void OnSearchComplete(CircleSearchParam param)
        {
            _circleSearchParam = param;
            _circleDataListSearch.Clear();
            ApplyTexts();
            foreach (var circleInfo in _circleSearchParam.circleInfos)
            {
                var rank = FindRanking(param.rankingArray, circleInfo.circle_id);
                _circleDataListSearch.Add(ConvertCircleListData(circleInfo, _circleSearchParam.userInfoAtFriends, rank));
            }

            SetupCircleList(TabType.CircleList);
        }

        #endregion

        //-----------------------------------------------------------------------------------------
        // 表示更新
        //-----------------------------------------------------------------------------------------

        // API送信、リストリロード
        private void ReloadList(System.Action<CircleListResponse.CommonResponse> onSuccess = null)
        {
            HttpManager.Instance.Send<CircleListRequest, CircleListResponse>(new CircleListRequest(), (res) =>
            {
                CreateCircleDataList(res);
                SetupCircleList(TabType.CircleList);
                SetupCircleList(TabType.Scout);
                onSuccess?.Invoke(res.data);
            });
        }

        /// <summary>
        /// 通信データからリストデータを作成する
        /// </summary>
        private void CreateCircleDataList(CircleListResponse response)
        {
            if (response == null)
                return;

            _circleDataListRecommend.Clear();
            _circleDataListRequest.Clear();
            _circleDataListScout.Clear();

            var data = response.data;
            var circleInfos = data.circle_info_array;
            if (circleInfos == null) return;
            if (response.data.leader_info_array == null)
            {
                Debug.LogWarning("_listResponse.data.leader_listがnullです");
                return;
            }

            foreach (var id in data.recommend_circle_id_array)
            {
                _circleDataListRecommend.Add(ConvertCircleListData(FindCircle(circleInfos, id), data.leader_info_array, FindRanking(data.circle_ranking_array, id)));
            }
            if (data.circle_request != null)
            {
                _circleDataListRequest.Add(ConvertCircleListData(FindCircle(circleInfos, data.circle_request.circle_id), data.leader_info_array, FindRanking(data.circle_ranking_array, data.circle_request.circle_id)));
            }
            foreach (var scoutData in data.circle_scout_array)
            {
                _circleDataListScout.Add(ConvertCircleListData(FindCircle(circleInfos, scoutData.circle_id), data.leader_info_array, FindRanking(data.circle_ranking_array, scoutData.circle_id)));
            }
        }

        /// <summary>
        /// リスト生成 
        /// </summary>
        private void SetupCircleList(TabType tabType)
        {
            List<PartsCircleList.ListData> circleDataList = null;
            var listDispType = PartsCircleList.DispType.Request;
            bool isSearch = _circleSearchParam.searchType != CircleSearchParam.SearchType.Disabled;
            PartsCircleList list = null;

            switch (tabType)
            {
                case TabType.CircleList:
                    listDispType = PartsCircleList.DispType.Request;
                    list = _circleList;
                    circleDataList = isSearch ? _circleDataListSearch : _circleDataListRecommend;
                    break;
                case TabType.Scout:
                    listDispType = PartsCircleList.DispType.Scout;
                    list = _scoutList;
                    circleDataList = _circleDataListScout;
                    break;
                default:
                    Debug.LogError("未実装のTabType: " + tabType);
                    break;
            }
            list.Setup(circleDataList, listDispType, _flickToggleGroupCommon.OnFlick);
        }

        //引数サークル削除
        public void RemoveScoutCircle(int circleId)
        {
            if (_circleDataListScout == null)
                return;

            var targetIndex = -1;
            for(int i = 0;i < _circleDataListScout.Count; i++)
            {
                if(_circleDataListScout[i].id == circleId)
                {
                    targetIndex = i;
                    break;
                }
            }
            if (targetIndex == -1)
                return;

            _circleDataListScout.RemoveAt(targetIndex);
            SetupCircleList(TabType.Scout);
        }
    }
}
