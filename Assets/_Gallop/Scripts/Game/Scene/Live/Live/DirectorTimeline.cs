using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Gallop.Live.Cutt;

namespace Gallop.Live
{
    /// <summary>
    /// Directorのタイムライン関連の処理.
    /// </summary>
    public partial class Director : MonoBehaviour
    {
        private static readonly Dictionary<int, int> _propsTimelineDataDictionary = new Dictionary<int, int>(); // hash, flag

        public bool IsLyrics()
        {
            return _liveTimelineControl.IsLyrics();
        }

        /// <summary>
        /// TimelinePrefabのInstanciateと初期化
        /// </summary>
        /// <param name="prefabPath"></param>
        private bool InstanciateTimeline(GameObject prefab)
        {
            GameObject go = null;
            LiveTimelineControl timelineControl = null;
            {
                if (prefab == null)
                {
                    Debug.LogWarning("Failed Load Prefab");
                    goto E_TIMELINE;
                }
#if UNITY_EDITOR
                //sparse-checkoutで読み飛ばされていた場合にUsingAssetBundleResourceはfalseを返してしまうため、
                //アセバンから読み込まれたプレハブかをNotAPrefabかどうかで判定している
                var prefabState = UnityEditor.PrefabUtility.GetPrefabInstanceStatus(prefab);
                if (prefabState == UnityEditor.PrefabInstanceStatus.NotAPrefab || AssetBundleHelper.UsingAssetBundleResource())
                {
                    //アセットバンドルからロードした場合はPrefabUtilityが失敗する
                    go = GameObject.Instantiate(prefab) as GameObject;
                    go.name = prefab.name;
                }
                else
                {
                    //正規のPrefabInstanciate処理ではPrefabとのリレーションがどうやら切れてしまって
                    //PrefabのApply（ReplacePrefab）ができないのでEditorではこっち
                    go = UnityEditor.PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                }
#else
                go = GameObject.Instantiate(prefab) as GameObject;
                go.name = prefab.name;
#endif
                if (go == null)
                {
                    Debug.LogWarning("Failed Instanciate Prefab");
                    goto E_TIMELINE;
                }
                go.transform.localPosition = Math.VECTOR3_ZERO;
                go.transform.localRotation = Math.QUATERNION_IDENTITY;
                go.transform.localScale = Math.VECTOR3_ONE;
                timelineControl = go.GetComponent<LiveTimelineControl>();

                if (timelineControl == null)
                {
                    Debug.LogWarning("Failed GetComponent LiveTimelineControl");
                    goto E_TIMELINE;
                }
                if (!InitializeTimeline(timelineControl))
                {
                    Debug.LogWarning("Failed Initialize LiveTimelineControl");
                    goto E_TIMELINE;
                }
                SetupLiveTimelineControl(timelineControl);

                return true;
            }

            E_TIMELINE:
            if (go != null)
            {
                Destroy(go);
            }
            //PrefabのUnloadはやったらエラーになったのでしない
            return false;
        }

        /// <summary>
        /// TimeLineリンク
        /// **TimelineのDirectorに置くか迷う
        /// </summary>
        /// <param name="timelineControl"></param>
        /// <returns></returns>
        private bool InitializeTimeline(LiveTimelineControl timelineControl)
        {
            if (timelineControl == null)
            {
                return false;
            }

            // シーン移動があるため必ずDirectorの子にしておく
            timelineControl.transform.SetParent(this.transform);

            timelineControl.Initialize();//ここからTimelineEventのPublish開始

            //以下、必要プロパティのセットやイベントの購読を行う

            CharacterObject.FollowSpotLightParentName = timelineControl.data.spotLightParentName;

            //Timelineカメラセット
            for (int i = 0; i < kTimelineCameraIndices.Length; i++)
            {
                var iCam = kTimelineCameraIndices[i];
                if (iCam < _cameraObjects.Length)
                {
                    timelineControl.SetTimelineCamera(_cameraObjects[iCam], i);
                }
            }
            //カメラスイッチイベントSubscribe
            //※カメラ切り替えはOnUpdateCameraSwitcherへ統合する予定。こっちはLegacyなもの
            timelineControl.eventPublisher.Subscribe(
                LiveTimelineEventID.SwitchCamera,
                (eventData) =>
                {
                    var evParam = eventData.GetParameter<CuttEventParam_SwitchCamera>();
                    if (evParam != null)
                    {
                        _activeCameraIndex = evParam.cameraID;
                    }
                });
            //カメラスイッチャー
            timelineControl.OnUpdateCameraSwitcher += (cameraIndex_) =>
            {
                if (cameraIndex_ < 0)
                {
                    _activeCameraIndex = kMotionCameraIndex;
                }
                else if (cameraIndex_ < kTimelineCameraIndices.Length)
                {
                    _activeCameraIndex = kTimelineCameraIndices[cameraIndex_];
                }
            };
            //LipSync
            timelineControl.OnUpdateLipSync += (keyData_, liveTime_) =>
            {
                for (int i = 0; i < MemberUnitNumber; ++i)
                {
                    CharacterObject characterObject = _characterObjectList[i];
                    if (characterObject == null)
                    {
                        continue;
                    }

                    for (int d = 0; d < characterObject.ReserveDressModelCount; d++)
                    {
                        LiveModelController liveModelController = characterObject.LiveModelControllerArray[d];
                        LiveFaceController liveFaceController = liveModelController.LiveFaceController;
                        liveFaceController.AlterUpdateAutoLip(liveTime_, keyData_, (LiveCharaPosition)i);
                    }
                }
            };
            //Facial
            timelineControl.OnUpdateFacial += (updateInfo_, liveTime_, charaPos_) =>
            {
                var i = (int)charaPos_;

                if (i < MemberUnitNumber)
                {
                    bool isSetParam = false;
                    CharacterObject characterObject = _characterObjectList[i];
                    if (characterObject == null)
                    {
                        return;
                    }

                    //キャラが非表示の時にはそもそも設定しない
                    if (!characterObject.liveCharaVisible)
                    {
                        return;
                    }

                    var eyeTrack = updateInfo_.eyeTrack;

                    for (int d = 0; d < characterObject.ReserveDressModelCount; d++)
                    {
                        LiveModelController liveModelController = characterObject.LiveModelControllerArray[d];
                        if (liveModelController == null)
                        {
                            return;
                        }
                        else if (liveModelController.UseOverrideFacialMotion)
                        {
                            return;
                        }

                        LiveFaceController liveFaceController = liveModelController.LiveFaceController;
                        if (liveFaceController == null)
                        {
                            return;
                        }

                        liveFaceController.AlterUpdateFacialNew(i, liveTime_, LiveTimelineControl.kTargetFps, ref updateInfo_);

                        if (eyeTrack != null)
                        {
                            if (characterObject.EyeTraceControllerArray[d] != null)
                            {
                                float rate = Mathf.Clamp01(LiveTimelineDefine.LIVE_EYE_TRACE_DELAY_RATE_BASE * eyeTrack.speedRatePer / LiveTimelineDefine.kFacialEyeTrackSpeedRatePer);
                                characterObject.EyeTraceControllerArray[d].SetDelayRate(rate);

                                // 時間を指定して目線移動を行う。
                                if (eyeTrack.IsEnabledDurationTime)
                                {
                                    if (characterObject.CurrentFrameEyeTrack != eyeTrack.frame)
                                    {
                                        const float FRAME_DURATION = 1f / LiveTimelineControl.kTargetFps;
                                        characterObject.EyeTraceControllerArray[d].SetDurationTime(eyeTrack.time * FRAME_DURATION, eyeTrack.interpolateType);
                                        characterObject.CurrentFrameEyeTrack = eyeTrack.frame;
                                    }
                                }
                                else
                                {
                                    characterObject.EyeTraceControllerArray[d].ClearDurationTime();
                                    characterObject.CurrentFrameEyeTrack = -1;
                                }
                            }

                            isSetParam = true;
                        }

                    }

                    if (isSetParam)
                    {
                        characterObject.EyeTrackTargetType = eyeTrack.targetType;
                        characterObject.EyeTrackVerticalRate = eyeTrack.verticalRatePer / LiveTimelineDefine.kFacialEyeTrackRangeRatePer;
                        characterObject.EyeTrackHorizontalRate = eyeTrack.horizontalRatePer / LiveTimelineDefine.kFacialEyeTrackRangeRatePer;
                        characterObject.EyeTrackDirectWorldPosition = _stageController.CachedTransform.position + _stageController.CachedTransform.rotation * eyeTrack.DirectPosition;
                    }

                }

#if (UNITY_EDITOR || UNITY_STANDALONE) && !CYG_PRODUCT
                //if (LiveDebug.isRecordingMode && _captureEyeTargetMode == CaptureEyeTargetMode.ForceCamera)
                //{
                //    _characterObjects[i].eyeTrackTargetType = FacialEyeTrackTarget.Camera;
                //    _characterObjects[i].eyeTrackTargetVPos = LiveModelController.EyeTargetVerticalPos.Middle;
                //}
#endif

                return;
            };

            //Facial Toon
            timelineControl.OnUpdateFacialToon += OnUpdateFacialToon;

            //PostEffect
            timelineControl.OnUpdatePostEffect_DOF += OnUpdatePostEffect_DOF;
            timelineControl.OnUpdatePostEffect_BloomDiffusion += OnUpdatePostEffect_BloomDiffusion;
            timelineControl.OnUpdateRadialBlur += OnUpdateRadialBlur;
            timelineControl.OnUpdatePostFilm += OnUpdatePostFilm;
            timelineControl.OnUpdateFluctuation += OnUpdateFluctuation;
            timelineControl.OnUpdateFade += OnUpdateFade;
            timelineControl.OnUpdateChromaticAberration += OnUpdateChromaticAberration;
            timelineControl.OnUpdateVortex += OnUpdateVortex;

            timelineControl.OnUpdatePostFilm2 += OnUpdatePostFilm2;
            timelineControl.OnUpdatePostFilm3 += OnUpdatePostFilm3;
            timelineControl.OnEnvironmentCharacterShadow += OnEnvironemntCharacterShadow;
            timelineControl.OnUpdateGlobalLight += OnUpdateGlobalLight;
            timelineControl.OnUpdateGlobalFog += OnUpdateGlobalFog;
            timelineControl.OnUpdateTiltShift += OnUpdateTiltShift;
            timelineControl.OnUpdateLightShafts += OnUpdateLightShafts;
            timelineControl.OnUpdateColorCorrection += OnUpdateColorCorrection;

            timelineControl.OnUpdateSpotlight3d += OnUpdateSpotlight3d;
            timelineControl.OnUpdateNodeScale += OnUpdateNodeScale;

            timelineControl.OnUpdateCharaFootLight += OnUpdateCharaFootLight;
            timelineControl.OnUpdatePostFilm1MultiCamera += OnUpdatePostFilm1MultiCamera;
            timelineControl.OnUpdatePostBloomDiffusionMultiCamera += OnUpdatePostEffect_BloomDiffusionMultiCamera;
            timelineControl.OnUpdateColorCorrectionMultiCamera += OnUpdateColorCorrectionMultiCamera;
            timelineControl.OnUpdateTiltShiftMultiCamera += OnUpdateTiltShiftMultiCamera;
            timelineControl.OnUpdateRadialBlurMultiCamera += OnUpdateRadialBlurMultiCamera;
            timelineControl.OnUpdatePostDOFMultiCamera += OnUpdatePostEffect_DOFMultiCamera;

            timelineControl.OnUpdateCharaParts += OnUpdateCharaParts;
            timelineControl.OnUpdateToneCurve += OnUpdateToneCurve;
            timelineControl.OnUpdateExposure += OnUpdateExposure;

            #region イベント購読系サンプルコード
#if false
            timelineControl.eventPublisher.Subscribe(
                LiveTimelineEventID.SwitchMonitor,
                (eventData) =>
                {
                    Debug.Log("SwitchMonitor event. " + Time.frameCount);
                });
            //Monitor制御 Timeline更新イベント購読
            timelineControl.OnUpdateMonitorControl += (updateInfo_) =>
            {
                Debug.Log(string.Format("OnUpdateMonitorControl {0}, {1}, {2}, {3}, {4}, {5}",
                    updateInfo_.pos, updateInfo_.size, updateInfo_.multiFlag, updateInfo_.dispID, updateInfo_.subDispID, updateInfo_.speed));
            };
#endif
            #endregion

            return true;
        }

        /// <summary>
        /// タイムラインコントローラへの設定.
        /// </summary>
        /// <param name="timelineControl"></param>
        private void SetupLiveTimelineControl(LiveTimelineControl timelineControl)
        {
            if (_liveTimelineControl != null)
            {
                return;
            }

            _liveTimelineControl = timelineControl;
            if (_liveTimelineControl != null)
            {
                _liveTimelineControl.OnUpdateProps += UpdateProps;
                _liveTimelineControl.OnUpdatePropsAttach += UpdatePropsAttach;
                _liveTimelineControl.OnUpdateParticle += UpdateParticle;
                _liveTimelineControl.OnUpdateParticleGroup += UpdateParticleGroup;
                _liveTimelineControl.OnUpdateSweatLocator += UpdateSweatLocator;
                _liveTimelineControl.OnUpdateEffect += UpdateEffect;
            }

            _propsTimelineDataDictionary.Clear();
            _propsTimelineDataDictionary.Add(FNVHash.Generate("PropsCenter"), (int)LiveCharaPositionFlag.Center);
            _propsTimelineDataDictionary.Add(FNVHash.Generate("PropsOther"), (int)LiveCharaPositionFlag.Other);
            _propsTimelineDataDictionary.Add(FNVHash.Generate("PropsFlags"), 0);
        }

        /// <summary>
        /// タイムラインのデリゲートを削除.
        /// </summary>
        private void DeleteTimelineDelegetes()
        {
            if (_liveTimelineControl == null)
            {
                return;
            }

            _liveTimelineControl.ClearDelegetes();
        }

        #region コールバック

        /// <summary>
        /// 小道具のステータスの更新.
        /// </summary>
        /// <param name="updateInfo"></param>
        private void UpdateProps(ref PropsUpdateInfo updateInfo)
        {
            // 設定するものを決める.
            int posFlags;
            if (!_propsTimelineDataDictionary.TryGetValue(updateInfo.data.nameHash, out posFlags))
            {
                return;
            }

            if (posFlags == 0)
            {
                if (updateInfo.settingFlags == 0)
                {
                    posFlags = (int)LiveCharaPositionFlag.All;
                }
                else
                {
                    posFlags = updateInfo.settingFlags;
                }
            }

            // 設定を行っていく.
            var color = updateInfo.color * updateInfo.colorPower;
            color.a = updateInfo.color.a;

            for (var i = 0; i < GameDefine.UNIT_MEMBER_NUM; ++i)
            {
                if ((posFlags & (1 << i)) == 0)
                {
                    continue;
                }
                _propsManager.ChangeRenderState(i, updateInfo.renderEnable, color, updateInfo.propsID);
            }
        }

        /// <summary>
        /// 小道具アタッチのステータスの更新.
        /// </summary>
        /// <param name="updateInfo"></param>
        private void UpdatePropsAttach(ref PropsAttachUpdateInfo updateInfo)
        {
            // 設定するものを決める.
            int posFlags;
            if (!_propsTimelineDataDictionary.TryGetValue(updateInfo._data.nameHash, out posFlags))
            {
                return;
            }

            if (posFlags == 0)
            {
                if (updateInfo._settingFlags == 0)
                {
                    posFlags = (int)LiveCharaPositionFlag.All;
                }
                else
                {
                    posFlags = updateInfo._settingFlags;
                }
            }

            // 設定を行っていく.
            for (var i = 0; i < GameDefine.UNIT_MEMBER_NUM; ++i)
            {
                if ((posFlags & (1 << i)) == 0)
                {
                    continue;
                }
                _propsManager.ChangeAttach(i, updateInfo._attachJointHash, updateInfo._copyPositionJointHash, updateInfo._offsetPosition, updateInfo.OffsetRotation, updateInfo.OffsetScale, updateInfo._propsId);
            }
        }

        /// <summary>
        /// パーティクル更新.
        /// </summary>
        /// <param name="updateInfo">更新情報</param>
        private void UpdateParticle(ref ParticleUpdateInfo updateInfo)
        {
            var particleControllerArray = StageController.ParticleControllerArray;
            if (particleControllerArray == null)
            {
                return;
            }

            for (var i = 0; i < particleControllerArray.Length; ++i)
            {
                if (particleControllerArray[i] == null)
                {
                    continue;
                }

                particleControllerArray[i].UpdateFromTimeline(ref updateInfo);

#if UNITY_EDITOR && CYG_DEBUG
                //もしシークバーなどで時間を巻き戻している場合には、パーティクルが散らばっているとUnityがクラッシュする可能性があるので止める。
                if (_liveTimelineControl.OldFrame > _liveTimelineControl.CurrentFrame)
                {
                    particleControllerArray[i].DebugClearByRewind();
                }
#endif
            }
        }

        /// <summary>
        /// パーティクルグループ更新.
        /// </summary>
        private void UpdateParticleGroup(ref ParticleGroupUpdateInfo updateInfo)
        {
            var particleControllerArray = StageController.ParticleControllerArray;
            if (particleControllerArray == null)
            {
                return;
            }

            for (var i = 0; i < particleControllerArray.Length; ++i)
            {
                if (particleControllerArray[i] == null)
                {
                    continue;
                }
                particleControllerArray[i].UpdateGroupFromTimeline(ref updateInfo);
            }
        }

        private void UpdateSweatLocator(ref SweatLocatorUpdateInfo updateInfo)
        {
            int charaIndex = (int)updateInfo.owner;
            if (charaIndex < 0 || _characterObjectList.Count <= charaIndex)
            {
                return;
            }
            var chara = _characterObjectList[charaIndex];
            if (null == chara)
            {
                return;
            }
            chara.UpdateSweatLocator(ref updateInfo);
        }

        private void UpdateEffect(ref EffectUpdateInfo updateInfo)
        {
            StageController.EffectController.Update(ref updateInfo);
        }

        /// <summary>
        /// グローバルフォグの更新
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateGlobalFog(ref GlobalFogUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            eff.GlobalFogParam.UseRadialDistance = updateInfo.useRadialDistance;
            eff.GlobalFogParam.HeightFog = updateInfo.isHeight;
            eff.GlobalFogParam.DistanceFog = updateInfo.isDistance;
            eff.GlobalFogParam.StartDistance = updateInfo.startDistance;
            eff.GlobalFogParam.FogHeight = updateInfo.height;
            eff.GlobalFogParam.FogHeightDensity = updateInfo.heightDensity;
            eff.GlobalFogParam.FogColor = updateInfo.color;
            eff.GlobalFogParam.SceneFogMode = updateInfo.fogMode;
            eff.GlobalFogParam.SceneFogDensity = updateInfo.expDensity;
            eff.GlobalFogParam.SceneFogStart = updateInfo.start;
            eff.GlobalFogParam.SceneFogEnd = updateInfo.end;
        }

        /// <summary>
        /// ポストエフェクトのパラメータのタイムラインアップデート
        /// </summary>
        private void OnUpdatePostEffect_DOF(ref PostEffectUpdateInfo_DOF updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            var param = eff.DofDiffusionBloomOverlayParam;

            param.IsEnableDof = updateInfo.IsEnableDOF;
            param.DofFocalSize = updateInfo.forcalSize;
            param.DofBlurType = updateInfo.dofBlurType;
            param.DofQualityType = updateInfo.dofQuality;

            param.DofForegroundSize = updateInfo.dofForegroundSize;
            param.DofMaxBlurSpread = (updateInfo.blurSpread);
            param.DofSmoothness = updateInfo.dofSoomthness;
            if (updateInfo.isUseFocalPoint == true)
            {
                param.DofFocalPoint = (updateInfo.dofFocalPoint);
            }
            else
            {
                param.DofFocalPosition = (updateInfo.forcalPosition);
            }

            // 拡張分
            eff.DofDiffusionBloomOverlayParam.BallBlurPowerFactor = updateInfo.BallBlurCurveFactor;
            eff.DofDiffusionBloomOverlayParam.BallBlurBrightnessThreshhold = updateInfo.BallBlurBrightnessThreshhold;
            eff.DofDiffusionBloomOverlayParam.BallBlurBrightnessIntensity = updateInfo.BallBlurBrightnessIntensity;
            eff.DofDiffusionBloomOverlayParam.BallBlurSpread = updateInfo.BallBlurSpread;
            eff.DofDiffusionBloomOverlayParam.IsPointBallBlur = updateInfo.IsPointBallBlur;
        }
        private void OnUpdatePostEffect_BloomDiffusion(PostEffectUpdateInfo_BloomDiffusion updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            var param = eff.DofDiffusionBloomOverlayParam;

            param.IsEnableBloom = updateInfo.IsEnableBloom;
            param.BloomDofWeight = (updateInfo.bloomDofWeight);
            param.BloomThreshold = (updateInfo.threshold);
            param.BloomIntensity = (updateInfo.intensity);
            param.BloomBlurSize = updateInfo.BloomBlurSize;
            param.BloomBlendMode = updateInfo.BloomBlendMode;
            param.IsEnableDiffusion = updateInfo.isEnabledDiffusion;
            param.DiffusionBlurSize = updateInfo.diffusionBlurSize;
            param.DiffusionBright = updateInfo.diffusionBright;
            param.DiffusionThreshold = updateInfo.diffusionThreshold;
            param.DiffusionSaturation = updateInfo.diffusionSaturation;
            param.DiffusionContrast = updateInfo.diffusionContrast;
        }

        /// <summary>
        /// ラジアルブラーのパラメータのタイムラインアップデート
        /// </summary>
        private void OnUpdateRadialBlur(ref RadialBlurUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            var param = eff.RadialBlurParam;

            param.Type = updateInfo.moveBlurType; // ブラーのかかる方向（縦、横、円　円は指定された点を中心に放射）
            param.RadialBlurOffset = updateInfo.radialBlurOffset;
            param.RadialBlurDownsample = updateInfo.radialBlurDownsample;
            param.RadialBlurStartArea = updateInfo.radialBlurStartArea;
            param.RadialBlurEndArea = updateInfo.radialBlurEndArea;
            param.RadialBlurPower = updateInfo.radialBlurPower;
            param.RadialBlurIteration = updateInfo.radialBlurIteration;
            param.RadialBlurEllipseDir = updateInfo.radialBlurEllipseDir;
            param.RadialBlurRollEulerAngles = updateInfo.radialBlurRollEulerAngles;
            param.IsEnabledDepth = updateInfo.isEnabledDepth;
            param.DepthPowerFront = updateInfo.depthPowerFront;
            param.DepthPowerBack = updateInfo.depthPowerBack;
            param.IsEnabledDepthCancelRect = updateInfo.isEnabledDepthCancelRect;
            param.DepthCancelRect = updateInfo.depthCancelRect;
            param.DepthCancelBlendLength = updateInfo.depthCancelBlendLength;
            param.IsExpandDepthCancelRect = updateInfo.isExpandDepthCancelRect;

#if CYG_DEBUG && UNITY_EDITOR
            param.IsDrawDebugDepthCancelRect = updateInfo.isDrawDebugDepthCancelRect;
#endif
        }

        private static readonly LiveCharaPositionFlag[] CharacterFlags =
        {
            LiveCharaPositionFlag.Place01,
            LiveCharaPositionFlag.Place02,
            LiveCharaPositionFlag.Place03,
            LiveCharaPositionFlag.Place04,
            LiveCharaPositionFlag.Place05,
            LiveCharaPositionFlag.Place06,
            LiveCharaPositionFlag.Place07,
            LiveCharaPositionFlag.Place08,
            LiveCharaPositionFlag.Place09,
            LiveCharaPositionFlag.Place10,
            LiveCharaPositionFlag.Place11,
            LiveCharaPositionFlag.Place12,
            LiveCharaPositionFlag.Place13,
            LiveCharaPositionFlag.Place14,
            LiveCharaPositionFlag.Place15,
            LiveCharaPositionFlag.Place16,
            LiveCharaPositionFlag.Place17,
            LiveCharaPositionFlag.Place18,
        };

        /// <summary>
        /// キャラクタオブジェクトをIDから取得する
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public CharacterObject GetCharacterObjectFromPosition(LiveCharaPosition id)
        {
            // _characterObjectsはListであるためサイズが可変
            return ((int)id < _characterObjectList.Count) ? _characterObjectList[(int)id] : null;
        }

        /// <summary>
        /// キャラクター影更新時に呼び出される
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnEnvironemntCharacterShadow(ref EnvironmentCharacterShadowUpdateInfo updateInfo)
        {
            if (!updateInfo.isValid)
            {
                return;
            }
            for (int i = 0; i < CharacterFlags.Length; i++)
            {
                var character = GetCharacterObjectFromPosition((LiveCharaPosition)i);
                if (character != null)
                {
                    character.realtimeShadowType = updateInfo.softShadow ? CharacterObject.RealtimeShadowType.SoftShadow : CharacterObject.RealtimeShadowType.HardShadow;
                    if (LiveCharaPositionFlag_Helper.hasFlag(updateInfo.positionFlag, CharacterFlags[i]))
                    {
                        character.enableRealtimeShadow = true;
                    }
                    else
                    {
                        character.enableRealtimeShadow = false;
                    }
                }
            }
        }

        private void UpdatePostFilm(ImageEffect.ScreenOverlay.Overlay overlay, ref PostFilmUpdateInfo updateInfo)
        {
#if CYG_DEBUG
            if (overlay.debug == false)
#endif
            {
#if UNITY_EDITOR
                overlay.ColorType = updateInfo.colorType;
#endif
                overlay.postFilmMode = updateInfo.filmMode;
                overlay.postFilmPower = updateInfo.filmPower;
                overlay.depthPower = updateInfo.depthPower;
                overlay.DepthClip = updateInfo.DepthClip;

                overlay.postFilmOffsetParam = updateInfo.filmOffsetParam;
                overlay.postFilmOptionParam = updateInfo.filmOptionParam;

                overlay.postFilmColor0 = updateInfo.color0;
                overlay.postFilmColor1 = updateInfo.color1;
                overlay.postFilmColor2 = updateInfo.color2;
                overlay.postFilmColor3 = updateInfo.color3;

                overlay.inverseVignette = updateInfo.inverseVignette;

                overlay.layerMode = updateInfo.layerMode;
                //ライブのUVMovieはスケールをかけない方式を使用する
                if (overlay.layerMode == ImageEffect.ScreenOverlay.Overlay.LayerMode.UVMovie)
                {
                    overlay.layerMode = ImageEffect.ScreenOverlay.Overlay.LayerMode.UVMovieNoScale;
                }
                overlay.movieResId = updateInfo.movieResId;
                overlay.colorBlend = updateInfo.colorBlend;
                overlay.colorBlendFactor = updateInfo.colorBlendFactor;

                overlay.SetRollAngle(updateInfo.RollAngle);
                overlay.SetScale(updateInfo.FilmScale);
            }

            // UVムービー対応
            overlay.SetMovieInfo(null, null, Math.VECTOR2_ZERO, Math.VECTOR2_ZERO);

            if ((overlay.postFilmMode != ImageEffect.ScreenOverlay.Overlay.PostFilmMode.None) &&
                ((overlay.layerMode == ImageEffect.ScreenOverlay.Overlay.LayerMode.UVMovie) ||
                 (overlay.layerMode == ImageEffect.ScreenOverlay.Overlay.LayerMode.UVMovieNoScale)))
            {
                var ctrl = StageController.GetMovieController(overlay.movieResId, true);

                //TODO:暫定対応だが、軽量版のUVムービーオーバーレイはとりあえず外した。
                //これのための実装がかなりめんどくさくなりそう。
                //マスターに軽量版UVアニメのリソースIDを登録して、UVアニメ配列にとびが出来てもいいならその実装
                if (ctrl != null && LiveQualitySettings.IsUseMonitor)
                {
                    overlay.SetMovieInfo(ctrl.MainTexture,
                        ctrl.ExistMaskTex ? ctrl.MaskTexture : null,
                        ctrl.MainTextureScale, ctrl.MainTextureOffset);

                    StageController.UpdateMovieController(ctrl,
                        updateInfo.movieFrameOffset, updateInfo.movieTime, updateInfo.movieReverse);
                }
                else
                {
                    // UVムービーを使用する設定で取得に失敗した場合はオーバーレイの処理を無効にする。
                    overlay.postFilmMode = ImageEffect.ScreenOverlay.Overlay.PostFilmMode.None;
                }
            }
        }

        /// <summary>
        /// ポストフィルターのパラメータのタイムラインアップデート
        /// </summary>
        private void OnUpdatePostFilm(ref PostFilmUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            UpdatePostFilm(eff.DofDiffusionBloomOverlayParam.ScreenOverlay.Overlay1, ref updateInfo);
        }

        private void OnUpdatePostFilm2(ref PostFilmUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            UpdatePostFilm(eff.DofDiffusionBloomOverlayParam.ScreenOverlay.Overlay2, ref updateInfo);
        }

        private void OnUpdatePostFilm3(ref PostFilmUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];
            UpdatePostFilm(eff.DofDiffusionBloomOverlayParam.ScreenOverlay.Overlay3, ref updateInfo);
        }

        private void OnUpdatePostFilm1MultiCamera(ref PostFilmUpdateInfo updateInfo, int cameraNo)
        {
            if (_multiCameraCompositionArray == null)
            {
                return;
            }
            if (cameraNo >= _multiCameraCompositionArray.Length)
            {
                return;
            }

            GallopImageEffect eff = _multiCameraCompositionArray[cameraNo].ImageEffect;
            UpdatePostFilm(eff.DofDiffusionBloomOverlayParam.ScreenOverlay.Overlay1, ref updateInfo);
        }

        private void OnUpdatePostEffect_DOFMultiCamera(ref PostEffectUpdateInfo_DOF updateInfo, int cameraNo)
        {
            if (_multiCameraCompositionArray == null)
            {
                return;
            }
            if (cameraNo >= _multiCameraCompositionArray.Length)
            {
                return;
            }

            GallopImageEffect eff = _multiCameraCompositionArray[cameraNo].ImageEffect;
            var param = eff.DofDiffusionBloomOverlayParam;

            param.IsEnableDof = updateInfo.IsEnableDOF;
            param.DofFocalSize = updateInfo.forcalSize;
            param.DofBlurType = updateInfo.dofBlurType;
            param.DofQualityType = updateInfo.dofQuality;

            param.DofForegroundSize = updateInfo.dofForegroundSize;
            param.DofMaxBlurSpread = (updateInfo.blurSpread);
            param.DofSmoothness = updateInfo.dofSoomthness;
            if (updateInfo.isUseFocalPoint == true)
            {
                param.DofFocalPoint = (updateInfo.dofFocalPoint);
            }
            else
            {
                param.DofFocalPosition = (updateInfo.forcalPosition);
            }
        }

        private void OnUpdatePostEffect_BloomDiffusionMultiCamera(ref PostEffectUpdateInfo_BloomDiffusion updateInfo, int cameraNo)
        {
            if (_multiCameraCompositionArray == null)
            {
                return;
            }
            if (cameraNo >= _multiCameraCompositionArray.Length)
            {
                return;
            }

            GallopImageEffect eff = _multiCameraCompositionArray[cameraNo].ImageEffect;
            var param = eff.DofDiffusionBloomOverlayParam;

            param.IsEnableBloom = updateInfo.IsEnableBloom;
            param.BloomDofWeight = (updateInfo.bloomDofWeight);
            param.BloomThreshold = (updateInfo.threshold);
            param.BloomIntensity = (updateInfo.intensity);
            param.BloomBlurSize = updateInfo.BloomBlurSize;
            param.BloomBlendMode = updateInfo.BloomBlendMode;
            param.IsEnableDiffusion = updateInfo.isEnabledDiffusion;
            param.DiffusionBlurSize = updateInfo.diffusionBlurSize;
            param.DiffusionBright = updateInfo.diffusionBright;
            param.DiffusionThreshold = updateInfo.diffusionThreshold;
            param.DiffusionSaturation = updateInfo.diffusionSaturation;
            param.DiffusionContrast = updateInfo.diffusionContrast;
        }

        private void OnUpdateColorCorrectionMultiCamera(ref ColorCorrectionUpdateInfo updateInfo, int cameraNo)
        {
            if (_multiCameraCompositionArray == null)
            {
                return;
            }
            if (cameraNo >= _multiCameraCompositionArray.Length)
            {
                return;
            }

            GallopImageEffect eff = _multiCameraCompositionArray[cameraNo].ImageEffect;
            Gallop.ImageEffect.ColorCorrectionParam param = eff.ColorCorrectionParam;
            param.Saturation = updateInfo.saturation;
            param.Mode = updateInfo.mode;
            param.RedChannel = updateInfo.redCurve;
            param.GreenChannel = updateInfo.greenCurve;
            param.BlueChannel = updateInfo.blueCurve;
            param.DepthRedChannel = updateInfo.depthRedCurve;
            param.DepthGreenChannel = updateInfo.depthGreenCurve;
            param.DepthBlueChannel = updateInfo.depthBlueCurve;
            param.ZCurve = updateInfo.blendCurve;
            param.IsSelectiveCc = updateInfo.selective;
            param.SelectiveFromColor = updateInfo.keyColor;
            param.SelectiveToColor = updateInfo.targetColor;

            if (updateInfo.enable)
            {
                param.UpdateParameters();
            }

            eff.ColorCorrectionParam.IsEnable = updateInfo.enable;
        }

        private void OnUpdateTiltShiftMultiCamera(ref TiltShiftUpdateInfo updateInfo, int cameraNo)
        {
            if (_multiCameraCompositionArray == null)
            {
                return;
            }
            if (cameraNo >= _multiCameraCompositionArray.Length)
            {
                return;
            }

            GallopImageEffect eff = _multiCameraCompositionArray[cameraNo].ImageEffect;
            var tiltShift = eff.TiltShiftParam;

            if (!tiltShift.UnlinkCutt)
            {
                tiltShift.Mode = updateInfo.mode;
                tiltShift.Quality = updateInfo.quality;

                tiltShift.BlurArea = updateInfo.blurArea;
                tiltShift.MaxBlurSize = updateInfo.maxBlurSize;
                tiltShift.Downsample = updateInfo.downsample;
                tiltShift.Offset = updateInfo.offset;
                tiltShift.Roll = updateInfo.roll;
            }
        }

        private void OnUpdateRadialBlurMultiCamera(ref RadialBlurUpdateInfo updateInfo, int cameraNo)
        {
            if (_multiCameraCompositionArray == null)
            {
                return;
            }
            if (cameraNo >= _multiCameraCompositionArray.Length)
            {
                return;
            }

            GallopImageEffect eff = _multiCameraCompositionArray[cameraNo].ImageEffect;
            var param = eff.RadialBlurParam;

            param.Type = updateInfo.moveBlurType; // ブラーのかかる方向（縦、横、円　円は指定された点を中心に放射）
            param.RadialBlurOffset = updateInfo.radialBlurOffset;
            param.RadialBlurDownsample = updateInfo.radialBlurDownsample;
            param.RadialBlurStartArea = updateInfo.radialBlurStartArea;
            param.RadialBlurEndArea = updateInfo.radialBlurEndArea;
            param.RadialBlurPower = updateInfo.radialBlurPower;
            param.RadialBlurIteration = updateInfo.radialBlurIteration;
            param.RadialBlurEllipseDir = updateInfo.radialBlurEllipseDir;
            param.RadialBlurRollEulerAngles = updateInfo.radialBlurRollEulerAngles;
            param.IsEnabledDepth = updateInfo.isEnabledDepth;
            param.DepthPowerFront = updateInfo.depthPowerFront;
            param.DepthPowerBack = updateInfo.depthPowerBack;
            param.IsEnabledDepthCancelRect = updateInfo.isEnabledDepthCancelRect;
            param.DepthCancelRect = updateInfo.depthCancelRect;
            param.DepthCancelBlendLength = updateInfo.depthCancelBlendLength;
            param.IsExpandDepthCancelRect = updateInfo.isExpandDepthCancelRect;

#if CYG_DEBUG && UNITY_EDITOR
            param.IsDrawDebugDepthCancelRect = updateInfo.isDrawDebugDepthCancelRect;
#endif
        }

        private bool _enableTimelineGlobalRimParam = true;

        public void SetEnableTimelineGlobalRimParam(bool enable)
        {
            _enableTimelineGlobalRimParam = enable;
        }

        /// <summary>
        /// 全体にかかるライトの向きを設定
        /// </summary>
        /// <param name="qua"></param>
        private void SetGlobalLightingDirection(Quaternion qua)
        {
            if (_globalLightTransform != null)
            {
                _globalLightTransform.localRotation = qua;
            }
        }

        /// <summary>
        /// ライトの向きをキャラごとに指定する
        /// </summary>
        /// <param name="updateInfo"></param>
        private void SetCharaLightDirection(ref GlobalLightUpdateInfo updateInfo)
        {
            for (var i = LiveCharaPosition.Place01; i <= LiveCharaPosition.CharacterMax; i++)
            {
                if (!updateInfo.IsApplyTarget(i))
                {
                    //対象じゃない
                    continue;
                }

                var characterObject = GetCharacterObjectFromPosition(i);
                if (characterObject == null)
                {
                    continue;
                }

                for (int d = 0; d < characterObject.ReserveDressModelCount; d++)
                {
                    characterObject.LiveModelControllerArray[d].SetAllLightRotation(true, updateInfo.lightRotation);
                }
            }
        }

        /// <summary>
        /// グローバルライト更新時に呼び出される
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateGlobalLight(ref GlobalLightUpdateInfo updateInfo)
        {
            SetCharaLightDirection(ref updateInfo);

            if (_enableTimelineGlobalRimParam)
            {
                #region リムのコントロールに使用するパラメータ TOON_RIM_CONTROLL

                //SetGlobalLightingParam(updateInfo.globalRimShadowRate, updateInfo.rimColor, updateInfo.rimStep, updateInfo.rimFeather, updateInfo.rimSpecRate);
                SetRimParameter(ref updateInfo);

                #endregion リムのコントロールに使用するパラメータ TOON_RIM_CONTROLL
            }
        }

        /// <summary>
        /// IndirectLightShaftsのパラメータ更新時に呼び出される
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateLightShafts(ref LightShaftsUpdateInfo updateInfo)
        {
            if (_indirectLightShaftsParamArray == null)
            {
                return;
            }

            for (int i = 0; i < _indirectLightShaftsParamArray.Length; i++)
            {
                var lightShaftsParam = _indirectLightShaftsParamArray[i];
                if (lightShaftsParam == null)
                {
                    continue;
                }
                lightShaftsParam.IsEnable = updateInfo.IsEnabled;
                lightShaftsParam.Angle = updateInfo.Angle / 360.0f;  //LightShaftsの都合で360=1.0となるはず
                lightShaftsParam.Speed = updateInfo.Speed;
                lightShaftsParam.Scale = updateInfo.Scale;
                lightShaftsParam.Offset = updateInfo.Offset;
                lightShaftsParam.Alpha = updateInfo.Alpha;
                lightShaftsParam.Alpha2 = updateInfo.Alpha2;
                lightShaftsParam.MaskAlpha = updateInfo.MaskAlpha;
            }
        }

        /// <summary>
        /// ColorCorrectionのパラメータ更新時に呼び出される
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateColorCorrection(ref ColorCorrectionUpdateInfo updateInfo)
        {
            if (_colorCorrectionParameter != null)
            {
                _colorCorrectionParameter.Saturation = updateInfo.saturation;
                _colorCorrectionParameter.Mode = updateInfo.mode;
                _colorCorrectionParameter.RedChannel = updateInfo.redCurve;
                _colorCorrectionParameter.GreenChannel = updateInfo.greenCurve;
                _colorCorrectionParameter.BlueChannel = updateInfo.blueCurve;
                _colorCorrectionParameter.DepthRedChannel = updateInfo.depthRedCurve;
                _colorCorrectionParameter.DepthGreenChannel = updateInfo.depthGreenCurve;
                _colorCorrectionParameter.DepthBlueChannel = updateInfo.depthBlueCurve;
                _colorCorrectionParameter.ZCurve = updateInfo.blendCurve;
                _colorCorrectionParameter.IsSelectiveCc = updateInfo.selective;
                _colorCorrectionParameter.SelectiveFromColor = updateInfo.keyColor;
                _colorCorrectionParameter.SelectiveToColor = updateInfo.targetColor;

                if (updateInfo.enable)
                {
                    _colorCorrectionParameter.UpdateParameters();
                }

                //カラコレは半透明カメラが担うので、各カメラのカラコレは無効にする
                if (_imageEffectLive3dList != null)
                {
                    int num = _imageEffectLive3dList.Count;
                    for (int i = 0; i < num; i++)
                    {
                        _imageEffectLive3dList[i].ColorCorrectionParam.IsEnable = updateInfo.enable;
                    }
                }
            }
        }

        private void OnUpdateTiltShift(ref TiltShiftUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];

            if (eff != null)
            {
                var tiltShift = eff.TiltShiftParam;

                if (tiltShift.UnlinkCutt == false)
                {
                    tiltShift.Mode = updateInfo.mode;
                    tiltShift.Quality = updateInfo.quality;

                    tiltShift.BlurArea = updateInfo.blurArea;
                    tiltShift.MaxBlurSize = updateInfo.maxBlurSize;
                    tiltShift.Downsample = updateInfo.downsample;
                    tiltShift.Offset = updateInfo.offset;
                    tiltShift.Roll = updateInfo.roll;
                }
            }
        }

        /// <summary>
        /// 顔Toonのパラメータのタイムラインアップデート
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateFacialToon(ref FacialToonDataUpdateInfo updateInfo)
        {
            int count = _characterObjectList.Count;
            for (int i = 0; i < count; i++)
            {
                if ((updateInfo.CharacterPos & (1 << i)) != 0)
                {
                    CharacterObject characterObject = _characterObjectList[i];
                    if (characterObject == null)
                    {
                        return;
                    }

                    for (int d = 0; d < characterObject.ReserveDressModelCount; d++)
                    {
                        LiveModelController liveModelController = characterObject.LiveModelControllerArray[d];
                        liveModelController.SetFacialToonUpdateInfo(updateInfo);
                    }
                }
            }
        }

        /// <summary>
        /// ゆらぎのパラメータのタイムラインアップデート
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateFluctuation(FluctuationUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];

            if (eff != null)
            {
                var fluctuation = eff.FluctuationParam;

                fluctuation.IsEnable = updateInfo.IsEnable;
                fluctuation.SetDisableGraphicSettings(updateInfo.DisableGraphicSettings);

                if (fluctuation.IsValidity)
                {
                    fluctuation.MoveDirection = updateInfo.MoveDirection;
                    fluctuation.MovePower = updateInfo.MovePower;
                    fluctuation.Power = updateInfo.Power;
                    fluctuation.DepthClip = updateInfo.DepthClip;
                    if (!LiveTimeController.IsCalcElapsedTime)
                    {
                        //一時停止状態の場合はMovePowerを止めないとOnRenderImageでパラメータが更新される
                        fluctuation.MovePower = 0.0f;
                    }
                }
            }
        }

        /// <summary>
        /// ToneCurveパラメータのタイムラインアップデート
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateToneCurve(ref ToneCurveUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];

            if (eff != null)
            {
                var param = eff.ToneCurveParam;

                param.IsEnable = updateInfo.IsEnable;

                if(param.IsValidity)
                {
                    param.ToneAnimationCurve = updateInfo.ToneAnimationCurve;
                    param.MaskToneCurve = updateInfo.MaskToneCurve;
                    param.MinCorrectionLevel = updateInfo.MinCorrectionLevel;
                    param.MaxCorrectionLevel = updateInfo.MaxCorrectionLevel;
                    param.MaskMinCorrectionLevel = updateInfo.MaskMinCorrectionLevel;
                    param.MaskMaxCorrectionLevel = updateInfo.MaskMaxCorrectionLevel;
                    param.DepthMask = updateInfo.DepthMask;
                }
            }
        }

        /// <summary>
        /// Exposureパラメータのタイムラインアップデート
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateExposure(ref ExposureUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            GallopImageEffect eff = _imageEffectLive3dList[iPostEff];

            if (eff != null)
            {
                var param = eff.ExposureParam;

                param.IsEnable = updateInfo.IsEnable;

                if(param.IsValidity)
                {
                    param.ExposureParameter = updateInfo.ExposureParameter;
                    param.DepthMask = updateInfo.DepthMask;
                }
            }
        }

        /// <summary>
        /// 渦（円状の歪み）のパラメータのタイムラインアップデート
        /// </summary>
        /// <param name="updateInfo"></param>
        private void OnUpdateVortex(ref VortexUpdateInfo updateInfo)
        {
            int iPostEff = GetActiveCameraIndex();
            if (iPostEff < 0) return;

            LiveImageEffect eff = _imageEffectLive3dList[iPostEff];

            if (eff != null)
            {
                var vortex = eff.Vortex;
                if (vortex == null)
                    return;

                vortex.SetParamEnable(updateInfo.IsEnable);

                if (vortex.IsEnable)
                {
                    vortex.SetParamArea(ref updateInfo.Area);
                    vortex.SetParamRotVolume(updateInfo.RotVolume);
                    vortex.SetParamDepth(updateInfo.DepthClip);
                }
            }
        }

        #region Fade

        private void OnUpdateFade(ref FadeUpdateInfo updateInfo)
        {
            // 以前はUIManager.SetFadeColorを使用していたが、
            // ライブシーン終了時（EndScene内、FinalizeSceneでは遅い）にUIManager.ResetFadeを呼ぶと
            // ライブの最後のカットが一瞬見えるようになったため、自前のフェード処理を使用する
            _fadeController.SetColor(updateInfo.fadeColor);
        }

        #endregion Fade

        #region ChromaticAberration

        private void OnUpdateChromaticAberration(ref ChromaticAberrationUpdateInfo updateInfo)
        {
            foreach (var imageEffect in _imageEffectLive3dList)
            {
                var chromacitAberration = imageEffect.ChromaticAberration;
                chromacitAberration.IsEnable = updateInfo.isEnable;
                chromacitAberration.SetOffset(ref updateInfo.redOffset, ref updateInfo.greenOffset, ref updateInfo.blueOffset);
                chromacitAberration.Power = updateInfo.power;
                chromacitAberration.Clip = updateInfo.clip;
                chromacitAberration.Type = (ImageEffect.ChromaticAberration.ChromaticAberrationType)updateInfo.type;
            }
        }

        #endregion

        #region Spotlight3d

        private void OnUpdateSpotlight3d(ref Spotlight3dUpdateInfo updateInfo)
        {
            for (var i = 0; i < CharacterFlags.Length; ++i)
            {
                if (!LiveCharaPositionFlag_Helper.hasFlag(updateInfo.characterFlag, CharacterFlags[i]))
                {
                    continue;
                }
                var character = GetCharacterObjectFromPosition((LiveCharaPosition)i);
                if (character == null)
                {
                    continue;
                }
                var spotlight3dController = character.GetSpotlight3dController(updateInfo.timelineIndex);
                if (spotlight3dController == null)
                {
                    continue;
                }
                spotlight3dController.SetUpdateInfo(ref updateInfo);
            }
        }

        #endregion Spotlight3d

        #region NodeScale

        /// <summary>
        /// 一時使用変数
        /// </summary>
        private Vector3 _tempRateVector = Math.VECTOR3_ZERO;
        private Vector3 _tempAttachRateVector = Math.VECTOR3_ZERO;

        private void OnUpdateNodeScale(ref NodeScaleUpdateInfo updateInfo)
        {
            float rate = 0f;
            float attachRate = 0f;
            if (updateInfo.scaleRate > 0f)
            {
                rate = updateInfo.scaleRate;
                attachRate = 1f / rate;
            }
            _tempRateVector.x = rate;
            _tempRateVector.y = rate;
            _tempRateVector.z = rate;
            _tempAttachRateVector.x = attachRate;
            _tempAttachRateVector.y = attachRate;
            _tempAttachRateVector.z = attachRate;

            Transform transform;
            for (var i = 0; i < CharacterFlags.Length; ++i)
            {
                if (!LiveCharaPositionFlag_Helper.hasFlag(updateInfo.characterFlag, CharacterFlags[i]))
                {
                    continue;
                }
                var characterObject = GetCharacterObjectFromPosition((LiveCharaPosition)i);
                if (characterObject == null)
                {
                    continue;
                }
                for (int d = 0; d < characterObject.ReserveDressModelCount; d++)
                {
                    var modelController = characterObject.LiveModelControllerArray[d];
                    // 左手首
                    if ((updateInfo.targetFlag & LiveNodeScaleTargetFlag.WristLeft) != 0)
                    {
                        transform = modelController.LeftWristTransform;
                        if (transform != null)
                        {
                            transform.localScale = _tempRateVector;
                        }
                        transform = modelController.LeftHandAttachTransform;
                        if (transform != null)
                        {
                            transform.localScale = _tempAttachRateVector;
                        }
                    }
                    // 右手首
                    if ((updateInfo.targetFlag & LiveNodeScaleTargetFlag.WristRight) != 0)
                    {
                        transform = modelController.RightWristTransform;
                        if (transform != null)
                        {
                            transform.localScale = _tempRateVector;
                        }
                        transform = modelController.RightHandAttachTransform;
                        if (transform != null)
                        {
                            transform.localScale = _tempAttachRateVector;
                        }
                    }
                }
            }
        }

        #endregion NodeScale

        #region CharaFootLight

        /// <summary>
        /// 足元ライトの更新
        /// </summary>
        /// <param name="info"></param>
        private void OnUpdateCharaFootLight(ref CharaFootLightUpdateInfo info)
        {
            var characterObject = GetCharacterObjectFromPosition(info.position);
            if (characterObject != null)
            {
                characterObject.SetFootLightParameter(info.hightMax, info.lightColor);
            }
        }

        #endregion

        #region CharaParts

        private void OnUpdateCharaParts(ref CharaPartsUpdateInfo info)
        {
            var characterObject = GetCharacterObjectFromPosition(info.CharacterPosition);
            if (characterObject == null)
                return;
            var modelArray = characterObject.LiveModelControllerArray;
            foreach (var model in modelArray)
            {
                if (model == null)
                    continue;
                model.SetRendererVisible(info.RendererHash, info.IsVisible);
            }
        }

        #endregion

        #endregion コールバック
    }
}
