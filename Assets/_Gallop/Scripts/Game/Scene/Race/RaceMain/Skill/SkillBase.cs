using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    public struct SkillBuildInfo
    {
        public readonly MasterSkillData.SkillData masterSkill;
        public readonly int Level;
        public readonly IHorseRaceInfo ownerInfo;
        public readonly IRaceHorseAccessor horseAccessor;
        public readonly IRaceCourseAttributeAccessor courseAttributeAccessor;
        public readonly IRaceTimeAccessor timeAccessor;
        public readonly IRaceRandomGenerator randomGenerator;
        public readonly IRaceEventRecorder SkillEventRecorder;
        public readonly RaceInfo raceInfo;
        public readonly RaceParamDefine.SkillParam SkillParam;

        public SkillBuildInfo(
            MasterSkillData.SkillData masterSkill,
            int level,
            IHorseRaceInfo owner,
            IRaceHorseAccessor horseAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceRandomGenerator randomGenerator,
            IRaceEventRecorder skillEventRecorder,
            RaceInfo raceInfo,
            RaceParamDefine.SkillParam skillParam)
        {
            this.masterSkill = masterSkill;
            this.Level = level;
            this.ownerInfo = owner;
            this.horseAccessor = horseAccessor;
            this.courseAttributeAccessor = courseAttributeAccessor;
            this.timeAccessor = timeAccessor;
            this.randomGenerator = randomGenerator;
            this.SkillEventRecorder = skillEventRecorder;
            this.raceInfo = raceInfo;
            this.SkillParam = skillParam;
        }
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル基底。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillBase
    {
        private const int SKILL_DETAIL_CAPACITY = 2;
        public const int SKILL_ACTIVATE_LOT_TRUE = 1;

        private readonly IHorseRaceInfo _ownerInfo;
        private readonly ISkillTriggerCreator _triggerCreator;
        private readonly IRaceRandomGenerator _randomGenerator;
        private readonly RaceParamDefine.SkillParam _skillParam;

        /// <summary>
        /// このレース中、発動可能かどうか。
        /// </summary>
        /// <remarks>レース出走前に一度だけ抽選される</remarks>
        public bool IsActivateEnable { get; private set; }
#if CYG_DEBUG
        private bool _dbgIsLotActivateInitialized;
#endif

        /// <summary>
        /// スキル発動単位のリスト取得。
        /// </summary>
        public List<ISkillDetail> Details { get; private set; }

        /// <summary>
        /// スキルマスター。
        /// </summary>
        public MasterSkillData.SkillData SkillMaster { get; private set; }

        /// <summary>
        /// スキルマスターのId。
        /// </summary>
        public int SkillMasterId
        {
            get { return SkillMaster.Id; }
        }

        /// <summary>
        /// レベル。1~。
        /// </summary>
        public int Level { get; private set; }

        /// <summary>
        /// クールダウン時間残り。
        /// </summary>
        private float _coolDownTime;
    #if CYG_DEBUG
        public float DbgCoolDownTime => _coolDownTime;
    #endif

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        //---------------------------------------------------------------
        public SkillBase(ref SkillBuildInfo buildInfo, ISkillTriggerCreator triggerCreator)
        {
            _ownerInfo = buildInfo.ownerInfo;
            _triggerCreator = triggerCreator;
            _randomGenerator = buildInfo.randomGenerator;
            _skillParam = buildInfo.SkillParam;
            SkillMaster = buildInfo.masterSkill;
            Level = buildInfo.Level;
            SetupDetail(
                buildInfo.Level,
                buildInfo.raceInfo,
                buildInfo.SkillParam,
                buildInfo.horseAccessor,
                buildInfo.courseAttributeAccessor,
                buildInfo.timeAccessor,
                buildInfo.randomGenerator,
                buildInfo.SkillEventRecorder);
        }

        //---------------------------------------------------------------
        private ISkillDetail CreateSkillDetail(
            int detailIndex, 
            float leftTime, 
            SkillDefine.SkillAbilityTimeUsage timeUsage, 
            float coolDownTime, 
            int courseDistance, 
            IRaceTimeAccessor timeAccessor, 
            IRaceEventRecorder skillEventRecorder,
            IRaceHorseAccessor horseAccessor)
        {
            return new SkillDetail(
                owner: _ownerInfo,
                timeAccessor: timeAccessor,
                skillEventRecorder: skillEventRecorder,
                horseAccessor: horseAccessor,
                masterSkill: SkillMaster,
                skillParam: _skillParam,
                skillLevel: Level,
                detailIndex: detailIndex,
                leftTime: leftTime,
                timeUsage: timeUsage,
                coolDownTime: coolDownTime,
                courseDistance: courseDistance,
                abilityTimeDivideDistance: _skillParam.AbilityTimeDivideDistance,
                coolDownTimeDivideDistance: _skillParam.CoolDownTimeDivideDistance);
        }

        //---------------------------------------------------------------
        private void SetupDetail(
            int level,
            RaceInfo raceInfo,
            RaceParamDefine.SkillParam skillParam,
            IRaceHorseAccessor horseAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceRandomGenerator randomGenerator,
            IRaceEventRecorder skillEventRecorder)
        {
            Details = new List<ISkillDetail>(SKILL_DETAIL_CAPACITY);

            var triggerBuildInfo = new SkillTriggerCreatorBuildInfo(
                _ownerInfo,
                horseAccessor,
                courseAttributeAccessor,
                timeAccessor,
                randomGenerator,
                raceInfo,
                skillParam,
                this);

            // 発動単位１つ目。
            {
                var leftTime = Math.MasterInt2Float(SkillMaster.FloatAbilityTime1);
                var coolDownTime = Math.MasterInt2Float(SkillMaster.FloatCooldownTime1);

                var newDetail = CreateSkillDetail(
                    detailIndex: 0, 
                    leftTime: leftTime,
                    (SkillDefine.SkillAbilityTimeUsage)SkillMaster.AbilityTimeUsage1,
                    coolDownTime: coolDownTime,
                    courseDistance: raceInfo.CourseDistance,
                    timeAccessor: timeAccessor,
                    skillEventRecorder: skillEventRecorder,
                    horseAccessor: horseAccessor);
                Details.Add(newDetail);

                // 前提条件。
                var preTrigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Precondition1);
                newDetail.SetPreTrigger(preTrigger);
                
                // 発動条件。
                var trigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Condition1);
                newDetail.SetTrigger(trigger);

                // この発動単位がデバフかどうか。
                newDetail.IsDebuff = RaceUtil.IsDebuff(
                    (SkillDefine.SkillAbilityType) SkillMaster.AbilityType11,
                    Math.MasterInt2Float(SkillMaster.FloatAbilityValue11));
                
                // スキルカテゴリ。
                newDetail.Category = RaceUtil.GetSkillCategory((SkillDefine.SkillAbilityType)SkillMaster.AbilityType11);

                // スキルエフェクト名キャッシュ。
                newDetail.SkillEffectName = ResourcePath.GetSkillEffectName(newDetail.IsDebuff, newDetail.Category);
                newDetail.ActivateEffectName = ResourcePath.GetSkillActivateEffectName(newDetail.IsDebuff, newDetail.Category);
                // スキルAudioIdキャッシュ。
                newDetail.MainCueName = ResourcePath.GetSkillMainAudioId(newDetail.IsDebuff, newDetail.Category);
                newDetail.ActivateCueName = ResourcePath.GetSkillActivateAudioId(newDetail.IsDebuff);
                
                SkillMaster.ProcessDetail1((abilityType, abilityValueUsage, abilityValueLevelUsage, abilityValue, targetType, targetValue) =>
                {
                    if (SkillDefine.SkillAbilityType.None == abilityType)
                    {
                        return;
                    }

                    var ability = CreateAbility(
                        level,
                        newDetail,
                        raceInfo,
                        horseAccessor,
                        randomGenerator,
                        targetType,
                        targetValue,
                        abilityType,
                        abilityValueUsage,
                        abilityValueLevelUsage,
                        abilityValue);

                    if (null != ability)
                    {
                        newDetail.AddAbility(ability);
                    }
                });
            }

            // 発動単位２つ目。
            {
                var leftTime = Math.MasterInt2Float(SkillMaster.FloatAbilityTime2);
                var coolDownTime = Math.MasterInt2Float(SkillMaster.FloatCooldownTime2);

                var newDetail = CreateSkillDetail(
                    detailIndex: 1,
                    leftTime: leftTime,
                    (SkillDefine.SkillAbilityTimeUsage)SkillMaster.AbilityTimeUsage2,
                    coolDownTime: coolDownTime,
                    courseDistance: raceInfo.CourseDistance,
                    timeAccessor: timeAccessor,
                    skillEventRecorder: skillEventRecorder,
                    horseAccessor: horseAccessor);
                Details.Add(newDetail);

                // 前提条件。
                var preTrigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Precondition2);
                newDetail.SetPreTrigger(preTrigger);
                
                // 発動条件。
                var trigger = _triggerCreator.CreateTriggerCollection(ref triggerBuildInfo, SkillMaster.Condition2);
                newDetail.SetTrigger(trigger);

                // この発動単位がデバフかどうか。
                newDetail.IsDebuff = RaceUtil.IsDebuff(
                    (SkillDefine.SkillAbilityType) SkillMaster.AbilityType21,
                    Math.MasterInt2Float(SkillMaster.FloatAbilityValue21));

                // スキルカテゴリ。
                newDetail.Category = RaceUtil.GetSkillCategory((SkillDefine.SkillAbilityType)SkillMaster.AbilityType21);
                
                // スキルエフェクト名キャッシュ。
                newDetail.SkillEffectName = ResourcePath.GetSkillEffectName(newDetail.IsDebuff, newDetail.Category);
                newDetail.ActivateEffectName = ResourcePath.GetSkillActivateEffectName(newDetail.IsDebuff, newDetail.Category);
                // スキルAudioIdキャッシュ。
                newDetail.MainCueName = ResourcePath.GetSkillMainAudioId(newDetail.IsDebuff, newDetail.Category);
                newDetail.ActivateCueName = ResourcePath.GetSkillActivateAudioId(newDetail.IsDebuff);

                SkillMaster.ProcessDetail2((abilityType, abilityValueUsage, abilityValueLevelUsage, abilityValue, targetType, targetValue) =>
                {
                    if (SkillDefine.SkillAbilityType.None == abilityType)
                    {
                        return;
                    }

                    var ability = CreateAbility(
                        level,
                        newDetail,
                        raceInfo,
                        horseAccessor,
                        randomGenerator,
                        targetType,
                        targetValue,
                        abilityType,
                        abilityValueUsage,
                        abilityValueLevelUsage,
                        abilityValue);

                    if (null != ability)
                    {
                        newDetail.AddAbility(ability);
                    }
                });
            }
        }

        private ISkillAbility CreateAbility(
            int level,
            ISkillDetail ownerDetail,
            RaceInfo raceInfo,
            IRaceHorseAccessor horseAccessor,
            IRaceRandomGenerator randomGenerator,
            SkillDefine.SkillTargetType targetType,
            int targetValue,
            SkillDefine.SkillAbilityType abilityType,
            SkillDefine.SkillAbilityValueUsage abilityValueUsage,
            SkillDefine.SkillAbilityValueLevelUsage abilityValueLevelUsage,
            float abilityValue)
        {
            // #71369 味方にデバフを与えないために、ability_type_*_*ごとにデバフ判定を行う。
            bool isDebuff = RaceUtil.IsDebuff(abilityType, abilityValue);
            
            var targetBuildInfo = new SkillTargetBuildInfo(_ownerInfo, horseAccessor, randomGenerator, raceInfo, targetValue, isDebuff);
            var abilityTarget = SkillTargetCreator.CreateSkillTarget(ref targetBuildInfo, targetType, SkillMasterId);
            if (null == abilityTarget)
            {
                return null;
            }

            var newAbility = SkillAbilityCreator.CreateSkillAbility(
                level,
                _ownerInfo,
                ownerDetail,
                abilityType,
                abilityValueUsage,
                abilityValueLevelUsage,
                abilityValue,
                abilityTarget,
                horseAccessor,
                randomGenerator,
                _skillParam);
            return newAbility;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スキル停止。
        /// </summary>
        //---------------------------------------------------------------
        public virtual void Stop()
        {
            for (int i = 0, cnt = Details.Count; i < cnt; ++i)
            {
                Details[i].Stop();
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// スキル効果発動中か。
        /// </summary>
        /// <returns>一つでも発動中のISkillDetailがあればtrueを返却。</returns>
        //---------------------------------------------------------------
        private bool IsActivatedAny()
        {
            // レース開始前の抽選に外れていたら、このスキルは発動不可能。
            if (!CheckActivateEnable())
            {
                return false;
            }

            for (int i = 0, cnt = Details.Count; i < cnt; ++i)
            {
                if (Details[i].IsActivated)
                {
                    return true;
                }
            }
            return false;
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 毎フレームの更新。
        /// </summary>
        //---------------------------------------------------------------
        public virtual void Update(float deltaTime)
        {
            // レース開始前の抽選に外れていたら、このスキルは発動不可能。
            if (!CheckActivateEnable())
            {
                return;
            }

            // 発動中の効果がある場合、残り効果時間の更新。
            if (IsActivatedAny())
            {
                for (int i = 0, cnt = Details.Count; i < cnt; ++i)
                {
                    // 発動していない効果は更新不要。
                    if (!Details[i].IsActivated)
                    {
                        continue;
                    }
                    
                    // 効果時間終了したら、クールダウンの開始。
                    if (Details[i].UpdateLeftTime(deltaTime))
                    {
                        _coolDownTime = Details[i].CoolDownTime;
                    }
                }
            }
            // 発動中の効果が無い場合、クールダウンや発動チェック。
            else
            {
                // クールダウン時間更新。
                if (CheckCoolDown(deltaTime))
                {
                    // 発動条件チェックと発動。
                    for (int i = 0, cnt = Details.Count; i < cnt; ++i)
                    {
                        // 既に発動中の効果はチェック不要。
                        if (Details[i].IsActivated)
                        {
                            continue;
                        }

                        // １フレーム内で発動するのは１つの効果のみ。indexが若い方が優先。
                        if (Details[i].UpdateTriggerAndActivate(deltaTime))
                        {
                            break;
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// クールダウン時間更新。
        /// </summary>
        /// <returns>クールダウンが0になって次のスキル発動可能ならtrue</returns>
        private bool CheckCoolDown(float deltaTime)
        {
            return RaceUtil.UpdateTimer(ref _coolDownTime, deltaTime);
        }

        //---------------------------------------------------------------
        /// <summary>
        /// 発動条件を満たしているスキルを発動。
        /// </summary>
        //---------------------------------------------------------------
        public virtual void CheckTriggerAndActivate()
        {
            // レース開始前の抽選に外れていたら、このスキルは発動不可能。
            if (!CheckActivateEnable())
            {
                return;
            }

            for (int i = 0, cnt = Details.Count; i < cnt; ++i)
            {
                // 発動条件チェック→発動を行う。
                Details[i].UpdateTriggerAndActivate(0);
            }
        }

        //---------------------------------------------------------------
        public void LotActivate()
        {
            // 賢さを元にした発動抽選を行う。
            if(SkillMaster.ActivateLot == SKILL_ACTIVATE_LOT_TRUE)
            {
                float per = RaceUtil.CalcActivatePer(_ownerInfo.BaseWiz, _skillParam);
                IsActivateEnable = _randomGenerator.GetRandom(100.0f) < per;
            }
            // 発動抽選は行わない（当たった扱い）。
            else
            {
                IsActivateEnable = true;
            }

        #if CYG_DEBUG
            _dbgIsLotActivateInitialized = true;
        #endif
        }

        //---------------------------------------------------------------
        private bool CheckActivateEnable()
        {
        #if CYG_DEBUG
            DebugUtils.Assert(_dbgIsLotActivateInitialized, "出走前の発動抽選が行われていません");
        #endif
            return IsActivateEnable;
        }
    }

}
