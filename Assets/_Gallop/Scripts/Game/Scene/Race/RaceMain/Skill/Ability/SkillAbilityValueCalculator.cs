using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機インターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface ISkillAbilityValueCalculator
    {
        float AbilityValue { get; }
    };

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：ctorでAbilityValue固定：int：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyBaseStaticInt : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfo _owner = null;
        protected readonly float _abilityValueBase = 0;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseStaticInt( IHorseRaceInfo owner, float abilityValueBase )
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
            AbilityValue = _abilityValueBase * GetMultiply(); 
        }

        //---------------------------------------------------------------
        public float AbilityValue { get; private set; }

        //---------------------------------------------------------------
        protected abstract int GetMultiply();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：ctorでAbilityValue固定：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyBaseStaticFloat : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfo _owner = null;
        protected readonly float _abilityValueBase = 0;
        protected readonly RaceParamDefine.SkillParam _skillParam;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseStaticFloat(IHorseRaceInfo owner, float abilityValueBase, RaceParamDefine.SkillParam skillParam)
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
            _skillParam = skillParam;
            AbilityValue = _abilityValueBase * GetMultiply(); 
        }

        //---------------------------------------------------------------
        public float AbilityValue { get; private set; }

        //---------------------------------------------------------------
        protected abstract float GetMultiply();
    }

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：AbilityValue取得毎に値計算：float：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyBaseDynamicFloat : ISkillAbilityValueCalculator
    {
        protected readonly IHorseRaceInfo _owner = null;
        protected readonly float _abilityValueBase = 0;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyBaseDynamicFloat( IHorseRaceInfo owner, float abilityValueBase )
        {
            _owner = owner;
            _abilityValueBase = abilityValueBase;
        }

        //---------------------------------------------------------------
        public float AbilityValue 
        { 
            get
            {
                return _abilityValueBase * GetMultiply();
            }
        }

        //---------------------------------------------------------------
        protected abstract float GetMultiply();
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：直値。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueDirect : ISkillAbilityValueCalculator
    {
        //---------------------------------------------------------------
        public SkillAbilityValueDirect( float abilityValueBase )
        {
            AbilityValue = abilityValueBase;
        }

        //---------------------------------------------------------------
        public float AbilityValue { get; private set; }
    };


    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：スキル所持数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplySkillNum : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary>最大倍率。</summary>
        private const float MULTIPLY_MAX = 1.2f;
        /// <summary>スキル１個あたりの増加倍率。</summary>
        private const float COEF_PER_SKILL = 0.01f;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplySkillNum(IHorseRaceInfo owner, float abilityValueBase) : base(owner, abilityValueBase)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            // このスキル自体は数から除くために-1。あり得ないはずだが一応0下回らないようにしておく。
            int skillNum = Mathf.Max(_owner.GetSkills().Length - 1, 0);
            // 所持スキル１個につき倍率加算。
            float multiply = Mathf.Min(1.0f + (skillNum * COEF_PER_SKILL), MULTIPLY_MAX);
            return multiply;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：回復スキルを使用した回数を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyHealSkillActivateCount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary>最大倍率。</summary>
        private const float MULTIPLY_MAX = 1.5f;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyHealSkillActivateCount(IHorseRaceInfo owner, float abilityValueBase) : base(owner, abilityValueBase)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int healSkillCount = _owner.GetActivateHealSkillCount();
            // 発動１回につきx0.1倍。
            float multiply = Mathf.Min(1.0f + (healSkillCount * 0.1f), MULTIPLY_MAX);
            return multiply;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【最終コーナー通過時の順位に応じた倍率】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyFinalCornerEndOrder : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        /// <summary>倍率の最大最小。</summary>
        private const float MULTIPLY_MAX = 1.2f;
        private const float MULTIPLY_MIN = 1.0f;
        /// <summary>順位が１下がるごとに倍率から引かれる値。</summary>
        private const float MULTIPLY_DOWN_RATE = 0.02f;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyFinalCornerEndOrder(IHorseRaceInfo owner, float abilityValueBase) : base(owner, abilityValueBase)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            // 最終コーナー終了時の順位が初期化される前なら倍率は1。
            if (!_owner.IsFinalCornerEndOrderInitialized)
            {
                return 1;
            }
            
            // 最終コーナー終了時の順位。0~の値だが計算上このまま使える。
            int finalCornerEndOrder = _owner.FinalCornerEndOrder;
            // １位通過（finalCornerEndOrder=0）でMULTIPLY_MAX倍。順位が１下がるごとにORDER_RATE%ずつ倍率が下がる。
            float multiply = MULTIPLY_MAX - (MULTIPLY_DOWN_RATE * finalCornerEndOrder);
            multiply = Mathf.Max(multiply, MULTIPLY_MIN);
            return multiply;
        }
    }

    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【チームメンバー数の逆数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyInvTeamMemberCount : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyInvTeamMemberCount(IHorseRaceInfo owner, float abilityValueBase, IRaceHorseAccessor horseAccessor) : base(owner, abilityValueBase)
        {
            _horseAccessor = horseAccessor;
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int teamId = _owner.TeamId;
            
            // どこのチームにも属していないならx1。
            if (teamId == HorseData.TEAM_ID_NULL)
            {
                return 1;
            }

            var teamHorseArray = _horseAccessor.GetTeamMemberHorseInfoArray(teamId);
            int teamMemberNum = teamHorseArray.Length;
            if (teamMemberNum <= 0)
            {
                Debug.LogError($"チームメンバー数が不正です. teamMemberNum={teamMemberNum}");
                return 1;
            }
            
            float multiply = 1.0f / teamMemberNum;
            return multiply;
        }
    }
    

    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値にParamDefine登録済みの値からランダムに選んだ値を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplyRandom : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly RaceParamDefine.SkillParam.AbiiltyValueUsageRandom[] _randomArray;
        private readonly IRaceRandomGenerator _randomGenerator;
        
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplyRandom(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor, 
            IRaceRandomGenerator randomGenerator, 
            RaceParamDefine.SkillParam.AbiiltyValueUsageRandom[] randomArray) : base(owner, abilityValueBase)
        {
            _horseAccessor = horseAccessor;
            _randomGenerator = randomGenerator;
            _randomArray = randomArray;
            Debug.Assert(randomArray.All(x => x.AbilityValueCoef >= 0), "AbiiltyValueUsageRandom[]のAbilityValueCoefに負の値が入力されている");
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            float multiply = LotValue(_randomArray);
            return multiply;
        }

        /// <summary>
        /// randomArrayに登録されている効果値から実際に使う値を抽選。
        /// </summary>
        private float LotValue(RaceParamDefine.SkillParam.AbiiltyValueUsageRandom[] randomArray)
        {
            int perTotal = randomArray.Sum(x => x.Per);
            int rand = _randomGenerator.GetRandom(perTotal);

            for (int i = 0; i < randomArray.Length; ++i)
            {
                var random = randomArray[i];
                
                rand -= random.Per;

                if (rand <= 0)
                {
                    return random.AbilityValueCoef;
                }
            }
            
            return 1;
        }
    }
    
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【チームメンバーの「基礎ステータス：***」に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class SkillAbilityValueMultiplyTeamTotalStatusBase : SkillAbilityValueMultiplyBaseDynamicFloat
    {
        private readonly IRaceHorseAccessor _horseAccessor;
        private readonly RaceParamDefine.SkillParam _paramDefine;
        private readonly IHorseRaceInfo[] _selfOnlyTeam;
        
        //---------------------------------------------------------------
        protected SkillAbilityValueMultiplyTeamTotalStatusBase(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase)
        {
            _horseAccessor = horseAccessor;
            _paramDefine = paramDefine;
            _selfOnlyTeam = new [] { _owner };
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            var statusTargetHorseArray = GetStatusTargetHorse();

            float statusTotal = 0;
            foreach (var teamHorse in statusTargetHorseArray)
            {
                statusTotal += GetStatus(teamHorse);
            }

            float multiply = GetMultiply(statusTotal);
            return multiply;
        }

        //---------------------------------------------------------------
        private IHorseRaceInfo[] GetStatusTargetHorse()
        {
            int teamId = _owner.TeamId;
            
            // どこのチームにも属していないなら自分だけ。
            if (teamId == HorseData.TEAM_ID_NULL)
            {
                return _selfOnlyTeam;
            }

            // 同じチームのメンバー取得。0人以下にはならないはず。
            var teamHorseArray = _horseAccessor.GetTeamMemberHorseInfoArray(teamId);
            if (teamHorseArray.Length <= 0)
            {
                Debug.LogError($"チームメンバー数が不正です. teamMemberNum={teamHorseArray.Length}");
            }

            return teamHorseArray;
        }

        //---------------------------------------------------------------
        private float GetMultiply(float statusTotal)
        {
            foreach (var coef in _paramDefine.TeamTotalStatusCoefAscArray)
            {
                if (statusTotal < coef.StatusThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            Debug.LogWarning($"チームのステータス合計値({statusTotal})に対応する係数がRaceParamDefineに定義されていないため、倍率が取得できない");
            return 1;
        }
        
        //---------------------------------------------------------------
        protected abstract float GetStatus(IHorseRaceInfo horse);
    }

    public class SkillAbilityValueMultiplyTeamTotalStatusSpeed : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusSpeed(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfo horse)
        {
            return horse.BaseSpeed;
        }
    }

    public class SkillAbilityValueMultiplyTeamTotalStatusStamina : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusStamina(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfo horse)
        {
            return horse.BaseStamina;
        }
    }
    
    public class SkillAbilityValueMultiplyTeamTotalStatusPower : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusPower(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfo horse)
        {
            return horse.BasePow;
        }
    }
    
    public class SkillAbilityValueMultiplyTeamTotalStatusGuts : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusGuts(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfo horse)
        {
            return horse.BaseGuts;
        }
    }

    public class SkillAbilityValueMultiplyTeamTotalStatusWiz : SkillAbilityValueMultiplyTeamTotalStatusBase
    {
        public SkillAbilityValueMultiplyTeamTotalStatusWiz(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            IRaceHorseAccessor horseAccessor,
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, horseAccessor, paramDefine)
        {
        }

        protected override float GetStatus(IHorseRaceInfo horse)
        {
            return horse.BaseWiz;
        }
    }
    
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果値計算機：効果値に【育成レースの勝利数に応じた係数】を乗算。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillAbilityValueMultiplySingleModeWinCount : SkillAbilityValueMultiplyBaseStaticFloat
    {
        //---------------------------------------------------------------
        public SkillAbilityValueMultiplySingleModeWinCount(
            IHorseRaceInfo owner, 
            float abilityValueBase, 
            RaceParamDefine.SkillParam paramDefine) : base(owner, abilityValueBase, paramDefine)
        {
        }

        //---------------------------------------------------------------
        protected override float GetMultiply()
        {
            int winCount = _owner.HorseData.SingleModeWinCount;
            
            foreach (var coef in _skillParam.RaceWinCountCoefAscArray)
            {
                if (winCount < coef.WinCountThreshold)
                {
                    return coef.Multiply; 
                }
            }
            
            return 1;
        }
    }
}
