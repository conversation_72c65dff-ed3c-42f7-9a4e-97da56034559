using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキル効果受信機。
    /// </summary>
    //-------------------------------------------------------------------
    public class SkillModifierReceiver
    {
        private Dictionary<SkillDefine.SkillModifierParam, List<ISkillParamModifier>> _modifier = new Dictionary<SkillDefine.SkillModifierParam, List<ISkillParamModifier>>();

        /// <summary>
        /// スキル効果量の継続時間更新。
        /// </summary>
        public void UpdateModifiers(float deltaTime)
        {
            foreach (var modifierByType in _modifier)
            {
                var modifierList = modifierByType.Value;
                for (int i = modifierList.Count - 1; i >= 0; --i)
                {
                    // 継続時間切れたmodifierはリストから除去。
                    if (modifierList[i].Update(deltaTime))
                    {
                        modifierList.Remove(modifierList[i]);
                    }
                }
            }
        }

        /// <summary>
        /// スキル効果量追加。
        /// </summary>
        public void AddModifier(SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
        {
            if (!_modifier.ContainsKey(type))
            {
                _modifier.Add(type, new List<ISkillParamModifier>());
            }
            _modifier[type].Add(modifier);
        #if CYG_DEBUG
            if(RaceManager.HasInstance())
            {
                var history = new ReceiveHistory(RaceManager.Instance.AccumulateTimeSinceStart, type, modifier);
                DbgReceiveHistoryList.Add(history);
            }
        #endif
        }
        
        /// <summary>
        /// SkillModifierParamのスキル効果が現在適用中かどうか。
        /// </summary>
        public bool HasModifier(SkillDefine.SkillModifierParam type)
        {
            // キーがまだ存在しない=まだ一度も効果発動していない。
            if (!_modifier.ContainsKey(type))
            {
                return false;
            }
            
            // modifierが存在するということは、効果時間が継続しているということ。
            return _modifier[type].Count > 0;
        }
        
        /// <summary>
        /// スキル効果量を適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        public float ApplyModifier(SkillDefine.SkillModifierParam type, float value)
        {
            if (_modifier.TryGetValue(type, out var modifierList))
            {
                foreach (var modifier in modifierList)
                {
                    value = modifier.Apply(value);
                }
            }
            return value;
        }
        
        /// <summary>
        /// スキル効果量を適用。
        /// </summary>
        /// <param name="type">効果量の種類。</param>
        /// <param name="value">この値に効果量を適用した結果を戻り値で返す。</param>
        public bool ApplyModifier(SkillDefine.SkillModifierParam type, bool value)
        {
            if (_modifier.TryGetValue(type, out var modifierList))
            {
                foreach (var modifier in modifierList)
                {
                    value = modifier.Apply(value);
                }
            }
            return value;
        }
        
    #if CYG_DEBUG
        public class ReceiveHistory
        {
            public float AccumulateTime;
            public SkillDefine.SkillModifierParam Type;
            public int FromHorseIndex;
            public int SkillId;
            public int SkillLevel;
            public int ItemId;
            public float Value;

            public ReceiveHistory()
            {
            }
            
            public ReceiveHistory(ReceiveHistory from)
            {
                AccumulateTime = from.AccumulateTime;
                Type = from.Type;
                FromHorseIndex = from.FromHorseIndex;
                SkillId = from.SkillId;
                SkillLevel = from.SkillLevel;
                ItemId = from.ItemId;
                Value = from.Value;
            }
            
            public ReceiveHistory(float time, SkillDefine.SkillModifierParam type, ISkillParamModifier modifier)
            {
                AccumulateTime = time;
                Type = type;
                FromHorseIndex = modifier.DbgHorseIndex;
                SkillId = modifier.DbgSkillId;
                SkillLevel = modifier.DbgSkillLevel;
                ItemId = modifier.DbgItemId;
                Value = modifier.DbgValue;
            }
        }

        public List<ReceiveHistory> DbgReceiveHistoryList = new List<ReceiveHistory>(); 
        
        public List<ISkillParamModifier> DbgGetModifier(SkillDefine.SkillModifierParam type)
        {
            _modifier.TryGetValue(type, out var modifierList);
            return modifierList;
        }
    #endif
    }
}
