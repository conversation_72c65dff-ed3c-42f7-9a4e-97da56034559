using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //---------------------------------------------------------------
    /// <summary>
    /// スキルエフェクトプール：Nullオブジェクト。
    /// </summary>
    //---------------------------------------------------------------
    public class SkillEffectPoolNull : ISkillEffectPool
    {
        public void CreatePool(IRaceHorseAccessor horseAccessor) {}
        public void ReleasePool() {}
        public SkillEffect Borrow(string effectName) { return null; }
        public void Return(SkillEffect target) {}
        public void StartWarmUp() {}
        public void EndWarmUp() {}
    }
}
