using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Cute.Cri;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース再生中BGM制御。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class RaceBGMController : IRaceBGMController
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private const float BGM_DELAY_SEC = 0.5f;
        public const float BGM_VOLUME = 1.0f;
        public const AudioId BGM_ID_DEFAULT = AudioId.BGM_RACE_DEFAULT;

        /// <summary>BGM停止時のフェードアウト時間。</summary>
        private const float STOP_BGM_1_FADE_SEC = 0.8f;
        private const float STOP_BGM_SKIP_FADE_SEC = 1.0f;

        /// <summary>カットインで再生したBGMのOverrun時の音量とフェード時間。</summary>
        private const float CUTIN_BGM_OVERRUN_VOLUME = 0.0f;
        private const float CUTIN_BGM_OVERRUN_FADE_SEC = 14.0f;

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>レース時間アクセッサ。</summary>
        private IRaceTimeAccessor _timeAccessor;
        /// <summary>レースキャラアクセッサ。</summary>
        private IRaceHorseAccessor _horseAccessor;
        /// <summary>レースシナリオ。</summary>
        private readonly RaceSimulateReader _simReader;
        /// <summary>着順１位のキャラ。</summary>
        private int _topHorseIndex;
        /// <summary>最初に終盤に入るキャラ。</summary>
        private int _firstPhaseEndHorseIndex;
        /// <summary>このレースで再生されるスキルカットインリスト。</summary>
        private readonly List<RaceSkillCutInReserveCreator.ReservedCutIn> _cutInList;
        /// <summary>このレースで使うrace_bgm.csvのデータ。</summary>
        private MasterRaceBgm.RaceBgm _cachedBgmMaster;
        /// <summary>このレースで使うchampions_bgm.csvのデータ。</summary>
        private MasterChampionsBgm.ChampionsBgm _cachedChampionsRaceBgmMaster;
        /// <summary>このレースの出走表で使うchampions_bgm.csvのデータ。</summary>
        private MasterChampionsBgm.ChampionsBgm _cachedChampionsEntryTableBgmMaster;

        /// <summary>１曲目再生リクエストを受けてから実際に再生するまでの遅延時間。</summary>
        private float _firstBGMDelayTime = 0.0f;
        /// <summary>１曲目再生リクエスト。</summary>
        private bool _isRequestFirstBGM = false;
        /// <summary>１曲目を停止済みかどうか</summary>
        private bool _isStoppedFirstBGM;
        /// <summary>１目を停止する距離。</summary>
        private readonly float _firstBgmStopDistance;
        /// <summary>１曲目はレースのこの時間で停止する。</summary>
        private float _firstBgmStopTime;

        /// <summary>２曲目を再生済みかどうか</summary>
        private bool _isPlayedSecondBGM;
        /// <summary>２曲目を再生する距離。</summary>
        private readonly float _secondBgmPlayDistance;
        /// <summary>２曲目の頭出し時間。</summary>
        private float _secondBgmStartTime;
        /// <summary>２曲目はレースのこの時間から再生始める。</summary>
        private float _secondBgmPlayStartTime;

        /// <summary>１曲目の停止が必要かどうか。</summary>
        private bool IsNeedFirstBgmStop => _firstBgmStopDistance > 0;
        /// <summary>２曲目の再生が必要かどうか。</summary>
        private bool IsNeedSecondBgmPlay => _secondBgmPlayDistance > 0;

        /// <summary>RaceMainView入場後、着順表まで直遷移するかどうか。</summary>
        private bool _isSkipToResult;

        /// <summary>ボリューム制御。※１曲目の時のみ制御。</summary>
        private RaceBGMVolumeController _volumeController;
        
        private bool HasTimeAccessor => _timeAccessor != null; 
        private bool HasHorseAccessor => _horseAccessor != null;

        public string EntryTableBgmCueSheetName { get; private set; }
        public string EntryTableBgmCueName { get; private set; }

        private string _firstBgmCueSheetName = string.Empty;
        private string _firstBgmCueName = string.Empty;
        public string FirstBgmCueSheetName => _firstBgmCueSheetName;
        public string FirstBgmCueName => _firstBgmCueName;

        private string _secondBgmCueSheetName = string.Empty;
        private string _secondBgmCueName = string.Empty;
        public string SecondBgmCueSheetName => _secondBgmCueSheetName;
        public string SecondBgmCueName => _secondBgmCueName;

        private string _resultCutInBgmCueSheetName = string.Empty;
        private string _resultCutInBgmCueName = string.Empty;
        public string ResultCutInBgmCueSheetName => _resultCutInBgmCueSheetName;
        public string ResultCutInBgmCueName => _resultCutInBgmCueName;
        
        private string _resultListBgmCueSheetName = string.Empty;
        private string _resultListBgmCueName = string.Empty;
        public string ResultListBgmCueSheetName => _resultListBgmCueSheetName;
        public string ResultListBgmCueName => _resultListBgmCueName;

        private readonly List<AudioPlayback> _cutInBgmPlaybackList = new List<AudioPlayback>();

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        public RaceBGMController( 
            RaceDefine.RaceType raceType, 
            RaceDefine.Grade grade, 
            RaceSimulateReader simReader,
            HorseData[] horseDataArray,
            float firstBgmStopDistance,
            float secondBgmPlayDistance,
            List<RaceSkillCutInReserveCreator.ReservedCutIn> cutInList,
            bool isSkipToResult,
            int playerHorseIndex)
        {
            
            _cachedBgmMaster = GetCurrentRaceBGMMaster(raceType, grade);
        #if CYG_DEBUG
            RaceDebugger.DbgBgmId = _cachedBgmMaster != null ? _cachedBgmMaster.Id : 0; 
            RaceDebugger.DbgCutInFirstBgmExtensionList.Clear();
            RaceDebugger.DbgCutInSecondBgmExtensionList.Clear();
            RaceDebugger.DbgFirstBgmStopDistance = firstBgmStopDistance;
#endif

            int sceneType = (int)MasterChampionsBgm.SceneType.Race;
            _cachedChampionsRaceBgmMaster = GetCurrentChampionsBGMMaster(raceType, sceneType);
            sceneType = (int)MasterChampionsBgm.SceneType.EntryTable;
            _cachedChampionsEntryTableBgmMaster = GetCurrentChampionsBGMMaster(raceType, sceneType);

            _simReader = simReader;
            _firstBgmStopDistance = firstBgmStopDistance;
            _secondBgmPlayDistance = secondBgmPlayDistance;
            _cutInList = cutInList;
            _isSkipToResult = isSkipToResult;

            InitEntryTableBgm();
            InitFirstBgm(horseDataArray);
            InitSecondBgm(horseDataArray);

            var res = simReader.GetHorseResultData(playerHorseIndex);
            InitResultBgm(res.FinishOrder);
        }

        //---------------------------------------------------------------
        private void InitEntryTableBgm()
        {
            // チャンピオンズミーティングで専用のBGM設定がある場合はそちらを使用する。
            if (_cachedChampionsEntryTableBgmMaster != null)
            {
                EntryTableBgmCueName = _cachedChampionsEntryTableBgmMaster.CueName;
                EntryTableBgmCueSheetName = _cachedChampionsEntryTableBgmMaster.CuesheetName;
                return;
            }

            EntryTableBgmCueName = _cachedBgmMaster.EntrytableBgmCueName;
            EntryTableBgmCueSheetName = _cachedBgmMaster.EntrytableBgmCuesheetName;
        }
        
        //---------------------------------------------------------------
        public void CreateVolumeController(IRaceHorseAccessor horseAccessor, float[] volumeArray, float[] volumeFadeSecArray)
        {
            _volumeController = new RaceBGMVolumeController(horseAccessor, volumeArray, volumeFadeSecArray);
        }

        //---------------------------------------------------------------
        public void AppendTimeAccessor(IRaceTimeAccessor timeAccessor)
        {
            _timeAccessor = timeAccessor;
        }

        //---------------------------------------------------------------
        public void AppendHorseAccessor(IRaceHorseAccessor horseAccessor)
        {
            _horseAccessor = horseAccessor;
        }

        //---------------------------------------------------------------
        public static MasterRaceBgm.RaceBgm GetCurrentRaceBGMMaster(RaceDefine.RaceType raceType, RaceDefine.Grade grade)
        {
            int singleModeProgramId = RaceManager.RaceInfo.SingleRaceProgramId;
            
        #if CYG_DEBUG
            // #53032 RaceDirectからレースを実行したときは、選んだレースのRaceGroup→RaceTypeに置き換えてrace_bgmのデータ選定に使う。
            if (RaceManager.RaceInfo.IsRaceDirectRace)
            {
                switch (RaceManager.RaceInfo.RaceGroup)
                {
                case RaceDefine.RaceGroup.Common: raceType = RaceDefine.RaceType.Single; break;
                case RaceDefine.RaceGroup.Single: raceType = RaceDefine.RaceType.Single; break;
                case RaceDefine.RaceGroup.Daily: raceType = RaceDefine.RaceType.Daily; break;
                case RaceDefine.RaceGroup.Legend: raceType = RaceDefine.RaceType.Legend; break;
                case RaceDefine.RaceGroup.TeamStadium: raceType = RaceDefine.RaceType.TeamStadium; break;
                case RaceDefine.RaceGroup.StoryCondition: raceType = RaceDefine.RaceType.StoryCondition; break;
                case RaceDefine.RaceGroup.TrainingPractice: raceType = RaceDefine.RaceType.Tutorial; break;
                }

                // #53032 育成レースの場合、RaceIntanceIdからSingleModeProgramIdを逆引き。 
                if (raceType == RaceDefine.RaceType.Single)
                {
                    var masterProgram = MasterDataManager.Instance.masterSingleModeProgram.dictionary.Values.FirstOrDefault(x => x.RaceInstanceId == RaceManager.RaceInfo.RaceInstanceId);
                    if (masterProgram != null)
                    {
                        singleModeProgramId = masterProgram.Id;
                    }
                }
            }
        #endif
            
            var masterRaceBgmList = MasterDataManager.Instance.masterRaceBgm.GetListWithRaceTypeOrderByIdAsc((int)raceType);
            if( null == masterRaceBgmList )
            {
                return null;
            }

            const int RACE_INSTANCE_ID_NULL = -1;
            const int GRADE_NULL = -1;
            const int SINGLE_MODE_ROUTE_RACE_ID_NULL = -1;
            const int SINGLE_MODE_PROGRAM_ID_NULL = -1;
            foreach (var masterRaceBgm in masterRaceBgmList)
            {
                // race_instance_id指定がある場合、一致したらそのデータを使う。
                if (masterRaceBgm.RaceInstanceId != RACE_INSTANCE_ID_NULL)
                {
                    if (masterRaceBgm.RaceInstanceId == RaceManager.RaceInfo.RaceInstanceId)
                    {
                        return masterRaceBgm;
                    }
                    else
                    {
                        continue;
                    }
                }

                // グレード指定がある場合、一致したらそのデータを使う。
                if (masterRaceBgm.Grade != GRADE_NULL)
                {
                    if (masterRaceBgm.Grade == (int)grade)
                    {
                        return masterRaceBgm;
                    }
                    else
                    {
                        continue;
                    }
                }
                
                // 育成の目標レース指定がある場合、一致したらそのデータを使う。
                if (masterRaceBgm.SingleModeRouteRaceId != SINGLE_MODE_ROUTE_RACE_ID_NULL)
                {
                    int routeRaceId = GetSingleModeRouteRaceId();
                    if (masterRaceBgm.SingleModeRouteRaceId == routeRaceId)
                    {
                        return masterRaceBgm;
                    }
                    else
                    {
                        continue;                        
                    }
                }

                // 育成のProgramId指定がある場合、一致したらそのデータを使う。
                if (masterRaceBgm.SingleModeProgramId != SINGLE_MODE_PROGRAM_ID_NULL)
                {
                    if (masterRaceBgm.SingleModeProgramId == singleModeProgramId)
                    {
                        return masterRaceBgm;
                    }
                    else
                    {
                        continue;                        
                    }
                }
                
                return masterRaceBgm;
            }

        #if CYG_DEBUG
            // RaceDirectレースでrace_bgmが見つからなかった場合はRaceType.Debugのデータを使う。
            if (RaceManager.RaceInfo.IsRaceDirectRace)
            {
                var debugRaceMaster = MasterDataManager.Instance.masterRaceBgm.GetWithRaceTypeOrderByIdAsc((int)RaceDefine.RaceType.Debug);
                return debugRaceMaster;
            }
        #endif
            
            return null;
        }

        public static MasterChampionsBgm.ChampionsBgm GetCurrentChampionsBGMMaster(RaceDefine.RaceType raceType, int sceneType)
        {
            if (raceType != RaceDefine.RaceType.Champions)
            {
                return null;
            }

            int championsId = WorkDataManager.Instance.ChampionsData.ChampionsId;
            int roundId = WorkDataManager.Instance.ChampionsData.RoundId;
            var league = WorkDataManager.Instance.ChampionsData.GetLeague();
            int raceNum = TempData.Instance.ChampionsData.RaceInfo.RaceNum;

            return MasterDataManager.Instance.masterChampionsBgm.Get(championsId, roundId, league, sceneType, raceNum);
        }

        //---------------------------------------------------------------
        private static int GetSingleModeRouteRaceId()
        {
            var singleModeData = WorkDataManager.Instance.SingleMode;
            if (singleModeData != null && singleModeData.Character != null)
            {
                int turn = singleModeData.GetCurrentTurn();
                int charaId = singleModeData.Character.CharaId;
                var programId = RaceManager.RaceInfo.SingleRaceProgramId;

                var masterRouteRace = MasterDataManager.Instance.masterSingleModeRouteRace
                    .GetListWithRaceSetIdOrderBySortIdAsc(charaId)
                    .FirstOrDefault(m => m.Turn == turn && m.ConditionId == programId);

                if (masterRouteRace != null)
                {
                    return masterRouteRace.Id;
                }
            }

            return 0;
        }

        //---------------------------------------------------------------
        private static void GetBGMCueNameByPattern(float bgmPlayTime, int patternId, out string cueSheetName, out string cueName)
        {
            cueSheetName = string.Empty;
            cueName = string.Empty;
            
            var bgmPatternMaster = MasterDataManager.Instance.masterRaceBgmPattern.Get(patternId);
            if (bgmPatternMaster == null)
            {
                return;
            }

            var bgmDescList = bgmPatternMaster.ToBgmDescList();
            foreach (var bgmDesc in bgmDescList)
            {
                if (bgmPlayTime <= bgmDesc.Time)
                {
                    cueSheetName = bgmDesc.CueSheetName;
                    cueName = bgmDesc.CueName;
                    return;
                }
            }
        }

        #region <1stBgm>
        //---------------------------------------------------------------
        public void PlayFirstBGM()
        {
            // リザルトに直遷移する場合は不要。
            if (_isSkipToResult)
            {
                return;
            }
            _firstBGMDelayTime = BGM_DELAY_SEC;
            _isRequestFirstBGM = true;
        }

        //---------------------------------------------------------------
        private void UpdateFirstBGM(float deltaTime)
        {
            UpdateFirstBgmPlay(deltaTime);
            UpdateFirstBgmStop();
            UpdateFirstBgmVolume(deltaTime);
        }

        //---------------------------------------------------------------
        private void UpdateFirstBgmVolume(float deltaTime)
        {
        #if CYG_DEBUG
            RaceDebugger.DbgIsFirstBgmVolumeUpdate = !_isStoppedFirstBGM;
        #endif
            if (_isStoppedFirstBGM)
            {
                return;
            }
            _volumeController.Update(deltaTime);   
        }

        //---------------------------------------------------------------
        private void UpdateFirstBgmPlay(float deltaTime)
        {
            if( !_isRequestFirstBGM )
            {
                return;
            }

            _firstBGMDelayTime -= deltaTime;
            if( _firstBGMDelayTime > 0.0f )
            {
                return;
            }

            _firstBGMDelayTime = 0.0f;
            AudioManager.Instance.PlayBgmFromName(FirstBgmCueSheetName, FirstBgmCueName, true, BGM_VOLUME);
            _isRequestFirstBGM = false;
        }
        
        //---------------------------------------------------------------
        private void UpdateFirstBgmStop()
        {
            if (IsNeedFirstBgmStop && HasHorseAccessor)
            {
                // トップでゴールするキャラが１曲目の停止距離に達したら停止する。
                // 誤って２曲目を停止してしまわないように、２曲目が未再生かもチェックする。
                if (!_isStoppedFirstBGM && !_isPlayedSecondBGM)
                {
                    if(_timeAccessor.AccumulateTimeSinceStart >= _firstBgmStopTime)
                    {
                        StopFirstBgm();
                    }
                }
            }
        }

        //---------------------------------------------------------------
        private void InitFirstBgm(HorseData[] horseDataArray)
        {
            float firstBgmPlayTime = CalcFirstBgmPlayTime(horseDataArray);
            GetFirstBGMCueName(firstBgmPlayTime, out _firstBgmCueSheetName, out _firstBgmCueName);
        }
    
        /// <summary>
        /// １曲目を再生する時間（尺）計算。
        /// </summary>
        private float CalcFirstBgmPlayTime(HorseData[] horseDataArray)
        {
            // 先頭のキャラ１曲目停止位置に入る時間。
            _firstBgmStopTime = GetTopHorseFirstBgmStopTime();
            if(_firstBgmStopTime <= 0)
            {
                return 0;
            }

            float secondBgmPlayStartTime = GetSecondBgmStartTime(_simReader, _secondBgmPlayDistance);

            // #76519 １曲目停止時間～２曲目開始時間の間にスキルカットインが挟まる場合、１曲目停止時間はそのカットイン再生時間に変更。
            if (GetLastCutInTime(_firstBgmStopTime, secondBgmPlayStartTime, out float lastCutInTime))
            {
                // 確実にカットイン明けに停止されるように0.1秒遅らせる。
                const float FIRST_BGM_STOP_DELAY = 0.1f;
                _firstBgmStopTime = lastCutInTime + FIRST_BGM_STOP_DELAY;
            }
            
            // 先頭のキャラが１曲目停止位置に入るまでが１曲目再生する時間。
            float firstBgmPlayTime = _firstBgmStopTime;
            // １曲目再生時間中に再生されるカットイン回数に応じて、１曲目の再生時間も伸ばす。
            float cutInTime = CalcCutInExtensionTime(-1, firstBgmPlayTime, horseDataArray, true);
            firstBgmPlayTime += cutInTime;
            
        #if CYG_DEBUG
            RaceDebugger.DbgFirstBgmStopTime = _firstBgmStopTime;
            RaceDebugger.DbgFirstBgmCutInPlayTime = cutInTime;
            RaceDebugger.DbgFirstBgmPlayTime = firstBgmPlayTime;
        #endif
            
            return firstBgmPlayTime;
        }

        /// <summary>
        /// 任意のキャラが最初に１曲目停止距離に入る時間取得。
        /// </summary>
        private float GetTopHorseFirstBgmStopTime()
        {
            return _simReader.GetFirstReachedTimeByDistance(_firstBgmStopDistance);
        }

        /// <summary>
        /// １曲目のキューシート名・キュー名取得。
        /// </summary>
        /// <param name="firstBgmPlayTime">１曲目を再生していたい時間。</param>
        private void GetFirstBGMCueName(float firstBgmPlayTime, out string cueSheetName, out string cueName)
        {
            cueSheetName = string.Empty;
            cueName = string.Empty;
            
            if(_cachedBgmMaster == null)
            {
                Debug.LogWarning($"Bgmがrace_bgm.csvに登録されていません。");
                return;
            }
            int patternId = (_cachedChampionsRaceBgmMaster != null) ? _cachedChampionsRaceBgmMaster.FirstBgmPattern : _cachedBgmMaster.FirstBgmPattern;
            GetBGMCueNameByPattern(firstBgmPlayTime, patternId, out cueSheetName, out cueName);
        }
        
        private void StopFirstBgm()
        {
            AudioManager.Instance.StopBgm(STOP_BGM_1_FADE_SEC);
            _isStoppedFirstBGM = true;
        }
        #endregion <1stBgm>
        
        #region <2ndBgm>
        private void InitSecondBgm(HorseData[] horseDataArray)
        {
            float secondBgmPlayTime = CalcSecondBgmPlayTime(horseDataArray);
            GetSecondBGMCueName(secondBgmPlayTime, out _secondBgmCueSheetName, out _secondBgmCueName);
        }

        /// <summary>
        /// ２曲目のキューシート名・キュー名取得。
        /// </summary>
        /// <param name="secondBgmPlayTime">２曲目を再生していたい時間。</param>
        private void GetSecondBGMCueName(float secondBgmPlayTime, out string cueSheetName, out string cueName)
        {
            cueSheetName = string.Empty;
            cueName = string.Empty;
            
            if(_cachedBgmMaster == null)
            {
                Debug.LogWarning($"Bgmがrace_bgm.csvに登録されていません。");
                return;
            }
            int patternId = (_cachedChampionsRaceBgmMaster != null) ? _cachedChampionsRaceBgmMaster.SecondBgmPattern : _cachedBgmMaster.SecondBgmPattern;
            GetBGMCueNameByPattern(secondBgmPlayTime, patternId, out cueSheetName, out cueName);
        }
        
        private void UpdateSecondBGM(float deltaTime)
        {
            // ２曲目再生が不要なら更新しない。
            if (!IsNeedSecondBgmPlay)
            {
                return;
            }

            if (!HasTimeAccessor)
            {
                return;
            }
        
            // 最初のBGMの再生がまだ行われていない場合、再生しない。
            // ※出走直後、最初のBGMの遅延再生前にここに来た後、最初のBGMで上書きされるバグ対策。
            if( _isRequestFirstBGM )
            {
                return;
            }

            // 既に終盤BGM再生済みなら処理不要。
            if( _isPlayedSecondBGM )
            {
                return;
            }

            // 終盤BGMが設定されていなければ、処理不要。
            if(string.IsNullOrEmpty(_secondBgmCueName))
            {
                return;
            }

            // ２曲目を再生開始する時間に到達していなければ再生しない。
            if (_timeAccessor.AccumulateTimeSinceStart < _secondBgmPlayStartTime)
            {
                return;
            }

            // ２曲目頭出し時間オフセット。
            // レーススキップによりAccumulateTimeSinceStartが_secondBgmPlayStartTimeを超えた位置に移動してここに来た場合、その分を頭出し時間に加える。
            float startTimeOffset = _timeAccessor.AccumulateTimeSinceStart - _secondBgmPlayStartTime;

            // ※ここまで来たら再生決定。

            // 念のため１曲目停止を入れる。
            StopFirstBgm();
            
            // 終盤Bgmを再生する。
            AudioManager.Instance.PlayBgmFromName( 
                _secondBgmCueSheetName,
                _secondBgmCueName,
                false, 
                BGM_VOLUME,
                0,
                0,
                startTime: _secondBgmStartTime + startTimeOffset,
                isCrossFade:false);

            // 一度ここに入ったら以降チェック不要。
            _isPlayedSecondBGM = true;
        
        #if CYG_DEBUG
            RaceDebugger.DbgSecondBgmStartTimeOffset = startTimeOffset;
        #endif
        }
        
        /// <summary>
        /// ２曲目を再生する時間（尺）計算。
        /// </summary>
        private float CalcSecondBgmPlayTime(HorseData[] horseDataArray)
        {
            // １着のキャラがゴールする時間。
            _topHorseIndex = _simReader.TopFinishHorseIndex;
            float topFinishTimeRaw = _simReader.TopFinishTimeRaw;
            
            // ２曲目再生開始する時刻。
            _secondBgmPlayStartTime = GetSecondBgmStartTime(_simReader, _secondBgmPlayDistance);
           
            // １着のキャラが【２曲目再生時刻～ゴール時刻】が２曲目再生する時間。
            float secondBgmPlayTime = topFinishTimeRaw - _secondBgmPlayStartTime;
            // 終盤以降に再生されるカットイン回数に応じて、２曲目の再生時間も伸ばす。
            float cutInTime = CalcCutInExtensionTime(_secondBgmPlayStartTime, -1, horseDataArray, false);
            secondBgmPlayTime += cutInTime;

        #if CYG_DEBUG
            RaceDebugger.DbgTopHorseFinishTimeRaw = topFinishTimeRaw;
            RaceDebugger.DbgSecondBgmCutInPlayTime = cutInTime;
            RaceDebugger.DbgSecondBgmPlayTime = secondBgmPlayTime;
            RaceDebugger.DbgSecondBgmPlayStartTime = _secondBgmPlayStartTime;
        #endif
            
            return secondBgmPlayTime;
        }
        
        /// <summary>
        /// ２曲目再生を始める時間取得。
        /// </summary>
        public static float GetSecondBgmStartTime(RaceSimulateReader simReader, float secondBgmPlayDistance)
        {
            // ２曲目再生位置に最初に誰かが到達する時間を返却する。
            return simReader.GetFirstReachedTimeByDistance(secondBgmPlayDistance);
        }

        /// <summary>
        /// minTime秒目～maxTime秒目以内に再生されるスキルカットインの中で、最後に再生される時間を取得。
        /// </summary>
        private bool GetLastCutInTime(float minTime, float maxTime, out float lastCutInTime)
        {
            bool isFound = false;
            lastCutInTime = 0;
            foreach (var cutIn in _cutInList)
            {
                // RaceDirectからレース実行時にTime=0で登録されているので、デバッグ用予約は計算から除く。
                if (cutIn.Time <= 0)
                {
                    continue;
                }
                
                // minTimeが負数なら比較しない。
                if (minTime > 0)
                {
                    if (cutIn.Time < minTime)
                    {
                        continue;
                    }
                }

                // maxTimeが負数なら比較しない。
                if (maxTime > 0)
                {
                    if (cutIn.Time > maxTime)
                    {
                        continue;
                    }
                }

                if (lastCutInTime < cutIn.Time)
                {
                    lastCutInTime = cutIn.Time;
                }
                
                // minTime~maxTimeの間に再生されるカットインが見つかった。
                isFound = true;
            }
            
            return isFound;
        }
        
        /// <summary>
        /// minTime秒目～maxTime秒目以内に再生されるスキルカットインによりBGMが延長される時間を計算。
        /// </summary>
        private float CalcCutInExtensionTime(float minTime, float maxTime, HorseData[] horseDataArray, bool isFirstBgm)
        {
            float cutInTime = 0;
            foreach (var cutIn in _cutInList)
            {
                // RaceDirectからレース実行時にTime=0で登録されているので、デバッグ用予約は計算から除く。
                if (cutIn.Time <= 0)
                {
                    continue;
                }
                
                // minTimeが負数なら比較しない。
                if (minTime > 0)
                {
                    if (cutIn.Time < minTime)
                    {
                        continue;
                    }
                }

                // maxTimeが負数なら比較しない。
                if (maxTime > 0)
                {
                    if (cutIn.Time > maxTime)
                    {
                        continue;
                    }
                }

                bool isShort = RaceUtil.CheckShortCutIn(cutIn.SkillId);
                int cardId = horseDataArray[cutIn.HorseIndex].CardId;
                int charaId = horseDataArray[cutIn.HorseIndex].charaId;
                float extension = GetCutInExtensionTime(cutIn.Category, cardId, charaId, isShort);
                cutInTime += extension;

            #if CYG_DEBUG
                {
                    var desc = new RaceDebugger.CutInBgmExtensionDesc()
                    {
                        HorseIndex = cutIn.HorseIndex,
                        CardId = horseDataArray[cutIn.HorseIndex].CardId,
                        CharaId = horseDataArray[cutIn.HorseIndex].charaId,
                        IsShort = isShort,
                        Category = cutIn.Category,
                        ExtensionTime = extension,
                    };
                    if (isFirstBgm)
                    {
                        RaceDebugger.DbgCutInFirstBgmExtensionList.Add(desc);
                    }
                    else
                    {
                        RaceDebugger.DbgCutInSecondBgmExtensionList.Add(desc);
                    }
                }
            #endif
            }
            return cutInTime;
        }

        /// <summary>
        /// race_bgm_cutin_extension_time.csvに登録されているカットインごとの延長時間取得。
        /// </summary>
        private float GetCutInExtensionTime(RaceManager.CutInCategory category, int cardId, int charaId, bool isShort)
        {
            var masterExtensionTimeList = MasterDataManager.Instance.masterRaceBgmCutinExtensionTime.GetListWithCutinCategoryOrderByIdAsc((int)category);
            for (int i = 0, cnt = masterExtensionTimeList.Count; i < cnt; ++i)
            {
                var masterExtensionTime = masterExtensionTimeList[i];
                
                // card_idが0以外入力されているならカットイン発動者のCardIdチェック。   
                if (masterExtensionTime.CardId != 0)
                {
                    if (masterExtensionTime.CardId != cardId)
                    {
                        continue;
                    }
                }
                
                // chara_idが0以外入力されているならカットイン発動者のCharaIdチェック。   
                if (masterExtensionTime.CharaId != 0)
                {
                    if (masterExtensionTime.CharaId != charaId)
                    {
                        continue;
                    }
                }

                return isShort
                    ? Math.MasterInt2Float(masterExtensionTime.ExtensionSec)
                    : Math.MasterInt2Float(masterExtensionTime.ExtensionSecLong);
            }

            return 0;
        }
        
        #endregion

        private void InitResultBgm(int finishOrder)
        {
            // 着順によりBGM変わる。
            // １着。
            if (finishOrder == 0)
            {
                _resultCutInBgmCueSheetName = _cachedBgmMaster.ResultCutinBgmCuesheetName1;
                _resultCutInBgmCueName = _cachedBgmMaster.ResultCutinBgmCueName1;
                _resultListBgmCueSheetName = _cachedBgmMaster.ResultListBgmCuesheetName1;
                _resultListBgmCueName = _cachedBgmMaster.ResultListBgmCueName1;
            }
            // ２着～５着。
            else if (finishOrder <= 4)
            {
                _resultCutInBgmCueSheetName = _cachedBgmMaster.ResultCutinBgmCuesheetName2;
                _resultCutInBgmCueName = _cachedBgmMaster.ResultCutinBgmCueName2;
                _resultListBgmCueSheetName = _cachedBgmMaster.ResultListBgmCuesheetName2;
                _resultListBgmCueName = _cachedBgmMaster.ResultListBgmCueName2;
            }
            // ６着以下。
            else
            {
                _resultCutInBgmCueSheetName = _cachedBgmMaster.ResultCutinBgmCuesheetName3;
                _resultCutInBgmCueName = _cachedBgmMaster.ResultCutinBgmCueName3;
                _resultListBgmCueSheetName = _cachedBgmMaster.ResultListBgmCuesheetName3;
                _resultListBgmCueName = _cachedBgmMaster.ResultListBgmCueName3;
            }
        }
            
        public void Update( float deltaTime )
        {
            // リザルトに直遷移する場合は不要。
            if (_isSkipToResult)
            {
                return;
            }
            UpdateFirstBGM( deltaTime );
            UpdateSecondBGM( deltaTime );
        }

        public void Stop()
        {
            AudioManager.Instance.StopBgm();
        }

        public void Pause()
        {
            AudioManager.Instance.PauseBgm();
        }

        public void Resume()
        {
            AudioManager.Instance.ResumeBgm();
        }

        public float GetVolume()
        {
            return AudioManager.Instance.GetBgmVolume();
        }

        public void SetVolume(float volume, float fadeSec)
        {
            AudioManager.Instance.SetBgmVolume(volume);
        }

        public void OnSkipToLast()
        {
        }

        public void OnSkipToResult()
        {
            // カットインが再生したBGMの停止。
            StopCutInBgm();

            // リザルトに直遷移する場合は２曲目に関する処理は不要。
            if (_isSkipToResult)
            {
                return;
            }

            // ２曲目再生前の場合、２曲目を再生しないようにしておく。
            if (!_isPlayedSecondBGM)
            {
                _isPlayedSecondBGM = true;
            }
            // ２曲目再生中の場合、２曲目の停止。
            else if (_isPlayedSecondBGM)
            {
                AudioManager.Instance.StopBgm(STOP_BGM_SKIP_FADE_SEC, null, FadeCurve.EaseOutCubic);
            }
        }

        /// <summary>
        /// カットインが再生したBGMのPlaybackを登録する (カットイン終了後もBGMを継続するフラグが設定された場合に実行)
        /// </summary>
        public void SetCutInBgmPlayback(AudioPlayback playback)
        {
            _cutInBgmPlaybackList.Add(playback);

            // ２曲目再生前の場合、２曲目を再生しないようにしておく。
            _isPlayedSecondBGM = true;
        }

        /// <summary>
        /// カットインのBGMを停止する
        /// </summary>
        public void StopCutInBgm()
        {
            int length = _cutInBgmPlaybackList.Count;
            for (int i = 0; i < length; i++)
            {
                if (AudioManager.IsPlay(_cutInBgmPlaybackList[i]))
                {
                    AudioManager.Stop(_cutInBgmPlaybackList[i], STOP_BGM_SKIP_FADE_SEC, null, FadeCurve.EaseOutCubic);
                }
            }

            _cutInBgmPlaybackList.Clear();
        }

        /// <summary>
        /// カットインのBGMをボリューム0に設定する
        /// </summary>
        public void SetCutInBgmOverrunVolume()
        {
            int length = _cutInBgmPlaybackList.Count;
            for (int i = 0; i < length; i++)
            {
                if (AudioManager.IsPlay(_cutInBgmPlaybackList[i]))
                {
                    AudioManager.SetVolume(_cutInBgmPlaybackList[i], CUTIN_BGM_OVERRUN_VOLUME, CUTIN_BGM_OVERRUN_FADE_SEC, FadeCurve.EaseOutCubic);
                }
            }
        }
    }
}

