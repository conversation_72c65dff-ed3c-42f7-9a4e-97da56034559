#if UNITY_EDITOR && CYG_DEBUG
// 実況ログツール用のログ収集仕込み
#define JIKKYOU_LOG
#endif

#if CYG_DEBUG
// 計測仕込み
//#define JIKKYOU_PROFILE
#endif

using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Gallop.Tutorial;

#if JIKKYOU_PROFILE
using UnityEngine.Profiling;
#endif

namespace Gallop
{
    /// <summary>
    /// 実況制御。
    /// </summary>
    public partial class Jikkyo : JikkyoControllerBase
    {
        public override Jikkyo.JikkyoSceneType SceneType => JikkyoSceneType.Race;
        
        private float NearGoalDistance => _jikkyoParam.NearGoalDistance;
        private float NearGoalInterruptDisableDistance => _jikkyoParam.NearGoalInterruptDisableDistance;
        private float CommentDisableDistance => _jikkyoParam.CommentDisableDistance;

        private bool _isRaceGateOpen = false; // レースのゲートオープンしたか


        public Jikkyo(
            RaceInfo raceInfo,
            IRaceHorseAccessor horseAccessor,
            IRaceTimeAccessor timeAccessor,
            IRaceCourseAttributeAccessor courseAttributeAccessor)
            : base(raceInfo, horseAccessor, timeAccessor, courseAttributeAccessor)
        {
        }
        
        protected override float GetFirstHorseDistance()
        {
            return _horseAccessor.GetFirstHorseInfo().GetDistance();
        }

        protected override void CreateTagProcessor()
        {
            _jikkyoTag = new JikkyoTagProcessor(
                _raceInfo.RaceCourseSet,
                _raceInfo.RaceId,
                _raceInfo.RaceName,
                _raceInfo.RaceHorse,
                _raceInfo.HorseIndexByPopularity,
                GetHorseIndexByOrder,
                GetPlayerHorseIndex);
        }
     
        public override void SetUp(bool isExistPlayer, RaceParamDefine.JikkyoParam jikkyoParam)
        {
            base.SetUp(isExistPlayer, jikkyoParam);
            // Event起動タイプ用初期化
            InitRaceEvent();
            // 掲示板確定表示
            _isFixedResultBoard = false;
        }
        
        public override void Go()
        {
            _isPause = false;
            ChangeMode(Mode.GateIn, true);
        }
        
        /// <summary>
        /// レースのゲートオープン通知
        /// </summary>
        public void NotifyRaceGateOpen()
        {
            _isRaceGateOpen = true;
        }

        /// <summary>
        /// 出走デモスキップ。
        /// </summary>
        public void SkipToStart()
        {
            // 表示中/再生待ち実況のクリア。
            ClearDisplay();
            ClearReserve();

            // 状態をスタート待ちに変更。
            ChangeMode(Mode.WaitStart, true);
        }

        /// <summary>
        /// レース終盤までスキップ。
        /// </summary>
        public void SkipToLast()
        {
            // 表示中/再生待ち実況のクリア。
            ClearDisplay();
            ClearReserve();
        }

        /// <summary>
        /// 出走前実況を全て終了しているか。
        /// </summary>
        public bool IsEndJikkyoGateIn()
        {
            return _mode >= Mode.WaitStart;
        }

        /// <summary>
        /// ゴール後実況を全て終了しているか。
        /// </summary>
        public bool IsEndJikkyouAfterGoal()
        {
        #if !CYG_PRODUCT
            if( _jikkyoReplay != null ) { return _jikkyoReplay.IsReplayFinished() && !IsJikkyoPlaying(); }
        #endif
            return _mode >= Mode.WaitRaceEnd;
        }
        
        
        /// <summary>
        /// 実況テキスト間・ワード間詰めを有効にするモードかどうか。
        /// </summary>
        protected override bool IsCrossTimeEnable => _mode != Mode.GateIn;
        
        
        /// <summary>
        /// 更新。
        /// </summary>
        public override void Exec(float elapsedTime)
        {
#if CYG_DEBUG
            // デバッグ機能：デバッグメニューからの実況再生中はレースを一時停止するため、elapsedTime = 0 となるが、強制的に実況を進行させるためにelapsedTimeを入れる
            if (JikkyoDebugger.IsCustomPlay)
            {
                elapsedTime = 1.0f / GameDefine.NORMAL_FRAME_RATE;
            }

            // デバッグ機能で指定実況再生時にレースを止める設定になっている場合、
            // デルタ=0（レース進行が止まっている）なら実況更新も行わない。
            // JikkyoExecに処理を通さないことで指定実況のボイス再生が行われるのを堰き止めている。
            if (JikkyoDebugger.IsJikkyoBreakEnable)
            {
                if (Mathf.Approximately(elapsedTime, 0))
                {
                    return;
                }
            }

            // デバッグ機能で出走馬数が3未満になると実況が破綻するので何もしない
            if (_raceInfo.NumRaceHorses < 3)
            {
                if (!IsEndJikkyoGateIn())
                {
                    SkipToStart();
                }
                return;
            }
#endif // CYG_DEBUG

            // ポーズ中は以降処理不要。
            if (IsPause())
            {
#if CYG_DEBUG
                // デバッグ機能：デバッグメニューからの実況再生中はポーズ中でも実況を進行させる
                if (JikkyoDebugger.IsCustomPlay)
                {
                    _jikkyoDisp.Update(elapsedTime);
                }
#endif // CYG_DEBUG

                return;
            }

            // 予約・再生禁止時間カウントダウン
            if (_reserveSuspendTime > 0) { _reserveSuspendTime -= elapsedTime; }
            if (_playSuspendTime > 0) { _playSuspendTime -= elapsedTime; }

            // 実況判定用フレームデータ等更新
            if (_isRaceGateOpen)
            {
                _jikkyoTriggerCmd.Update();
            }

            // レース状況を元に実況の状態遷移。
            ChangeMode(GetNextModeByRaceState());

            // テキスト表示/ボイス再生更新。
            _jikkyoDisp.Update(elapsedTime);

            // ゴール付近になったら実況の発行を控えるため、以降処理不要。
            if (IsNearGoal())
            {
                return;
            }

            // テキストかボイスの再生中であれば、以降処理不要。
            {
                float crossTime = CalcSentenceCrossTime();
                if (IsJikkyoPlaying() && GetRemainingPlayTime() > crossTime)
                {
                    return;
                }
            }

#if !CYG_PRODUCT
            // 実況固定リプレイ
            // 各割り込み実況を再生させたくないので最優先のこの位置で実行。
            if (_jikkyoReplay != null)
            {
                // レース開始以降の実況のみ乗っ取る
                if (_mode >= Mode.Start)
                {
                    if (HasReserve())
                    {
                        JikkyoReserveExec();
                    }
                    else
                    {
                        // firstHorseの位置に応じて実況を選択
                        float distance = GetFirstHorseDistance();
                        var jikkyoElement = _jikkyoReplay.GetNextJikkyo(distance);
                        if (jikkyoElement != null)
                        {
                            AddReserve(jikkyoElement);
                            ResetSilentTime();
                        }
                    }
                    return;
                }
            }
#endif

            // プレイヤ系割り込み実況チェック
            UpdateInterruptPlayer();

            // 予約分まで再生終了後の割り込み実況
            if (!HasReserve())
            {
                UpdateInterruptCorner();
            }

            if (!HasReserve()) // 上でコーナーが予約されたらスルー
            {
                UpdateInterruptLastBaseId();
            }

            // 馬の順位タグ更新。
            UpdateTagHorseRank();

            // 再生待ちバッファから実況取り出し
            if (HasReserve())
            {
                JikkyoReserveExec();
            }
            else
            {
                if (_raceInfo.RaceType == RaceDefine.RaceType.Tutorial)
                {
                    if( TutorialManager.Instance.IsStopPlayingJikkyo )
                    {
                        return;
                    }
                }
                
                // 新たに実況を予約
                JikkyoExec(elapsedTime);
            }
        }
     
        /// <summary>
        /// ゴール付近かどうか。
        /// </summary>
        private bool IsNearGoal()
        {
            var remainDistance = GetRaceRemain();
            return (remainDistance <= NearGoalDistance && remainDistance > 0f);
        }
        
        /// <summary>
        /// レースの残り距離取得。
        /// </summary>
        /// <returns></returns>
        private float GetRaceRemain()
        {
            // 先頭馬の距離で比較する。
            return _raceInfo.CourseDistance - GetFirstHorseDistance();
        }
        
        /// <summary>
        /// 順位指定でHorseIndex取得。
        /// </summary>
        /// <param name="order">ゴール前なら現在順位、ゴール後なら着順と解釈する。</param>
        protected override int GetHorseIndexByOrder(int order)
        {
            var mode = CurMode;
            bool goal = mode == Jikkyo.Mode.Goal || mode == Jikkyo.Mode.AfterGoal || mode == Jikkyo.Mode.WaitRaceEnd;

            // ゴール後は着順を使用する
            int horseIndex = goal
                ? _horseAccessor.GetHorseIndexByFinishOrder(order)
                : _horseAccessor.GetHorseIndexByOrder(order);

            return horseIndex;
        }
        
        protected override void JikkyoExec(float elapsedTime)
        {
#if CYG_DEBUG
            if (!JikkyoDebugger.IsJikkyoExec) { return; }
#endif // CYG_DEBUG

            // 抽選停止中
            if (IsReserveSuspend()) { return; }

            List<RaceJikkyoBaseElementCache> jikkyoBaseList = null;

#if JIKKYOU_LOG
            bool isSelectedBySituation = false;
#endif

            if (_situation != Jikkyo.SituationNone)
            {
                // シチュエーション＆サブシチュエーションで選定
                jikkyoBaseList = GetJikkyoBaseListBySituation();

                if (jikkyoBaseList == null || jikkyoBaseList.Count == 0)
                {
                    // シチュエーション内で選べるものがなかったのでモードでの選定へ切り替える
                    ChangeSituation(Jikkyo.SituationNone);

                    // 現況をモードに反映
                    // 元のモードのままのときはサブモードが継続される
                    var nextMode = GetNextModeByRaceState();
                    if (_mode == Jikkyo.Mode.InterruptOrder && nextMode < Jikkyo.Mode.Dochu) { nextMode = Jikkyo.Mode.Dochu; }
                    ChangeMode(nextMode);

                    // モード＆サブモードで選定
                    jikkyoBaseList = GetJikkyoBaseListByMode();
                }
#if JIKKYOU_LOG
                else
                {
                    isSelectedBySituation = true;
                }
#endif
            }
            else
            {
                // モード＆サブモードで選定
                jikkyoBaseList = GetJikkyoBaseListByMode();
            }

#if JIKKYOU_LOG
            var logElement = JikkyouLogger.AddLogElement(GetFirstHorseDistance());
            if (logElement != null)
            {
                logElement.selectType = (isSelectedBySituation)
                    ? JikkyouLogger.LogElement.SelectType.Situation
                    : JikkyouLogger.LogElement.SelectType.Mode;
            }
#endif

            // 何も選定できなかった場合は次のモードへ遷移
            if (jikkyoBaseList == null || jikkyoBaseList.Count == 0)
            {
                if (ChangeMode(GetNextModeByEmpty(_mode))) { return; }
            }
            else
            {
                // 選定したリスト中の状況を満たす実況から抽選して予約
#if JIKKYOU_PROFILE
                Profiler.BeginSample("PickJikkyoBase");
#endif
                var nextJikkyo = PickJikkyoBase(jikkyoBaseList);
#if JIKKYOU_PROFILE
                Profiler.EndSample();
#endif
                if (nextJikkyo != null)
                {
                    AddReserve(nextJikkyo);
                    ResetSilentTime();
                    return;
                }
            }

            // 道中で一定時間予約できる実況がないときは順位実況を予約
            if (_mode == Mode.Dochu)
            {
                UpdateSilentTime(elapsedTime);
            }
        }
        
        
        /// <summary>
        /// SubMode進める。
        /// </summary>
        protected override bool AddSubMode()
        {
            bool ret = base.AddSubMode();
        #if JIKKYOU_LOG
            JikkyouLogger.AddChangeSubModeMark(GetFirstHorseDistance(), _mode, _subMode);
        #endif
            return ret;
        }
        
        /// <summary>
        /// 実況が無い時間の間を埋めるための実況更新。
        /// </summary>
        protected override bool UpdateSilentTime(float elapsedTime)
        {
            if (!base.UpdateSilentTime(elapsedTime))
            {
                return false;
            }

            // 順位実況を入れる。
            AddReserveInterruptOrder();
            return true;
        }
        
        
        /// <summary>
        /// Mode状態遷移。
        /// </summary>
        protected override bool ChangeMode(Mode nextMode, bool isForce = false, bool isOrderCamera = false)
        {
            // 順位実況から道中以降への遷移は可能
            bool isOrderToDochu = _mode == Mode.InterruptOrder && Mode.Dochu <= nextMode;

            // 現在のModeより進む、順位実況→道中以降、強制
            if (_mode < nextMode || isOrderToDochu || isForce)
            {
                _mode = nextMode;
                _subMode = 0;
                _subModeMax = JikkyoBaseCache.GetSubModeMax(nextMode);

                if (nextMode == Mode.InterruptOrder)
                {
                    _jikkyoTriggerCmd.OnStartInterruptOrder();
                }
                else if (nextMode == Mode.Goal)
                {
                    // ゴール時は強制的にシチュエーションから抜ける
                    ChangeSituation(SituationNone);
                }

            #if JIKKYOU_LOG
                JikkyouLogger.AddChangeModeMark(GetFirstHorseDistance(), nextMode, isOrderCamera);
            #endif
                return true;
            }

            return false;
        }
        
        
        /// <summary>
        /// 遷移先のMode取得。現在のModeでの実況データが無くなったとき。
        /// </summary>
        protected override Mode GetNextModeByEmpty(Mode mode)
        {
            switch (mode)
            {
            // ゲートイン→出走待ち。
            case Mode.GateIn: return Mode.WaitStart;
            // スタート→先行争い。
            case Mode.Start: return Mode.Senko;
            // 先行争い→道中。
            case Mode.Senko: return Mode.Dochu;
            // 順位実況→レース進行による選択（道中以降）
            case Mode.InterruptOrder:
                var nextMode = GetNextModeByRaceState();
                if (nextMode < Mode.Dochu) return Mode.Dochu;
                else return nextMode;
            // ゴール→ゴール後実況
            case Mode.Goal:
                // 掲示板に確定表示が出たら遷移
                if (_isFixedResultBoard) { return Mode.AfterGoal; }
                return Mode.Goal;
            // ゴール後実況→レース終了待ち。
            case Mode.AfterGoal: return Mode.WaitRaceEnd;
            // その他：実況対象が無くなっても遷移させない。
            default: return mode;
            }
        }
     
        /// <summary>
        /// 遷移先のMode取得。レースの進行状況をもとに。
        /// </summary>
        private Mode GetNextModeByRaceState()
        {
            Mode retMode = _mode;

            switch (RaceManager.Instance.State)
            {
            // ゲートイン実況
            case RaceDefine.RaceState.GateIn:
                return Mode.GateIn;

            // レース中実況
            case RaceDefine.RaceState.Race:
            case RaceDefine.RaceState.OverRun:
            case RaceDefine.RaceState.OverRunResult:
                var firstHorseInfo = _horseAccessor.GetHorseInfoByOrder(0);
                // ゴール
                if (firstHorseInfo.GetPhase() == HorseRaceInfo.Phase.Finished) { return Mode.Goal; }

                float remainDistance = GetRaceRemain();
                // 最終直線
                if (remainDistance <= _lastStraightRemainDistance) { return Mode.LastStraight; }
                // 最終コーナー（第4コーナー通過 ＆ 残り距離）
                if (RaceUtil.IsFinalCorner(remainDistance, LastCornerNo)) { return Mode.LastCorner; }
                // スタート
                return Mode.Start;

            // レース終了
            case RaceDefine.RaceState.WinningCircleInit:
            case RaceDefine.RaceState.WinningCircle:
            case RaceDefine.RaceState.End:
                return Mode.WaitRaceEnd;
            }

            return retMode;
        }
        
        /// <summary>
        /// 解説発行可能な状態か取得。
        /// </summary>
        protected override bool IsCommentEnable()
        {
            // #80286 フリー編ではゴール後も解説を発行できるようにor条件追加している。
            return GetRaceRemain() > CommentDisableDistance || (JikkyoTrigger.IsSingleModeScenarioFree() && _mode >= Mode.Goal);
        }

        protected override void FirstExec(ReserveInfo execReserveInfo)
        {
            // イベントカメラ再生。
            PlayEventCamera(execReserveInfo);
        }
        
        /// <summary>
        /// 実況によるイベントカメラ再生
        /// </summary>
        private void PlayEventCamera(ReserveInfo reserveInfo)
        {
        #if !CYG_PRODUCT
            // FPS検証のため乱数はできるだけ排除
            if (DebugPerfSystem.Instance.IsMeasurementDetailFPS)
            {
                return;
            }
        #endif

            //エピソードレースは独自でイベントカメラが再生される
            if (_raceInfo.RaceType == RaceDefine.RaceType.Story)
                return;

            var jikkyoBase = reserveInfo.JikkyoBase;

            // カメラなし
            if (jikkyoBase.CameraId <= 0) { return; }

            var camera = MasterDataManager.Instance.masterRacePlayerCamera.Get(jikkyoBase.CameraId);
            if (camera == null) { return; }

            GetEventCameraTargetHorseIndex(reserveInfo, out int horseIndex, out int rivalIndex);
            
        #if CYG_DEBUG
            bool isEventCameraSuccess = RaceCameraManager.Instance.PlayEventCamera(horseIndex, rivalIndex, camera.Id);
            if (isEventCameraSuccess)
            {
                JikkyoDebugger.AddJikkyoHistoryYellow("実況イベントカメラ発動 Camera ID = " + jikkyoBase.CameraId);
            }
            else
            {
                JikkyoDebugger.AddJikkyoHistoryRed("実況イベントカメラ発動 失敗 Camera ID = " + jikkyoBase.CameraId);
            }

        #if JIKKYOU_LOG
            JikkyouLogger.AddEventCameraMark(
                GetFirstHorseDistance(),
                jikkyoBase.CameraId, jikkyoBase.CameraHorse1, horseIndex, isEventCameraSuccess);
        #endif // JIKKYOU_LOG
        #else // CYG_DEBUG
            RaceCameraManager.Instance.PlayEventCamera(horseIndex, rivalIndex, camera.Id);
        #endif // CYG_DEBUG
        }
        
        /// <summary>
        /// イベントカメラ対象のウマ娘インデックス取得
        /// </summary>
        private void GetEventCameraTargetHorseIndex(ReserveInfo reserveInfo, out int horseIndex1, out int horseIndex2)
        {
            horseIndex1 = GetEventCameraTargetHorseIndexOne(
                reserveInfo.JikkyoBase.CameraHorse1,
                reserveInfo.CurHorseIndex, 
                reserveInfo.PickupHorseIndex, 
                _raceInfo.PlayerHorseIndex); // 一人目注視対象は必ず必要なので、デフォルト値はプレイヤー。
            
            horseIndex2 = GetEventCameraTargetHorseIndexOne(
                reserveInfo.JikkyoBase.CameraHorse2,
                reserveInfo.CurHorseIndex, 
                reserveInfo.PickupHorseIndex,
                RaceDefine.HORSE_INDEX_NULL); // 二人目注視対象は無くてもOK。
        }
        
        /// <summary>
        /// イベントカメラ対象のウマ娘インデックス取得。
        /// </summary>
        /// <param name="defaultHorseIndex">注視対象が無い場合に返却されるインデックス。</param>
        private int GetEventCameraTargetHorseIndexOne(JikkyoTrigger.HorseType type, int curHorseIndex, int picupHorseIndex, int defaultHorseIndex)
        {
            // CharaIdThreshold以上の値はそのCharaIdのキャラを返す。
            if (type >= JikkyoTrigger.HorseType.CharaIdThreshold)
            {
                int charaId = (int)type;
                var targetHorse = _raceInfo.RaceHorse.FirstOrDefault(x => x.charaId == charaId);
                return targetHorse != null ? targetHorse.horseIndex : defaultHorseIndex;
            }
        
            // Noneはイベントカメラ注視対象無し。
            if (type == JikkyoTrigger.HorseType.None)
            {
                return defaultHorseIndex;
            }
            
            int horseIndex;
            
            if (type == JikkyoTrigger.HorseType.CurHorse &&
                curHorseIndex >= 0)
            {
                horseIndex = curHorseIndex;
            }
            else if (type == JikkyoTrigger.HorseType.PickupHorse &&
                     picupHorseIndex >= 0)
            {
                horseIndex = picupHorseIndex;
            }
            else
            {
                IHorseRaceInfo info = _jikkyoTriggerCmd.GetHorse(type);
                if (info != null)
                {
                    horseIndex = info.HorseIndex;
                }
                else
                {
                    // 未設定時はプレイヤーを見る
                    horseIndex = _raceInfo.PlayerHorseIndex;
                }
            }
            return horseIndex;
        }
        
        /// <summary>
        /// 順位実況を再生中かどうか。
        /// </summary>
        public bool IsOrderPlaying()
        {
            if (_curReserveInfo.JikkyoBase == null) { return false; }

            return _curReserveInfo.JikkyoBase.Mode == Mode.InterruptOrder;
        }

        /// <summary>
        /// 割込み順位実況を再生待ちバッファに積む。
        /// </summary>
        public void AddReserveInterruptOrder(bool isOrderCamera = false)
        {
            // 既に再生待ちに順位実況が積まれている場合は処理不要。
            if (!isOrderCamera)
            {
                for (int i = 0, count = _reserveList.Count; i < count; ++i)
                {
                    if (_reserveList[i].JikkyoBase.Mode == Mode.InterruptOrder) { return; }
                }
            }

            // 割り込み順位へ移行
            ChangeMode(Mode.InterruptOrder, false, isOrderCamera);
        }
        
        
    }
}
