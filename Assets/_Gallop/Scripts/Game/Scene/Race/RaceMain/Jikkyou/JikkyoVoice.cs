#if UNITY_EDITOR && CYG_DEBUG
// キューシート名・キューIDの存在チェックを行う
//#define CHECK_EXIST_CUE
#endif

#if CYG_DEBUG
// デバッグログ出力
#define VOICE_DEBUG_LOG_ENABLE
#endif

using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 実況ボイス再生。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class JikkyoVoice
    {
        /////////////////////////////////////////////////////////////////
        // 定義。
        /////////////////////////////////////////////////////////////////
        public const char   VOICE_COMMAND_SEPARATOR = '|';      // ボイスコマンドを分離する文字

        public const string CMD_PREFIX_HORSENAME    = "h_";   // ウマ娘名
        public const string CMD_PREFIX_HORSENAME_L  = "hl_";  // ウマ娘名（語尾伸ばし）
        public const string CMD_PREFIX_HORSENAME_NO = "hn_";  // ウマ娘名＋の
        public const string CMD_PREFIX_HORSENAME_TO = "hto_"; // と＋ウマ娘名
        public const string CMD_PREFIX_HORSENAME_TA = "hta_"; // たのは＋ウマ娘名
        public const string CMD_PREFIX_HORSENAME_A  = "ha_";  // 「と＋ウマ娘名」に続く場合の発音
        public const string CMD_PREFIX_COURCENAME   = "crs_"; // コース名
        public const string CMD_PREFIX_RACENAME     = "rce_"; // レース名＋レース固有
        public const string CMD_PREFIX_WAKU_BAN     = "wbn_"; // 枠番ウマ番
        public const string CMD_PREFIX_BAN          = "ubn_"; // ウマ番
        public const string CMD_PREFIX_POP          = "pop_"; // 〇番人気
        public const string CMD_PREFIX_BASIN        = "bsn_"; // 馬身
        public const string CMD_PREFIX_BASIN_NO_TENSION = "bsnnt_"; // 馬身（テンションでキューを分岐しない）
        public const string CMD_PREFIX_HORSE_NUM    = "num_"; // 出走人数
        public const string CMD_PREFIX_CATEGORY     = "cat_"; // レースカテゴリ：芝ダート・距離
        public const string CMD_PREFIX_SINGLE_MODE_TEAM_NAME = "tnm_"; // チーム対抗戦チーム名。
        public const string CMD_PREFIX_SINGLE_MODE_TEAM_HONOR = "thr_"; // チーム対抗戦チーム称号。

        public const string CMD_PREFIX_WAIT         = "wait_";  // n秒ウェイト
        public const string CMD_PREFIX_CROSS        = "cross_"; // n秒詰めて次を再生

        // CueId桁操作用
        private const int RACE_ID_DIGIT   = 100; // レース名CueIdのレースId桁

        // ウマ娘名CueIDの10の桁分類
        public enum HorseNameType
        {
            Default = 0, // 通常
            Long = 1,    // 語尾延ばし
            No = 2,      // ウマ娘名＋の
            To = 3,      // と＋ウマ娘名
            Ta = 4,      // たのは＋ウマ娘名
            A = 5,       // 「と＋ウマ娘名」に続く場合の発音
        }

        // ウマ番CueIDの10の桁分類
        public enum BanType
        {
            Default = 0, // 通常
            No = 1,      // ウマ番＋の
            To = 2,      // と＋ウマ番
            Ta = 3       // たのは＋ウマ番
        }

        // レース名CueIDの10の桁分類
        public enum RaceType
        {
            Default = 0, // 通常
            Wo = 1,      // ＋を制しました
        }

        // 再生コマンド作成関数delegate
        private delegate IVoiceCmd CreateCmdFunc(ref CommandCreateDesc desc);

        /// <summary>
        /// 再生コマンド作成関数の引数パラメータ。
        /// </summary>
        private struct CommandCreateDesc
        {
            public string Value;
            public Jikkyo.JikkyoType Type;
            public int Tension;
            public IVoiceCmd PrevCmd;
            public bool CheckExistCue;
            public bool IsLastCommand;
            public bool IsCrossTimeEnable;
        }
        
        
        /////////////////////////////////////////////////////////////////
        // 変数。
        /////////////////////////////////////////////////////////////////
        private List<IVoiceCmd> _cmds               = new List<IVoiceCmd>();
        private int             _cmdPlayingIndex    = 0;
        private bool            _isEnd              = true;
        private bool            _isCmdError;
        private string          _cmdErrorMessage    = "";

#if CYG_DEBUG
        private bool _debugcheckExistCue = false;
#endif

        private float[] _beforeHorseNameCrossTimeArray;
        private float[] _beforeHorseNameBanCrossTimeArray;
        private float[] _afterHorseNameCrossTimeArray;
        private float[] _beforeHorseNameWithPostpositionalCrossTimeArray;
        
        // 再生コマンド作成関数テーブル
        private Dictionary<string, CreateCmdFunc> _createCmdFuncDictionary = null;

        /////////////////////////////////////////////////////////////////
        // 関数。
        /////////////////////////////////////////////////////////////////

        /// <summary>
        /// 初期化
        /// </summary>
        public void Init(RaceParamDefine.JikkyoParam jikkyoParam)
        {
            Clear();

            _beforeHorseNameCrossTimeArray = jikkyoParam?.BeforeHorseNameCrossTimeArray;
            _beforeHorseNameBanCrossTimeArray = jikkyoParam?.BeforeHorseNameBanCrossTimeArray;
            _afterHorseNameCrossTimeArray = jikkyoParam?.AfterHorseNameCrossTimeArray;
            _beforeHorseNameWithPostpositionalCrossTimeArray = jikkyoParam?.BeforeHorseNameWithPostpositionalCrossTimeArray;

#if CHECK_EXIST_CUE
            _debugcheckExistCue = true;
#endif
            // 再生コマンド作成関数テーブル
            InitCreateCommandFunc();
        }

        /// <summary>
        /// 再生情報のクリア
        /// </summary>
        public void Clear()
        {
            _cmds.Clear();
            _cmdPlayingIndex = 0;
            _isEnd = true;
            ClearCmdError();
        }

        /// <summary>
        /// 再生中のボイスを停止
        /// </summary>
        public void Cancel()
        {
            foreach (var cmd in _cmds)
            {
                cmd.Cancel();
            }

        }

        /// <summary>
        /// 一時停止/再開。
        /// </summary>
        /// <param name="isPause">一時停止ならtrue。</param>
        public void Pause( bool isPause )
        {
            foreach (var cmd in _cmds)
            {
                cmd.Pause(isPause);
            }
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void Update( float deltaTime )
        {
            if (_isEnd)
            {
                return;
            }
            if (_cmdPlayingIndex >= _cmds.Count)
            {
                _isEnd = true;
                return;
            }

            var currentCommand = _cmds[_cmdPlayingIndex];
            currentCommand.Update(deltaTime);

            // 再生が終了している、もしくは残り時間がCrossTime未満なら、次のコマンドを再生
            if (currentCommand.IsEnd || (
                   currentCommand.CrossTime > 0.0f &&
                   currentCommand.GetRemainingTime() < currentCommand.CrossTime))
            {
                PlayCommand(++_cmdPlayingIndex);
            }
        }

        /// <summary>
        /// 食い気味再生コマンドか否か
        /// </summary>
        private static bool IsCrossCommand(string value)
        {
            return value.StartsWith(CMD_PREFIX_CROSS);
        }

        /// <summary>
        /// 一連のボイスを再生
        /// </summary>
        public void PlayVoiceSequential(string voiceCmd, Jikkyo.JikkyoType type, Jikkyo.Tension tension, bool isAdditonal = false, bool isCrossTimeEnable = true)
        {
#if CYG_DEBUG
            CreateCommand(voiceCmd, type, tension, _debugcheckExistCue, isAdditonal, isCrossTimeEnable);
#else
            CreateCommand(voiceCmd, type, tension);
#endif

            // 先頭のコマンド実行。
            _isEnd = PlayCommand(_cmdPlayingIndex);
        }

        /// <summary>
        /// 再生コマンド実行
        /// </summary>
        private bool PlayCommand(int index)
        {
            // 指定インデックスのコマンドが無い場合は終了扱い。
            if (index >= _cmds.Count)
            {
                return true;
            }

            _cmds[index].Play();
            return _cmds[index].IsEnd;
        }

        /// <summary>
        /// 再生終了したか
        /// </summary>
        public bool IsEnd()
        {
            return _isEnd;
        }

        /// <summary>
        /// 一連のボイスの長さを取得
        /// </summary>
        public float GetLength()
        {
            float length = 0;
            for (int i = 0, count = _cmds.Count; i < count; ++i)
            {
                var cmd = _cmds[i];
                length += cmd.GetLength() - cmd.CrossTime;
            }
            return length;
        }

        /// <summary>
        /// 一連のボイスの再生中時間を取得
        /// </summary>
        public float GetPlayTime()
        {
            float length = 0;
            for (int i = 0, count = _cmds.Count; i < count; ++i)
            {
                var cmd = _cmds[i];
                length += Mathf.Min(cmd.GetPlayTime(), cmd.GetLength() - cmd.CrossTime);
            }
            return length;
        }

        /// <summary>
        /// エラーがあるか否か
        /// </summary>
        public bool IsCmdError()
        {
            return _isCmdError;
        }

        /// <summary>
        /// エラーメッセージ取得
        /// </summary>
        public string GetCmdErrorMessage()
        {
            return _cmdErrorMessage;
        }

        /// <summary>
        /// エラーメッセージ設定
        /// </summary>
        private void SetCmdErrorMessage(string message)
        {
            _isCmdError = true;
            _cmdErrorMessage = message;
#if CYG_DEBUG
            JikkyoDebugger.AddJikkyoHistoryRed(message);
            Debug.LogError(message);
#endif // CYG_DEBUG
        }

        /// <summary>
        /// エラー情報クリア
        /// </summary>
        private void ClearCmdError()
        {
            _isCmdError = false;
            _cmdErrorMessage = "";
        }

        /// <summary>
        /// 一連の再生コマンドを取得
        /// </summary>
        public List<IVoiceCmd> GetCommands()
        {
            return _cmds;
        }

        /// <summary>
        /// 再生コマンドを積む
        /// </summary>
        /// <param name="voiceCmd">一連のボイスコマンド</param>
        /// <param name="type">実況か解説か</param>
        /// <param name="tension">テンション</param>
        /// <param name="checkExistCue">CueSheet, CueIdの存在チェックをするか否か</param>
        /// <param name="isAdditional">クリアをせずに追加する</param>
        public void CreateCommand(string voiceCmd, Jikkyo.JikkyoType type, Jikkyo.Tension tension, bool checkExistCue = false, bool isAdditional = false, bool isCrossTimeEnable = true)
        {
            if (string.IsNullOrEmpty(voiceCmd))
            {
                return;
            }

            string[] voiceCmdSplitted = voiceCmd.Split(VOICE_COMMAND_SEPARATOR);

            if (!isAdditional)
            {
                _cmds.Clear();
                _cmds.Capacity = voiceCmdSplitted.Length;
                _cmdPlayingIndex = 0;
                ClearCmdError();
            }

            IVoiceCmd prevCmd = null;
            for (int i = 0, cnt = voiceCmdSplitted.Length; i < cnt; ++i)
            {
                string val = voiceCmdSplitted[i];

                // 空文字の場合はスルー
                if (string.IsNullOrEmpty(val))
                {
                    continue;
                }

                // 次のコマンドが食い気味で再生されるようにする
                if (prevCmd != null && IsCrossCommand(val))
                {
                    float crossTime;
                    if (float.TryParse(val.Substring(CMD_PREFIX_CROSS.Length), out crossTime))
                    {
                        prevCmd.CrossTime += crossTime;
                    }
                    continue;
                }

                // コマンド追加
                bool isLastCommand = i == cnt-1;
                var cmd = CreateCommandInner(val, type, tension, prevCmd, checkExistCue, isLastCommand, isCrossTimeEnable);
                _cmds.Add(cmd);

                prevCmd = cmd;
            }
        }

        #region CueId取得
        /// 実況CueSheet-CueName-CueId一覧
        /// https://xxxxxxxxxx/pages/viewpage.action?pageId=25930174
        /// CueIdの先頭に0が来るとサウンドツールの制約上扱いづらいため、
        /// 先頭に意味のない1を付けているものがある。

        /// <summary>
        /// ウマ娘名 CueId（テンション込み）
        /// (charaId:4)(variation:1)(tension:1)
        /// variation 0=通常, 1=語尾延ばし, 2=＋の, 3=と＋, 4=たのは＋, 5=「と＋」に続く場合の発音
        /// </summary>
        private static int GetHorseNameCueId(int charaId, int tension, HorseNameType type)
        {
            const int CHARA_ID_DIGIT = 100;
            const int TYPE_DIGIT = 10;
            return charaId * CHARA_ID_DIGIT + (int)type * TYPE_DIGIT + tension;
        }

        /// <summary>
        /// 枠番ウマ番 CueId取得
        /// (waku:1)(ban:2)(tension:1)
        /// waku 枠番
        /// ban ウマ番
        /// variation 0=通常, 1=＋の
        /// </summary>
        public static string GetWakuBanCueId(int waku, int ban)
        {
            const int WAKU_DIGIT = 1000;
            const int BAN_DIGIT = 10;
            int cueId = waku * WAKU_DIGIT + ban * BAN_DIGIT;
            return cueId.ToString();
        }

        /// <summary>
        /// ウマ番 CueId取得
        /// 1(ban:2)(variation:1)(tension:1)
        /// ban ウマ番
        /// variation 0=通常, 1=＋の, 2=と＋, 3=たのは＋
        /// </summary>
        public static string GetBanCueId(int ban, BanType banType)
        {
            const int CAP = 10000;
            const int TYPE_DIGIT = 10;
            const int BAN_DIGIT = 100;
            int cueId = ban * BAN_DIGIT + (int)banType * TYPE_DIGIT + CAP;
            return cueId.ToString();
        }

        /// <summary>
        /// ウマ番 CueIdからタイプ取得
        /// </summary>
        /// <param name="cueId"></param>
        /// <returns></returns>
        public static BanType GetBanTypeFromCueId(int cueId)
        {
            const int TYPE_DIGIT = 10;
            return (BanType)((cueId / TYPE_DIGIT) % 10);
        }

        /// <summary>
        /// 〇番人気 CueId取得
        /// 1(pop:2)(variation:1)(tension:1)
        /// variation 0=通常, 1=＋の
        /// </summary>
        public static string GetPopCueId(int popularity, bool isNo)
        {
            const int CAP = 10000;
            const int VARIATION_NO = 10;
            const int POP_DIGIT = 100;
            int variation = isNo ? VARIATION_NO : 0;
            int cueId = popularity * POP_DIGIT + variation + CAP;
            return cueId.ToString();
        }

        /// <summary>
        /// 〇馬身 CueId取得
        /// 1(basin:2)(tension:1)
        /// basin 1馬身なら01、20馬身なら20
        /// </summary>
        public static string GetBasinCueId(int basin)
        {
            const int CAP = 1000;
            const int BASIN_DIGIT = 10;
            int cueId = basin * BASIN_DIGIT + CAP;
            return cueId.ToString();
        }

        /// <summary>
        /// 〇馬身リード！ CueId取得
        /// 1(basin:2)(tension:1)
        /// basin 1馬身なら01、20馬身なら20
        /// </summary>
        public static string GetBasinLCueId(int basin)
        {
            const int CAP = 1003;
            const int BASIN_DIGIT = 10;
            int cueId = basin * BASIN_DIGIT + CAP;
            return cueId.ToString();
        }

        /// <summary>
        /// リードは〇馬身 CueId取得
        /// 1(basin:2)(tension:1)
        /// basin 1馬身なら01、20馬身なら20
        /// </summary>
        public static string GetLBasinCueId(int basin)
        {
            const int CAP = 1004;
            const int BASIN_DIGIT = 10;
            int cueId = basin * BASIN_DIGIT + CAP;
            return cueId.ToString();
        }
        
        /// <summary>
        /// コース名 CueId取得
        /// (courseId:5)
        /// </summary>
        public static string GetCourseCueId(int courseId)
        {
            int cueId = courseId;
            return cueId.ToString();
        }

        /// <summary>
        /// レース名 CueId取得
        /// (raceId:4)(variation:1)(tension:1)
        /// variation 0=通常, 1=＋を制しました
        /// </summary>
        public static string GetRaceCueId(int raceId, RaceType raceType)
        {
            const int TYPE_DIGIT = 10;
            int variation = (int)raceType * TYPE_DIGIT;
            int cueId = raceId * RACE_ID_DIGIT + variation;
            return cueId.ToString();
        }

        /// <summary>
        /// レース名 通常CueIdから「＋を制しました」CueIdに変換
        /// </summary>
        public static int ConvertRaceCueIdToRaceWoCueId(int cueId)
        {
            const int TYPE_DIGIT = 10;
            return cueId + (int)RaceType.Wo * TYPE_DIGIT;
        }

        /// <summary>
        /// 芝・ダート CueId取得
        /// (category:1)(Id:3)
        /// category 1=芝・ダート, 2:距離
        /// </summary>
        public static string GetGroundTypeCueID(int groundType)
        {
            const int BASE = 1000;
            int cueId = BASE + groundType;
            return cueId.ToString();
        }

        /// <summary>
        /// コース距離 CueId取得
        /// (category:1)(Id:3)
        /// category 1=芝・ダート, 2:距離
        /// </summary>
        public static string GetDistanceCueID(int distance)
        {
            const int KEY_BASE = 20000;
            int key = KEY_BASE + distance;
            var masterDataManager = MasterDataManager.Instance;
            if (masterDataManager == null)
            {
                return string.Empty;
            }

            var raceElement = masterDataManager.masterRaceJikkyoRace.Get(key);
            if (raceElement != null)
            {
                return raceElement.CueId.ToString();
            }
#if VOICE_DEBUG_LOG_ENABLE
            JikkyoDebugger.DebugLog("実況ボイス：コース距離[" + distance + "]に該当するCueIDがありません ");
#endif // VOICE_DEBUG_LOG_ENABLE
            return string.Empty;
        }

        /// <summary>
        /// 出走人数 CueId取得
        /// 1(num:2)(variation:1)
        /// variation 0=通常, 1=＋の
        /// </summary>
        public static string GetHorseNumCueId(int horseNum, bool isNo)
        {
            const int CAP = 1000;
            const int HORSE_NUM_DIGIT = 10;
            const int VARIATION_NO = 1;
            int variation = isNo ? VARIATION_NO : 0;
            int cueId = horseNum * HORSE_NUM_DIGIT + variation + CAP;
            return cueId.ToString();
        }

        /// <summary>
        /// チーム対抗戦チーム名CueId取得。
        /// </summary>
        public static string GetTeamNameCueId(int teamId)
        {
            const int TEAM_NAME_ID_BASE = 10000;
            const int TEAM_ID_DIGIT = 10; // チームIdを十の位。
            return (TEAM_NAME_ID_BASE + teamId * TEAM_ID_DIGIT).ToString();
        }
        
        /// <summary>
        /// チーム対抗戦チーム称号CueId取得。
        /// </summary>
        public static string GetTeamHonorCueId(int honorId)
        {
            const int TEAM_HONOR_ID_BASE = 20001; // 一の位はテンションだが1固定。
            const int HONOR_ID_DIGIT = 10; // 称号Idを十の位。
            return (TEAM_HONOR_ID_BASE + honorId * HONOR_ID_DIGIT).ToString();
        }
#endregion CueId取得
    }
}