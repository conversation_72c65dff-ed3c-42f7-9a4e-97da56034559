using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中情報プレートUINullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class RaceUIMessagePlateNull : IRaceUIMessagePlate
    {
        public void Init(RaceLoaderManager raceLoader, RaceDefine.RaceType raceType)
        {
        }

        public void Update(float deltaTime)
        {
        }

        public void RequestShowMessagePlate(ref RaceUIMessagePlate.PlayDesc desc)
        {
        }

        public void HideMessagePlateImmediately()
        {
        }
        
        public void SetVisible(bool isVisible)
        {
        }

        public bool IsVisible()
        {
            return false;
        }

        public void Pause()
        {
        }

        public void Resume()
        {
        }
        public void SetSpeed(float speed)
        {
        }
    }
}

