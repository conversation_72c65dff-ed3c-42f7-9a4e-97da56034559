using UnityEngine;
using System;
using System.Collections;
using System.Linq;
using UnityEngine.UI;
using DG.Tweening;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 順位表示UIの制御。
    /// </summary>
    /// <remarks>
    /// キャラの順位ポーズと一緒に表示されるやつ。
    /// </remarks>
    //-------------------------------------------------------------------
    public class RaceResultSceneUI : MonoBehaviour, IRaceResultSceneUI
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        private enum Step
        {
            Wait,   //外部からの待ち
            PlayFinishOrder,
            End,
        }

        public const float OrderDispTimeDefault = 1.15f; // 2着以下の順位UI表示タイミング
        public const float OrderDispTimeFirst = 2.8f;  // 1着の順位UI表示タイミング

        private const float RANK_FLASH_SCALE = 3.0f;
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>順位Flashペアレント先。</summary>
        [SerializeField]
        private RectTransform _rankRoot = null;

        /// <summary>順位Flash。</summary>
        private RaceUIFinishOrderFlash _finishOrderUI;

        /// <summary>CntentsRoot。</summary>
        [SerializeField]
        private RectTransform _contentsRoot = null;
        /// <summary>スキップボタン。</summary>
        [SerializeField]
        private ButtonCommon _skipButton = null;
        
        /// <summary>演出の進捗。</summary>
        private Step _step = Step.Wait;
        
        /// <summary>遅延再生用。</summary>
        private float _timer = 0.0f;

        /// <summary>PlayFinishOrder後、この秒数経過してからFlash再生する。</summary>
        private float _orderDispTime = OrderDispTimeDefault;

        /// <summary>チャレンジマッチ累計ポイント</summary>
        [SerializeField] 
        private ChallengeMatchRaceResultHorsePoint _challengeMatchRankPoint = null;

        /// <summary>チャレンジマッチポイント演出行うかどうか。</summary>
        private bool _isUseChallengeMatchPoint;
        
        /// <summary>初期化済みかどうか。</summary>
        private bool _isInitialize = false;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public void Setup()
        {
            _isUseChallengeMatchPoint = RaceManager.RaceInfo.RaceType == RaceDefine.RaceType.ChallengeMatch;
            
            // UI表示時間の決定。
            InitFinishOrderDispTime();

            _finishOrderUI = new RaceUIFinishOrderFlash();
            
            if(_skipButton != null)
            {
                _skipButton.onClick.RemoveAllListeners();
                _skipButton.onClick.AddListener(OnPushSkip);
            }

            _isInitialize = true;
        }

        public void InitSafeArea()
        {
            UIManager.Instance.AdjustContentsRootRect(_contentsRoot);
        }

        /// <summary>
        /// 更新。
        /// </summary>
        private void Update()
        {
            if (!_isInitialize) return;

            _timer += Time.deltaTime;

            switch (_step)
            {
                case Step.PlayFinishOrder:// 順位表示
                    if (_timer > _orderDispTime)
                    {
                        var player = RaceManager.Instance.GetPlayerHorseInfo();
                        PlayRankFlash();
                        PlayFinishOrderSound(player.FinishOrder);
                        PlayRankPoint(player.HorseIndex);

                        _step = Step.End;

                        OnPlayFinishOrder();
                    }
                    break;
            }
        }

        protected virtual void OnPlayFinishOrder()
        {
            var player = RaceManager.Instance.GetPlayerHorseInfo();
            PlayRankFlash();
            PlayFinishOrderSound(player.FinishOrder);

            _step = Step.End;
        }

        /// <summary>
        /// 順位UI表示開始時間初期化。
        /// </summary>
        private void InitFinishOrderDispTime()
        {
            // プレイヤーの順位によって、UI表示開始までの時間が変わる。
            IHorseRaceInfo playerHorseInfo = RaceManager.Instance.GetPlayerHorseInfo();
            if (playerHorseInfo != null)
            {
                _orderDispTime = CalcFinishOrderDispTime(playerHorseInfo.FinishOrder);
            }
        }

        /// <summary>
        /// 順位Flash表示開始するまでの遅延時間取得。
        /// </summary>
        /// <param name="finishOrder">着順。0~</param>
        public static float CalcFinishOrderDispTime(int finishOrder)
        {
            return finishOrder > 0
                ? OrderDispTimeDefault
                : OrderDispTimeFirst;
        }

        public void PlayFinishOrder()
        {
            _step = Step.PlayFinishOrder;
            _timer = 0;
        }

        public bool IsPlayedFinishOrder()
        {
            return _step >= Step.End;
        }

        public void Hide()
        {
            gameObject.SetActive(false);

            if (_isUseChallengeMatchPoint)
            {
                _challengeMatchRankPoint.Hide();
            }
        }

        /// <summary>
        /// 順位ごとのサウンド再生
        /// </summary>
        /// <param name="finishOrder">着順。0~</param>
        public static void PlayFinishOrderSound(int finishOrder)
        {
            int playIndex = finishOrder;
            if (finishOrder >= 4)
            {
                playIndex = 3;
            }

            AudioId[] rankPlaySe = new AudioId[]
            {
                AudioId.SFX_RACE_RANK_1,
                AudioId.SFX_RACE_RANK_2,
                AudioId.SFX_RACE_RANK_3,
                AudioId.SFX_RACE_RANK_4, // 4着以下は一緒
            };

            AudioManager.Instance.PlaySe(rankPlaySe[playIndex]);

        }

        /// <summary>
        /// 順位FLASH再生
        /// </summary>
        private void PlayRankFlash()
        {
            IHorseRaceInfo playerHorseInfo = RaceManager.Instance.GetPlayerHorseInfo();
            PlayRankFlash(RaceManager.Instance.RaceLoader.ResultRankFlashPrefab, playerHorseInfo.FinishOrder);
        }

        private void PlayRankFlash(GameObject resultRankFlashPrefab, int finishOrder)
        {
            _finishOrderUI.Setup(
                resultRankFlashPrefab, 
                _rankRoot, 
                new Vector3(RANK_FLASH_SCALE, RANK_FLASH_SCALE, 1.0f));
            _finishOrderUI.Play(finishOrder);
        }

        /// <summary>
        /// チャレンジマッチの着順ポイント演出再生。
        /// </summary>
        private void PlayRankPoint(int horseIndex)
        {
            // チャレンジマッチだけで必要。
            if (!_isUseChallengeMatchPoint)
            {
                return;
            }
            
            int rankPoint = GetRankPoint(horseIndex);
            if (rankPoint <= 0)
            {
                return;
            }

            // 着順ポイント・累計ポイントのアニメーション開始。
            int totalPoint = GetTotalPoint(horseIndex);
            _challengeMatchRankPoint.gameObject.SetActive(true);
            _challengeMatchRankPoint.Setup(rankPoint, totalPoint);
            _challengeMatchRankPoint.Show();
        }

        /// <summary>
        /// チャレンジマッチ着順ポイント取得。
        /// </summary>
        private int GetRankPoint(int targetHorseIndex)
        {
            var simEventList = RaceManager.Instance.GetSimulateEventAsc();
            var pointEventList = simEventList.Where(x => x.type == SimulateEventType.ChallengeMatchPoint);
            foreach (var pointEvent in pointEventList)
            {
                RaceEventPlayer.GetChallengeMatchPointEventParam(
                    pointEvent,
                    out int horseIndex,
                    out int rawPointId,
                    out int point);
                
                if (horseIndex != targetHorseIndex)
                {
                    continue;
                }

                var masterRawPoint = MasterDataManager.Instance.masterChallengeMatchRawPoint.Get(rawPointId);
                if (masterRawPoint == null)
                {
                    Debug.LogError($"rawPointId={rawPointId}がchallenge_match_raw_point.csvに登録されていない");
                    continue;
                }

                if (masterRawPoint.ConditionType != (int)HorseChallengeMatchPointCalculator.ConditionType.FinishOrder)
                {
                    continue;
                }

                // 着順ポイントは１件しか登録されないはずなので、最初の１件のポイントを返せばok。
                return point;
            }

            Debug.LogWarning($"targetHorseIndex={targetHorseIndex}の着順ポイントがイベントに登録されていない");
            return 0;
        }

        /// <summary>
        /// チャレンジマッチ累計ポイント取得。
        /// </summary>
        private int GetTotalPoint(int targetHorseIndex)
        {
            int retTotalPoint = 0;
            
            var simEventList = RaceManager.Instance.GetSimulateEventAsc();
            var pointEventList = simEventList.Where(x => x.type == SimulateEventType.ChallengeMatchPoint);
            foreach (var pointEvent in pointEventList)
            {
                RaceEventPlayer.GetChallengeMatchPointEventParam(
                    pointEvent,
                    out int horseIndex,
                    out int _,
                    out int point);
                
                if (horseIndex != targetHorseIndex)
                {
                    continue;
                }

                retTotalPoint += point;
            }
            
            return retTotalPoint;
        }

        /// <summary>
        /// スキップボタン押下時処理。
        /// </summary>
        private void OnPushSkip()
        {
        #if CYG_DEBUG
            // ベンチマーク実行中はスキップボタン無効。
            if (RaceDebugger.IsDebugBenchMode)
            {
                return;
            }
        #endif
            Skip();
        }
        
        /// <summary>
        /// スキップ
        /// </summary>
        private void Skip()
        {
            // スキップボタンを消す。
            SetVisibleSkipButton(false);

            RaceManager.Instance.OnClickSkipButton();
        }

        /// <summary>
        /// スキップボタンON/OFF
        /// </summary>
        public void SetVisibleSkipButton(bool isVisible)
        {
            if(_skipButton != null)
            {
                _skipButton.gameObject.SetActive(isVisible);
            }
        }
        
    #if CYG_DEBUG
        /// <summary>
        /// 順位FLASH表示用に初期化
        /// CourseViewerから再生する時用に用意している
        /// </summary>
        public void DbgSetup()
        {
            _finishOrderUI = new RaceUIFinishOrderFlash();

            _isInitialize = true;
        }

        /// <summary>
        /// 順位FLASH再生
        /// CourseViewerから再生するとき用に公開している
        /// </summary>
        /// <param name="resultRankFlashPrefab">順位エフェクトFLASHのPrefab</param>
        /// <param name="finishOrder">順位</param>
        public void DbgPlayRankFlash(GameObject resultRankFlashPrefab, int finishOrder)
        {
            _finishOrderUI.Setup(
                resultRankFlashPrefab, 
                _rankRoot, 
                new Vector3(RANK_FLASH_SCALE, RANK_FLASH_SCALE, 1.0f));
            _finishOrderUI.Play(finishOrder);
        }

        /// <summary>
        /// 順位FLASH破棄
        /// CourseViewerから再生するとき用に公開している
        /// </summary>
        public void DbgDestroyRankFlash()
        {
            if (_finishOrderUI == null)
                return;

            _finishOrderUI.Release();
        }

        public void DbgUpdate(float deltaTime, GameObject resultRankFlashPrefab, int finishOrder)
        {
            _timer += deltaTime;

            var dbgOrderDispTIme = CalcFinishOrderDispTime(finishOrder);

            switch (_step)
            {
                case Step.PlayFinishOrder:// 順位表示
                    if (_timer > dbgOrderDispTIme)
                    {
                        _finishOrderUI = new RaceUIFinishOrderFlash();
                        DbgPlayRankFlash(resultRankFlashPrefab, finishOrder);
                        PlayFinishOrderSound(finishOrder);
                        _step = Step.End;
                    }
                    break;
            }
        }
    #endif
    }
}
