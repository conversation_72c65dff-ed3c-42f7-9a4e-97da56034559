using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    [System.Serializable]
    public class CourseCharacterFacialParamEyebrow : CourseCharacterFacialParamBase
    {
        public bool IsEnableEyeBlow = true;
        public bool IsMayuVisible;// 眉の表示ON/OFFを行うオプション。
        public int DurationFrame;
        public DrivenKeyComponent.InterpolateType InterpolationType;
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();
        public bool UsePartsScale = true;

        public CourseCharacterFacialParamEyebrow()
        {
            IsMayuVisible = true;
        }
        public CourseCharacterFacialParamEyebrow(CourseCharacterFacialParamEyebrow src) : base(src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCharacterFacialParamEyebrow(this);
        }


        public override void Set(CourseBaseParam param)
        {
            base.Set(param);

            var src = param as CourseCharacterFacialParamEyebrow;
            if (src == null)
            {
                return;
            }

            IsEnableEyeBlow = src.IsEnableEyeBlow;
            DurationFrame = src.DurationFrame;
            InterpolationType = src.InterpolationType;
            IsMayuVisible = src.IsMayuVisible;
            Interpolation = new InterpolationParamSimple(src.Interpolation);
            UsePartsScale = src.UsePartsScale;
        }

    }

}
