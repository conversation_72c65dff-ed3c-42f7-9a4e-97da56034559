using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    [System.Serializable]
    public class CourseCharacterFacialMouthParam : CourseCharacterFacialParamBase
    {
        public bool IsEnableMouth;
        public int DurationFrame;
        public DrivenKeyComponent.InterpolateType InterpolationType;
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCharacterFacialMouthParam()
        {
        }
        public CourseCharacterFacialMouthParam(CourseCharacterFacialMouthParam src) : base(src)
        {
            Set(src);
        }
        public override CourseBaseParam Clone()
        {
            return new CourseCharacterFacialMouthParam(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);

            var src = param as CourseCharacterFacialMouthParam;
            if (src == null)
            {
                return;
            }

            IsEnableMouth = src.IsEnableMouth;
            DurationFrame = src.DurationFrame;
            InterpolationType = src.InterpolationType;
            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }
    }
}
