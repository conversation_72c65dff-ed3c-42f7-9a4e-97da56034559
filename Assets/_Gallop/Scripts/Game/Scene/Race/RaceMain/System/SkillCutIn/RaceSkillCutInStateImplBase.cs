using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スキルカットイン再生フロー：基底。
    /// </summary>
    //-------------------------------------------------------------------
    public abstract class RaceSkillCutInStateImplBase : IRaceSkillCutInStateImpl
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>フェード時間。</summary>
        protected const float CUTIN_FADE_SEC = 10.0f / 30.0f;
        
        /// <summary>
        /// 内部状態。
        /// </summary>
        protected enum State
        {
            Null,
            Activate, // 目線カットイン。
            Main, // スキルカットイン。
            End, // カットイン終了。
        }
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>カットイン再生情報。</summary>
        protected RaceManagerReplayBase.CutInInfo _cutInInfo = null;

        /// <summary>カットイン再生時の状態をキャッシュし、終了時に復元する値。</summary>
        private bool _isVisibleSkipOnStartCutIn = false;
        private bool _isVisibleFastForwardOnStartCutIn = false;
        private bool _isVisibleCameraOnStartCutIn = false;
        private bool _isVisibleMiniMapOnStartCutIn;
        private bool _isVisibleRankOnStartCutIn;
        private bool _isVisibleJikkyoOnStartCutIn;
        private bool _isVisibleSkillInfo;
        private bool _isVisibleChallengeMatchPoint;
        private float _cachedTimeScale = 1.0f; // カットイン再生前のタイムスケール保存。
        
        /// <summary>カットイン再生準備が整った。</summary>
        protected bool _isCutInReady = false;

        /// <summary>カットインのためにインスタンス化したオブジェクトのペアレント先。</summary>
        private readonly Transform _cutInParent;

        /// <summary>カットイン再生に必要な情報をここから取る。</summary>
        protected readonly RaceManagerReplayBase _raceManager;
        protected readonly RaceSkillCutInHelper _cutInHelper;
        protected readonly RaceMainViewController _raceMainView;
        protected readonly IJikkyoAccessor _jikkyoAccessor;
        private readonly ICourseManager _courseManager;
        private readonly IRaceView _raceView;

        /// <summary>状態機械。</summary>
        protected FSM<State> _fsm = new FSM<State>();
        /// <summary>現在の状態。</summary>
        protected State CurState => _fsm.GetStateKey();

        /// <summary>短縮版カットインかどうか。</summary>
        protected bool _isShort = true;
        /// <summary>短縮版カットインかをチェック済みかどうか。</summary>
        protected bool _isCheckedShort = false;

        /// <summary>カットイン再生開始後にSE再生する。</summary>
        private bool _isReqPlaySound = false;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public RaceSkillCutInStateImplBase(
            Transform cutInParent,
            RaceManagerReplayBase raceManager,
            RaceSkillCutInHelper cutInHelper,
            RaceMainViewController raceMainView,
            IJikkyoAccessor jikkyoAccessor,
            ICourseManager courseManager,
            IRaceView raceView)
        {
            _raceManager = raceManager;
            _raceMainView = raceMainView;
            _jikkyoAccessor = jikkyoAccessor;
            _cutInHelper = cutInHelper;
            _cutInParent = cutInParent;
            _courseManager = courseManager;
            _raceView = raceView;
            InitState();
        }

        //---------------------------------------------------------------
        public void Enter(RaceSkillCutInState.CutInEnterInfo enterInfo)
        {
            _cutInInfo = enterInfo.CutInInfo;
            _isCutInReady = false;

            // レース画面のキャプチャは1フレ待つ必要があるそうなのでコルーチンで行う。
            _raceManager.StartCoroutine(CaptureRace(OnCaptureEnd));

            _fsm.SetState(State.Activate);

            // レース用のDirectionalLight設定になっているのを一旦リセット
            var raceViewBase = _raceView as RaceViewBase;
            if (raceViewBase != null)
            {
                raceViewBase.DisableDirectionalLight();
            }
        }

        //---------------------------------------------------------------
        public virtual void Exit()
        {
            // UIの状態復元。
            BackUIStatus();

            //カットインの後片付け
            CleanupCutInHelper();

            // #73429 BackRaceTimeの前にGameObjectを有効化しないと、非再生のParticleSysteのスピード復帰をスキップする処理に引っかかってしまう(ex. RceModelController.SetCharaColorEffectSpeed)。
            //レース中に表示しているものを全て表示にする
            VisibleRace(true);
            
            //レースの時間を戻す
            BackRaceTime();

            RaceCameraManager.Instance.EnableCutInCamera(false);

            if(_raceView.CourseEnvParam != null)
            {
                //Lightmapの設定を戻す
                _raceView.CourseEnvParam.GlobalLightMapParam.UpdateGraphicSetting();
            }
            // リセットしたDirectionalLightを再設定
            var raceViewBase = _raceView as RaceViewBase;
            if (raceViewBase != null)
            {
                raceViewBase.EnableDirectionalLight();
            }

            _cutInInfo = null;
        }

        //---------------------------------------------------------------
        public virtual void FixedUpdate()
        {
            if (!_isCutInReady)
            {
                return;
            }

            _cutInHelper.FixedUpdateForHighSpeed(_raceManager.GetCurFastForwardTimeScale());
        }

        //---------------------------------------------------------------
        public virtual bool Update(float deltaTime)
        {
            // ※基底のUpdateを完全に置き換えるため、敢えてbase.Updateを呼ばない。
            
            if (!_isCutInReady)
            {
                return false;
            }

            //レース側からFogとLightmapパラメータが上書きされるので、一旦デフォルトに戻す
            GraphicSettings.Instance.SetDefaultFog();
            GraphicSettings.Instance.SetDefaultLightmap();
            _cutInHelper.AlterUpdate();
            _fsm.Update(deltaTime);

            return CurState == State.End;
        }

        //---------------------------------------------------------------
        public void LateUpdate()
        {
            _cutInHelper.AlterLateUpdate();
        }

        /// <summary>
        /// カットイン状態入場時に行われるレース画面のキャプチャ。
        /// </summary>
        /// <param name="OnCaptureEnd">キャプチャ処理完了コールバック。</param>
        protected IEnumerator CaptureRace(Action OnCaptureEnd)
        {
            // カットインの背景に使う今のレース画面のキャプチャを行う。
            RaceCameraManager.Instance.Capture();

            // レースの進行は止める。
            CacheRaceTime();
            // UIの状態キャッシュと非表示。
            CacheUIStatus();

            // Capture ~ EnableCutInCameraの間１フレ待つ必要があるらしい。
            yield return null;

            //レース中に表示しているものを全て非表示にする
            VisibleRace(false);

            RaceCameraManager.Instance.EnableCutInCamera(true);

            //CacheRaceTime内でエフェクトUVスクロールを止めているが
            //全体設定で、CutIn再生中にも止まってしまうのでここで戻す
            //レース中スキルエフェクトも動いてしまうが、全体再生時間などは動かないのでエフェクトが終了する事はない
            GraphicSettings.SetEffectTimeScale(1.0f);

            // CutInHelperの再生初期化。
            var cutInName = ResourcePath.GetSSRareSkillActivateCutInName();
            SetupCutInHelper(cutInName);

            OnCaptureEnd?.Invoke();
        }

        /// <summary>
        /// キャプチャ処理完了コールバック。派生先で実装。
        /// </summary>
        private void OnCaptureEnd()
        {
            _isCutInReady = true;
            
            // スキルカット用の静止画を撮り終えてから雨雪を停止する(リッチ化のみ機能)
            _raceView?.CourseWeatherEffectController?.DisableEffect();

            // スキル発動Flash再生。
            PlaySkillActivateFlash();
            
            // SSRの場合はカットイン終了後に情報プレートを表示させるため、ポーズしておく。
            _raceMainView.PauseMessagePlate();
        }

        /// <summary>
        /// RaceCutInHelperのカットイン再生初期化。
        /// </summary>
        private void SetupCutInHelper(string cutInName)
        {
            _cutInHelper.SetHorseRaceInfo(_cutInInfo.HorseInfo);

            var cutInPath = ResourcePath.GetSkillCutInTimelineControlPath(cutInName);
            _cutInHelper.Play(cutInPath, _cutInParent);
        }

        private void SetupCutInHelperWithResumeFrame(string cutInName)
        {
            SetupCutInHelper(cutInName);

            // スキルカットインはガチャのカットの途中からの再生になる.
            // カットの設定ファイルに指定があれば途中から再生になる
            _isShort = RaceUtil.CheckShortCutIn(_cutInInfo.SkillId);
            _isCheckedShort = true;
            
            if(_isShort)
            {
                _cutInHelper.TimelineController.UseResumeEndFrame(true);
                if (_cutInHelper.TimelineController.GetActiveWorkSheet()._resumePointFrame > 0)
                {
                    _cutInHelper.TimelineController.SkipRuntime(_cutInHelper.TimelineController.GetActiveWorkSheet()._resumePointFrame);
                }
            }
            else
            {
                // フル尺カットインを再生したスキル追加。
                RaceUtil.SaveLongSkillCutInTodayPlayed(_cutInInfo.SkillId);
            }
        }

        /// <summary>
        /// RaceCutInHelperのカットイン再生後処理。
        /// </summary>
        protected void CleanupCutInHelper()
        {
            _cutInHelper.CleanupPlaying();
        }

        /// <summary>
        /// レースの3D描画物ON/OFF。
        /// </summary>
        /// <param name="isVisible"></param>
        private void VisibleRace(bool isVisible)
        {
            _raceView.SetVisibleHorseAll(isVisible);
            _courseManager.SetVisibleCourse(isVisible);
        }

        /// <summary>
        /// カットイン再生開始時のUI状態をキャッシュ。UIをカットイン用にON/OFFする。
        /// </summary>
        private void CacheUIStatus()
        {
            _isVisibleSkipOnStartCutIn = _raceMainView.IsVisibleSkipButton();
            _isVisibleFastForwardOnStartCutIn = _raceMainView.IsVisibleFastForwardButton();
            _isVisibleCameraOnStartCutIn = _raceMainView.IsVisibleCameraButton();
            _isVisibleMiniMapOnStartCutIn = _raceMainView.IsVisibleMiniMap();
            _isVisibleRankOnStartCutIn = _raceMainView.IsVisibleRank();
            _isVisibleJikkyoOnStartCutIn = _raceMainView.IsVisibleJikkyoWindow();
            _isVisibleSkillInfo = _raceMainView.IsVisibleSkillInfo();
            _isVisibleChallengeMatchPoint = _raceMainView.IsVisibleChallengeMatchPoint();
            _raceMainView.SetVisibleSkipButton(false);
            _raceMainView.SetVisibleFastForwardButton(false);
            _raceMainView.SetVisibleCameraButton(false);
            _raceMainView.SetVisibleMiniMap(false);
            _raceMainView.SetVisibleRank(false);
            _raceMainView.SetVisibleJikkyoWindow(false);
            _raceMainView.SetVisibleSkillInfo(false);
            _raceMainView.SetVisibleTeamStadiumScore(false);
            _raceMainView.SetVisibleTeamStadiumStatusPlate(false);
            _raceMainView.SetVisibleChallengeMatchPoint(false);
            // ポーズボタンはON。
            _raceMainView.SetVisiblePauseButton(true);
        }

        /// <summary>
        /// 再生開始時にキャッシュされたUIの状態を復元。
        /// </summary>
        private void BackUIStatus()
        {
            _raceMainView.SetVisibleSkipButton(_isVisibleSkipOnStartCutIn);
            _raceMainView.SetVisibleFastForwardButton(_isVisibleFastForwardOnStartCutIn);
            _raceMainView.SetVisibleCameraButton(_isVisibleCameraOnStartCutIn);
            _raceMainView.SetVisibleMiniMap(_isVisibleMiniMapOnStartCutIn);
            _raceMainView.SetVisibleRank(_isVisibleRankOnStartCutIn);
            _raceMainView.SetVisibleJikkyoWindow(_isVisibleJikkyoOnStartCutIn);
            _raceMainView.SetVisibleSkillInfo(_isVisibleSkillInfo);
            _raceMainView.SetVisibleChallengeMatchPoint(_isVisibleChallengeMatchPoint);
            
            // これらのUIは内部で必要なものだけ描画ONにするようになっているため、true渡しておけばok。
            _raceMainView.SetVisibleTeamStadiumScore(true);
            _raceMainView.SetVisibleTeamStadiumStatusPlate(true);
        }

        /// <summary>
        /// カットイン再生開始時のレースの時間情報をキャッシュ。カットイン用にレースの時間を止める。
        /// </summary>
        private void CacheRaceTime()
        {
            _cachedTimeScale = _raceManager.MainDeltaTimeScale;
            _raceManager.SetDeltaTimeScale(0);
        }

        /// <summary>
        /// 再生開始時にキャッシュしたレースの時間情報を復元。
        /// </summary>
        private void BackRaceTime()
        {
            _raceManager.SetDeltaTimeScale(_cachedTimeScale);
        }
        
        /// <summary>
        /// スキル発動UI（画面右上に出るプレート）の予約情報があれば表示。
        /// </summary>
        private void PlayMessageUIByReserve()
        {
            _raceManager.PlayReserveMessageUISkill();
            _raceManager.ClearReserveMessageUISkill();
        }

        /// <summary>
        /// スコア獲得UIの予約情報があれば表示。
        /// </summary>
        private void PlayScoreUIByReserve()
        {
            _raceManager.PlayReserveTeamStadiumScore();
            _raceManager.ClearReserveTeamStadiumScore();
        }

        /// <summary>
        /// スキル効果UIの予約があれば表示。
        /// </summary>
        private void PlaySkillInfoUIByReserve()
        {
            _raceManager.PlayReserveSkillInfoUI();
            _raceManager.ClearReserveSkillInfoUI();
        }

        /// <summary>
        /// スキル名Flash再生。
        /// </summary>
        protected void PlaySSRSkillNameFlash(int skillId, int level, RaceManager.CutInCategory category)
        {
            _raceMainView.PlaySSRSkillNameFlash(skillId, level, category);
        }
        
        /// <summary>
        /// スキル名Flash不活化。
        /// </summary>
        protected void StopSkillNameFlash()
        {
            _raceMainView.StopSSRSkillNameFlash();
        }
        
        /// <summary>
        /// スキルカットイン名取得。
        /// </summary>
        protected abstract string GetMainCutInName();

        #region <内部状態>
        /// <summary>
        /// FSM初期化。
        /// </summary>
        private void InitState()
        {
            _fsm.Clear();
            _fsm.Add(
                State.Null,
                null,
                null,
                null,
                null);
            _fsm.Add(
                State.Activate,
                StateActivate_Enter,
                null,
                StateActivate_Update,
                StateActivate_Exit);
            _fsm.Add(
                State.Main,
                StateMain_Enter,
                null,
                StateMain_Update,
                StateMain_Exit);
            _fsm.Add(
                State.End,
                null,
                null,
                null,
                null);
        }

        /// <summary>
        /// 状態：目線カットイン：入場。
        /// </summary>
        private void StateActivate_Enter()
        {
        }
        
        /// <summary>
        /// 状態：目線カットイン：更新。
        /// </summary>
        private void StateActivate_Update(float deltaTime)
        {
            if (_cutInHelper.Status == CutInHelper.CutInStatus.End)
            {
                _fsm.SetState(State.Main);
                return;
            }
        }
        
        /// <summary>
        /// 状態：目線カットイン：退場。
        /// </summary>
        private void StateActivate_Exit()
        {
            StopSkillActivateFlash();
            CleanupCutInHelper();
        }

        /// <summary>
        /// 状態：スキルカットイン：入場。
        /// </summary>
        protected virtual void StateMain_Enter()
        {
            // 実況は停止。
            _jikkyoAccessor.ClearVoice();

            // 表示中の情報プレートOFF。
            _raceMainView.SetVisibleMessagePlate(false);
            // スコア獲得UIのパーティクルがカットインの上に描画されるので表示OFF。
            _raceMainView.SetVisibleTeamStadiumScore(false);
            
            //スキルカットインを再生するときに軽量版であればポストエフェクトOn(標準版だと元々有効なので何もしない)
            var appSaveLoader = SaveDataManager.Instance.SaveLoader;
            if (appSaveLoader.GameQuality == GraphicSettings.GameQuality.Light)
            {
                GraphicSettings.Instance.SetGameQualityImageEffect(true);
            }
            
            // スキルカットイン再生。
            SetupCutInHelperWithResumeFrame(GetMainCutInName());

            // 次回Update時にSE再生させる。
            _isReqPlaySound = true;
        }

        /// <summary>
        /// システムボイス再生。
        /// </summary>
        protected abstract void PlaySystemVoice();

        /// <summary>
        /// カットイン再生開始後の１フレーム目で再生するSE。
        /// </summary>
        protected virtual void PlaySoundOnDelayStart()
        {
            //レース早送りの場合システムボイス再生させない
            if(RaceManager.Instance.IsFastForward)
            {
                return;
            }
            // RaceSkillCutInHelper.Play/TimelineController.SkipRuntimeと同時にSEやシステムボイスを再生すると、
            // 主にTimelineController内でのプレハブのロード・生成やCySpringの初期化に時間がかかり、カットインの進行とSEの進行がズレる（SEが先行する）。
            // そのため、カットインとタイミングを合わせたいSE類はカットイン再生が始まった１フレーム目で再生する。
            
            // システムボイス再生。
            PlaySystemVoice();
        }

        /// <summary>
        /// 状態：スキルカットイン：更新。
        /// </summary>
        protected virtual void StateMain_Update(float deltaTime)
        {
            // カットイン再生開始後１フレーム目に再生するSE類。
            if (_isReqPlaySound)
            {
                PlaySoundOnDelayStart();
                _isReqPlaySound = false;
            }
            
            if (_cutInHelper.Status == CutInHelper.CutInStatus.End)
            {
                _fsm.SetState(State.End);
                return;
            }
        }

        /// <summary>
        /// 状態：スキルカットイン：退場。
        /// </summary>
        private void StateMain_Exit()
        {
            StopSkillNameFlash();
            
            // 情報プレートの更新にポーズがかかっているため、ここで再開。
            _raceMainView.SetVisibleMessagePlate(true);
            _raceMainView.ResumeMessagePlate();
            // スコア獲得UIの表示再開。
            _raceMainView.SetVisibleTeamStadiumScore(true);

            PlayMessageUIByReserve();
            PlayScoreUIByReserve();
            PlaySkillInfoUIByReserve();
            
            // スキルカット用で止めた雨雪の処理を再開する(リッチ化のみ機能)
            _raceView?.CourseWeatherEffectController?.EnableEffect();
            
            //スキルカットインを終わる際に軽量版であればポストエフェクトOFF
            var appSaveLoader = SaveDataManager.Instance.SaveLoader;
            if (appSaveLoader.GameQuality == GraphicSettings.GameQuality.Light)
            {
                GraphicSettings.Instance.SetGameQualityImageEffect(false);
            }

            // レースに戻る時のホワイトイン。
            // ※ここに来る前にカットインデータ側でホワイトアウトされている。
            FadeManager.Instance.FadeIn(Color.white, CUTIN_FADE_SEC);
        }
        #endregion

        /// <summary>
        /// 目線カットインの途中でホワイトアウトさせる。
        /// </summary>
        private void OnStartFadeOut()
        {
            FadeManager.Instance.FadeOut(Color.white, 0);
        }

        #region <Flash>
        /// <summary>
        /// 目線カットインに乗せる通知Flash再生。
        /// </summary>
        private void PlaySkillActivateFlash()
        {
            _raceMainView.PlaySSRSkillActivateFlash(OnStartFadeOut);
        }
        
        /// <summary>
        /// 目線カットインに乗せる通知Flash不活化。
        /// </summary>
        private void StopSkillActivateFlash()
        {
            _raceMainView.StopSSRSkillActivateFlash();
        }
        #endregion
    }
}

