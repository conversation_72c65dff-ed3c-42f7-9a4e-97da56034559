#if CYG_DEBUG
using System.IO;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース初期化データインポーター：jsonファイル入力。
    /// </summary>
    //-------------------------------------------------------------------
    public class LoadRaceInfoImporterJsonFromFile : LoadRaceInfoImporterJsonString
    {
        private readonly string _fileName = string.Empty;

        //---------------------------------------------------------------
        public LoadRaceInfoImporterJsonFromFile( string fileName )
        {
            _fileName = string.IsNullOrEmpty( fileName ) ? LoadRaceInfoExporterJson.LoadRaceInfoPath + LoadRaceInfoExporterJson.LoadRaceInfoFileName : fileName;
        }

        //---------------------------------------------------------------
        public void ImportFromFile()
        {
            string jsonStr = string.Empty;
            using ( var sr = new StreamReader( _fileName/*LoadRaceInfoExporterJson.LoadRaceInfoPath + LoadRaceInfoExporterJson.LoadRaceInfoFileName*/ ) )
            {
                jsonStr = sr.ReadToEnd();
            }

            ImportFromString( jsonStr );
        }
    }


    //-------------------------------------------------------------------
    /// <summary>
    /// レース初期化データインポーター：json文字列入力。
    /// </summary>
    //-------------------------------------------------------------------
    public class LoadRaceInfoImporterJsonString
    {
        //---------------------------------------------------------------
        public void ImportFromString( string json )
        {
            var serializedParam = JsonUtility.FromJson( json, typeof( RaceInitializer.LoadRaceInfo.JsonSerializedParam ) ) as RaceInitializer.LoadRaceInfo.JsonSerializedParam;

            if( serializedParam.race_type == RaceDefine.RaceType.None )
            {
                serializedParam.race_type = RaceDefine.RaceType.Debug;
            }

            var buildParme = new RaceInitializer.LoadRaceInfo.BuildParam( serializedParam );
            ImportedData = new RaceInitializer.LoadRaceInfo( ref buildParme );
        }

        //---------------------------------------------------------------
        public RaceInitializer.LoadRaceInfo ImportedData { get; private set; }
    }

}
#endif
