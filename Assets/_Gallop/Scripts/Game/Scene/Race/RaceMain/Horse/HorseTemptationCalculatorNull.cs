using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 掛かり機能計算処理Nullオブジェクト。
    /// </summary>
    //-------------------------------------------------------------------
    public class HorseTemptationCalculatorNull : IHorseTemptationCalculator
    {
        public void LotTemptationStartEnable() { }
        public HorseTemptationCalculator.TemptationMode Mode { get { return HorseTemptationCalculator.TemptationMode.Null; } }
        public bool IsTemptation { get { return false; } }
        public int TemptationCount { get { return 0; } }
        public bool IsTemptationStartEnable { get { return false; } }
        public void Update( float deltaTime ) { }
        public void SetForceEndTimeBySkill(float time) { }
    }
}
