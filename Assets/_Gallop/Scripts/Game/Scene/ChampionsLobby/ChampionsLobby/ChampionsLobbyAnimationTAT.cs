using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using static Gallop.ChampionsLobbyViewController;

namespace Gallop
{
    /// <summary>
    /// 拠点のアニメーション、TAT用
    /// </summary>
    public class ChampionsLobbyAnimationTAT : MonoBehaviour , IChampionsLobbyAnimation
    {

        private const string DEFAULT_LOOP_LABEL = "tat_loop";

        [System.Serializable]
        public class AnimParam 
        {
            private const float SEC_PER_FRAME = 1f / GameDefine.NORMAL_FRAME_RATE;
            public float Delay => DelayFrame * SEC_PER_FRAME;

            public int Conditional = 0; //条件値
            public int DelayFrame = 0;
            public string In;
            public string Out;
            public bool Loop;
            public bool PlayDefaultLoopInEnd;
            public string CueSheetName;
            public string CueName;
        }

        [System.Serializable]
        public class KeyValuePair : KeyAndValue<InAnim, AnimParam>
        {
            public KeyValuePair(InAnim key, AnimParam value) : base(key, value)
            {
            }
            public override KeyAndValue<InAnim, AnimParam> CreateCopyInstance() { return new KeyValuePair(Key, Value); }
        }

        [System.Serializable]
        public class ParamTable : TableBase<InAnim, AnimParam, KeyValuePair> { }

        [SerializeField] private ParamTable _inTable;

        public int ConditionalValue { get; set; } = 0; //動的に再生を切り分ける場合の条件値

        private Dictionary<InAnim, AnimParam> _inDic;
        private TweenAnimationTimelineComponent[] _components;
        private Tween _tween;
        private TweenAnimationTimelineComponent _in;
        private TweenAnimationTimelineComponent _defaultLoop;

        /// <summary>
        /// 初期化時に一度だけ呼ばれるロード
        /// </summary>
        public void Load(InAnim inAnim)
        {
            _inDic = _inTable.DataDic;
            if (_components == null)
            {
                _components = GetComponentsInChildren<TweenAnimationTimelineComponent>(true);
            }

            _defaultLoop = FindDefaultLoop();
            if (_inDic.TryGetValue(inAnim, out var p))
            {
                _in = FindIn(p);
                _tween = CreateTween(_in, p);
            }
            else if(_inDic.TryGetValue( InAnim.Common, out var common))
            {
                _in = FindIn(common);
                _tween = CreateTween(_in, common);
            }
            if(_tween != null)
            {
                _tween.Pause();
            }
        }

        /// <summary>
        /// 入り
        /// </summary>
        public void PlayIn()
        {
            if(_tween != null)
            {
                _tween.Play();
            }
        }

        /// <summary>
        /// 捌け
        /// </summary>
        public void PlayOut()
        {
            //outの需要が出たら実装、UIとかTweenあたりでPlayIn,Outは対応してるので持ってくる
        }

        private TweenAnimationTimelineComponent FindIn(AnimParam param)
        {
            if (param.Conditional != ConditionalValue)
                return null;    //条件不一致

            TweenAnimationTimelineComponent target = null;
            foreach (var t in _components)
            {
                if (t.TimelineName == param.In)
                {
                    target = t;
                    break;
                }
            }

            return target;
        }

        //デフォルトでループさせるラベルを持つコンポーネントを取得
        private TweenAnimationTimelineComponent FindDefaultLoop()
        {
            TweenAnimationTimelineComponent target = null;
            foreach (var t in _components)
            {
                if (t.TimelineName == DEFAULT_LOOP_LABEL)
                {
                    target = t;
                    break;
                }
            }

            return target;
        }

        private Tween CreateTween(TweenAnimationTimelineComponent target, AnimParam param)
        {
            if (target == null)
                return null;

            target.gameObject.SetActive(false);
            var tween = DOVirtual.DelayedCall(param.Delay, () =>
            {
                if (target == null)
                    return;

                target.gameObject.SetActive(true);
                if (param.Loop)
                {
                    target.PlayLoop();
                }
                else
                {
                    target.Play(() =>
                    {
                        Comp();
                        if(param.PlayDefaultLoopInEnd && _defaultLoop != null)
                        {
                            _defaultLoop.PlayLoop();
                        }
                    });
                }

                //SE指定
                if (string.IsNullOrEmpty(param.CueSheetName) == false &&
                   string.IsNullOrEmpty(param.CueName) == false)
                {
                    AudioManager.Instance.PlaySe(cueName: param.CueName, cueSheet: param.CueSheetName);
                }
            });

            return tween;
        }

        public void Stop()
        {
            Comp();
            if (_in != null)
            {
                _in.Stop(true);
            }
            if (_defaultLoop != null) 
            {
                _defaultLoop.Stop();
            }
        }

        private void Comp()
        {
            if (_tween == null)
                return;

            _tween.Complete();
            _tween = null;
        }
    }
}