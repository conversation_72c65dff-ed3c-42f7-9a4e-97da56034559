using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 予約中レースが存在するダイアログ
    /// </summary>
    public class DialogSingleModeReserveAlertDialog : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        }
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        [SerializeField]
        private DialogSingleModeRaceListItem _raceListItem = null;

        public static void Open(System.Action onClickRaceButton, System.Action onCancelCallBack)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var context = workSingleMode.RaceReserveContext;
            var turn = workSingleMode.GetCurrentTurn();

            // 機能解放確認
            if (context.CanUseMultiReserve())
            {
                // 現在のターンの予約されているProgramId
                context.TryGetReservedProgramId(turn, 0, out var programId);

                Open(turn, programId, onClickRaceButton, onCancelCallBack);
            }
            else
            {
                Open(turn, WorkDataManager.Instance.SingleMode.Character.ReservedRaceProgramId, onClickRaceButton, onCancelCallBack);
            }
        }

        public static void Open(int turn, int programId, System.Action onClickRaceButton,System.Action onCancelCallBack)
        {
            var obj = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_SINGLE_MODE_RESERVE_ALERT));
            var dialog = obj.GetComponent<DialogSingleModeReserveAlertDialog>();
            var dialogData = dialog.CreateDialogData();
            dialogData.RightButtonText = TextId.Race0042.Text();
            dialogData.LeftButtonText = TextId.Common0007.Text();
            dialogData.Title = TextId.SingleMode0410.Text();
            dialogData.EnableOutsideClick = false;

            dialogData.RightButtonCallBack = (_) =>
            {
                onClickRaceButton?.Invoke();
            };

            dialogData.LeftButtonCallBack = (_) =>
            {
                onCancelCallBack?.Invoke();
            };
            DialogManager.PushDialog(dialogData);
            dialog.Setup(turn, programId);

        }

        public void Setup(int turn, int programId)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var character = workSingleMode.Character;
            var turnSetId = workSingleMode.GetTurnSetId();
            var singleModeRaceEntryViewModel = new SingleModeRaceEntryViewModel();
            _raceListItem.Setup(
                singleModeRaceEntryViewModel,
                SingleModeUtils.GetDegreeType(turnSetId, workSingleMode.GetCurrentTurn()),
                character,
                MasterDataManager.Instance.masterSingleModeProgram.Get(programId),
                null,
                turn
            );
        }
    }
}