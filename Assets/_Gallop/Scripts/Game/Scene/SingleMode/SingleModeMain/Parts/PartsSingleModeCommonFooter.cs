using System;
using DG.Tweening;
using Gallop.Tutorial;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成中の共通フッター
    /// </summary>
    public sealed class PartsSingleModeCommonFooter : MonoBehaviour, IAdjustSafeArea
    {
        [SerializeField]
        private RectTransform _contentsRoot = null;

        [SerializeField]
        private GameObject _animationRootObject = null;

        [SerializeField]
        private ButtonCommon _skipButton = null;
        [SerializeField]
        private TextCommon _skipButtonOffText = null;
        [SerializeField]
        private ImageCommon[] _skipButtonIconArray = null;
        [SerializeField]
        private ImageCommon[] _skipButtonArrowBaseArray = null;

        [SerializeField]
        private ButtonCommon _shortCutButton = null;
        
        [SerializeField]
        private ButtonCommon _logButton = null;

        [SerializeField]
        private ButtonCommon _menuButton = null;
        public ButtonCommon MenuButton => _menuButton;

        [SerializeField]
        private Canvas _canvas = null;


        private bool _isIn;
        private Action _onClickSkipButton;
        private Action _onOpenDialog;
        private Action _onDestroyDialog;
        private int _defaultSortOrder;
        
        /// <summary>APIクールタイム中フラグ </summary>
        private bool _shortcutChangeCoolTime;
        /// <summary>リクエスト中のショートカット状態 </summary>
        private bool _shortcutChangeRequest;
        
        /// <summary>
        /// 必要なビュー判定
        /// </summary>
        public static bool IsNeedView(SceneDefine.ViewId viewId)
        {
            switch (viewId)
            {
                case SceneDefine.ViewId.SingleModeMonthStart:
                case SceneDefine.ViewId.SingleModeMain:
                case SceneDefine.ViewId.SingleModeRaceEntry:
                case SceneDefine.ViewId.SingleModeSkillLearning:
                case SceneDefine.ViewId.SingleModeSuccessionCut:
                case SceneDefine.ViewId.SingleModeSuccessionEvent:
                case SceneDefine.ViewId.SingleModeScenarioTeamRaceTop: 
                case SceneDefine.ViewId.SingleModeScenarioTeamRaceDeck: 
                case SceneDefine.ViewId.SingleModeScenarioTeamRaceCharaSelect:
                case SceneDefine.ViewId.SingleModeScenarioFreeShop:
                case SceneDefine.ViewId.Story:
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 生成時処理
        /// </summary>
        public void OnCreate(ResourceManager.ResourceHash resourceHash)
        {
            AdjustSafeArea(UIManager.Instance);

            _skipButton.SetOnClick(OnClickSkip);
            _shortCutButton.SetOnClick(OnClickShortCut);
            _menuButton.SetOnClick(OnClickMenu);
            _logButton.SetOnClick(OnClickLog);

            //クリックのコールバックでボタンの画像が変わるので、プッシュエフェクトはクリックコールバックの後に指定して表示
            _skipButton.SetAutoShowPushEffect(false);

            _defaultSortOrder = _canvas.sortingOrder;
            Setup();
        }

        /// <summary>
        /// セーフエリア対応
        /// </summary>
        public void AdjustSafeArea(UIManager uiManager)
        {
            uiManager.AdjustContentsRootRect(_contentsRoot);
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        public void Setup()
        {
            _onClickSkipButton = null;
            _onOpenDialog = null;
            SetButtonEnable(true);

            // ビューを継続して跨ぐ時に、表示順をビューの上に持ってくる
            transform.SetSiblingIndex(transform.parent.childCount-1);
            
            // ショートカット表示
            UpdateShortCutButton();
            // スキップボタン見た目更新
            UpdateSkipButton();
        }

        /// <summary>
        /// ストーリー用ボタン設定：ショートカット無効、スキップ時の処理設定、ログ押下時の処理設定
        /// </summary>
        public void SetButtonEnableFromStory(Action onClickSkipButton, Action onOpenDialog, Action onDestroyDialog)
        {
            _onClickSkipButton = onClickSkipButton;
            _onOpenDialog = onOpenDialog;
            _onDestroyDialog = onDestroyDialog;
            SetButtonEnableFromStory();
        }

        /// <summary>
        /// ストーリー用ボタン設定：ショートカット無効
        /// </summary>
        public void SetButtonEnableFromStory()
        {
            if (TutorialManager.IsTutorialExecuting())
            {
                // 育成チュートリアル中はすべて無効
                SetButtonEnableTutorial();
            }
            else
            {
                SetButtonEnable(skip: true, shortcut: false, menu: true, log: true);
            }
        }

        /// <summary>
        /// ストーリー終了用ボタン設定：ショートカット、ログ、メニュー無効
        /// </summary>
        public void SetButtonEnableFromStoryEnd()
        {
            if (TutorialManager.IsTutorialExecuting())
            {
                // 育成チュートリアル中はすべて無効
                SetButtonEnableTutorial();
            }
            else
            {
                SetButtonEnable(skip: true, shortcut: false, menu: false, log: false);
            }
        }

        /// <summary>
        /// カット用ボタン設定：ショートカット無効、ログ無効、メニュー無効
        /// </summary>
        public void SetButtonEnableFromCutt()
        {
            if (TutorialManager.IsTutorialExecuting())
            {
                // 育成チュートリアル中はすべて無効
                SetButtonEnableTutorial();
            }
            else
            {
                SetButtonEnable(skip: true, shortcut: false, menu: false, log: false);
            }
        }

        /// <summary>
        /// ギャラリー用ボタン設定：ショートカット無効、メニュー無効
        /// </summary>
        public void SetButtonEnableFromGallery()
        {
            SetButtonEnable(skip: true, shortcut: false, menu: false, log: true);
        }

        /// <summary>
        /// チュートリアル用ボタン設定：すべて無効
        /// </summary>
        private void SetButtonEnableTutorial()
        {
            var skip = false;
#if CYG_DEBUG
            if (DebugPageSingleModeTutorial.CanSkip)
            {
                skip = true;
            }
#endif
            // 育成チュートリアル中はすべて無効
            SetButtonEnable(skip: skip, shortcut: false, menu: false, log: false);
        }
        
        /// <summary>
        /// ボタン系の有効/無効
        /// </summary>
        public void SetButtonEnable(bool enable)
        {
            if (TutorialSingleMode.IsTutorial)
            {
                // 育成チュートリアル中はすべて無効
                SetButtonEnableTutorial();
            }
            else
            {
                SetButtonEnable(skip: enable, shortcut: enable, menu: enable, log: enable);
            }
        }
        
        /// <summary>
        /// ボタンの有効/無効
        /// </summary>
        public void SetButtonEnable(bool skip, bool shortcut, bool menu, bool log)
        {
            _skipButton.interactable = skip;
            _shortCutButton.interactable = shortcut;
            _menuButton.interactable = menu;
            _logButton.interactable = log;
        }

        /// <summary>
        /// UIの描画順を設定する
        /// </summary>
        public void SetSortingOrder(int sortingOrder)
        {
            _canvas.sortingOrder = sortingOrder;
        }

        /// <summary>
        /// 現在のUIの描画順を取得する
        /// </summary>
        /// <returns></returns>
        public int GetSortingOrder()
        {
            return _canvas.sortingOrder;
        }

        /// <summary>
        /// UIの描画順を初期化にする
        /// </summary>
        public void ResetSortingOrder()
        {
            _canvas.sortingOrder = _defaultSortOrder;
        }

        /// <summary>
        /// スキップボタンの見た目更新
        /// </summary>
        public void UpdateSkipButton()
        {
            SetSkipButton(StoryManager.GetSavedHighSpeedSetting());
        }

        public void UpdateShortCutButton()
        {
            if (WorkDataManager.HasInstance() == false ||
                WorkDataManager.Instance.SingleMode.Character == null || !WorkDataManager.Instance.SingleMode.IsPlaying) return;
            var isShortcut = WorkDataManager.Instance.SingleMode.Character.EventShortcutType != SingleModeDefine.EventShortCutType.None;
            SetSizeSButtonSprite(_shortCutButton , isShortcut);
        }

        /// <summary>
        /// メニューボタン
        /// </summary>
        public void OnClickMenu()
        {
            DialogSingleModeTopMenu.Open(_onDestroyDialog);
            _onOpenDialog?.Invoke();
        }

        /// <summary>
        /// ショートカットボタン
        /// </summary>
        private void OnClickShortCut()
        {
            // ショートカット設定ダイアログ
            DialogSingleModeShortCutSetting.PushDialog(() =>
            {
                UpdateShortCutButton();
            }, false, false);
        }

        /// <summary>
        /// ログボタン
        /// </summary>
        private void OnClickLog()
        {
            if (WorkDataManager.HasInstance() &&
                WorkDataManager.Instance.SingleMode.IsPlaying)
            {
                // シングルモード中はグローバルログ
                SingleModeLogDialog.PushDialog(_onDestroyDialog);
            }
            else
            {
                // デバッグ起動用にストーリーログに対応
                StoryLogDialog.PushDialog(
                    StoryManager.Instance.AllLogList,
                    StoryTimelineController.DisplayMode.SingleMode,
                    onDestroy: _onDestroyDialog);
            }
            _onOpenDialog?.Invoke();
        }

        /// <summary>
        /// スキップボタン
        /// </summary>
        private void OnClickSkip()
        {
            if (_onClickSkipButton != null)
            {
                // ストーリーなどでオート高速切り替え処理を上書き
                _onClickSkipButton.Invoke();
            }
            else
            {
                StoryManager.ChangeSavedAutoHighSpeedSetting();
            }
            
            SetSkipButton(StoryManager.GetSavedHighSpeedSetting());
            
            //画像を変え終わったのでプッシュエフェクトを表示
            _skipButton.ShowPushEffect();
        }
        
        /// <summary>
        /// スキップボタンの見た目設定
        /// </summary>
        private void SetSkipButton(StoryTimelineController.HighSpeedType highSpeedMode)
        {
            // ボタンの画像変更
            bool isAuto = highSpeedMode > StoryTimelineController.HighSpeedType.None;
            SetSizeSButtonSprite(_skipButton, isAuto);
            
            //テキストのセット
            _skipButtonOffText.SetActiveWithCheck(!isAuto);
            // 矢印
            int index = (int) highSpeedMode;
            for(int i = 0 ; i < _skipButtonIconArray.Length; ++i)
            {
                _skipButtonIconArray[i].SetActiveWithCheck(isAuto && i < index);
            }
            // 矢印下地（Autoなら表示)
            for(int i = 0 ; i < _skipButtonArrowBaseArray.Length; ++i)
            {
                _skipButtonArrowBaseArray[i].SetActiveWithCheck(isAuto);
            }
        }

        /// <summary>
        /// ButtonSの白/緑スプライト設定
        /// </summary>
        private void SetSizeSButtonSprite(ButtonCommon button , bool isOn)
        {
            string spriteName = isOn ? AtlasSpritePath.PreIn.BUTTON_S_01 : AtlasSpritePath.PreIn.BUTTON_S_00;
            var sprite = UIManager.PreInAtlas.GetSprite(spriteName);
            button.SetSprite(sprite);
            if (button.TargetText != null)
            {
                button.TargetText.FontColor = isOn ? FontColorType.White : FontColorType.Brown;
                button.TargetText.UpdateColor();
            }
        }

        /// <summary>
        /// IN再生
        /// </summary>
        public void PlayIn()
        {
            if (_isIn) return;
            _isIn = true;
            TweenAnimationBuilder.CreateSequence(_animationRootObject, TweenAnimation.PresetType.BottomRightPartsIn);
        }

        /// <summary>
        /// OUT再生
        /// </summary>
        public void PlayOut()
        {
            _isIn = false;
            TweenAnimationBuilder.CreateSequence(_animationRootObject, TweenAnimation.PresetType.BottomRightPartsOut);
        }

        /// <summary>
        /// 会話シーンがセットしたDelegate参照をクリア
        /// </summary>
        public void ResetDelegateFromStory()
        {
            _onClickSkipButton = null;
            _onOpenDialog = null;
            _onDestroyDialog = null;
        }
    }
}
