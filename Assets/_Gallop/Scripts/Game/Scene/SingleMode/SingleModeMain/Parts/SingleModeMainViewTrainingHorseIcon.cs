using DG.Tweening;
using UnityEngine;
using System.Collections.Generic;
using AnimateToUnity;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 併せ馬アイコン
    /// </summary>
    public sealed class SingleModeMainViewTrainingHorseIcon : MonoBehaviour
    {

        private const int KNACK_ICON_LOWER_OFFSET = 200;
        private const int KNACK_ICON_UPPER_OFFSET = 3000;
        private const int AOHARU_SOUL_ICON_OFFSET = 200;

        /// <summary>
        /// ゲージ色
        /// </summary>
        private class GaugeColor
        {
            public int Evaluation; // 評価
            public Color32 Color;// 色

            public GaugeColor(int evaluation, Color32 color)
            {
                Evaluation = evaluation;
                Color = color;
            }
        }

        /// <summary>
        /// 評価値別ゲージ色設定リスト
        /// </summary>
        private static readonly List<GaugeColor> GAUGE_COLOR_LIST = new List<GaugeColor>
        {
            new  GaugeColor(0,  new Color32(0x2a,0xc0,0xff,0xff)),
            new  GaugeColor(60, new Color32(0xa2,0xe6,0x1e,0xff)),
            new  GaugeColor(80, new Color32(0xff,0xad,0x1e,0xff)),
            new  GaugeColor(100,new Color32(0xff,0xeb,0x78,0xff)),
        };
        
        [SerializeField]
        private ButtonCommon _myButton;

        public ButtonCommon MyButton => _myButton;
        
        private FlashPlayer _flashPlayer;
        private FlashPlayer _badgeFlashPlayer;
        private FlashPlayer _guideIconFlash;

        private FlashActionPlayer _aoharuSoulBadgeFlash;

        private int _evaluationValue;
        public int EvaluationValue => _evaluationValue;

        private int _charaId;
        public int CharaId => _charaId;

        private int _positionId;
        public int PositionId => _positionId;
        private WorkSupportCardData.SupportCardData _workSupportCardData;
        private WorkSingleModeData.TrainingHorse _trainingHorse;
        private bool _isSystemCharacter;
        private int _supportCardId;
        private WorkSingleModeScenarioTeamRace.TeamMember _workTeamMember; // チーム対抗戦：サポートウマ娘ステータス（野良の場合もWork化して詳細表示に使用)
        private bool _isGuest; // チーム対抗戦：野良ウマ娘か
        private SingleModeScenarioTeamRaceDefine.InterestState _interestState = SingleModeScenarioTeamRaceDefine.InterestState.Guest; // チーム対抗戦：興味状態
        private int _soulThresholdId; //チーム対抗戦:アオハル魂爆発期待度


        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_PARTNER_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_BADGE_KNACK_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_OBTAIN_ICON_SPEED);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_OBTAIN_ICON_STAMINA);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_OBTAIN_ICON_POWER);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_OBTAIN_ICON_GUTS);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_OBTAIN_ICON_WIZ);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_OBTAIN_ICON_FRIEND);

            switch(WorkDataManager.Instance.SingleMode.GetScenarioId())
            {
                case SingleModeDefine.ScenarioId.TeamRace:
                    register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TEAMRACE_PARTNER_FLASH_PATH);
                    register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TEAMRACE_BADGE_SOUL00);
                    break;
            }
        }
        
        /// <summary>
        /// 併せ馬アイコン準備
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="trainingHorse"></param>
        public void Setup( int charaId, WorkSingleModeData.TrainingHorse trainingHorse)
        {
            _trainingHorse = trainingHorse;
            _workSupportCardData = null;
            _workTeamMember = null;
            _supportCardId = 0;
            _charaId = charaId;
            _positionId = trainingHorse.PositionId;
            _isSystemCharacter = SingleModeUtils.IsUniqCharaPosition(_positionId);
            _isGuest = trainingHorse.IsGuest;
            
            var evaluation = trainingHorse.GetEvaluation();
            if(evaluation != null)
            {
                _evaluationValue = evaluation.Value;
                _soulThresholdId = evaluation.SoulThresholdId;
                _interestState = evaluation.InterestState;
            }

            var isTeamRaceScenario = WorkDataManager.Instance.SingleMode.GetScenarioId() == SingleModeDefine.ScenarioId.TeamRace; // チーム対抗戦か
            if (_isSystemCharacter == false)
            {
                // 装備サポカ
                var equipSupportCard = WorkDataManager.Instance.SingleMode.Character.GetEquipSupportCard(_positionId);
                if (equipSupportCard != null)
                {
                    _supportCardId = equipSupportCard.SupportCardId;
                    _workSupportCardData = new WorkSupportCardData.SupportCardData(equipSupportCard.SupportCardId);
                    var work = WorkDataManager.Instance.SupportCardData.GetSupportCardData(equipSupportCard.SupportCardId);
                    // ロック状態は最新Workを反映
                    _workSupportCardData.IsFavoriteLock = work != null ? (bool)work.IsFavoriteLock : false;
                    // 育成開始時点のレベルと上限突破
                    _workSupportCardData.SetExp(equipSupportCard.Exp);
                    _workSupportCardData.SetLimitBreak(equipSupportCard.LimitBreakCount);
                }

                // チーム対抗戦
                if(isTeamRaceScenario)
                {
                    // メンバー
                    _workTeamMember = SingleModeScenarioTeamRaceUtils.GetTeamMemberByCharaId(charaId, false);
                    if (_workTeamMember == null)
                    {
                        // 野良ウマ娘
                        _workTeamMember = WorkSingleModeScenarioTeamRace.TeamMember.CreateDefaultStatusMember(charaId);
                    }
                    if (_workTeamMember != null)
                    {
                        _supportCardId = _workTeamMember.SupportCardId;
                    }
                    // たずななどメンバーにならないキャラは_workTeamMemberがnullになります

                    // 装備していない（野良ウマ娘）は必ずメンバー化できる設定にするはずだが_workTeamMemberがnullだとCSVの設定がおかしいのでエラーログ出す
                    if (_workSupportCardData == null && _workTeamMember == null)
                    {
                        Debug.LogError($"野良ウマ娘のスカウトステータスが設定されていません。charaId[{charaId}]。single_mode_scout_chara.csv");
                        gameObject.SetActive(false);
                        return;
                    }
                }
            }

            if (_flashPlayer == null)
            {
                var flashPath = isTeamRaceScenario ? ResourcePath.SINGLE_MODE_TEAMRACE_PARTNER_FLASH_PATH : ResourcePath.SINGLE_MODE_PARTNER_FLASH_PATH;
                _flashPlayer = FlashLoader.LoadOnView(flashPath, transform);
                _flashPlayer.Init();
            }

            var badgeRootObj = _flashPlayer.GetObj("OBJ_loc_badge_knack00");
            if(_badgeFlashPlayer == null)
            {
                CreateBadgeFlash(out _badgeFlashPlayer, ResourcePath.SINGLE_MODE_BADGE_KNACK_PATH);
            }
            // チーム対抗戦：指導イベントバッジ
            if(isTeamRaceScenario &&　_guideIconFlash == null)
            {
                CreateBadgeFlash(out _guideIconFlash, ResourcePath.SINGLE_MODE_SCENARIO_TEAM_RACE_GUIDE_BADGE_PATH);
            }

            // local func : バッジA2U生成
            void CreateBadgeFlash(out FlashPlayer player, string path)
            {
                player = FlashLoader.LoadOnView(path, badgeRootObj.Transform);
                player.SortOffset = KNACK_ICON_UPPER_OFFSET;
                player.Init();
            }


            SetIcon(charaId);

            SetObtainIcon();

            // 評価ゲージ
            SetEvaluationGauge(_isGuest);

            // ヒントバッジ、指導バッジ、吹き出し
            SetBadgeBalloon(trainingHorse);

            _myButton.SetOnClick(()=> OnClick(_positionId));
        }

        /// <summary>
        /// バッジなどの描画オーダーを調整
        /// 通常時はやる気アイコンより上に表示したいがワイプ表示時にはオフセットを下げることでワイプを貫通しないようにする
        /// </summary>
        /// <param name="isUpper"></param>
        public void SetBadgeSortOffset(bool isUpper)
        {
            var offset = isUpper ? KNACK_ICON_UPPER_OFFSET : KNACK_ICON_LOWER_OFFSET;
            if(_badgeFlashPlayer != null)
            {
                _badgeFlashPlayer.SortOffset = offset;
            }
            if(_guideIconFlash != null)
            {
                _guideIconFlash.SortOffset = offset;
            }
        }

        /// <summary>
        /// 評価ゲージ設定
        /// </summary>
        private void SetEvaluationGauge(bool isGuest)
        {
            //ゲージ設定
            var progress = _flashPlayer.GetProgressBar("OBJ_prg_gauge_supporter00");
            var trainingMenuObj = _flashPlayer.GetMotion("MOT_mc_btn_trainingmenu00");
            
            if (progress != null)
            {
                progress.SetRange(0, SingleModeDefine.EVALUATION_MAX_VALUE);
                progress.Component.gameObject.SetActive(true); //一旦表示する
                if(isGuest) //ゲストの絆ゲージは非表示にする
                {
                    progress.Component.gameObject.SetActive(false);
                    trainingMenuObj?.SetMotionPlay("in00"); //Maxが残ることがあるので一律で非表示にする
                    return;
                }
            }
            SetGaugeValue(_evaluationValue, false);
            
            //Max
            var isMax = _evaluationValue == SingleModeDefine.EVALUATION_MAX_VALUE;
            var motionName = isMax ? "in_max_end" : "in00";
            trainingMenuObj?.SetMotionPlay(motionName);
            
            // ゲージ色が評価によって変化
            SetGaugeColor(_evaluationValue);
        }

        /// <summary>
        /// ヒントバッジ、指導バッジ、吹き出しの表示設定
        /// </summary>
        private void SetBadgeBalloon(WorkSingleModeData.TrainingHorse trainingHorse)
        {
            const string IN_LABEL = "in";
            const string OFF_LABEL = "in00";
            var isTips = trainingHorse.IsTips;
            var isGuide = trainingHorse.IsGuide;
            var isSoulExplode = trainingHorse.IsSoulExplode;
            
            // ヒントor指導でON
            var obj = _flashPlayer.GetMotion("MOT_mc_ico_knack00");
            bool isRootActive = (isTips || isGuide || isSoulExplode);
            
            // ヒントバッジ
            bool isTipsBadgeActive = (isTips && isGuide == false && isSoulExplode == false); // 指導＞ヒント
            _badgeFlashPlayer.Play(isTipsBadgeActive ? IN_LABEL : OFF_LABEL);
            // 指導バッジ
            if (_guideIconFlash != null)
            {
                _guideIconFlash.Play(isGuide || isSoulExplode ? IN_LABEL : OFF_LABEL);

                if(isSoulExplode)
                {
                    _guideIconFlash.GetMotion("MOT_mc_badge_coaching00").SetMotionPlay("in_coaching00");
                }
                else if(isGuide)
                {
                    _guideIconFlash.GetMotion("MOT_mc_badge_coaching00").SetMotionPlay("in_coaching01");
                }
            }

            //必要であればアオハル魂表示
            SetAoharuSoulBadge(isGuide,isSoulExplode);
            
            obj?.SetMotionPlay(isRootActive ? IN_LABEL : OFF_LABEL);
        }

        /// <summary>
        /// アオハル魂バッジ表示
        /// </summary>
        private void SetAoharuSoulBadge(bool isGuide,bool isSoulExplode)
        {
            if(WorkDataManager.Instance.SingleMode.GetScenarioId() == SingleModeDefine.ScenarioId.TeamRace)
            {
                if(_aoharuSoulBadgeFlash == null)
                {
                    var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_TEAMRACE_BADGE_SOUL00);
                    _aoharuSoulBadgeFlash = Instantiate(prefab,_flashPlayer.GetObj("OBJ_loc_ico_right00").Transform).GetComponent<FlashActionPlayer>();
                }
                var badgeFlash = _aoharuSoulBadgeFlash.LoadFlashPlayer();
                badgeFlash.Play("in00"); //魂爆発すると消えていることがあるのでイリの状態にセットする
                badgeFlash.SortOffset = AOHARU_SOUL_ICON_OFFSET;

                var memberInfo = WorkDataManager.Instance.SingleMode.TeamRace.TeamMemberList.FirstOrDefault(member => member.CharaId == _charaId);
                //チームメンバーならアイコン表示
                if(memberInfo != null)
                {
                    _aoharuSoulBadgeFlash.SetActiveWithCheck(true);
                    //アオハル魂爆発済みか？
                    if(memberInfo.IsAoharuSoulActive())
                    {
                        //右側に配置する
                        _aoharuSoulBadgeFlash.transform.SetParent(_flashPlayer.GetObj("OBJ_loc_ico_right00").Transform);
                        _aoharuSoulBadgeFlash.transform.localPosition = Math.VECTOR3_ZERO;
                        var aoharuSoulMotion = badgeFlash.GetMotion("MOT_mc_badge_soul00");
                        aoharuSoulMotion.SetMotionPlay("s00");
                    }
                    else
                    {
                        //左側に配置する
                        _aoharuSoulBadgeFlash.transform.SetParent(_flashPlayer.GetObj("OBJ_loc_ico_left00").Transform);
                        _aoharuSoulBadgeFlash.transform.localPosition = Math.VECTOR3_ZERO;
                        var aoharuSoulMotion = badgeFlash.GetMotion("MOT_mc_badge_soul00");

                        //覚醒まだなら段階表示
                        if( isSoulExplode && _soulThresholdId == SingleModeScenarioTeamRaceDefine.AOHARU_SOUL_TRETHOLD_ID_MAX )
                        {
                            aoharuSoulMotion.SetMotionPlay("s_max");
                        }
                        else
                        {
                            aoharuSoulMotion.SetMotionPlay(TextUtil.Format("s{0:D2}",_soulThresholdId));
                        }
                    }
                }
                else
                {
                    //チームメンバーじゃない(友人など)ならそもそも表示しない
                    _aoharuSoulBadgeFlash.SetActiveWithCheck(false);
                }
            }
        }

        /// <summary>
        /// 魂爆発演出
        /// </summary>
        /// <param name="onCut2"></param>
        public void PlaySoulExplode(System.Action onCut2)
        {
            var badgeFlash = _aoharuSoulBadgeFlash.LoadFlashPlayer();
            badgeFlash.Play("in_explosion");
            badgeFlash.SetActionCallBack("in_cut2",onCut2,AnimateToUnity.AnMotionActionTypes.Start);
        }

        /// <summary>
        /// 併せウマアイコンの並び値
        /// システムキャラクター＞友人サポートカード＞その他のサポートカード
        /// </summary>
        public int GetIconSortOrder()
        {
            const int SYSTEM_CHARA_SORT_ORDER = SingleModeDefine.EVALUATION_MAX_VALUE + 4;
            const int FRINED_CHARA_SORT_ORDER = SingleModeDefine.EVALUATION_MAX_VALUE + 3;
            const int TEAM_MEMBER_SORT_ORDER = SingleModeDefine.EVALUATION_MAX_VALUE + 2;
            const int SEMI_MEMBER_SORT_ORDER = SingleModeDefine.EVALUATION_MAX_VALUE + 1;
            if (_isSystemCharacter) return SYSTEM_CHARA_SORT_ORDER;
            var supportCard = MasterDataManager.Instance.masterSupportCardData.Get(_supportCardId);
            if (supportCard == null) return 0;
            if (supportCard.IsFriendSupportCard)
            {
                return FRINED_CHARA_SORT_ORDER;
            }
            // チームメンバー＞セミメンバー＞野良
            if (_interestState == SingleModeScenarioTeamRaceDefine.InterestState.TeamMember) return TEAM_MEMBER_SORT_ORDER;
            else if (_interestState == SingleModeScenarioTeamRaceDefine.InterestState.SemiMember) return SEMI_MEMBER_SORT_ORDER;
            return _evaluationValue;
        }

        /// <summary>
        /// ゲージ色設定
        /// </summary>
        private void SetGaugeColor(int evaluation)
        {
            var gaugePlane = _flashPlayer.GetPlane("PLN_clr_gauge_supporter_bar00");
            gaugePlane?.SetBaseColor(GetGaugeColor(evaluation));
        }

        /// <summary>
        /// 評価値に合わせたゲージの色を取得する
        /// </summary>
        /// <param name="evaluation"></param>
        /// <returns></returns>
        public static Color32 GetGaugeColor(int evaluation)
        {
            var gaugeColor = GAUGE_COLOR_LIST[0];
            foreach (var data in GAUGE_COLOR_LIST)
            {
                if (data.Evaluation <= evaluation && gaugeColor.Evaluation < data.Evaluation)
                {
                    gaugeColor = data;
                }
            }
            return gaugeColor.Color;
        }

        /// <summary>
        /// 友情タッグトレーニングのエフェクト準備
        /// </summary>
        public void SetupTagEffect(bool isValid)
        {
            var obj = _flashPlayer.GetMotion("MOT_mc_eff_tag00");
            obj?.SetMotionPlay( isValid ? "in" : "in00");
        }

        /// <summary>
        /// アイコン設定 
        /// </summary>
        private void SetIcon(int charaId)
        {
            var path = ResourcePath.GetCharaTrainingIconPath(charaId);
            var texture = ResourceManager.LoadOnHash<Texture2D>(path, hash: ResourceManager.ResourceHash.SingleModeTrainingResource);
            _flashPlayer.SetTexture("PLN_dum_ico_singlemode_supporter00", texture);
        }

        /// <summary>
        /// 得意アイコン
        /// </summary>
        private void SetObtainIcon()
        {
            var planeObtain = _flashPlayer.GetPlane("PLN_dum_ico_obtain00");
            if (planeObtain == null) return;
            //ユニークキャラ（理事長、記者など）
            if (_isSystemCharacter)
            {
                planeObtain.GameObject.SetActiveWithCheck(false);
                return;
            }
            
            //友人サポカまたはゲストの場合は得意練習を非表示にする
            string path = string.Empty;
            var supportCard = MasterDataManager.Instance.masterSupportCardData.Get(_supportCardId);
            if (supportCard == null || _isGuest)
            {
                planeObtain.GameObject.SetActiveWithCheck(false);
                return;
            }
            if (supportCard.IsFriendSupportCard)
            {
                //友人サポカ
                path = ResourcePath.SINGLE_MODE_OBTAIN_ICON_FRIEND;
            }
            else
            {
                //得意練習
                var cmd = (TrainingDefine.TrainingCommandId) supportCard.CommandId;
                switch (cmd)
                {
                    case TrainingDefine.TrainingCommandId.Turf: path = ResourcePath.SINGLE_MODE_OBTAIN_ICON_SPEED; break;
                    case TrainingDefine.TrainingCommandId.Pool: path = ResourcePath.SINGLE_MODE_OBTAIN_ICON_STAMINA; break;
                    case TrainingDefine.TrainingCommandId.Dirt: path = ResourcePath.SINGLE_MODE_OBTAIN_ICON_POWER; break;
                    case TrainingDefine.TrainingCommandId.Slope: path = ResourcePath.SINGLE_MODE_OBTAIN_ICON_GUTS; break;
                    case TrainingDefine.TrainingCommandId.Study: path = ResourcePath.SINGLE_MODE_OBTAIN_ICON_WIZ; break;
                }
            }

            if (string.IsNullOrEmpty(path) == false)
            {
                planeObtain.GameObject.SetActiveWithCheck(true);
                var texture = ResourceManager.LoadOnHash<Texture2D>(path, hash: ResourceManager.ResourceHash.SingleModeTrainingResource);
                planeObtain.SetOuterTexture(texture, null);
            }
            else
            {
                planeObtain.GameObject.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// ゲージ上昇
        /// </summary>
        public void PlayGaugeUp(TrainingDefine.TrainingResultType resultType, int value, float delay)
        {
            if (delay > 0f)
            {
                DOVirtual.DelayedCall(delay, () => 
                {
                    PlayGaugeUp(value);
                    PlayScenarioContentGaugeUp(resultType);
                } );
                return;
            }

            PlayGaugeUp(value);
            PlayScenarioContentGaugeUp(resultType);
        }

        /// <summary>
        /// ゲージ上昇メイン部
        /// </summary>
        /// <param name="value"></param>
        private void PlayGaugeUp(int value)
        {
            if(_isGuest) return; //ゲストはゲージ非表示なので上昇演出を再生しない
            var isMaxAlready = _evaluationValue == SingleModeDefine.EVALUATION_MAX_VALUE;
            var isMax = value == SingleModeDefine.EVALUATION_MAX_VALUE;
            var motionName = "in";
            if (isMaxAlready)
            {
                motionName = "in_max_end";// すでにMax
            }            
            else if (isMax)
            {
                motionName = "in_max"; // Maxになった
            }            
            
            var obj = _flashPlayer.GetMotion("MOT_mc_btn_trainingmenu00");
            obj?.SetMotionPlay(motionName);
            
            //アーティスト指定のDelayTime
            const float DELAY_TIME = 0.2f;
            DOVirtual.DelayedCall(DELAY_TIME, () =>
            {
                //アニメーション有りで値設定
                SetGaugeValue(value, true);
            } );
            // ゲージ色変化
            DOVirtual.DelayedCall(0.167f, () =>
            {
                SetGaugeColor(value);
            } );
        }

        /// <summary>
        /// ゲージに値を設定する
        /// </summary>
        /// <param name="value">値</param>
        /// <param name="isAnimation">アニメーションする？</param>
        private void SetGaugeValue(int value, bool isAnimation)
        {
            var progress = _flashPlayer.GetProgressBar("OBJ_prg_gauge_supporter00");
            if (progress != null)
            {
                progress.SetValue(value, isAnimation);
            }
        }

        /// <summary>
        /// シナリオ別併せウマアイコンのゲージ上昇時演出
        /// </summary>
        /// <param name="delay"></param>
        private void PlayScenarioContentGaugeUp(TrainingDefine.TrainingResultType resultType)
        {
            switch(WorkDataManager.Instance.SingleMode.GetScenarioId())
            {
                case SingleModeDefine.ScenarioId.TeamRace:
                    PlayAoharuSoulGaugeUp(resultType);
                    break;
            }
        }

        /// <summary>
        /// アオハル魂ゲージ上昇演出
        /// </summary>
        /// <param name="delay"></param>
        private void PlayAoharuSoulGaugeUp(TrainingDefine.TrainingResultType resultType)
        {
            if(resultType != TrainingDefine.TrainingResultType.Success) //成功以外の場合は上昇しない
            {
                return;
            }

            if(_aoharuSoulBadgeFlash != null)
            {
                var memberInfo = WorkDataManager.Instance.SingleMode.TeamRace.TeamMemberList.FirstOrDefault(member => member.CharaId == _charaId);
                
                
                if(!_trainingHorse.IsGuide) return; //特訓発生していなければそのまま

                if(memberInfo != null && !memberInfo.IsAoharuSoulActive()) //メンバーかつアオハル魂爆発済みでない
                {
                    var aoharuSoulMotion = _aoharuSoulBadgeFlash.FlashPlayer.GetMotion("MOT_mc_badge_soul00");
                    //覚醒まだなら段階表示
                    if( _trainingHorse.IsSoulExplode )
                    {
                        aoharuSoulMotion.SetMotionPlay("in_explosion");
                    }
                    else
                    {
                        _aoharuSoulBadgeFlash.FlashPlayer.Play("in");
                        AudioManager.Instance.PlaySe(AudioId.SFX_ADDON01_GAUGE_UP);
                        aoharuSoulMotion.SetMotionPlay(TextUtil.Format("s{0:D2}",Mathf.Min(_soulThresholdId + 1 , SingleModeScenarioTeamRaceDefine.AOHARU_SOUL_TRETHOLD_ID_MAX )));
                    }
                }
            }
        }

        
        /// <summary>
        /// クリック時の処理
        /// </summary>
        private void OnClick(int positionId)
        {
            // システムキャラ（理事長、記者など）
            if (_isSystemCharacter)
            {
                DialogSingleModeEvaluationMessage.Open(_charaId, _evaluationValue);
                return;
            }
            switch (WorkDataManager.Instance.SingleMode.GetScenarioId())
            {
                // チーム対抗戦：サポートウマ娘詳細
                case SingleModeDefine.ScenarioId.TeamRace:
                    if (_workTeamMember != null)
                    {
                        // チーム対抗戦用サポカ詳細
                        DialogSingleModeScenarioTeamRaceSupportCardDetail.PushDialog(_workTeamMember, _isGuest);
                    }
                    else
                    {
                        var equip = WorkDataManager.Instance.SingleMode.Character.GetEquipSupportCard(positionId);
                        // 通常のサポカ詳細（たずな、桐生院などスカウトできないキャラ）
                        DialogSupportCardDetail.Open(_workSupportCardData, isFavoriteButton: !equip.IsFriend);
                    }
                    break;
                
                //通常シナリオ
                default:
                    // システムキャラ以外ならサポカ情報を表示
                    // フレンド枠はお気に入りボタン表示をしない
                    var equipSupportCard = WorkDataManager.Instance.SingleMode.Character.GetEquipSupportCard(positionId);
                    var isViewFavoriteButton = !equipSupportCard.IsFriend;
                    DialogSupportCardDetail.Open(_workSupportCardData, isFavoriteButton: isViewFavoriteButton);
                    break;
            }
        }
        
    }
}
