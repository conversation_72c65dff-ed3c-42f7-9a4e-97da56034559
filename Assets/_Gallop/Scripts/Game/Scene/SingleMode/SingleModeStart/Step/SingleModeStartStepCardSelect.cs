using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System;
using Gallop.Tutorial;

namespace Gallop
{
    /// <summary>
    /// シングル開始画面：育成カード選択
    /// </summary>
    public sealed class SingleModeStartStepCardSelect : SingleModeStartStepBase
    {
        #region Const

        private const int SELECT_NONE = SingleModeStartViewController.SELECT_NONE; //未選択状態
        private const int DEFAULT_MEMBER_CARD = 100101;

        #endregion Const

        #region SerializeField

        /// <summary>
        /// 帯のアニメーションUI
        /// </summary>
        [Header("ヘッダー")]
        [SerializeField]
        private PartsCharacterNamePlateRibbon _ribbon = null;

        /// <summary>
        /// 名前パネル
        /// </summary>
        [Header("ステータス")]
        [SerializeField]
        private PartsCharacterNamePlate _partsNamePanel = null;

        [SerializeField]
        private PartsSingleModeStartParamFrame _paramFrame = null;

        [SerializeField]
        private PartsSingleModeStartProperFrame _properFrame = null;

        /// <summary>
        /// キャラのタッチ判定用
        /// </summary>
        [SerializeField]
        private ButtonCommon _charaTouchButton = null;

        /// <summary>
        /// キャラ一覧
        /// </summary>
        [Header("キャラ一覧")]
        [SerializeField]
        private PartsCardListVertical _cardList = null;

        [SerializeField]
        private TweenAnimationTimelineComponent _tweenTimeline;

        #endregion SerializeField

        #region Member

        private int _prevCardId = GameDefine.INVALID_CARD_ID;

        private SingleModeStartViewController.EntryInfo _entryInfo = null;
        private List<CharacterButtonInfo> _memberCardList { get; set; }

        public override TextId HeaderTextId { get { return TextId.SingleMode0038; } }
        public override DialogTutorialGuide.TutorialGuideId GuideId { get { return DialogTutorialGuide.TutorialGuideId.SingleModeCardSelect; } }

        /// <summary>
        /// リボンアニメ再生が一度目か (true: そう, false: 違う)
        /// </summary>
        private bool IsRibbonAnimFirstCalled { get; set; } = true;

        #endregion Member

        #region Method

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="viewController"></param>
        /// <param name="step"></param>
        public override void Initialize(SingleModeStartViewController viewController, SingleModeStartView.Step step)
        {
            base.Initialize(viewController, step);

            // 詳細ダイアログからノートに遷移して、こちらに戻ってくるための設定
            Func<BackableStateInfo> createBackInfo = () =>
            {
                HomeViewInfo homeViewInfo = null;
                var homeHubView = SceneManager.Instance.GetCurrentViewController() as HomeHubViewController;
                if (homeHubView != null)
                {
                    homeViewInfo = homeHubView.GetViewInfo() as HomeViewInfo;
                }

                return new BackableStateInfo(SceneDefine.ViewId.HomeHub, new HomeHubViewController.HomeHubViewInfo()
                {
                    DefaultViewId = SceneDefine.ViewId.SingleModeStart,
                    HomeViewInfo = homeViewInfo,
                    StartViewInfo = new SingleModeStartViewController.ViewInfo(SingleModeStartView.Step.CardSelect, ViewController.Entry),
                });
            };
            // ↑をキャストしてダイアログを開く動作を追加するためのメソッド
            Action<BackableStateInfo, Action> setActionInBackInfo = (backInfo, action) =>
            {
                var homeInfo = backInfo.TargetViewInfo as HomeHubViewController.HomeHubViewInfo;
                var singleInfo = homeInfo?.GetDefaultViewInfo() as SingleModeStartViewController.ViewInfo;
                if (singleInfo != null)
                {
                    singleInfo.OpenDialogAction = action;
                }
            };

            _memberCardList = WorkDataManager.Instance.CardData.GetCardList()
                .Select(d => new CharacterButtonInfo
                {
                    Id = d.CardId,
                    IdType = CharacterButtonInfo.IdTypeEnum.Card,
                    EnableRarity = true,
                    SortInfoType = CharacterButtonInfo.SortInfo.TalentLevel,
                    Level = d.TalentLevel,
                    OnUpdate = OnUpdateCard,
                    OnLongTap = (_) =>
                    {
                        var backInfo = createBackInfo();
                        setActionInBackInfo(backInfo, () => DialogCharacterCardDetail.Open(d, backableStateInfo: backInfo, useOpenAnimation: false, hasLvUpButton: true));

                        DialogCharacterCardDetail.Open(d, backableStateInfo: backInfo, hasLvUpButton: true); 
                    },
                    ButtonSeType = ButtonCommon.ButtonSeType.Cursor01,
                    EnableStoryEventEpBonusIcon = true, // ストーリーイベント開催期間中なら特効（イベントPt獲得ボーナスアイコン）を表示する
                    EnableScenarioLinkHighlight = true, // シナリオリンクハイライト
                }).ToList();

            //矢印コールバック設定
            for (int i = 0, num = _memberCardList.Count; i < num; i++)
            {
                _memberCardList[i].OnTap = OnTapCharaListButton;
            }

            CreateCharaList();
            
            _partsNamePanel.SetupCreateBackInfoFunc(createBackInfo, setActionInBackInfo);
        }
        
        /// <summary>
        /// View遷移時に毎回呼ばれる関数。State切り替えごとではないので注意
        /// </summary>
        public override void InitializeEachPlayIn(long initializeTimeStamp)
        {
            //リボンはアンロードされるので入るたびにロード
            _ribbon.InitializePlayer(true);
            //ホームなどに遷移した場合は前回選択したカードを初期化
            _prevCardId = GameDefine.INVALID_CARD_ID;
        }
        
        /// <summary>
        /// 画面遷移するたびに行う初期化
        /// </summary>
        public override void EndView()
        {
            if (_ribbon != null)
            {
                _ribbon.Release();
            }
            base.EndView();
        }

        private void OnUpdateCard(CharacterButton button)
        {
            var info = button.Info;
            //覚醒Lvのみ値渡しになっていたため再度カード情報を取得&更新
            //才能開花はCardIdから引っ張っているようなのでそのまま反映されている
            var cardData = WorkDataManager.Instance.CardData.GetCardList().FirstOrDefault(card => card.CardId == info.Id);
            info.Level = cardData.TalentLevel;
            button.Setup(info);
        }

        /// <summary>
        /// メンバーUI初期化
        /// </summary>
        public override void Show()
        {
            base.Show();
            _charaTouchButton.SetOnClick(ViewController.PlayTapMotion);

            var viewOverSafeAreaContentsRoot = ViewController.GetViewBase().OverSafeAreaContentsRoot;
            if (viewOverSafeAreaContentsRoot != null)
            {
                transform.SetParent(viewOverSafeAreaContentsRoot);
                var rectTransform = transform as RectTransform;
                rectTransform.Stretch();
            }

            // HubViewになった関係で作成後に
            // ・覚醒Lvなど表示情報に更新が必要になるケースがある
            // ・シナリオを切り替えた際にシナリオ依存の絞り込み結果の更新が必要になるケースがある
            // などの不都合が生じるためリストUIに再更新をかける
            bool hasFilter = _cardList.CurrentFilterMenuList != null && _cardList.CurrentFilterMenuList.Count > 0;
            if (hasFilter)
            {
                // Note:
                // 画面側の仕様に影響を受け"_cardList.Refresh(true)"だとLoopScrollの更新が不十分となる
                // そのためLoopScrollのSetupメソッドが呼び出されるよう絞り込み処理を再実行する
                _cardList.ExecuteCurrentSortFilter(false);
            }
            else
            {
                _cardList.Refresh();
            }

            _cardList.Scroll.enabled = true;
            var info = GetEntryInfo();
            if (info.CardId == SELECT_NONE)
            {
                info.CardId = FindAvailableMemberCardId();
                if (info.CardId == SELECT_NONE)
                {
                    //一番上にスクロールを合わせる
                    _cardList.Scroll.verticalNormalizedPosition = 1f;
                }
                else
                {
                    _cardList.SelectButton(info.CardId);
                    _cardList.ScrollToCursor(true);
                }
            }

            // キャラ読み込み
            // ダイアログの表示があるなら挨拶をカット
            IsRibbonAnimFirstCalled = true;
            var playVoice = ViewController.OpenDialogAction == null;
            LoadMemberInfo(info, playVoice);

            //カーソルをinfo.CardIdに合わせる
            _cardList.EnableCursor(info.CardId);
            
            // チュートリアル
            if (Tutorial.TutorialManager.IsTutorialExecuting())
            {
                Tutorial.TutorialSingleModeStart.SetGuideOnSingleStartView(this);   
            }

            if (ViewController.OpenDialogAction != null)
            {
                ViewController.OpenDialogAction.Invoke();
                ViewController.OpenDialogAction = null;// 1回実行したら消す
                var vi = ViewController.GetViewInfo() as SingleModeStartViewController.ViewInfo;
                if(vi != null)
                {
                    vi.OpenDialogAction = null;
                }
            }
        }

        public override void Hide(bool force = true)
        {
            base.Hide(force);
            _cardList.Scroll.enabled = false;
            ViewController.StopVoice();
        }

        /// <summary>
        /// MemberInfoからUI構築
        /// </summary>
        /// <param name="entryInfo"></param>
        public void LoadMemberInfo(SingleModeStartViewController.EntryInfo entryInfo, bool playVoice)
        {
            _entryInfo = entryInfo;
            var masterCardData = entryInfo.GetMasterCardData();
            var workCardData = WorkDataManager.Instance.CardData.GetCardData(entryInfo.CardId);
            if (masterCardData == null)
            {
                return;
            }
            
            // チュートリアル中は強制的にボイス再生を止める
            if (TutorialManager.IsTutorialExecuting()) { playVoice = false; }

            // モデル作成
            CreateCardModel(_entryInfo, playVoice);
            // 名前設定
            _partsNamePanel.Setup(workCardData, true);
            // パラメータ表示
            _paramFrame.Setup(workCardData);
            //適性表示
            _properFrame.Setup(workCardData);

            // 帯のアニメーションを再生
            PlayRibbonAnimation(IsRibbonAnimFirstCalled);
            IsRibbonAnimFirstCalled = false;
        }

        /// <summary>
        /// 一番最初に見つかった使用可能なカードIDを返す
        /// </summary>
        /// <returns></returns>
        private int FindAvailableMemberCardId()
        {
            // 先に直前に使用されたカードがあれば優先する
            var lastCardId = SaveDataManager.Instance.SaveLoader.LastSingleModeTrainingCard;
            if (_cardList.CurrentButtonInfoList.Any(m => m.Id == lastCardId))
            {
                return lastCardId;
            }
            
            return _cardList.CurrentButtonInfoList.First().Id;
        }

        /// <summary>
        /// キャラモデル作成
        /// </summary>
        private void CreateCardModel(SingleModeStartViewController.EntryInfo entryInfo, bool playVoice)
        {
            //マスターからのデータ取得
            var masterData = entryInfo.GetMasterCardData();
            if (masterData == null)
                return;

            //モデル 表示
            ViewController.CharaViewer.CreateModel(entryInfo.GetCardDressIdSet(), (model) =>
            {
                if (playVoice)
                {
                    PlaySelectedMotion(model, false);
                }
                else
                {
                    model.PlayMotion(model.IdleMotionSetMaster);
                }

                ViewController.CharaViewer.ChangeCameraPosition(SingleModeStartView.Step.CardSelect);
            });
            _prevCardId = masterData.Id;
        }

        /// <summary>
        /// キャラ選択時ボイス、モーション再生
        /// </summary>
        /// <param name="model"></param>
        private void PlaySelectedMotion(SimpleModelController model, bool force)
        {
            if ((model == null || model.GetCardId() == _prevCardId) && !force) return;
            ViewController.PlaySelectedMotion(model);
        }

        /// <summary>
        /// 帯のアニメーションを再生
        /// </summary>
        private void PlayRibbonAnimation(bool isPlayInView = false)
        {
            if (_entryInfo == null)
                return;

            var dressId = _entryInfo.GetCardDressIdSet()._dressId;
            var masterDressData = MasterDataManager.Instance.masterDressData.Get(dressId);
            if (masterDressData == null)
                return;

            _partsNamePanel.PlayFadeOut();

            //アニメーション中に別のビューに遷移されるとリボンがアンロードされてエラーになるため防ぐ
            UIManager.Instance.LockGameCanvas();
            _ribbon.PlayNext(masterDressData.MainColor, masterDressData.SubColor, null,() => 
            {
                UIManager.Instance.UnlockGameCanvas();
                _partsNamePanel.PlayFadeIn();
            }, isPlayInView);
        }

        /// <summary>
        /// キャラリストのボタンタップ
        /// </summary>
        private void OnTapCharaListButton(CharacterButton button)
        {
            OnTapCharaListButton(button.Info);
        }

        /// <summary>
        /// キャラリストのボタンタップ
        /// </summary>
        /// <param name="buttonInfo"></param>
        private void OnTapCharaListButton(CharacterButtonInfo buttonInfo)
        {
            //既に選択中であれば何もしない
            var entryInfo = GetEntryInfo();
            if (buttonInfo.Id == entryInfo.CardId)
            {
                return;
            }

            entryInfo.CardId = buttonInfo.Id;
            _cardList.SelectButton(buttonInfo.Id);
            if (entryInfo.SuccessionTrainedChara_First != null && entryInfo.SuccessionTrainedChara_First.CharaId == entryInfo.GetCharaId())
            {
                entryInfo.SuccessionTrainedChara_First = null;
            }

            if (entryInfo.SuccessionTrainedChara_Second != null && entryInfo.SuccessionTrainedChara_Second.CharaId == entryInfo.GetCharaId())
            {
                entryInfo.SuccessionTrainedChara_Second = null;
            }
            LoadMemberInfo(entryInfo, true);
        }


        /// <summary>
        /// キャラ一覧開く
        /// </summary>
        private void CreateCharaList()
        {
            // ソート・絞り込みの設定読込み
            var sortFilterSetting = SortFilterSetting.Load(SortFilterSetting.SaveTag.SingleModeStartCard);
            
            void SaveSortFilterSetting(PartsListSortButton sortButton)
            {
                SortFilterSetting.Save(SortFilterSetting.SaveTag.SingleModeStartCard, sortButton.Setting);
            }

            Action<List<CharacterButtonInfo>> additionalSortFunc = null;
            if (TutorialManager.IsTutorialExecuting())
            {
                additionalSortFunc = (list) => {
                    // チュートリアル用処理 : チュートリアルの育成ウマ娘をリストの先頭に設定する
                    if (TutorialManager.IsTutorialExecuting())
                    {
                        for (int i = 0; i < list.Count; ++i)
                        {
                            if (list[i].Id == TutorialSingleMode.TRAINING_CARD_ID)
                            {
                                // 育成キャラをリストの先頭に移動
                                CharacterButtonInfo trainingCardInfo = list[i];
                                list.RemoveAt(i);
                                list.Insert(0, trainingCardInfo);
                                break;
                            }
                        }
                    }
                };
            }

            _cardList.Create(
                _memberCardList,
                sortFilterSetting,
                SaveSortFilterSetting,
                true,
                onResetFilter:SaveSortFilterSetting,
                additionalSort: additionalSortFunc
             );
        }

        /// <summary>
        /// カードIDからカードリスト内インデックス値に変換する
        /// ** 現状カードを重複して持てないから成立してる、重複するようになるなら対応必須
        /// </summary>
        /// <param name="cardId"></param>
        private int GetCardListIndex(int cardId)
        {
            return _memberCardList.FindIndex(d => d.Id == cardId);
        }

        public override void OnClickNextButton()
        {
            ViewController.SetStep(SingleModeStartView.Step.SuccessionSelect);
        }

        public override void OnClickBackButton()
        {
            ViewController.SetStep(SingleModeStartView.Step.RouteSelect);
            //シナリオ選択に戻った場合も選択したキャラを初期化,キャラ選択に再度進んできたときにボイスを再生できるようにする
            _prevCardId = GameDefine.INVALID_CARD_ID;
        }

        protected override void Release()
        {
            base.Release();
            _entryInfo = null;
            _memberCardList.Clear();
        }

        #region チュートリアル

        /// <summary>
        /// チュートリアルで選択した御三家カードに対応するボタンを取得
        /// </summary>
        /// <returns></returns>
        public CharacterButton GetTutorialChooseCardButton()
        {
            var info = _cardList.CurrentButtonInfoList.Find((buttonInfo) => buttonInfo.CardData.CardId == TutorialSingleMode.TRAINING_CARD_ID);
            var button = _cardList.GetButton(info);
            if (button == null)
            {
                button = _cardList.FindButtonByIndex(0);
            }

            return button;
        }
        
        #endregion

        #endregion Method
    }
}
