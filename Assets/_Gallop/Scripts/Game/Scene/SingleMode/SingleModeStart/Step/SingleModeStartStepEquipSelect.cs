using System.Collections;
using System.Linq;
using Gallop.Tutorial;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// シングル開始画面：サポート選択
    /// </summary>
    public sealed class SingleModeStartStepEquipSelect : SingleModeStartStepBase
    {
        #region SerializeField

        [Header("サポートカード選択")]
        [SerializeField]
        private PartsSupportCardDeck _partsSupportCardDeck = null;
        
        [SerializeField]
        private ButtonCommon _effectListButton = null;
        
        #endregion SerializeField

        #region Member

        /// <summary>
        /// メンバー情報
        /// </summary>
        private SingleModeStartViewController.EntryInfo _entryInfo = null;

        #region チュートリアル

        public PartsSupportCardDeck SupportCardDeck => _partsSupportCardDeck;

        #endregion

        #endregion Member

        #region Override

        public override TextId HeaderTextId => TextId.SingleMode0286;
        public override DialogTutorialGuide.TutorialGuideId GuideId => DialogTutorialGuide.TutorialGuideId.SingleModeSupportEdit;

        private bool _isInit = false;

        /// <summary>
        /// メンバーUI初期化
        /// </summary>
        public override void Show()
        {
            base.Show();

            _entryInfo = GetEntryInfo();

            if (!_isInit)
            {
                //初回のみ初期化。Initializeで行うとEntryInfoがなくてエラーになるので、このタイミングで行う
                _isInit = true;

                // サポートカードデッキ表示初期化
                _partsSupportCardDeck.Initialize(PartsSupportCardDeck.DisplayType.Single, OnUpdateDeckCard, OnUpdateDeckCard, true);

                _effectListButton.SetOnClick(() =>
                {
                    WorkSupportCardData.SupportCardData friendData = null;
                    if (_entryInfo.SelectFriendCardInfo != null)
                    {
                        friendData = new WorkSupportCardData.SupportCardData(_entryInfo.SelectFriendCardInfo.SupportCardId);
                        friendData.SetLevelForce(_entryInfo.SelectFriendCardInfo.SupportCardLevel);
                        friendData.SetLimitBreak(_entryInfo.SelectFriendCardInfo.LimitBreakCount);
                    }

                    // サポートカードデッキ効果一覧を開く
                    var cardId = _entryInfo.CardId;
                    var cardRarity = GameDefine.CardRarity.None;
                    var cardData = WorkDataManager.Instance.CardData.GetCardData(cardId);
                    if (cardData != null)
                    {
                        cardRarity = (GameDefine.CardRarity) cardData.Rarity.GetDecrypted();
                    }
                    
                    DialogSupportCardDeckEffectList.Open(_partsSupportCardDeck.GetCurrentDeckId(), friendData, cardId, cardRarity);
                });
            }

            //キャラモデルを消す
            for (var index = SingleModeStartResultCharaViewer.ModelIndex.Center; index <= SingleModeStartResultCharaViewer.ModelIndex.Right; index++)
            {
                ViewController.CharaViewer.SetModelVisible(false, (int)index);
            }

            // デッキ表示を更新
            _partsSupportCardDeck.SetUpdateData();

            UpdateNextButtonStatus();
            UpdateEffectListButton();

            // 選択中の育成キャラにミニキャラ画像差し替え
            var workCardData = WorkDataManager.Instance.CardData.GetCardData(_entryInfo.CardId);
            SingleModeUtils.SetupButtonCharaPetit(
                workCardData.GetCharaId(),
                workCardData.GetMasterRarityCard().RaceDressId,
                _nextButton,
                ResourcePath.CharaPetitCatetory.SingleStartOff,
                ResourcePath.CharaPetitCatetory.SingleStartOn);


            //サポカ強化画面からの復帰処理
            var viewInfo = ViewController.GetViewInfo() as HomeHubViewController.HomeHubViewInfo;
            if (viewInfo != null)
            {
                var startViewInfo = viewInfo.StartViewInfo as SingleModeStartViewController.ViewInfo;
                if (startViewInfo != null)
                {
                    var resumeInfo = startViewInfo.ResumeInfo;
                    //サポカ選択ダイアログから詳細ダイアログを開いている場合
                    if (resumeInfo.ShowSupportCardSelectDialog)
                    {
                        var tempDeckData = WorkDataManager.Instance.SupportDeckData.GetTempDeckData(_partsSupportCardDeck.GetCurrentDeckId());
                        var equipInfo = new DialogSupportDeckCardSelect.EquipmentInfo
                        {
                            SelectedCharaId = _entryInfo.GetMasterCardData().CharaId,
                            DeckName = tempDeckData.DeckName,
                        };
                        equipInfo.EquippedSupportSerialIdArray = new int[tempDeckData.SupportCardIdArray.Length];
                        for (int i = 0; i < tempDeckData.SupportCardIdArray.Length; ++i)
                        {
                            equipInfo.EquippedSupportSerialIdArray[i] = tempDeckData.SupportCardIdArray[i];
                        }
                        //サポカ選択ダイアログを表示する
                        _partsSupportCardDeck.GetCurrentItem().OpenSupportCardSelectDialog(equipInfo, resumeInfo.SupportCardIndex, true);
                    }
                    ViewController.SetViewInfo(null);
                }
            }

            if (ViewController.OpenDialogAction != null)
            {
                ViewController.OpenDialogAction.Invoke();
                ViewController.OpenDialogAction = null;// 1回実行したら消す

                //ViewInfo側のOpenDialogActionも同様に消す
                //そのままにすると別のビューに遷移して戻ってきたときに ViewInfo に残っていた OpenDialogAction が実行されて問題がある
                var vi = ViewController.GetViewInfo() as SingleModeStartViewController.ViewInfo;
                if (vi != null)
                {
                    vi.OpenDialogAction = null;
                }
            }

            // チュートリアル
            if (TutorialManager.IsTutorialExecuting())
            {
                TutorialSingleModeStart.SetGuideOnSingleStartView(this);
            }

            if (_partsSupportCardDeck != null)
            {
                _partsSupportCardDeck.isAllowMouseGragCpntroll = true;
            }

        }


        public override void Hide(bool force = true)
        {
            if( WorkDataManager.Instance.SupportDeckData.TempDataDic.Any() && 
                SupportCardDeck.DeckList.IsDirty() &&
                !TutorialManager.IsTutorialExecuting())    // チュートリアル中はデッキ保存通信は送らない
            {
                WorkDataManager.Instance.SupportDeckData.SendRequestUpdateSupportCardDeck();
                SupportCardDeck.DeckList.ClearDirtyFlag(); //StartTaskでエラーが起きてホームに戻る際にはHideが2連続で呼ばれる。通信連打エラーを避けるためにフラグはリセットしておく
            }
            //キャラモデルを出す
            for (var index = SingleModeStartResultCharaViewer.ModelIndex.Center; index <= SingleModeStartResultCharaViewer.ModelIndex.Right;index++)
            {
                ViewController.CharaViewer.SetModelVisible(true, (int)index);
            }

            if (_partsSupportCardDeck != null)
            {
                _partsSupportCardDeck.isAllowMouseGragCpntroll = false;
            }

            base.Hide(force);
        }

        public override void OnClickNextButton()
        {
            //　殿堂入りウマ娘上限が上限に達していたら、殿堂入りウマ娘上限ダイアログを出して、殿堂入りウマ娘一覧へ
            // ※ここは保険処理です（現状はシナリオ選択時にチェック、歴代評価点＞シナリオレコード＞育成時にチェックしています）
            if (WorkDataManager.Instance.TrainedCharaData.List.Count >= WorkTrainedCharaData.TrainedCharaNumMax)
            {
                DialogSingleModeRemoveTrainedHorseWarning.PushDialog();
                return;
            }            
            
            // 消費TP量をEntryInfoに登録
            _entryInfo.UseTp = ViewController.SingleModeStartModel.GetSingleModeStartUseTp(_entryInfo.ScenarioId);

            // TPが不足確認
            int remainTpNum = WorkDataManager.Instance.UserData.TrainerPoint.CurrentTp - _entryInfo.UseTp;
            if (remainTpNum >= 0)
            {
                int deckId = _partsSupportCardDeck.GetCurrentDeckId();
                var tempDeckData = WorkDataManager.Instance.SupportDeckData.GetTempDeckData(deckId);
                _entryInfo.SupportSerialIdArray = new int[tempDeckData.SupportCardIdArray.Length];
                for (int i = 0; i < tempDeckData.SupportCardIdArray.Length; ++i)
                {
                    _entryInfo.SupportSerialIdArray[i] = tempDeckData.SupportCardIdArray[i];
                }

                // TP消費キャンペーン有効確認
                var enableUseTpCampaign = ViewController.SingleModeStartModel.IsSingleModeTpCampaign(_entryInfo.ScenarioId);
                var enableFreeRental = ViewController.SingleModeStartModel.IsEnableFreeRental();

                // チャレンジマッチ関連の表示をさせるか
                var enableChallengeMatch = WorkDataManager.Instance.ChallengeMatchData.IsInSession(); // チャレンジマッチが開催期間中ならtrue

                // TP消費を確認してシングルモード開始を開始
                DialogSingleModeStartConfirmEntry.Open(_entryInfo, enableUseTpCampaign, enableFreeRental, enableChallengeMatch: enableChallengeMatch);
            }
            else
            {
                int remainTpNumWhenBoostMode = WorkDataManager.Instance.UserData.TrainerPoint.CurrentTp - _entryInfo.GetUseTpWhenBoostMode();
                ShowTPRecoverDialogForDeckSelect(remainTpNum, remainTpNumWhenBoostMode);
            }
        }

        public override void OnClickBackButton()
        {
            ViewController.SetStep(SingleModeStartView.Step.SuccessionSelect);
        }

        #endregion Override

        /// <summary>
        /// TP回復ダイアログの表示(サポカ編成の育成開始ボタン用)
        /// ※TP30未満の時 & TPブーストのできるイベント期間のみ、テキスト変更
        /// </summary>
        private static void ShowTPRecoverDialogForDeckSelect(int remainTpNum, int remainTpNumWhenBoostMode)
        {
            var needTp = -1 * remainTpNum;
            var message = TextUtil.Format(TextId.SingleMode0153.Text(), needTp);
            if (ServerDefine.SingleModeStartUseTp > WorkDataManager.Instance.UserData.TrainerPoint.CurrentTp)
            {
                // ※TP30未満の時 & TPブーストのできるイベント期間のみ、テキスト変更
                var workStoryEventData = WorkDataManager.Instance.StoryEventData;
                var isOpen = workStoryEventData.IsOpen() && !workStoryEventData.IsReceivingPeriod();
                if (isOpen)
                {
                    var storyEventData = MasterDataManager.Instance.masterStoryEventData.Get(workStoryEventData.StoryEventId);
                    if (storyEventData != null)
                    {
                        // ブースト設定が有効
                        if (storyEventData.EnableTpBoost)
                        {
                            var needTpWhenBoostMode = -1 * remainTpNumWhenBoostMode;
                            message = TextUtil.Format(TextId.SingleMode194007.Text(), needTp, needTpWhenBoostMode);
                        }
                    }
                }
            }
            ShowTPRecoverDialog(message);
        }
        
        /// <summary>
        /// TP回復ダイアログの表示（最終確認ダイアログ用）
        /// </summary>
        public static void ShowTPRecoverDialogForConfirmEntry(int remainTpNum)
        {
            var message = TextUtil.Format(TextId.SingleMode0153.Text(), -1 * remainTpNum);
            ShowTPRecoverDialog(message);
        }
        
        /// <summary>
        /// TP回復ダイアログの表示
        /// </summary>
        private static void ShowTPRecoverDialog(string message)
        {
            var data = new DialogCommon.Data();
            data.AutoClose = false;
            data.SetSimpleTwoButtonMessage(
                TextId.Common0009.Text(),
                message,
                (dialog) =>
                {
                    //ダイアログを閉じる
                    if (dialog != null)
                        dialog.Close();

                    // TP回復ダイアログを表示
                    DialogRecoverTpItemList.Open();
                },
                TextId.Common0002,
                TextId.Outgame0151,
                (dialog) =>
                {
                    //ダイアログを閉じる
                    if (dialog != null)
                    {
                        dialog.Close();
                    }
                }
            );
            DialogManager.PushDialog(data);
        }
        
        /// <summary>
        /// デッキに更新があった場合のコールバック
        /// </summary>
        private void OnUpdateDeckCard()
        {
            UpdateNextButtonStatus();
            UpdateEffectListButton();
        }

        /// <summary>
        /// 効果一覧ボタンの更新、デッキが空でなければ有効
        /// </summary>
        private void UpdateEffectListButton()
        {
            var isEmpty = WorkDataManager.Instance.SupportDeckData.IsEmptyTempDeck(_partsSupportCardDeck.GetCurrentDeckId());
            _effectListButton.interactable = !isEmpty || _entryInfo.SelectFriendCardInfo != null;
        }


        /// <summary>
        /// サポートカードの選択状態に応じて次へボタンの状態を更新する
        /// </summary>
        public void UpdateNextButtonStatus()
        {
            if(_entryInfo == null)
            {
                return;
            }

            int deckId = _partsSupportCardDeck.GetCurrentDeckId();
            var tempDeckData = WorkDataManager.Instance.SupportDeckData.GetTempDeckData(deckId);
            var isSetAllSupportCard = tempDeckData.SupportCardIdArray.All(id => id > 0);

            //編成しているキャラとフレンドのサポカが被っていれば進行できない。
            bool isConflictSupportChara = false;
            bool isSameSupportChara = false;
            bool isSetFriendSupport = _entryInfo.SelectFriendCardInfo != null;
            if (isSetFriendSupport)
            {
                int friendSupportCardId = _entryInfo.SelectFriendCardInfo.SupportCardId;
                MasterSupportCardData.SupportCardData friendSupportCardData = MasterDataManager.Instance.masterSupportCardData.Get(friendSupportCardId);
                if (friendSupportCardData != null)
                {
                    for (int i = 0; i < tempDeckData.SupportCardIdArray.Length; ++i)
                    {
                        int serialId = tempDeckData.SupportCardIdArray[i];
                        WorkSupportCardData.SupportCardData supportData = WorkDataManager.Instance.SupportCardData.GetSupportCardData(serialId);
                        if (supportData == null)
                        {
                            continue;
                        }
                        if (supportData.GetCharaId() == friendSupportCardData.CharaId)
                        {
                            isConflictSupportChara = true;
                            break;
                        }
                    }
                    if(friendSupportCardData.CharaId == _entryInfo.GetCharaId())
                    {
                        isSameSupportChara = true;
                    }
                }
            }

            var supportCardDataList = tempDeckData.SupportCardIdArray.Select(id => WorkDataManager.Instance.SupportCardData.GetSupportCardData(id)).Where(data => data != null);
            foreach (var data in supportCardDataList)
            {
                if (data.GetCharaId() == _entryInfo.GetCharaId())
                {
                    isSameSupportChara = true;
                    break;
                }

                if(supportCardDataList.Count(d => d.GetCharaId() == data.GetCharaId()) >= 2) //サポート重複判定
                {
                    isConflictSupportChara = true;
                    break;
                }
            }

            string disableText = string.Empty;
            if (!isSetAllSupportCard)
            {
                disableText = TextId.SingleMode0198.Text();
            }
            else if (!isSetFriendSupport)
            {
                disableText = TextId.SingleMode0304.Text();
            }
            else if (isSameSupportChara)
            {
                disableText = TextId.SingleMode0367.Text(); 
            }
            else if (isConflictSupportChara)
            {
                disableText = TextId.SingleMode0199.Text();
            }

            UpdateNextButton(isSetAllSupportCard && isSetFriendSupport && !isSameSupportChara && !isConflictSupportChara, disableText);
        }
        protected override void Release()
        {
            base.Release();
            _entryInfo = null;
            _partsSupportCardDeck.Release();
        }
    }
}