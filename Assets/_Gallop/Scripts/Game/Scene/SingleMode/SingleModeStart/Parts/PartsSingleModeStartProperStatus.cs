using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 適性表示
    /// </summary>
    public sealed class PartsSingleModeStartProperStatus : UIBehaviour
    {
        [Header("馬場")]
        [SerializeField]
        private ImageCommon _properTurf;
        [SerializeField]
        private TextCommon _properTurfText;
        [SerializeField]
        private ImageCommon _properDirt;
        [SerializeField]
        private TextCommon _properDirtText;

        [Header("距離")]
        [SerializeField]
        private RectTransform _distanceRoot;
        [SerializeField]
        private ImageCommon _properShort;
        [SerializeField]
        private TextCommon _properShortText;
        [SerializeField]
        private ImageCommon _properMile;
        [SerializeField]
        private TextCommon _properMileText;
        [SerializeField]
        private ImageCommon _properMiddle;
        [SerializeField]
        private TextCommon _properMiddleText;
        [SerializeField]
        private ImageCommon _properLong;
        [SerializeField]
        private TextCommon _properLongText;
        
        [Header("走法")]
        [SerializeField]
        private RectTransform _styleRoot;
        [SerializeField]
        private ImageCommon _properNige;
        [SerializeField]
        private ImageCommon _properSenko;
        [SerializeField]
        private ImageCommon _properSashi;
        [SerializeField]
        private ImageCommon _properOikomi;


        public void Setup(WorkCardData.CardData data)
        {
            _properTurf.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperGroundTurf());
            _properDirt.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperGroundDirt());
            
            _properShort.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperDistanceShort());
            _properMile.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperDistanceMile());
            _properMiddle.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperDistanceMiddle());
            _properLong.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperDistanceLong());

            _properNige.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperRunningStyleNige());
            _properSenko.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperRunningStyleSenko());
            _properSashi.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperRunningStyleSashi());
            _properOikomi.sprite = SingleModeDefine.GetProperGradeSprite(data.GetProperRunningStyleOikomi());
        }
        
        /// <summary>
        /// 育成データから適正登録
        /// </summary>
        /// <param name="trainedData"></param>
        /// <param name="dispType"></param>
        /// <param name="courseInfo"></param>
        public void Setup(WorkTrainedCharaData.TrainedCharaData trainedData, DispType dispType, MasterRaceCourseSet.IRaceCourseInfo courseInfo = null)
        {
            _properTurf.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperGroundTurf);
            _properDirt.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperGroundDirt);
            
            _properShort.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperDistanceShort);
            _properMile.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperDistanceMile);
            _properMiddle.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperDistanceMiddle);
            _properLong.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperDistanceLong);

            _properNige.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperRunningStyleNige);
            _properSenko.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperRunningStyleSenko);
            _properSashi.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperRunningStyleSashi);
            _properOikomi.sprite = SingleModeDefine.GetProperGradeSprite(trainedData.ProperRunningStyleOikomi);
            if (courseInfo != null)
            {
                SetInteractable(courseInfo.GroundType == RaceDefine.GroundType.Turf, _properTurf,_properTurfText);
                SetInteractable(courseInfo.GroundType == RaceDefine.GroundType.Dirt, _properDirt,_properDirtText);

                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Short, _properShort,_properShortText);
                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Mile, _properMile,_properMileText);
                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Middle, _properMiddle,_properMiddleText);
                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Long, _properLong,_properLongText);
            
                //コース指定がある場合のみdispTypeによる見た目の変更を行う
                if(dispType == DispType.Target)
                {
                    SetupDispTypeTarget(courseInfo);
                }
            }
        }
        
        /// <summary>
        /// キャラデータから適正登録
        /// </summary>
        /// <param name="charaData"></param>
        public void Setup(WorkSingleModeCharaData charaData)
        {
            _properTurf.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperGroundTurf);
            _properDirt.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperGroundDirt);
            
            _properShort.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperDistanceShort);
            _properMile.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperDistanceMile);
            _properMiddle.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperDistanceMiddle);
            _properLong.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperDistanceLong);

            _properNige.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperRunningStyleNige);
            _properSenko.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperRunningStyleSenko);
            _properSashi.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperRunningStyleSashi);
            _properOikomi.sprite = SingleModeDefine.GetProperGradeSprite(charaData.ProperRunningStyleOikomi);
        }

        /// <summary>
        /// 育成：チーム対抗戦メンバーから適正登録
        /// </summary>
        /// <param name="charaData"></param>
        public void Setup(WorkSingleModeScenarioTeamRace.TeamMember teamMember, MasterRaceCourseSet.IRaceCourseInfo courseInfo)
        {
            if(teamMember.MasterScoutChara != null)
            {
                _properTurf.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperGroundTurf);
                _properDirt.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperGroundDirt);

                _properShort.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperDistanceShort);
                _properMile.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperDistanceMile);
                _properMiddle.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperDistanceMiddle);
                _properLong.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperDistanceLong);

                _properNige.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperRunningStyleNige);
                _properSenko.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperRunningStyleSenko);
                _properSashi.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperRunningStyleSashi);
                _properOikomi.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)teamMember.MasterScoutChara.ProperRunningStyleOikomi);
            }
            else
            {
                if(SingleModeScenarioTeamRaceUtils.IsSingleModeCharaByCharaId(teamMember.CharaId))
                {
                    Setup(WorkDataManager.Instance.SingleMode.Character);
                }
            }

            if(courseInfo != null)
            {
                SetInteractable(courseInfo.GroundType == RaceDefine.GroundType.Turf, _properTurf, _properTurfText);
                SetInteractable(courseInfo.GroundType == RaceDefine.GroundType.Dirt, _properDirt, _properDirtText);

                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Short, _properShort, _properShortText);
                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Mile, _properMile, _properMileText);
                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Middle, _properMiddle, _properMiddleText);
                SetInteractable(courseInfo.DistanceType == RaceDefine.CourseDistanceType.Long, _properLong, _properLongText);
            }
        }

        /// <summary>
        /// interactableの設定
        /// </summary>
        /// <param name="interactable"></param>
        public void SetInteractable(bool interactable,ImageCommon gradeImage,TextCommon properText)
        {
            properText.SetMulColor(interactable ? ButtonCommon.DEFAULT_COLOR_WHITE : ButtonCommon.NO_INTERACTERABLE_COLOR);
            gradeImage.SetMulColor(interactable ? ButtonCommon.DEFAULT_COLOR_WHITE : ButtonCommon.NO_INTERACTERABLE_COLOR);
        }

        #region 対象適性のみ表示

        /// <summary>
        /// 表示モード
        /// </summary>
        public enum DispType
        {
            /// <summary>
            /// 対象レースの適性のみ表示
            /// </summary>
            Target,
            /// <summary>
            /// 全部表示
            /// </summary>
            All,
        }

        //---Targetの場合に適性表示を下記サイズ・位置に補正する
        //All→TargetはできるがTarget→Allには対応してない
        private static Vector2 SIZE_TARGET_ONLY = new Vector2(516, 42);
        private static Vector2 TEXT_POS_TARGET_ONLY = new Vector2(-28, -1);
        private static Vector2 PROPER_POS_TARGET_ONLY = new Vector2(53, -1);
        private static Vector2 STYLE_POS_TARGET_ONLY = new Vector2(21, -190);

        //テキストと適性イメージを対にしたオブジェクト
        private class ProperObj
        {
            public TextCommon Text;
            public ImageCommon Proper;
            public ProperObj(TextCommon text, ImageCommon proper) { Text = text; Proper = proper; }

            //DispType=Targetの時の位置に補正
            public void SetTargetPos()
            {
                if (Text != null)
                    Text.rectTransform.anchoredPosition = TEXT_POS_TARGET_ONLY;
                if (Proper != null)
                    Proper.rectTransform.anchoredPosition = PROPER_POS_TARGET_ONLY;
            }

            //表示非表示
            public void SetActive(bool active)
            {
                if (Text != null)
                    Text.SetActiveWithCheck(active);
                if (Proper != null)
                    Proper.SetActiveWithCheck(active);
            }
        }

        //種別ごとの定義値とProperObjを紐づけた辞書
        //box化対策のためkeyはintにしてある
        //https://docs.unity3d.com/ja/2019.4/Manual/BestPracticeUnderstandingPerformanceInUnity4-1.html
        private Dictionary<int, ProperObj> _groundObjDic = null;
        private Dictionary<int, ProperObj> _distanceObjDic = null;
        private void CreateProperObjDic()
        {
            if (_groundObjDic == null)
            {
                _groundObjDic = new Dictionary<int, ProperObj>()
                {
                    { (int)RaceDefine.GroundType.Turf, new ProperObj(_properTurfText, _properTurf) },
                    { (int)RaceDefine.GroundType.Dirt, new ProperObj(_properDirtText, _properDirt) },
                };
            }
            if (_distanceObjDic == null)
            {
                _distanceObjDic = new Dictionary<int, ProperObj>()
                {
                    { (int)RaceDefine.CourseDistanceType.Short, new ProperObj(_properShortText, _properShort) },
                    { (int)RaceDefine.CourseDistanceType.Mile, new ProperObj(_properMileText, _properMile) },
                    { (int)RaceDefine.CourseDistanceType.Middle, new ProperObj(_properMiddleText, _properMiddle) },
                    { (int)RaceDefine.CourseDistanceType.Long, new ProperObj(_properLongText, _properLong) },
                };
            }
        }

        //DispType.Targetの表示に変更する
        private void SetupDispTypeTarget(MasterRaceCourseSet.IRaceCourseInfo courseInfo)
        {
            if (courseInfo == null)
                return;

            CreateProperObjDic();

            //距離適性のサイズ変える
            _distanceRoot.sizeDelta = SIZE_TARGET_ONLY;
            //脚質適性の位置変える
            _styleRoot.anchoredPosition = STYLE_POS_TARGET_ONLY;

            //対象となる馬場適性のみ表示
            foreach(var kv in _groundObjDic)
            {
                var isTarget = (int)courseInfo.GroundType == kv.Key;
                if (isTarget)
                {
                    kv.Value.SetTargetPos();
                }
                kv.Value.SetActive(isTarget);
            }

            //対象となる距離適性のみ表示
            foreach(var kv in _distanceObjDic)
            {
                var isTarget = (int)courseInfo.DistanceType == kv.Key;
                if (isTarget)
                {
                    kv.Value.SetTargetPos();
                }
                kv.Value.SetActive(isTarget);
            }
        }

        #endregion
    }
}