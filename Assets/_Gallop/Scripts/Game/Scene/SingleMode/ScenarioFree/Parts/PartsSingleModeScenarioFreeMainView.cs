using DG.Tweening;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using AnimateToUnity;
namespace Gallop
{
    using BonusIconSceneCategory = PartsSingleModeScenarioFreeMainViewModel.BonusIconSceneCategory;
    using ItemBonusGroupData = System.Tuple<bool, IGrouping<int, int>>;

    public class PartsSingleModeScenarioFreeMainViewModel
    {
        public enum BonusIconSceneCategory
        {
            Top, Training,
        }


        private List<ItemBonusGroupData> _trainingItemEffectGroups; // トレーニングTOPで表示するアイテムボーナス
        private List<ItemBonusGroupData> _topItemEffectGroups;      // 育成TOPで表示するアイテムボーナス


        public Sprite WinPointClassWrapSprite
        {
            get
            {
                var degreeType = WorkDataManager.Instance.SingleMode.GetDegreeType();
                var atlas = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioFree);
                var path = AtlasSpritePath.SingleModeScenarioFreeRace.GetSingleModeScenarioFreeWinPointClassImagePath((int)degreeType, true);
                return atlas.GetSprite(path);
            }
        }

        public string WinPointText
        {
            get
            {
                return TextUtil.Format(TextUtil.GetStaticText(TextId.TeamStadium0074), WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree.WinPoints.ToCommaSeparatedString());
            }
        }

        public string ExtraRaceText
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                var roundNum = workScenarioFree.SingleModeTwikleRaceNpcResultList.Count();

                return TextUtil.Format(TextUtil.GetStaticText(TextId.SingleModeScenarioFree419020), roundNum, SingleModeScenarioFreeDefine.SINGLE_MODE_SCENARIO_FREE_EXTRA_RACE_NUM);
            }
        }

        /// <summary>
        /// トレーニングアイテムボーナスアイコンのInアニメーションを再生する必要がある場合true
        /// </summary>
        public bool NeedsPlayBonusIconInAnimationAtTraining { get; set; }


        public void UpdateItemEffectGroups()
        {
            var workScenareioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
            var trainingItemEffectGroups = ConvertGroups(true, workScenareioFree.GetTrainingEffectItemGroup());
            var raceItemEffectGroups = ConvertGroups(false, workScenareioFree.GetRaceBonusItemGroup());

            _topItemEffectGroups = JoinAndSortGroups(trainingItemEffectGroups, raceItemEffectGroups);
            _trainingItemEffectGroups = JoinAndSortGroups(trainingItemEffectGroups);


            IEnumerable<ItemBonusGroupData> ConvertGroups(bool isTraining, List<IGrouping<int, int>> groups)
            {
                if (groups.IsNullOrEmpty()) return null;

                return groups.Select(group => new ItemBonusGroupData(isTraining, group));
            }

            List<ItemBonusGroupData> JoinAndSortGroups(params IEnumerable<ItemBonusGroupData>[] groupsArray)
            {
                // 残りターンで降順ソートする
                return groupsArray
                    .Where(x => x != null)
                    .SelectMany(x => x)
                    .OrderByDescending(x => x.Item2.Key).ToList();
            }
        }

        public List<ItemBonusGroupData> GetItemBonusGroups(BonusIconSceneCategory category)
        {
            switch (category)
            {
                case BonusIconSceneCategory.Top: return _topItemEffectGroups;
                case BonusIconSceneCategory.Training: return _trainingItemEffectGroups;
 
                default:
                    throw new System.ArgumentException($"不正なカテゴリ {category}");
            }
        }

        /// <summary>
        /// ボーナスアイコンの効果に応じてフィルターをかけて表示を切り替える
        /// </summary>
        public IEnumerable<IGrouping<int, int>> FilterItemBonusIcon(BonusIconSceneCategory category, System.Predicate<SingleModeFreeItemEffect> predicate)
        {
            var workSingleFree = WorkDataManager.Instance.SingleMode?.Character?.WorkScenarioFree;
            if (workSingleFree == null) return null;

            var effects = workSingleFree.SingleModeFreeItemEffectArray;
            if (effects.IsNullOrEmpty()) return null;

            // 引数のpredicateで引っかかった後、再度残りターンごとにグルーピングする
            return 
                GetItemBonusGroups(category)
                .Select(groupData => groupData.Item2)
                .SelectMany(group => group.Select(itemId => new { key = group.Key, effect = effects.FirstOrDefault(effect => effect.item_id == itemId) }))
                .Where(x => x.effect != null && predicate(x.effect))
                .GroupBy(x => x.key, x => x.effect.item_id);
        }
    }


    /// <summary>
    /// 育成追加シナリオ：フリー編
    /// </summary>
    public sealed class PartsSingleModeScenarioFreeMainView : MonoBehaviour, ISingleModeScenarioMainViewAdditivePartsBase
    {

        private const int WINPT_NOTICE_TURN_OFFSET = 7;
        private const int NOTICE_FLASH_ORDER = 4001;

        [System.Serializable]
        public class ItemIconViewData
        {
            [SerializeField] private GameObject _itemEffectRoot;
            [SerializeField] private TweenAnimationTimelineComponent _itemIconTweenTimeline;
            [SerializeField] private CanvasGroup _rootCanvasGroup;

            public GameObject Root => _itemEffectRoot;
            public TweenAnimationTimelineComponent TweenTimeLine => _itemIconTweenTimeline;
            public CanvasGroup RootCanvasGroup => _rootCanvasGroup;
            public List<PartsSingleModeScenarioFreeItemBonusIcon> Views { get; } = new List<PartsSingleModeScenarioFreeItemBonusIcon>();
        }

        #region SerializeField

        [SerializeField]
        private GameObject _animationRoot = null;

        [SerializeField]
        private CanvasGroup _animationRootCanvasGroup = null;

        [SerializeField]
        private GameObject _normalRoot = null;

        [SerializeField]
        private GameObject _extraRaceRoot = null;

        [SerializeField, Header("トレーニング用アイテムアイコン")]
        private ItemIconViewData _trainingItemData = null;

        [SerializeField, Header("育成TOP用アイテムアイコン")]
        private ItemIconViewData _topItemData = null;

        [SerializeField, Header("育成TOP用(TSC時)アイテムアイコン")]
        private ItemIconViewData _topTscItemData = null;

        [SerializeField]
        private ImageCommon _winPointTitleImage = null;

        [SerializeField]
        private TextCommon _winPointText = null;

        [SerializeField]
        private PartsSingleModeScenarioFreeExtraRace _partsSingleModeScenarioFreeExtraRace = null;

        [SerializeField]
        private TextCommon _extraRaceNum = null;

        [SerializeField]
        private ImageCommon _noticeBg = null;

        #endregion

        #region Member

        private PartsSingleModeScenarioFreeMainViewModel _model = null;
        private PartsSingleModeScenarioFreeMainViewModel Model
        {
            get
            {
                if(_model == null)
                {
                    _model = new PartsSingleModeScenarioFreeMainViewModel();
                }
                return _model;
            }
        }


        private Vector3 _defaultPosition;

        private Dictionary<BonusIconSceneCategory, ItemIconViewData> _itemIconViewsDict = new Dictionary<BonusIconSceneCategory, ItemIconViewData>();

        #endregion


        /// <summary>
        /// 生成
        /// </summary>
        public static PartsSingleModeScenarioFreeMainView Create(Transform parent)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_SINGLE_MODE_SCENARIO_FREE_MAIN_VIEW);
            var obj = Instantiate(prefab, parent);
            var parts = obj.GetComponent<PartsSingleModeScenarioFreeMainView>();
            parts._defaultPosition = parts._animationRoot.transform.localPosition;
            return parts;
        }
        public static void RegisterDownlaod(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.PARTS_SINGLE_MODE_SCENARIO_FREE_ITEM_BONUS_ICON);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_EFFECT_BUTTON_RIVALRUN_00);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_EFFECT_BUTTON_RIVALRUN_01);
        }

        #region ISingleModeScenarioMainViewAdditivePartsBase
        
        /// <summary>
        /// 表示更新
        /// </summary>
        public void Setup()
        {
            SetupRoot();

            SetupWinPointClassImage();

            SetupWinPoint();

            SetupExtraRace();

            SetupItemEffect();
        }


        /// <summary>
        /// IN
        /// </summary>
        public void PlayIn()
        {
            if (gameObject.activeSelf)
            {
                PlayInCommon(TweenAnimation.PresetType.PartsInMoveAndFade);

                PlayInItemIconAtTop();
            }
        }

        /// <summary>
        /// OUT
        /// </summary>
        public void PlayOut()
        {
            if (gameObject.activeSelf)
            {
                TweenAnimationBuilder.CreateSequence(_animationRoot, TweenAnimation.PresetType.PartsOutMoveAndFade).OnComplete(() => gameObject.SetActive(false));

                PlayOutItemViewCore(BonusIconSceneCategory.Top);
            }
        }

        public void PlayInItemIconAtTop() =>
            PlayInItemViewCore(BonusIconSceneCategory.Top);

        /// <summary>
        /// トレーニング選択へ
        /// </summary>
        public void PlayGoTrainingSelect()
        {
            TweenAnimationBuilder.CreateSequence(_animationRoot, TweenAnimation.PresetType.PartsOutFadeFromLeft);

            // TOPのアイコンはアクティブを切る
            SetItemViewsActive(BonusIconSceneCategory.Top, false);

            Model.NeedsPlayBonusIconInAnimationAtTraining = true;
        }

        /// <summary>
        /// トレーニング選択から戻る
        /// </summary>
        public void PlayReturnTrainingSelect()
        {
            TweenAnimationBuilder.CreateSequence(_animationRoot, TweenAnimation.PresetType.PartsInFadeFromLeft); //左に戻ってくる

            SetItemViewsActive(BonusIconSceneCategory.Training, false);
            PlayInItemViewCore(BonusIconSceneCategory.Top);
        }

        private void PlayInCommon(TweenAnimation.PresetType presetType)
        {
            gameObject.SetActive(true);
            _animationRoot.transform.localPosition = _defaultPosition;
            TweenAnimationBuilder.CreateSequence(_animationRoot, presetType);
        }

        /// <summary>
        /// トレーニングコマンド選択時
        /// </summary>
        public void PlayExecTrainingCut()
        {
        }

        public void OnTrainingItemSelected(TrainingDefine.TrainingCommandId commandId)
        {
            // Inアニメーション再生前であれば再生する
            if (Model.NeedsPlayBonusIconInAnimationAtTraining)
            {
                PlayInItemViewCore(BonusIconSceneCategory.Training);

                Model.NeedsPlayBonusIconInAnimationAtTraining = false;
            }

            // アイテムボーナスアイコンに対して指定コマンドIDに効果があるアイテムボーナスのみを抽出する
            FilterItemBonusIconView(BonusIconSceneCategory.Training,
                effect =>
                {
                    return SingleModeScenarioFreeUtils.IsTargetCommandItemEffect(effect, (int)commandId);
                });
        }

        public void SetActive(bool enable)
        {
            gameObject.SetActive(enable);
            if(enable)
            {
                _animationRoot.transform.localPosition = _defaultPosition;
                _animationRootCanvasGroup.alpha = 1f;
            }
        }

        /// <summary>
        /// 勝利ポイント杯開催表示をする必要があるか
        /// </summary>
        /// <returns></returns>
        public bool IsNeedShowScenarioNotice()
        {
            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var nextRouteTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            //目標がない or 勝利Ptでなかった場合は表示しない
            if( nextRouteTarget == null || 
                nextRouteTarget.ConditionType != (int)SingleModeDefine.ConditionType.WinPoint)
            {
                return false;
            }

            //すでに目標達成していた場合も表示しない
            if( WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree.WinPoints >= nextRouteTarget.ConditionValue1 )
            {
                return false;
            }
            return currentTurn == nextRouteTarget.Turn - WINPT_NOTICE_TURN_OFFSET; //目標の7ターン前に通知を表示する
        }

        public void ShowScenarioNotice(System.Action onComplete)
        {
            var nextRouteTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();
            var remainWinPt = nextRouteTarget.ConditionValue1 - WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree.WinPoints;
            var digitsLength = (int)System.Math.Log10(remainWinPt) + 1; //桁数
            var tempWinPt = remainWinPt;
            var digitValueArray = new int[digitsLength];

            //各桁の数値を取得して配列に入れる
            for(var i = digitsLength - 1 ; i >= 0 ; --i)
            {
                var d = (int)System.Math.Pow(10,i); //1000,100,10,1
                var val = tempWinPt / d; //最大桁数の数値を取得
                digitValueArray[i] = val; // 123であれば [3,2,1] のように入る
                tempWinPt -= d * val; //最大桁数分引く
            }

            var player = FlashLoader.LoadOnView(ResourcePath.SINGLE_MODE_FREE_REMIND_WINPT_00,_noticeBg.transform);

            _noticeBg.SetActiveWithCheck(true);

            player.SortOffset = NOTICE_FLASH_ORDER;
            player.Init();
            player.Play("in");

            //表示桁数に応じて位置調整をする
            var digitsLabelName = TextUtil.Format("digit{0:D2}",digitsLength);

            var winptMotion = player.GetMotion("MOT_mc_num_winpt00");
            var ptMotion = player.GetMotion("MOT_mc_txt_pt00");
            winptMotion.SetMotionPlay(digitsLabelName); //桁ごとに表示するモーションを変更
            ptMotion.SetMotionPlay(digitsLabelName); //桁数に応じてptの表示ラベルを調整

            //各桁の数字を設定する
            for(var i = 1 ; i <= digitsLength ; ++i)
            {
                var obj = player.GetObj(TextUtil.Format("OBJ_mc_num{0:D2}",i)).GameObject; //各桁の親のObjを取得
                var num = player.GetMotion("MOT_mc_num00",rootGameObject:obj); //桁ごとの数字Motionを取得
                num.SetMotionPlay(TextUtil.Format("n{0}", digitValueArray[i-1])); //数値を設定
            }

            //ジュニア ~ シニア級表示設定
            var degreeType = WorkDataManager.Instance.SingleMode.GetDegreeType();
            var atlas = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioFree);
            var path = AtlasSpritePath.SingleModeScenarioFreeRace.GetSingleModeScenarioFreeWinPointClassImagePath((int)degreeType);
            player.SetSprite("PLN_dum_txt_winpt00",atlas.GetSprite(path));

            AudioManager.Instance.PlaySe(AudioId.SFX_ADDON01_NOTICE); //SEを再生


            //背景のフェード表示
            DOTween.ToAlpha(()=> _noticeBg.color,color => _noticeBg.color = color, 0.5f,0.13f).SetEase(Ease.OutCubic);

            //背景のフェードアウト登録
            player.SetActionCallBack("out",()=>
            {
                DOTween.ToAlpha(()=> _noticeBg.color,color => _noticeBg.color = color, 0f,0.13f).SetEase(Ease.OutCubic);
            },AnMotionActionTypes.Start);

            //フェードアウト完了後
            player.SetActionCallBack("end",() => 
            {
                _noticeBg.SetActiveWithCheck(false);
                onComplete?.Invoke();
            },AnMotionActionTypes.End);
        }

        #endregion

        private void SetupRoot()
        {
            _normalRoot.SetActiveWithCheck(!SingleModeUtils.IsDegreeTypeFinalTurn());
            _extraRaceRoot.SetActiveWithCheck(SingleModeUtils.IsDegreeTypeFinalTurn());
        }

        private void SetupWinPointClassImage()
        {
            _winPointTitleImage.sprite = Model.WinPointClassWrapSprite;
        }

        private void SetupWinPoint()
        {
            _winPointText.text = Model.WinPointText;
        }


        private void SetupExtraRace()
        {
            if(SingleModeUtils.IsDegreeTypeFinalTurn())
            {
                _partsSingleModeScenarioFreeExtraRace.Setup(OnClickExtraRaceInfoButton);

                _extraRaceNum.text = Model.ExtraRaceText;
            }
        }

        public void SetupItemEffect()
        {
            SetupItemIconViewDataIfNeeded();

            // 既存のアイコンを削除する
            foreach (var pair in _itemIconViewsDict)
            {
                var views = pair.Value.Views;
                views.ForEach(view => Destroy(view.gameObject));
                views.Clear();
            }

            _model.UpdateItemEffectGroups();

            GameObject prefab = null;

            // アイコンを構築しなおす
            foreach (var pair in _itemIconViewsDict)
            {
                var type = pair.Key;
                var data = pair.Value;

                foreach (var group in Model.GetItemBonusGroups(type))
                {
                    if (prefab == null)
                    {
                    }

                    var itemBonusIcon = PartsSingleModeScenarioFreeItemBonusIcon.Create(data.Root.transform, group.Item2, group.Item1);
                    data.Views.Add(itemBonusIcon);
                }

                data.TweenTimeLine.Play("out");
                data.TweenTimeLine.Complete("out", true);
            }
        }

        private void SetupItemIconViewDataIfNeeded()
        {
            if (_itemIconViewsDict.Count() > 0) return;

            // TSc期間中の場合TOPは座標を変更させる
            var topItemData = SingleModeUtils.IsDegreeTypeFinalTurn() ? _topTscItemData : _topItemData;
            
            _itemIconViewsDict[BonusIconSceneCategory.Training] = _trainingItemData;
            _itemIconViewsDict[BonusIconSceneCategory.Top] = topItemData;
        }

        private void OnClickExtraRaceInfoButton()
        {
            DialogSingleModeScenarioFreeExtraRaceRanking.OpenDialog();
        }

        private void PlayInItemViewCore(BonusIconSceneCategory type)
        {
            // アクティブが切れている可能性がある為設定しなおす
            SetItemViewsActive(type, true);

            var data = _itemIconViewsDict[type];
            data.RootCanvasGroup.alpha = 0f;
            data.TweenTimeLine.Play("in");
        }

        private void PlayOutItemViewCore(BonusIconSceneCategory type) =>
            _itemIconViewsDict[type].TweenTimeLine.Play("out");

        private void SetItemViewsActive(BonusIconSceneCategory type, bool isActive) =>
            _itemIconViewsDict[type].Views.ForEach(view => view.gameObject.SetActive(isActive));

        /// <summary>
        /// ボーナスアイコンの効果に応じてフィルターをかけて表示を切り替える
        /// </summary>
        private void FilterItemBonusIconView(BonusIconSceneCategory category, System.Predicate<SingleModeFreeItemEffect> predicate)
        {
            if (!_itemIconViewsDict.TryGetValue(category, out var data)) return;

            // フィルターした結果でItemViewを構築しなおす
            var views = data.Views;
            views.ForEach(x => x.SetActiveWithCheck(false));

            var filterGroups = Model.FilterItemBonusIcon(category, predicate);
            if (filterGroups == null) return;

            var index = 0;
            var isTraining = category == BonusIconSceneCategory.Training;

            foreach (var group in filterGroups)
            {
                var view = views[index++];
                view.Setup(group, isTrainingEffectItem: isTraining);
                view.SetActiveWithCheck(true);
            }
        }

    }
}
