using UnityEngine;
using System.Linq;

namespace Gallop
{
    public class PartsSingleModeScenarioFreeUserItemButtonModel
    {
        public bool HasUserItem
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                return workScenarioFree.UserItemInfoArray != null && workScenarioFree.UserItemInfoArray.Any();
            }
        }

        public int UserItemNum
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                if(!HasUserItem)
                {
                    return 0;
                }

                return workScenarioFree.UserItemInfoArray.Sum(info => info.num);
            }
        }

    }


    public class PartsSingleModeScenarioFreeUserItemButton : MonoBehaviour
    {
        #region SerializeField

        [SerializeField]
        private ButtonCommon _button = null;

        [SerializeField]
        private CanvasGroup _canvasGroup = null;


        [SerializeField]
        private GameObject _badgeRoot = null;

        #endregion

        #region Member

        private PartsSingleModeScenarioFreeUserItemButtonModel _model = null;
        private PartsSingleModeScenarioFreeUserItemButtonModel Model
        {
            get
            {
                if(_model == null)
                {
                    _model = new PartsSingleModeScenarioFreeUserItemButtonModel();
                }
                return _model;
            }
        }

        private System.Action _onClick = null;
        private BadgeCommon _badge = null;


        #endregion

        #region Method

        public static PartsSingleModeScenarioFreeUserItemButton Create(Transform parent, System.Action onClick)
        {
            var obj = GameObject.Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_SINGLE_MODE_SCENARIO_FREE_USER_ITEM_BUTTON), parent);
            var partsSingleModeScenarioFreeUserItemButton = obj.GetComponent<PartsSingleModeScenarioFreeUserItemButton>();

            partsSingleModeScenarioFreeUserItemButton.Setup(onClick);

            return partsSingleModeScenarioFreeUserItemButton;
        }

        public void Setup(System.Action onClick = null)
        {
            if(onClick != null)
            {
                _onClick = onClick;
            }
            _button.SetOnClick(OnClick);

            SetupBadge();
        }

        public void SetCanvasGroupAlpha(int alpha)
        {
            _canvasGroup.alpha = alpha;
        }


        private void SetupBadge()
        {
            // 生成
            if (_badge == null)
            {
                if(!Model.HasUserItem)
                {
                    return;
                }
                _badge = BadgeCommon.CreateBadge(_badgeRoot.transform, ResourceManager.ResourceHash.Common);
            }

            // アイテム所持数を表示
            _badge.SetupBadgeNumText(Model.UserItemNum);

            // アイテムを所持していれば表示
            _badge.SetActiveWithCheck(Model.HasUserItem);
        }

        private void OnClick()
        {
            _onClick?.Invoke();
        }

        #endregion
    }
}



