using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    public class PartsSingleModeScenarioFreeExtraRaceRankingItemModel
    {
        WorkSingleModeScenarioFree.SingleModeFreeTwinkleRaceNpcInfo _npcInfo;

        public PartsSingleModeScenarioFreeExtraRaceRankingItemModel(WorkSingleModeScenarioFree.SingleModeFreeTwinkleRaceNpcInfo npcInfo = null)
        {
            _npcInfo = npcInfo;
        }

        public int CharaId
        {
            get
            {
                if (_npcInfo == null)
                {
                    // NPC情報がなければプレイヤー
                    var workSingleModeCharacter = WorkDataManager.Instance.SingleMode.Character;
                    return workSingleModeCharacter.CharaId;
                }

                if (_npcInfo.CharaId == ModelLoader.MOB_CHARA_ID)
                {
                    // モブウマ娘の場合はNpcIDをキャラIDとする
                    return _npcInfo.NpcId;
                }
                return _npcInfo.CharaId;
            }
        }

        public int Rank
        {
            get
            {
                if(_npcInfo == null)
                {
                    // NPC情報がなければプレイヤー
                    return PlayerExtraRaceRank;
                }

                return NpcExtraRaceRank;
            }
        }

        private int PlayerExtraRaceRank
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                if (workScenarioFree.SingleModeFreeTwinkleRaceNpcInfoList == null || !workScenarioFree.SingleModeFreeTwinkleRaceNpcInfoList.Any())
                {
                    return 0;
                }

                var sumWinPoint = workScenarioFree.WinPoints + workScenarioFree.SingleModeFreeTwinkleRaceNpcInfoList.Sum(npcInfo => npcInfo.WinPoints);
                if (sumWinPoint <= 0)
                {
                    // ポイントを所持しているキャラがいない
                    return 0;
                }


                // 自分よりもポイントが上のNPC数を取得
                var orderTopCount = workScenarioFree.SingleModeFreeTwinkleRaceNpcInfoList.Count(npcInfo => npcInfo.WinPoints > workScenarioFree.WinPoints);
                return orderTopCount + 1;
            }
        }

        private int NpcExtraRaceRank
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;

                var sumWinPoint = workScenarioFree.WinPoints + workScenarioFree.SingleModeFreeTwinkleRaceNpcInfoList.Sum(sumNpcInfo => sumNpcInfo.WinPoints);
                if (sumWinPoint <= 0)
                {
                    // ポイントを所持しているキャラがいない
                    return 0;
                }

                // 自分よりもポイントが上のNPC数を取得
                var orderTopCount = workScenarioFree.SingleModeFreeTwinkleRaceNpcInfoList.Where(countNpcInfo => countNpcInfo.NpcId != _npcInfo.NpcId)
                                                                                         .Count(countNpcInfo => countNpcInfo.WinPoints > _npcInfo.WinPoints);
                // プレイヤーとも比較
                if (workScenarioFree.WinPoints > _npcInfo.WinPoints)
                {
                    orderTopCount++;
                }

                return orderTopCount + 1;
            }
        }

        public int WinPoint
        {
            get
            {
                if(_npcInfo == null)
                {
                    // NPC情報がなければプレイヤー
                    var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                    return workScenarioFree.WinPoints;
                }

                return _npcInfo.WinPoints;
            }
        }

        public List<int> ExtraRaceResultList
        {
            get
            {
                if(_npcInfo == null)
                {
                    // NPC情報がなければプレイヤー
                    return PlayerExtraRaceResultList;
                }

                return NpcExtraRaceResultList;
            }
        }

        private List<int> PlayerExtraRaceResultList
        {
            get
            {
                var workSingleMode = WorkDataManager.Instance.SingleMode;
                var workScenarioFree = workSingleMode.Character.WorkScenarioFree;

                var playerExtraRaceResultList = new List<int>();
                if (workScenarioFree.SingleModeTwikleRaceNpcResultList != null && workScenarioFree.SingleModeTwikleRaceNpcResultList.Any())
                {
                    workScenarioFree.SingleModeTwikleRaceNpcResultList.ForEach(singleModeTwikleRaceNpcResult =>
                    {
                        var extraRaceHistoryInfo = workSingleMode.RaceHistoryInfoList.FirstOrDefault(raceHistoryInfo => raceHistoryInfo.ProgramId == singleModeTwikleRaceNpcResult.ProgramId);
                        playerExtraRaceResultList.Add(extraRaceHistoryInfo.ResultRank);
                    });
                }

                return playerExtraRaceResultList;
            }
        }

        private List<int> NpcExtraRaceResultList
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;

                var npcRaceResultList = new List<WorkSingleModeScenarioFree.SingleModeNpcResult>();
                if (workScenarioFree.SingleModeTwikleRaceNpcResultList != null && workScenarioFree.SingleModeTwikleRaceNpcResultList.Any())
                {
                    workScenarioFree.SingleModeTwikleRaceNpcResultList.ForEach(singleModeTwikleRaceNpcResult =>
                    {
                        var npcRaceResult = singleModeTwikleRaceNpcResult.RaceResultList.FirstOrDefault(raceResult => raceResult.NpcId == _npcInfo.NpcId);
                        npcRaceResultList.Add(npcRaceResult);
                    });
                }

                return npcRaceResultList.Select(npcRaceResult => npcRaceResult.ResultRank).ToList();
            }
        }

        public int SortId
        {
            get
            {
                if(_npcInfo == null)
                {
                    // NPC情報がなければプレイヤー
                    return PlayerSortId;
                }

                return NpcSortId;
            }
        }

        private int PlayerSortId
        {
            get
            {
                var workSingleMode = WorkDataManager.Instance.SingleMode;
                var workScenarioFree = workSingleMode.Character.WorkScenarioFree;

                if (workScenarioFree.SingleModeTwikleRaceNpcResultList == null || !workScenarioFree.SingleModeTwikleRaceNpcResultList.Any())
                {
                    return 0;
                }

                // 直近行われたレース情報を取得
                var npcResultListMaxTurn = workScenarioFree.SingleModeTwikleRaceNpcResultList.Max(npcResult => npcResult.Turn);
                var npcResultMaxTurn = workScenarioFree.SingleModeTwikleRaceNpcResultList.FirstOrDefault(npcResult => npcResult.Turn == npcResultListMaxTurn);

                // レース履歴を取得
                var extraRaceHistoryInfo = workSingleMode.RaceHistoryInfoList.FirstOrDefault(raceHistoryInfo => raceHistoryInfo.ProgramId == npcResultMaxTurn.ProgramId);

                return extraRaceHistoryInfo.ResultRank;
            }
        }

        private int NpcSortId
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                if (workScenarioFree.SingleModeTwikleRaceNpcResultList == null || !workScenarioFree.SingleModeTwikleRaceNpcResultList.Any())
                {
                    return 0;
                }

                // 直近行われたレース情報を取得
                var npcResultListMaxTurn = workScenarioFree.SingleModeTwikleRaceNpcResultList.Max(npcResult => npcResult.Turn);
                var npcResultMaxTurn = workScenarioFree.SingleModeTwikleRaceNpcResultList.FirstOrDefault(npcResult => npcResult.Turn == npcResultListMaxTurn);

                var npcRaceResult = npcResultMaxTurn.RaceResultList.FirstOrDefault(raceResult => raceResult.NpcId == _npcInfo.NpcId);
                return npcRaceResult.ResultRank;
            }
        }



        public Sprite BgSprite
        {
            get
            {
                if(IsPlayer)
                {
                    return UIManager.PreInAtlas.GetSprite("utx_frm_list_base_03_sl");
                }

                return UIManager.PreInAtlas.GetSprite("utx_frm_list_base_02_sl");
            }
        }


        public bool IsPlayer
        {
            get
            {
                var workSingleModeCharacter = WorkDataManager.Instance.SingleMode.Character;
                return workSingleModeCharacter.CharaId == CharaId;
            }
        }

        private MasterCharaData.CharaData _masterCharaData = null;
        private MasterCharaData.CharaData MasterCharaData
        {
            get
            {
                if(_masterCharaData == null)
                {
                    _masterCharaData = MasterDataManager.Instance.masterCharaData.Get(CharaId);
                }
                return _masterCharaData;
            }
        }

        private MasterMobData.MobData _masterMobData = null;
        private MasterMobData.MobData MasterMobData
        {
            get
            {
                if (_masterMobData == null)
                {
                    var masterSingleModeNpc = MasterDataManager.Instance.masterSingleModeNpc.Get(CharaId);
                    _masterMobData = MasterDataManager.Instance.masterMobData.Get(masterSingleModeNpc.MobId);
                }
                return _masterMobData;
            }
        }

        public int MobId
        {
            get
            {
                return MasterMobData.MobId;
            }
        }

        public bool IsUniqueCharacter
        {
            get
            {
                return MasterCharaData != null;
            }
        }

        public Sprite RankBaseSprite
        {
            get
            {
                return CircleUtil.GetRankingBaseSprite(Rank);
            }
        }

        public OutlineColorType RankTextOutlineColor
        {
            get
            {
                switch (Rank)
                {
                    case 1:
                        return OutlineColorType.CircleRanking01;
                    case 2:
                        return OutlineColorType.CircleRanking02;
                    case 3:
                        return OutlineColorType.CircleRanking03;
                    default:
                        return OutlineColorType.CircleRanking04;
                }
            }
        }


        public bool IsStartExtraRace
        {
            get
            {
                // 0位はまだ順位が決まってない
                return Rank > 0;
            }
        }


        public string CharacterName
        {
            get
            {
                if(IsUniqueCharacter)
                {
                    return MasterCharaData.Name;
                }
                else
                {
                    return MasterMobData.Name;
                }
            }
        }

        public Texture CharacterIcon
        {
            get
            {
                var workSingleModeCharacter = WorkDataManager.Instance.SingleMode.Character;
                var workScenarioFree = workSingleModeCharacter.WorkScenarioFree;

                if(IsPlayer)
                {
                    var path = ResourcePath.GetCardThumbnailIconPath(CharaId, workSingleModeCharacter.RaceDressId, workSingleModeCharacter.TalentLevel);
                    return ResourceManager.LoadOnView<Texture>(path);
                }
                else
                {
                    var singleModeFreeTwinkleRaceNpcInfo = workScenarioFree.GetSingleModeFreeTwinkleRaceNpcInfo(CharaId);
                    if(IsUniqueCharacter)
                    {
                        // ユニークキャラ
                        var path = ResourcePath.GetCardThumbnailIconPath(CharaId, singleModeFreeTwinkleRaceNpcInfo.DressId, singleModeFreeTwinkleRaceNpcInfo.TalentLevel);
                        return ResourceManager.LoadOnView<Texture>(path);
                    }
                    else
                    {
                        // モブウマ娘
                        var path = ResourcePath.GetMobCharaThumbnailIconPath(MobId, singleModeFreeTwinkleRaceNpcInfo.DressId, singleModeFreeTwinkleRaceNpcInfo.TalentLevel);
                        return ResourceManager.LoadOnView<Texture>(path);
                    }
                }
            }
        }

    }

    public sealed class PartsSingleModeScenarioFreeExtraRaceRankingItem : LoopScrollItemBase
    {
        [System.Serializable]
        public class RaceResult
        {
            [SerializeField]
            private ImageCommon _rankImage = null;

            [SerializeField]
            private GameObject _textObj = null;

            public void SetRank(int rank)
            {
                // ランク0は未出走表示
                _rankImage.SetActiveWithCheck(rank > 0);
                _textObj.SetActiveWithCheck(rank <= 0);

                var rankId = rank - 1;
                _rankImage.sprite = RaceUtil.GetRankSmallSprite(rankId);
            }
        }


        [SerializeField]
        private ImageCommon _bgImage;

        public Transform BgImageTransform => _bgImage.transform; //エフェクト配置用


        [SerializeField]
        private HorizontalLayoutGroup _horizontalLayoutGroup = null;

        [SerializeField]
        private ImageCommon _rankBaseImage;

        [SerializeField]
        private TextCommon _rankBaseText;

        [SerializeField]
        private RawImageCommon _characterIcon;

        [SerializeField]
        private TextCommon _characterName;

        [SerializeField]
        private TextCommon _winPoint;

        [SerializeField]
        private RaceResult[] _raceResultArray;

        [SerializeField]
        private ButtonCommon _infoButton = null;

        [SerializeField]
        private CanvasGroup _canvasGroup = null;



        private PartsSingleModeScenarioFreeExtraRaceRankingItemModel _model = null;

        private System.Action<PartsSingleModeScenarioFreeExtraRaceRankingItemModel> _onClickInfoButton = null;

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        public void Setup(PartsSingleModeScenarioFreeExtraRaceRankingItemModel model, System.Action<PartsSingleModeScenarioFreeExtraRaceRankingItemModel> onClickInfoButton = null)
        {
            _model = model;

            SetupBg();

            SetupRankBase();

            SetupCharacterIcon();

            SetCharacterName();

            SetWinPoint();

            SetupExtraRaceResultList();

            SetupInfoButton(onClickInfoButton);
        }

        private void SetupBg()
        {
            _bgImage.sprite = _model.BgSprite;
        }

        private void SetupRankBase()
        {
            _rankBaseImage.gameObject.SetActiveWithCheck(_model.IsStartExtraRace);
            _rankBaseImage.sprite = _model.RankBaseSprite;

#if BUMA_T
            // 巅峰赛排名对话框排名里面不要加上空格，by WangYuDi 
            _rankBaseText.text = TextUtil.Format(TextUtil.GetStaticText(TextId.SingleModeScenarioFree419021), _model.Rank);
#else
            _rankBaseText.text = TextUtil.Format(TextUtil.GetStaticText(TextId.SingleModeScenarioFree419021), _model.Rank + " ");
#endif
            _rankBaseText.OutlineColor = _model.RankTextOutlineColor;

            // ランク表示がなければ名前表示にpaddingを設定
            int NAME_PADDING = 24;
            _horizontalLayoutGroup.padding.left = _model.IsStartExtraRace ? 0 : NAME_PADDING;
        }

        private void SetupCharacterIcon()
        {
            _characterIcon.texture = _model.CharacterIcon;
        }

        private void SetCharacterName()
        {
            _characterName.text = _model.CharacterName;
        }

        private void SetWinPoint()
        {
            _winPoint.text = TextUtil.Format(TextUtil.GetStaticText(TextId.TeamStadium0074), _model.WinPoint);
        }


        private void SetupExtraRaceResultList()
        {
            var index = 0;
            foreach (var raceResult in _raceResultArray)
            {
                if (index >= _model.ExtraRaceResultList.Count)
                {
                    // レース結果がないので0を設定
                    raceResult.SetRank(0);
                    continue;
                }

                var rank = _model.ExtraRaceResultList[index];
                raceResult.SetRank(rank);

                index++;
            }
        }

        private void SetupInfoButton(System.Action<PartsSingleModeScenarioFreeExtraRaceRankingItemModel> onClickInfoButton)
        {
            _onClickInfoButton = onClickInfoButton;

            _infoButton.SetOnClick(OnClickInfoButton);
            _infoButton.SetActiveWithCheck(_onClickInfoButton != null);
        }


        private void OnClickInfoButton()
        {
            _onClickInfoButton?.Invoke(_model);
        }

        public void HideObject()
        {
            _canvasGroup.alpha = 0;
        }

        public bool IsPlayerItem()
        {
            return _model.IsPlayer;
        }
    }
}
