using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// SingleModeMain フリー編用処理群
    /// </summary>
    public sealed partial class SingleModeMainViewController : ViewControllerBase<SingleModeMainView>
    {
        
        public enum AlertDialogTypeFree
        {
            ShopUpdate, //ショップ更新
            NeedWinpoint //勝利Pt不足
        }
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        private void RegisterDownloadScenarioFree(DownloadPathRegister register)
        {
            DialogSingleModeScenarioFreeShopUpdate.RegisterDownload(register);
            DialogSingleModeScenarioFreeUserItemList.RegisterDownload(register);
            DialogSingleModeScenarioFreeExtraRaceRanking.RegisterDownload(register);
            DialogSingleModeScenarioFreeWinPointAlert.RegisterDownload(register);
            PartsSingleModeScenarioFreeUseItemPerformance.RegisterDownload(register);
            PartsSingleModeScenarioFreeMainView.RegisterDownlaod(register);

            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_FREE_BADGE_ITEM_BONUS_00);

            // 購買部商品リスト
            RegisterDownloadSingleModeFreeShopItem(register);
        }

        private void RegisterDownloadSingleModeFreeShopItem(DownloadPathRegister register)
        {
            var masterSingleModeFreeShopItemList = MasterDataManager.Instance.masterSingleModeFreeShopItem.dictionary.Values.ToList();
            if (masterSingleModeFreeShopItemList == null || !masterSingleModeFreeShopItemList.Any())
            {
                return;
            }

            masterSingleModeFreeShopItemList.ForEach(shopItem =>
            {
                var iconPath = ResourcePath.GetSingleModeScenarioFreeItemIconPath(shopItem.ItemId);
                register.RegisterPathWithoutInfo(iconPath);
            });
        }

        #region InitializeView

        #region SetupMainTopView

        private PartsSingleModeScenarioFreeUserItemButton _userItemButton = null;
        private readonly Vector3 USER_ITEM_BUTTON_POSITION = new Vector3(-140, 0);

        private void SetupMainTopViewScenarioFree()
        {
            if (WorkDataManager.Instance.SingleMode.GetScenarioId() != SingleModeDefine.ScenarioId.Free)
            {
                return;
            }

            SetupScenarioFreeMainView();

            SetupMainTopShopButton();
        }

        private void SetupScenarioFreeMainView()
        {
            if (_scenarioParts == null)
            {
                _scenarioParts = PartsSingleModeScenarioFreeMainView.Create(_view.ContentsRoot);
            }
            _scenarioParts.Setup();
        }

        private void SetupMainTopShopButton()
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;

            // 購買部ボタン設定
            _view.StablesPanel.OnClickAddonScenario = OnClickScenarioFreeShop;

            // 所持アイテムボタン生成
            if (_userItemButton == null &&
                SingleModeScenarioFreeUtils.IsSingleModeTurnUniqueCommandOn(workSingleMode.GetCurrentTurn()))
            {
                _userItemButton = PartsSingleModeScenarioFreeUserItemButton.Create(_view.EtcButtonContainer, OnClickScenarioFreeUserItem);
                var userItemButtonRectTransform = _userItemButton.GetComponent<RectTransform>();
                userItemButtonRectTransform.anchorMax = new Vector2(0.5f, 0.5f);
                userItemButtonRectTransform.anchorMin = new Vector2(0.5f, 0.5f);
                _userItemButton.transform.localPosition = USER_ITEM_BUTTON_POSITION;
            }
            _userItemButton?.Setup();
        }


        /// <summary>
        /// 購買部遷移
        /// </summary>
        private void OnClickScenarioFreeShop()
        {
            SceneManager.Instance.AdditiveView(SceneDefine.ViewId.SingleModeScenarioFreeShop);
        }

        /// <summary>
        /// 所持アイテム遷移
        /// </summary>
        private void OnClickScenarioFreeUserItem()
        {
            DialogSingleModeScenarioFreeUserItemList.Open(OnClickUserItem);
        }


        #region アイテム使用ダイアログ処理

        private void OnClickUserItem(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, DialogCommon dialogSingleModeScenarioFreeUserItemList, DialogCommon dialogItemUseConfirm)
        {
            // アイテム使用API実行
            var singleModeScenarioFreeShopViewModel = new SingleModeScenarioFreeShopViewModel();
            singleModeScenarioFreeShopViewModel.SendSingleModeFreeItemUseRequest(singleModeFreeUserItem, useNum, () => CallbackSendSingleModeFreeItemUseRequest(singleModeFreeUserItem, 1, dialogSingleModeScenarioFreeUserItemList, dialogItemUseConfirm));
        }

        private void CallbackSendSingleModeFreeItemUseRequest(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, DialogCommon dialogSingleModeScenarioFreeUserItemList, DialogCommon dialogItemUseConfirm)
        {
            // ダイアログを閉じる
            dialogItemUseConfirm.Close();
            dialogSingleModeScenarioFreeUserItemList.Close();

            // 育成TOP更新
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            _view.StablesPanel.Setup();
            _view.TrainingStatus.Setup(workSingleMode.Character);
            _view.TrainingFooter.Setup();
            _view.SkillPointPanel.SetSkillPoint(workSingleMode.Character.SkillPoint);
            var partsScenarioFreeMainView = (_scenarioParts as PartsSingleModeScenarioFreeMainView);
            if(partsScenarioFreeMainView != null)
            {
                partsScenarioFreeMainView.SetupItemEffect(); //アイテム効果アイコン表示更新

                // アイテム使用後、Inアニメーションを再生しておく
                partsScenarioFreeMainView.PlayInItemIconAtTop();
            }
            
            // 所持アイテムボタン表示更新
            _userItemButton.Setup();

            _enableBackKey = false; //バックキー使用不可
            // アイテム使用演出再生
            PartsSingleModeScenarioFreeUseItemPerformance.PlayUseItemPerformance(_view.ContentsRoot,
                                                                                 UIManager.SingleModeHeader.HpGauge,
                                                                                 UIManager.SingleModeHeader.MotivationButton,
                                                                                 singleModeFreeUserItem,
                                                                                 useNum,
                                                                                 () => CallbackPlayUseItemPerformance());
        }

        private void CallbackPlayUseItemPerformance()
        {
            // バックキー使用可能
            _enableBackKey = true;

            // 所持アイテムダイアログを表示
            DialogSingleModeScenarioFreeUserItemList.Open(OnClickUserItem);
        }

        #endregion

        #endregion SetupMainTopView



        /// <summary>
        /// フリー編用に共通UIの位置調整を行う
        /// </summary>
        private void UpdateCommonUIPositionToFree()
        {
            /// キャラグレードを下に移動
            UpdateCommonUIPositionCharaGrade();

            // メッセージを上にずらす
            UpdateCommonUIPositionCharaMessage();
        }

        private void UpdateCommonUIPositionCharaGrade()
        {
            const float CHARA_GRADE_POSITION_X = -505f;
            const float CHARA_GRADE_POSITION_Y = 735f;

            var charaGradeRect = _view.CharaGradeRoot.transform as RectTransform;
            //アンカーを下に変更する
            var charaGradeAnchor = new Vector2(0.5f, 0);
            charaGradeRect.SetAnchorWithKeepingPosition(charaGradeAnchor, charaGradeAnchor);
            //下付きグループ扱いに変更する
            charaGradeRect.SetParent(_view.BottomRect.transform);
            //キャラグレードを下に移動する
            charaGradeRect.anchoredPosition = new Vector2(CHARA_GRADE_POSITION_X, CHARA_GRADE_POSITION_Y);
        }

        private void UpdateCommonUIPositionCharaMessage()
        {
            const float CHARA_MESSAGE_POSITION_X = -207f;
            const float CHARA_MESSAGE_POSITION_Y = 885f;

            //キャラメッセージを上にずらす
            var messageRect = _view.CharaMessage.transform as RectTransform;
            messageRect.anchoredPosition = new Vector2(CHARA_MESSAGE_POSITION_X, CHARA_MESSAGE_POSITION_Y);
        }

        #endregion InitializeView


        private void PlayInCommandViewScenarioFree()
        {
            if (WorkDataManager.Instance.SingleMode.GetScenarioId() != SingleModeDefine.ScenarioId.Free)
            {
                return;
            }

            // シナリオパーツ表示更新
            if (_scenarioParts != null)
            {
                _scenarioParts.Setup();
            }

            // 所持アイテムボタン表示更新
            if (_userItemButton != null)
            {
                _userItemButton.Setup();
            }
        }

        private TextId GetSingleModeScenarioFreeHeaderTitle()
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var degreeType = workSingleMode.GetDegreeType();
            if (degreeType == SingleModeDefine.DegreeType.Final)
            {
                return TextId.SingleModeScenarioFree419068;
            }
            return TextId.Common0273;
        }

        private DialogTutorialGuide.TutorialGuideId GetScenarioFreeTutorialGuideId()
        {
            var degreeType = WorkDataManager.Instance.SingleMode.GetDegreeType();
            if (degreeType == SingleModeDefine.DegreeType.Final)
            {
                // 4年目ならTSC
                return DialogTutorialGuide.TutorialGuideId.SingleModeScenarioFreeTscRace; 
            }

            return SingleModeUtils.GetTutorialGuideId(WorkDataManager.Instance.SingleMode.GetScenarioId());
        }

        /// <summary>
        /// 育成TOP チュートリアル処理
        /// </summary>
        /// <param name="callback"></param>
        private void TutorialCommandSelectStartScenarioFree(System.Action callback)
        {
            TutorialSingleModeScenarioFreeMain(() =>
            {
                TutorialSingleModeScenarioFreeTscRace(callback);
            });
        }

        /// <summary>
        /// フリー編チュートリアル
        /// </summary>
        /// <param name="callback"></param>
        private void TutorialSingleModeScenarioFreeMain(System.Action callback)
        {
            var SCENARIO_FREE_TUTORIAL_GUILD_OPEN_TURN = 12;
            if (WorkDataManager.Instance.SingleMode.GetCurrentTurn() <= SCENARIO_FREE_TUTORIAL_GUILD_OPEN_TURN)
            {
                callback?.Invoke();
                return;
            }
            var tutorialGuideId = SingleModeUtils.GetTutorialGuideId(WorkDataManager.Instance.SingleMode.GetScenarioId());
            DialogTutorialGuide.PushDialogWithReadCheck(tutorialGuideId, callback);
        }

        /// <summary>
        /// TSCレースチュートリアル
        /// </summary>
        /// <param name="callback"></param>
        private void TutorialSingleModeScenarioFreeTscRace(System.Action callback)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var degreeType = workSingleMode.GetDegreeType();
            if (degreeType != SingleModeDefine.DegreeType.Final)
            {
                callback?.Invoke();
                return;
            }
            DialogTutorialGuide.PushDialogWithReadCheck(DialogTutorialGuide.TutorialGuideId.SingleModeScenarioFreeTscRace, callback);
        }

        /// <summary>
        /// 該当タイプのアラートダイアログ表示チェック日を取得
        /// Dictionaryに登録されていなければキーを追加する
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private SingleModeYearMonthAndHalf TryGetLastCheckScenarioAlertDialogDateFree(AlertDialogTypeFree type)
        {
            //保存された最終チェック日を取得
            if(!SingleModeChangeViewManager.Instance.LastCheckScenarioAlertDialogDictionary.TryGetValue((int)type,out var tempYearMonthAndHalf))
            {
                //そもそもなければキーを追加する
                SingleModeChangeViewManager.Instance.LastCheckScenarioAlertDialogDictionary.Add((int)type,null);
            }
            return tempYearMonthAndHalf;
        }

        /// <summary>
        /// ショップの更新表示をする必要があるか
        /// </summary>
        /// <param name="yearMonthAndHalf"></param>
        /// <returns></returns>
        private bool IsNeedShowUpdateShopFree(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;

            if( TryGetLastCheckScenarioAlertDialogDateFree(AlertDialogTypeFree.ShopUpdate) != yearMonthAndHalf) //ショップ更新をチェック済みではない
            {
                if(SingleModeScenarioFreeUtils.IsUpdateShopTurn()) //ショップ更新があるならば通知を表示する必要がある
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// ショップ更新ダイアログ表示実行
        /// </summary>
        /// <param name="yearMonthAndHalf"></param>
        /// <returns></returns>
        private IEnumerator ShowUpdateShopFree(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            // 購買部の商品更新ターンならば
            if( IsNeedShowUpdateShopFree(yearMonthAndHalf) )
            {
                var isShow = true;
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                // 商品更新ダイアログを表示
                DialogSingleModeScenarioFreeShopUpdate.Open(OnClickScenarioFreeShop, () => isShow = false);
                SingleModeChangeViewManager.Instance.LastCheckScenarioAlertDialogDictionary[(int)AlertDialogTypeFree.ShopUpdate] = yearMonthAndHalf;
                yield return new WaitWhile(() => isShow); //商品更新ダイアログの破棄待ち
            }
        }

        /// <summary>
        /// 必要勝利Pt表示をする必要があるか
        /// </summary>
        /// <param name="yearMonthAndHalf"></param>
        /// <returns></returns>
        private bool IsNeedShowWinptNoticeFree(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
            if( workScenarioFree.LastCheckWinptNoticeTurn == null || 
                workScenarioFree.LastCheckWinptNoticeTurn != yearMonthAndHalf) //勝利Pt通知をチェック済みではない
            {
                if( _scenarioParts.IsNeedShowScenarioNotice() ) //勝利Pt通知を表示する必要がある
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 勝利Pt表示演出実行
        /// </summary>
        /// <param name="yearMonthAndHalf"></param>
        /// <returns></returns>
        private IEnumerator ShowWinptNoticeFree(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            //必要勝利Pt表示
            if( IsNeedShowWinptNoticeFree(yearMonthAndHalf) )
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                //残り勝利ポイント演出
                var showNotice = true;
                _scenarioParts.ShowScenarioNotice(() => showNotice = false);
                workScenarioFree.LastCheckWinptNoticeTurn = yearMonthAndHalf; //表示ターン更新
                yield return new WaitWhile(() => showNotice);
            }
        }

        /// <summary>
        /// 勝利Pt不足アラートを表示する必要があるか
        /// </summary>
        /// <param name="yearMonthAndHalf"></param>
        /// <returns></returns>
        private bool IsNeedShowWinptAlert(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;

            if( TryGetLastCheckScenarioAlertDialogDateFree(AlertDialogTypeFree.NeedWinpoint) != yearMonthAndHalf ) //勝利Ptアラートをチェック済みではない
            {
                if( SingleModeScenarioFreeUtils.IsNeedShowWinptAlert() ) //勝利Ptアラートを表示する必要がある
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 勝利Pt不足アラートダイアログ表示
        /// </summary>
        /// <returns></returns>
        private IEnumerator ShowWinptAlertDialg(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            if( IsNeedShowWinptAlert(yearMonthAndHalf) )
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                var isShow = true;
                DialogSingleModeScenarioFreeWinPointAlert.Open(OnClickRace, () => isShow = false);
                //チェック日を更新 表示判定時にキーは追加されているはず
                SingleModeChangeViewManager.Instance.LastCheckScenarioAlertDialogDictionary[(int)AlertDialogTypeFree.NeedWinpoint] = yearMonthAndHalf;
                yield return new WaitWhile(() => isShow);
            }
        }


        /// <summary>
        /// MainViewに入ったタイミングで表示する通知が存在するか
        /// </summary>
        /// <param name="yearMonthAndHalf"></param>
        /// <returns></returns>
        public bool IsNeedShowScenarioNoticeFree(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {
            return  IsNeedShowUpdateShopFree(yearMonthAndHalf) || 
                    IsNeedShowWinptNoticeFree(yearMonthAndHalf) ||
                    IsNeedShowWinptAlert(yearMonthAndHalf); //ショップ更新or勝利Pt不足警告or勝利Pt表示の必要がある
        }
        
        /// <summary>
        /// フリー編 CoroutineDialogOnBeginView
        /// </summary>
        private IEnumerator BeginViewScenarioFree(SingleModeYearMonthAndHalf yearMonthAndHalf)
        {

            yield return _view.StartCoroutine(ShowWinptAlertDialg(yearMonthAndHalf)); //勝利Pt不足アラート表示
            
            // レース選択へ遷移する場合は処理を終了する
            if(SceneManager.Instance.GetNextViewId() == SceneDefine.ViewId.SingleModeRaceEntry)
            {
                yield break;
            }

            yield return _view.StartCoroutine(ShowUpdateShopFree(yearMonthAndHalf)); //ショップの更新表示

            // 購買部へ遷移する場合は処理を終了する
            if(SceneManager.Instance.GetNextViewId() == SceneDefine.ViewId.SingleModeScenarioFreeShop)
            {
                yield break;
            }

            yield return _view.StartCoroutine(ShowWinptNoticeFree(yearMonthAndHalf)); //勝利Pt表示
        }


    }
}
