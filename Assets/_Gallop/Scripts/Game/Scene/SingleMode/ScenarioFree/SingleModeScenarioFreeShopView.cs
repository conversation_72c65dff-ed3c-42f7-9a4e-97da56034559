using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;
using DG.Tweening;

namespace Gallop
{
    public class SingleModeScenarioFreeShopViewModel
    {
        public string BackgroundPath
        {
            get
            {
                var workSingleMode = WorkDataManager.Instance.SingleMode;

                var masterSingleModeFreeShopBgDic = MasterDataManager.Instance.masterSingleModeFreeShopBg.dictionary;
                foreach (KeyValuePair<int, MasterSingleModeFreeShopBg.SingleModeFreeShopBg> keyValuePair in masterSingleModeFreeShopBgDic)
                {
                    var masterSingleModeFreeShopBg = keyValuePair.Value;
                    if (masterSingleModeFreeShopBg.StartTurn <= workSingleMode.GetCurrentTurn() && workSingleMode.GetCurrentTurn() <= masterSingleModeFreeShopBg.EndTurn)
                    {
                        return ResourcePath.GetBackgroundPath(masterSingleModeFreeShopBg.BgId, masterSingleModeFreeShopBg.BgSubId);
                    }
                }

                return string.Empty;
            }
        }

        public string EnvPath
        {
            get
            {
                var workSingleMode = WorkDataManager.Instance.SingleMode;
                if (workSingleMode.IsSummerCampTurn())
                {
                    // 夏合宿期間
                    return ResourcePath.SINGLE_MODE_SCENARIO_FREE_SHOP_SUMMER_ENV_PARAM;
                }
                return ResourcePath.SINGLE_MODE_SCENARIO_FREE_SHOP_ENV_PARAM;
            }
        }



        public List<SingleModeFreePickUpItem> SingleModeFreePickUpItemList
        {
            get
            {
                var workSingleModeFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                var pickUpItemInfoArray = workSingleModeFree.PickUpItemInfoArray;
                if (pickUpItemInfoArray == null || !pickUpItemInfoArray.Any())
                {
                    return null;
                }

                return pickUpItemInfoArray.OrderByDescending(item => item.shop_item_id).ToList();
            }
        }

        private List<PartsSingleModeScenarioFreeShopListItemModel> _shopListItemModelList = null;
        public List<PartsSingleModeScenarioFreeShopListItemModel> ShopListItemModelList
        {
            get
            {
                if (_shopListItemModelList == null)
                {
                    _shopListItemModelList = GetShopItemModelList();
                }
                return _shopListItemModelList;
            }
        }

        private List<PartsSingleModeScenarioFreeShopListItemModel> GetShopItemModelList()
        {
            var workSingleModeFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;

            _shopListItemModelList = new List<PartsSingleModeScenarioFreeShopListItemModel>();
            if (SingleModeFreePickUpItemList == null || !SingleModeFreePickUpItemList.Any())
            {
                // 購買部商品情報がないので終了
                return _shopListItemModelList;
            }

            foreach (var pickUpItemInfo in SingleModeFreePickUpItemList)
            {
                var isNew = !CheckedShopItemIdList.Any(id => id == pickUpItemInfo.shop_item_id);
                var shopListItemModel = new PartsSingleModeScenarioFreeShopListItemModel(isNew);
                shopListItemModel.PickUpItem = pickUpItemInfo;

                _shopListItemModelList.Add(shopListItemModel);
            }

            return _shopListItemModelList;
        }

        private void RefreshShopItemModelList()
        {
            _shopListItemModelList = null;
        }

        public bool IsSale
        {
            get
            {
                var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
                return workScenarioFree.SaleValue > 0;
            }
        }

        public bool IsSoldOut
        {
            get
            {
                return !ShopListItemModelList.Any(model => model.IsRemainBuyNum);
            }
        }

        /// <summary>
        /// ラインナップ更新後初遷移
        /// </summary>
        public bool IsLineupUpdateFirst
        {
            get
            {
                var workSingleMode = WorkDataManager.Instance.SingleMode;
                var workScenarioFree = workSingleMode.Character.WorkScenarioFree;

                if(workScenarioFree.LastShopOpenTurn == workSingleMode.GetCurrentTurn())
                {
                    // すでに遷移済みなので終了
                    return false;
                }

                // ショップマスタを取得
                var masterSingleModeFreeShop = MasterDataManager.Instance.masterSingleModeFreeShop.Get(workScenarioFree.ShopId);
                if (masterSingleModeFreeShop == null)
                {
                    return false;
                }

                // 前に遷移したターンは更新前
                return workScenarioFree.LastShopOpenTurn < masterSingleModeFreeShop.StartTurn;
            }
        }

        private List<int> _checkedShopItemIdList;
        public List<int> CheckedShopItemIdList
        {
            get{return _checkedShopItemIdList;}
            set{_checkedShopItemIdList = value;}
        }


        #region API

        /// <summary>
        /// 交換API実行
        /// </summary>
        /// <param name="singleModeFreePickUpItem"></param>
        /// <param name="exchangeNum"></param>
        /// <param name="callback"></param>
        public void SendSingleModeFreeItemExchangeRequest(SingleModeFreePickUpItem singleModeFreePickUpItem, int exchangeNum, System.Action callback = null)
        {
            // ユーザーのアイテム所持数を取得
            var currentUserItemNum = 0;
            var userItem = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree.GetUserItem(singleModeFreePickUpItem.item_id);
            if (userItem != null)
            {
                currentUserItemNum = userItem.num;
            }

            SingleModeFreeAPI.SendSingleModeFreeItemExchangeRequest(singleModeFreePickUpItem.shop_item_id, exchangeNum, currentUserItemNum, () =>
            {
                if(!this.CheckedShopItemIdList.Any( id => id == singleModeFreePickUpItem.shop_item_id))
                {
                    CheckedShopItemIdList.Add(singleModeFreePickUpItem.shop_item_id);
                }
                RefreshShopItemModelList();
                callback?.Invoke();
            });
        }

        /// <summary>
        /// アイテム使用API実行
        /// </summary>
        /// <param name="singleModeFreeUserItem"></param>
        /// <param name="useNum"></param>
        /// <param name="callback"></param>
        public void SendSingleModeFreeItemUseRequest(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, System.Action callback = null)
        {
            // ユーザーのアイテム所持数を取得
            var currentUserItemNum = 0;
            var userItem = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree.GetUserItem(singleModeFreeUserItem.item_id);
            if (userItem != null)
            {
                currentUserItemNum = userItem.num;
            }

            SingleModeFreeAPI.SendSingleModeFreeItemUseRequest(singleModeFreeUserItem.item_id, useNum, currentUserItemNum, () =>
            {
                callback?.Invoke();
            });
        }

        #endregion
    }

    /// <summary>
    /// ショップView
    /// </summary>
    public sealed class SingleModeScenarioFreeShopView : ViewBase
    {
        /// <summary>
        /// セリフと吹き出し
        /// </summary>
        [field: SerializeField, RenameField]
        public PartsSingleModeScenarioFreeShopCharaMessage CharaMessage { get; private set; }

        [SerializeField]
        private PartsSingleModeScenarioFreeShopList _partsScenarioFreeShopList = null;
        public PartsSingleModeScenarioFreeShopList PartsScenarioFreeShopList => _partsScenarioFreeShopList;

        /// <summary>
        /// 所持アイテムボタン
        /// </summary>
        [SerializeField]
        private PartsSingleModeScenarioFreeUserItemButton _userItemButton = null;
        public PartsSingleModeScenarioFreeUserItemButton UserItemButton => _userItemButton;

        /// <summary>
        /// 能力詳細ボタン
        /// </summary>
        [SerializeField]
        private ButtonCommon _detailButton = null;
        public ButtonCommon DetailButton => _detailButton;

        [SerializeField]
        private CanvasGroup _detailButtonCanvasGroup = null;
        public CanvasGroup DetailButtonCanvasGroup => _detailButtonCanvasGroup;

    }

    /// <summary>
    /// ショップViewのコントローラ
    /// </summary>
    public sealed class SingleModeScenarioFreeShopViewController : ViewControllerBase<SingleModeScenarioFreeShopView>
    {

        #region Member

        private SingleModeScenarioFreeShopViewModel _singleModeScenarioFreeShopViewModel = null;
        private SingleModeScenarioFreeShopViewModel Model
        {
            get
            {
                if (_singleModeScenarioFreeShopViewModel == null)
                {
                    _singleModeScenarioFreeShopViewModel = new SingleModeScenarioFreeShopViewModel();
                }
                return _singleModeScenarioFreeShopViewModel;
            }
        }

        private PartsSingleModeScenarioFreeShopHeader _partsSingleModeScenarioFreeShopHeader = null;

        private bool _isPlayUseItemAnimation = false;

        #endregion

        #region Override ViewControllerBase

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // 背景
            register.RegisterPathWithoutInfo(Model.BackgroundPath);
            // 環境設定
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_FREE_SHOP_ENV_PARAM);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_FREE_SHOP_SUMMER_ENV_PARAM);
            // オーディオ
            SingleModeUtils.RegisterDownloadSkillGetSe(register);
            // セリフ
            PartsShopTopCharaMessage.RegisterDownload(register);
            // ヘッダー
            PartsSingleModeScenarioFreeShopHeader.RegisterDownload(register);

            // 所持アイテムリスト
            DialogSingleModeScenarioFreeUserItemList.RegisterDownload(register);

            // セールエフェクト
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_FREE_SHOP_SALE_EFFECT);
        }

        /// <summary>
        /// BGM
        /// </summary>
        public override AudioId GetDynamicBgmId()
        {
            return WorkDataManager.Instance.SingleMode.GetCurrentTurnBgmId();
        }
        public override AudioManager.CueSheetCueNameInfo GetDynamicBgmCueInfo()
        {
            return WorkDataManager.Instance.SingleMode.GetCurrentTurnBgmInfo();
        }


        /// <summary>
        /// 初期化シーケンス
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            DisableSingleModeSceneCamera();

            // 背景キャラクターを設定
            SetCharacterBg();

            // View関連のSetup
            InitializeViewSetupView();

            //既読判定更新
            SavePickupItemCheckedState();

            yield return base.InitializeView();
        }

        private void InitializeViewSetupView()
        {
            // ヘッダー表示設定
            UIManager.Instance.CreateSingleModeHeader();
            UIManager.SingleModeHeader.Setup();
            UIManager.SingleModeHeader.SetVisible(false); // タイトル以外OFF
            _partsSingleModeScenarioFreeShopHeader = PartsSingleModeScenarioFreeShopHeader.Create(_view.ContentsRoot, UIManager.UI_SORTING_LAYER_NAME);

            Model.CheckedShopItemIdList = new List<int>(SaveDataManager.Instance.SaveLoader.CheckedShopItemIdArray.Select(id => (int)id));

            // 購買部表示設定
            SetupPartsScenarioFreeShopList();

            // 所持アイテムボタン設定
            _view.UserItemButton.Setup(OpenDialogSingleModeScenarioFreeUserItemList);

            // 能力詳細ボタン
            _view.DetailButton.SetOnClick(OnClickDetailButton);
        }

        /// <summary>
        /// IN再生
        /// </summary>
        public override IEnumerator PlayInView()
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var workScenarioFree = workSingleMode.Character.WorkScenarioFree;

            // 全てを非表示にしておく
            _partsSingleModeScenarioFreeShopHeader.SetActiveWithCheck(false);
            _partsSingleModeScenarioFreeShopHeader.SetPartsSingleModeScenarioFreeShopUserCoinCanvasGroupAlpha(0);
            _view.UserItemButton.SetCanvasGroupAlpha(0);
            _view.DetailButtonCanvasGroup.alpha = 0;
            _view.PartsScenarioFreeShopList.SetSaleIconCanvasGroupAlpha(0);
            _view.PartsScenarioFreeShopList.SetLoopScrollCanvasGroupAlpha(0);


            var sequence = DOTween.Sequence();
            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() => 
            {
                // ヘッダー
                UIManager.SingleModeHeader.SetTitleHeader(TextId.SingleModeScenarioFree419005, DialogTutorialGuide.TutorialGuideId.SingleModeScenarioFreeShop);

                _partsSingleModeScenarioFreeShopHeader.SetActiveWithCheck(true);
                _partsSingleModeScenarioFreeShopHeader.PlayInHeader();

            });

            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                // 所持コイン
                _partsSingleModeScenarioFreeShopHeader.PlayInPartsSingleModeScenarioFreeShopUserCoin();
            });

            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                var animDataList = new List<ViewBase.InOutAnimationData>();

                // 所持アイテムボタン
                animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsInFade, _view.UserItemButton.gameObject));

                // 能力詳細ボタン
                animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsInFade, _view.DetailButton.gameObject));

                // セール表示
                if (_singleModeScenarioFreeShopViewModel.IsSale)
                {
                    animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsInFadeFromLeft, _view.PartsScenarioFreeShopList.SaleIconObj));
                }
                UIUtil.PlayInViewParts(animDataList, null, null, 0);
            });


            var playInPartsScenarioFreeShopList = false;
            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                // リストフェードイン
                _view.PartsScenarioFreeShopList.PlayInLoopScroll(() =>
                {
                    // フェードインが終了したらリスト全アイテムを表示
                    _view.PartsScenarioFreeShopList.SetLoopScrollCanvasGroupAlpha(1);
                    playInPartsScenarioFreeShopList = true;
                });
            });

            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL * 6);
            sequence.AppendCallback(() =>
            {
                // チュートリアルが未読の場合はボイスを再生しない
                if (DialogTutorialGuide.IsAlreadyRead(DialogTutorialGuide.TutorialGuideId.SingleModeScenarioFreeShop))
                {
                    // 吹き出しボイス再生
                    PlayCharaMessageShopEnter();
                }
            });

            yield return new WaitUntil(() => playInPartsScenarioFreeShopList);

            // 最後にショップに遷移したターンを保存
            workScenarioFree.LastShopOpenTurn = workSingleMode.GetCurrentTurn();

            yield return base.PlayInView();
        }

        public override float GetBackButtonAnimationDelayTime()
        {
            float BACK_BUTTON_ANIMATION_DELAY_TIME = 0.3f;
            return BACK_BUTTON_ANIMATION_DELAY_TIME;
        }

        public override void BeginView()
        {
            // チュートリアルTips表示
            DialogTutorialGuide.PushDialogWithReadCheck(DialogTutorialGuide.TutorialGuideId.SingleModeScenarioFreeShop, ()=> 
            {
                // 吹き出しボイス再生
                // すでに再生中なら処理しない
                if (_view.CharaMessage.IsPlaying == false)
                {
                    PlayCharaMessageShopEnter();
                }
            });
        }


        /// <summary>
        /// 画面終了時処理
        /// </summary>
        public override IEnumerator EndView()
        {
            _view.CharaMessage.Stop();
            _view.CharaMessage.SetEnable(false);

            var sequence = DOTween.Sequence();
            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                // ヘッダー
                _partsSingleModeScenarioFreeShopHeader.PlayOutHeader();

            });

            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                // 所持コイン
                _partsSingleModeScenarioFreeShopHeader.PlayOutPartsSingleModeScenarioFreeShopUserCoin();
            });

            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                var animDataList = new List<ViewBase.InOutAnimationData>();

                // 所持アイテムボタン
                animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsOutMoveAndFade, _view.UserItemButton.gameObject));

                // 能力詳細ボタン
                animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsOutMoveAndFade, _view.DetailButton.gameObject));

                // セール表示
                if (_singleModeScenarioFreeShopViewModel.IsSale)
                {
                    animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsOutFadeFromLeft, _view.PartsScenarioFreeShopList.SaleIconObj));
                }
                UIUtil.PlayInViewParts(animDataList, null, null, 0);
            });


            var playOutPartsScenarioFreeShopList = false;
            sequence.AppendInterval(UIUtil.VIEW_IN_PARTS_INTERVAL);
            sequence.AppendCallback(() =>
            {
                // セリフ吹き出しハケ
                _view.CharaMessage.Out();

                // リストフェードアウト
                _view.PartsScenarioFreeShopList.PlayOutLoopScroll(() =>
                {
                    playOutPartsScenarioFreeShopList = true;
                });
            });
            yield return new WaitUntil(() => playOutPartsScenarioFreeShopList);

            yield return base.EndView();
        }

        private void SavePickupItemCheckedState()
        {
            var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;
            if(workScenarioFree.PickUpItemInfoArray.IsNullOrEmpty())
            {
                return;
            }
            var checkedIdList = SaveDataManager.Instance.SaveLoader.CheckedShopItemIdArray.ToList();
            //現在のショップにあるアイテムを一通り表示済みとして登録する
            foreach(var item in workScenarioFree.PickUpItemInfoArray)
            {
                if(!checkedIdList.Contains(item.shop_item_id))
                {
                    checkedIdList.Add(item.shop_item_id);
                }
            }

            SaveDataManager.Instance.SaveLoader.CheckedShopItemIdArray = checkedIdList.ToArray();
            SaveDataManager.Instance.Save();
        }

        public override IEnumerator FinalizeView()
        {
            yield return base.FinalizeView();
        }


        /// <summary>
        /// 戻るボタン押下時のコールバック
        /// </summary>
        public override void OnClickBackButton()
        {
            //アイテム使用演出中はバックキーで戻らないようにする
            if(!_isPlayUseItemAnimation)
            {
                SceneManager.Instance.SubtractionView();
            }
            else
            {
                UIUtil.ShowNotificationBackKey();
            }
        }

        /// <summary>
        /// OSのバックキー押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            OnClickBackButton();
        }

        #endregion

        /// <summary>
        /// シーンカメラを無効
        /// </summary>
        private void DisableSingleModeSceneCamera()
        {
            var sceneController = GetSceneController<SingleModeSceneController>();
            if (sceneController != null)
            {
                sceneController.SetCameraEnable(false);
            }
        }


        /// <summary>
        /// 背景を設定
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="dressId"></param>
        private void SetCharacterBg()
        {
            BGManager.Instance.CharacterBg.Setup(Model.EnvPath, Model.BackgroundPath, GameDefine.TAZUNA_CHARA_ID, GameDefine.TAZUNA_DRESS_ID);
            if (BGManager.Instance.CharacterBg.Model != null)
            {
                _view.CharaMessage.SetModel(BGManager.Instance.CharacterBg.Model, false);
            }

            var workSingleMode = WorkDataManager.Instance.SingleMode;
            if (workSingleMode.IsSummerCampTurn())
            {
                // 夏合宿中の設定
                Vector3 SUMMER_BG_POSITION = new Vector3(-0.1f, 2.438314f, 20f);
                BGManager.Instance.CharacterBg.SetBgPosition(SUMMER_BG_POSITION);
            }
        }


        /// <summary>
        /// 能力詳細
        /// </summary>
        private void OnClickDetailButton()
        {
            DialogSingleModeMainCharacterDetail.Open(WorkDataManager.Instance.SingleMode.Character);
        }

        #region PlayCharaMessage

        private IEnumerator _coroutinePlayCharaMessage = null;


        private void PlayCharaMessage(Queue<PartsSingleModeScenarioFreeShopCharaMessage.Trigger> charaMessageTriggerQueue)
        {
            // セリフ再生
            _coroutinePlayCharaMessage = CoroutinePlayCharaMessage(charaMessageTriggerQueue);
            _view.StartCoroutine(_coroutinePlayCharaMessage);
        }

        /// <summary>
        /// 吹き出しセリフ再生
        /// </summary>
        private IEnumerator CoroutinePlayCharaMessage(Queue<PartsSingleModeScenarioFreeShopCharaMessage.Trigger> charaMessageTriggerQueue)
        {
            // ダイアログ表示処理と同時に呼ばれたとき用に待つ
            yield return 0;

            // ダイアログ表示中は待つ
            yield return new WaitWhile(() => DialogManager.IsShowDialog);

            // 再生中の音声を停止
            _view.CharaMessage.Stop();

            // Queueに積まれたセリフ再生を処理
            while (charaMessageTriggerQueue.Any())
            {
                // セリフ再生
                var trigger = charaMessageTriggerQueue.Dequeue();
                _view.CharaMessage.Play(trigger);

                // セリフ終了を待つ
                yield return new WaitWhile(() => _view.CharaMessage.IsPlaying);
            }

            _coroutinePlayCharaMessage = null;
        }


        /// <summary>
        /// 購買部遷移時セリフ再生
        /// </summary>
        private void PlayCharaMessageShopEnter()
        {
            var charaMessageTriggerQueue = new Queue<PartsSingleModeScenarioFreeShopCharaMessage.Trigger>();

            if (Model.IsSoldOut)
            {
                // 売り切れ
                charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ShopSoldOutItem);
                PlayCharaMessage(charaMessageTriggerQueue);
                return;
            }

            if (Model.IsSale)
            {
                // セール発生中
                charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ScenarioFreeShopEnterSale);
                PlayCharaMessage(charaMessageTriggerQueue);
                return;
            }

            if (Model.IsLineupUpdateFirst)
            {
                // ラインナップ更新
                charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ScenarioFreeShopEnterLineupUpdate);
                PlayCharaMessage(charaMessageTriggerQueue);
                return;
            }

            charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ScenarioFreeShopEnter);
            PlayCharaMessage(charaMessageTriggerQueue);
        }

        /// <summary>
        /// アイテム交換時セリフ再生
        /// </summary>
        private void PlayCharaMessageItemExchange()
        {
            var charaMessageTriggerQueue = new Queue<PartsSingleModeScenarioFreeShopCharaMessage.Trigger>();

            charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ScenarioFreeShopExchange);

            if (Model.IsSoldOut)
            {
                // 売り切れセリフ登録
                charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ShopSoldOutItem);
            }

            PlayCharaMessage(charaMessageTriggerQueue);
        }

        /// <summary>
        /// アイテム交換＋アイテム使用時セリフ再生
        /// </summary>
        private void PlayCharaMessageItemExchangeUseItem()
        {
            var charaMessageTriggerQueue = new Queue<PartsSingleModeScenarioFreeShopCharaMessage.Trigger>();

            charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ScenarioFreeShopExchangeUseItem);

            if (Model.IsSoldOut)
            {
                // 売り切れセリフ登録
                charaMessageTriggerQueue.Enqueue(PartsSingleModeScenarioFreeShopCharaMessage.Trigger.ShopSoldOutItem);
            }

            PlayCharaMessage(charaMessageTriggerQueue);
        }

        #endregion PlayCharaMessage


        #region PartsScenarioFreeShopList
        private void SetupPartsScenarioFreeShopList()
        {
            var shopListItemModelList = Model.ShopListItemModelList;
            _view.PartsScenarioFreeShopList.Setup(shopListItemModelList, OnClickShopListItem);
        }

        private void OnClickShopListItem(SingleModeFreePickUpItem singleModeFreePickUpItem, DialogCommon dialogSingleModeScenarioFreeShopExchangeConfirm)
        {
            // 実行中のセリフ再生処理を停止
            if (_coroutinePlayCharaMessage != null)
            {
                _view.StopCoroutine(_coroutinePlayCharaMessage);
                _coroutinePlayCharaMessage = null;
            }

            // 交換API実行
            Model.SendSingleModeFreeItemExchangeRequest(singleModeFreePickUpItem, 1, () => CallbackSendSingleModeFreeItemExchangeRequest(singleModeFreePickUpItem.item_id, singleModeFreePickUpItem.shop_item_id, 1, dialogSingleModeScenarioFreeShopExchangeConfirm));
        }

        private void CallbackSendSingleModeFreeItemExchangeRequest(int itemId, int shopItemId, int exchangeNum, DialogCommon dialogSingleModeScenarioFreeShopExchangeConfirm)
        {
            // 交換確認ダイアログを閉じる
            if(dialogSingleModeScenarioFreeShopExchangeConfirm != null)
            {
                dialogSingleModeScenarioFreeShopExchangeConfirm.Close();
            }

            var dialogSingleModeScenarioFreeShopExchange = new DialogSingleModeScenarioFreeShopExchangeModel(itemId,
                                                                                                             shopItemId,
                                                                                                             exchangeNum,
                                                                                                             DialogSingleModeScenarioFreeShopExchangeModel.ShopExchangeDialogType.Complete);

            // 完了ダイアログを表示
            DialogSingleModeScenarioFreeShopExchange.Open(dialogSingleModeScenarioFreeShopExchange, (dialog) => OnUseItemExchangeCompleteDialog(dialog, itemId));

            // リスト表示更新
            SetupPartsScenarioFreeShopList();

            // ヘッダー情報を更新
            _partsSingleModeScenarioFreeShopHeader.Setup();

            // 所持アイテムボタン表示更新
            _view.UserItemButton.Setup();

            // 吹き出しセリフ再生
            PlayCharaMessageItemExchange();
        }

        /// <summary>
        /// アイテム交換完了ダイアログ アイテム使用
        /// </summary>
        /// <param name="dialog"></param>
        private void OnUseItemExchangeCompleteDialog(DialogCommon dialogSingleModeScenarioFreeShopExchange, int itemId)
        {
            // 交換完了ダイアログを閉じる
            dialogSingleModeScenarioFreeShopExchange.Close();

            // アイテム使用確認ダイアログ表示
            var dialogSingleModeScenarioFreeUseItemConfirmModel = new DialogSingleModeScenarioFreeUseItemConfirmModel(itemId);
            DialogSingleModeScenarioFreeUseItemConfirm.Open(dialogSingleModeScenarioFreeUseItemConfirmModel, (dialog, useNum) =>
            {
                OnClickUseItemExchangeCompleteDialog(dialogSingleModeScenarioFreeUseItemConfirmModel.UserItem, useNum, dialog);
            });
        }

        private void OnClickUseItemExchangeCompleteDialog(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, DialogCommon dialogItemUseConfirm)
        {
            // 実行中のセリフ再生処理を停止
            if (_coroutinePlayCharaMessage != null)
            {
                _view.StopCoroutine(_coroutinePlayCharaMessage);
                _coroutinePlayCharaMessage = null;
            }

            // アイテム使用API実行
            Model.SendSingleModeFreeItemUseRequest(singleModeFreeUserItem,
                                                   useNum,
                                                   () => CallbackSendSingleModeFreeItemUseRequest(singleModeFreeUserItem, 1, dialogItemUseConfirm));
        }

        private void CallbackSendSingleModeFreeItemUseRequest(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, DialogCommon dialogItemUseConfirm)
        {
            // 使用確認ダイアログを閉じる
            dialogItemUseConfirm.Close();

            // 所持アイテムボタン表示更新
            _view.UserItemButton.Setup();

            // リスト表示更新
            SetupPartsScenarioFreeShopList();

            _isPlayUseItemAnimation = true;
            // アイテム使用演出再生
            PartsSingleModeScenarioFreeUseItemPerformance.PlayUseItemPerformance(_view.ContentsRoot,
                                                                                 _partsSingleModeScenarioFreeShopHeader.HpGauge,
                                                                                 _partsSingleModeScenarioFreeShopHeader.MotivationButton,
                                                                                 singleModeFreeUserItem,
                                                                                 useNum,
                                                                                 () => CallbackPlayUseItemPerformance(false, true));

        }

        #endregion PartsScenarioFreeShopList


        #region DialogSingleModeScenarioFreeUserItemList

        private void OpenDialogSingleModeScenarioFreeUserItemList()
        {
            DialogSingleModeScenarioFreeUserItemList.Open(OnClickUserItem);
        }

        private void OnClickUserItem(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, DialogCommon dialogSingleModeScenarioFreeUserItemList, DialogCommon dialogItemUseConfirm)
        {
            // アイテム使用API実行
            Model.SendSingleModeFreeItemUseRequest(singleModeFreeUserItem, useNum, () => CallbackSendSingleModeFreeItemUseRequest(singleModeFreeUserItem, 1, dialogSingleModeScenarioFreeUserItemList, dialogItemUseConfirm));
        }

        private void CallbackSendSingleModeFreeItemUseRequest(SingleModeFreeUserItem singleModeFreeUserItem, int useNum, DialogCommon dialogSingleModeScenarioFreeUserItemList, DialogCommon dialogItemUseConfirm)
        {
            // ダイアログを閉じる
            dialogItemUseConfirm.Close();
            dialogSingleModeScenarioFreeUserItemList.Close();

            // 所持アイテムボタン表示更新
            _view.UserItemButton.Setup();

            // リスト表示更新
            SetupPartsScenarioFreeShopList();

            _isPlayUseItemAnimation = true;

            // アイテム使用演出再生
            PartsSingleModeScenarioFreeUseItemPerformance.PlayUseItemPerformance(_view.ContentsRoot,
                                                                                 _partsSingleModeScenarioFreeShopHeader.HpGauge,
                                                                                 _partsSingleModeScenarioFreeShopHeader.MotivationButton,
                                                                                 singleModeFreeUserItem,
                                                                                 useNum,
                                                                                 () => CallbackPlayUseItemPerformance(true, false));
        }

        #endregion


        private void CallbackPlayUseItemPerformance(bool openItemList, bool playCharaMessage)
        {
            _isPlayUseItemAnimation = false;

            // 所持アイテムダイアログを表示
            if (openItemList)
            {
                DialogSingleModeScenarioFreeUserItemList.Open(OnClickUserItem);
            }

            // 吹き出しセリフ再生登録
            if (playCharaMessage)
            {
                PlayCharaMessageItemExchangeUseItem();
            }
        }

    }

}
