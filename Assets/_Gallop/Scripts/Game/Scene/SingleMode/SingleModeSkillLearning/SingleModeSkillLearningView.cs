using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using Object = UnityEngine.Object;

namespace Gallop
{
    public sealed class SingleModeSkillLearningView : ViewBase
    {
        /// <summary>スキルリスト</summary>
        [field: SerializeField, RenameField]
        public ScrollRectCommon ScrollRect { get; private set; }

        /// <summary>所持スキルリスト要素</summary>
        [field: SerializeField, RenameField]
        public PartsSingleModeSkillLearningListItem Item { get; private set; }
        /// <summary>所持スキルポイント</summary>
        [field: SerializeField, RenameField]
        public TextCommon SkillPointText { get; private set; }

        /// <summary>能力詳細ボタン</summary>
        [field: SerializeField, RenameField]
        public ButtonCommon DetailButton { get; private set; }

        /// <summary>決定ボタン</summary>
        [field: SerializeField, RenameField]
        public ButtonCommon DecideButton { get; private set; }

        /// <summary>リセットボタン</summary>
        [field: SerializeField, <PERSON>ameField]
        public ButtonCommon ResetButton { get; private set; }

        /// <summary> アニメーション対象リスト </summary>
        [field: SerializeField, RenameField]
        public GameObject[] AnimationTargetList { get; private set; }
        /// <summary> アニメーション対象下部 </summary>
        [field: SerializeField, RenameField]
        public GameObject AnimationTargetBottom { get; private set; }
        
        //--------------------------------------------------------
        [field: Header("チュートリアル:スキルPt")]
        [field: SerializeField, RenameField]
        public RectTransform TutorialStep1 { get; private set; } = null;
    }

    public sealed class SingleModeSkillLearningViewController : ViewControllerBase<SingleModeSkillLearningView>
    {
        private const int BG_ID = 15;
        private const int BG_SUB_ID = 110;
        private readonly string BG_PATH = ResourcePath.GetBackgroundPath(BG_ID, BG_SUB_ID);
        private readonly string ENV_PATH = ResourcePath.SINGLE_MODE_SKILL_LEARNING_VIEW_ENV_PATH;

        public class SkillInfo
        {
            private List<PartsSingleModeSkillLearningListItem.Info> _skillList =
                new List<PartsSingleModeSkillLearningListItem.Info>();

            public List<PartsSingleModeSkillLearningListItem.Info> SkillList => _skillList;

            /// <summary>
            /// データ追加関数（再帰）
            /// </summary>
            /// <param name="skillId"></param>
            public void AddInfo(int skillId, bool recursive = true)
            {
                var workChara = WorkDataManager.Instance.SingleMode.Character;
                var mdm = MasterDataManager.Instance;
                var masterSkillData = mdm.masterSkillData;
                var masterSingleModeSkillNeedPoint = mdm.masterSingleModeSkillNeedPoint;

                var skillData = masterSkillData.Get(skillId);
                if (skillData == null)
                {
                    Debug.LogError("Not Found Skill! SkillId:" + skillId);
                    return;
                }

                var masterNeedPoint = masterSingleModeSkillNeedPoint.Get(skillId);

                // 習得状況
                var isGet = workChara.AcquiredSkillList.Exists(a =>
                {
                    if (a.MasterId == skillId) return true; //習得済み
                    if (skillData.GroupId == a.MasterData.GroupId)
                    {
                        if (skillData.GroupRate < a.MasterData.GroupRate) return true;// 上位スキル習得済みなら習得済み
                    }
                    return false;
                });
                // Badスキルを所持している場合は打ち消しを行えるようにする
                if (skillData.GroupRate < 0)
                {
                    isGet = false;
                }
                _skillList.Add(new PartsSingleModeSkillLearningListItem.Info(skillData, masterNeedPoint, isGet, workChara));

                // 再帰確認しない場合はここで終了
                if (recursive == false) return;
                
                // ノーマルスキル
                if (PartsSingleModeSkillLearningListItem.IsRareSkill(skillData) == false)
                {
                    // グループの上位互換がノーマルなら派生習得可能
                    var skillGroupList = mdm.masterSkillData.GetListWithGroupIdOrderByIdAsc(skillData.GroupId);
                    skillGroupList.Sort((a, b) => a.GradeValue - b.GradeValue);
                    foreach (var groupSkill in skillGroupList)
                    {
                        if (skillData.GroupRate >= groupSkill.GroupRate) continue;// 下位互換は除外
                        if (PartsSingleModeSkillLearningListItem.IsRareSkill(groupSkill)) continue;// レアは除外
                        AddInfo(groupSkill.Id, false);
                    }
                }
            }

            /// <summary>
            /// 選択中かどうか
            /// </summary>
            /// <returns></returns>
            public bool IsSelected()
            {
                return _skillList.Exists(skill => skill.IsSelected);
            }

            /// <summary>
            /// 選択中か or レベル変更中か
            /// </summary>
            public bool IsSelectedOrLevelChange()
            {
                return IsSelected();                
            }

            /// <summary>
            /// 選択中全解除
            /// </summary>
            public void ClearSelected()
            {
                _skillList.ForEach(info =>
                {
                    info.IsSelected = false;
                    info.ChangeLevel = info.Level; // レベル変更戻す
                });
            }
            
            /// <summary>
            /// 選択中のスキル習得に必要なスキルポイントを取得する
            /// </summary>
            /// <returns></returns>
            public int GetNeedSkillPoint()
            {
                //通常スキルの場合
                int value = 0;
                _skillList.ForEach(skill =>
                {
                    if (skill.IsSelected && !skill.IsGet)
                    {
                        value += skill.NeedPoint;
                    }
                });
                return value;
            }

            public bool IsEnableDecide()
            {
                return IsSelectedOrLevelChange();
            }

            /// <summary>
            /// 表示優先度を取得（ソートに使用）
            /// </summary>
            /// <returns></returns>
            public int GetDispOrder()
            {
                if (_skillList.Count == 0)
                {
                    //来ないはずだけど一応
                    return 0;
                }

                var skillId = _skillList[0].SkillId;
                
                // 育成チュートリアルで習得させたいスキルの表示位置を最上段へ
                if (TutorialSingleMode.IsTutorial)
                {
                    var tutorialSkillId = TutorialSingleMode.GetSkillLeaningSkillId();
                    if (skillId == tutorialSkillId)
                    {
                        return 0;
                    }
                }

                var masterSkill = MasterDataManager.Instance.masterSkillData.Get(skillId);

                if (masterSkill == null)
                {
                    //来ないはずだけど一応
                    return 0;
                }

                return masterSkill.DispOrder;
            }

        }// end class SkillInfo

        private List<SkillInfo> _skillInfoList = new List<SkillInfo>();
        private readonly List<PartsSingleModeSkillLearningListItem> _itemList = new List<PartsSingleModeSkillLearningListItem>();
        /// <summary>
        /// アニメーション用のRectTransformリスト
        /// </summary>
        private readonly List<RectTransform> _itemRectTransformList = new List<RectTransform>();
        public int RemainingPoint { get; private set; }

        DialogCommon _isSelectedDialog = null;

        #region ViewController

        /// <summary>
        /// ダウンロード対象リソースの登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // 背景
            register.RegisterPathWithoutInfo(BG_PATH);
            // 環境設定
            register.RegisterPathWithoutInfo(ENV_PATH);
            // オーディオ
            SingleModeUtils.RegisterDownloadSkillGetSe(register);
            // 育成ヘッダー
            PartsSingleModeCommonHeader.RegisterDownload(register);
        }

        /// <summary>
        /// ビュー初期化
        /// </summary>
        public override IEnumerator InitializeView()
        {
            // 育成共通ヘッダー
            UIManager.Instance.CreateSingleModeHeader();
            UIManager.SingleModeHeader.Setup();
            UIManager.SingleModeHeader.SetVisible(false); // タイトル以外OFF
            // 育成リザルトでなければ育成共通フッターあり
            if (IsResultState() == false)
            {
                UIManager.Instance.CreateSingleModeFooter();
            }
            var sceneController = GetSceneController<SingleModeSceneController>();
            if (sceneController != null)
            {
                sceneController.SetCameraEnable(false);　// CharacterBgを使うのでシーンカメラは無効
            }

            _view.ResetButton.SetOnClick(OnClickResetButton);
            _view.DecideButton.SetOnClick(OnClickDecideButton);
            _view.DetailButton.SetOnClick(OnClickDetailButton);

            var workData = WorkDataManager.Instance.SingleMode;
            var workCharaData = workData.Character;
            //Top画面の衣装と合わせる
            var dressId = workCharaData.RaceDressId;
            //季節に合わせて衣装を変える
            dressId = SingleModeMainViewController.GetPreferredDressId(workData, dressId);

            var charaId = workData.Character.CharaId;
            BGManager.Instance.CharacterBg.Setup(ENV_PATH, BG_PATH, charaId, dressId);
            BGManager.Instance.CharacterBg.SetupDefaultPosition(); // デフォルト（完了画面からの遷移時前位置が調整されている）
            BGManager.Instance.CharacterBg.Model.SetTouchCallbackDefault(); //触ったらリアクション
            Setup();

            // CharacterBg内で用意したモデルのCySpringが落ち着くまで1フレーム待機する
            yield return null;
        }

        public override void BeginView()
        {
            if (TutorialSingleMode.IsTutorial)
            {
                // 育成チュートリアル
                var skillId = TutorialSingleMode.GetSkillLeaningSkillId(); // 習得対象のスキルID
                var item = _itemList.Find(a => a.GetSkillId() == skillId);// リスト内から対象スキル検索
                if (item == null)
                {
                    Debug.LogError($"チュートリアルで習得するスキルがスキルリストにありませんskillId = {skillId}");
                    return;
                }
                TutorialSingleMode.SetGuideOnSkillLearningView(_view, item);
            }
        }


        /// <summary>
        /// BGM
        /// </summary>
        public override AudioId GetDynamicBgmId()
        {
            // 育成リザルトの場合BGMはリザルト用
            if (IsResultState())
            {
                return AudioId.BGM_SINGLE_MODE_CONFIRM_COMPLETE;
            }

            //アオハル杯のレース開催トップ画面から遷移してきた場合GetCurrentTurnBgmInfo()を使用
            if (WorkDataManager.Instance.SingleMode.GetScenarioId() == SingleModeDefine.ScenarioId.TeamRace &&
                SceneManager.Instance.GetPrevViewId() == SceneDefine.ViewId.SingleModeScenarioTeamRaceTop)
            {
                return AudioId.INVALID;
            }

            return WorkDataManager.Instance.SingleMode.GetCurrentTurnBgmId();
        }
        public override AudioManager.CueSheetCueNameInfo GetDynamicBgmCueInfo()
        {
            return WorkDataManager.Instance.SingleMode.GetCurrentTurnBgmInfo();
        }

        //ロードタイプ指定
        public override void OverrideDynamicNowLoadingType(ref NowLoading.Type loadingType, SceneDefine.ViewId prevViewID)
        {
            if(prevViewID == SceneDefine.ViewId.SingleModeScenarioTeamRaceTop)
            {
                //チーム対抗戦レーストップからの遷移の場合蹄鉄エフェクトを出す
                loadingType = NowLoading.Type.WhiteOutWithHorseShoe;
            }
        }

        /// <summary>
        /// IN再生
        /// </summary>
        public override IEnumerator PlayInView()
        {
            UIManager.SingleModeHeader.SetTitleHeader(TextId.SingleMode0214, DialogTutorialGuide.TutorialGuideId.SingleModeSkillLearning);
            if (!TutorialSingleMode.IsTutorial)
            {
                // チュートリアル以外の初回育成時にガイドを開く
                DialogTutorialGuide.PushDialogWithReadCheck(DialogTutorialGuide.TutorialGuideId.SingleModeSkillLearning);
            }

            // アニメーション再生
            var complete = false;

            // 全体
            var animDataList = new List<ViewBase.InOutAnimationData>();
            foreach (var gameObject in _view.AnimationTargetList)
            {
                if (gameObject == null)
                {
                    continue;
                }

                animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsInFade, gameObject));
            }
            UIUtil.PlayInViewParts(animDataList, null, null, 0);

            // リストフェード
            LoopScroll.PlayInAnimation(GetActiveItemList(), () => complete = true, _view.AnimationTargetBottom);
            yield return new WaitUntil(() => complete);
        }

        /// <summary>
        /// スクロールビュー範囲内に描画されているオブジェクト取得
        /// </summary>
        private List<GameObject> GetActiveItemList()
        {
            var contentRect = _view.ScrollRect.content;
            var viewportRect = _view.ScrollRect.viewport;
            var viewportTopY = -contentRect.anchoredPosition.y;
            var viewportBotY = -contentRect.anchoredPosition.y - viewportRect.rect.height;
            const int ITEM_BALLOON_OFFSET = 32; // ヒントの吹き出しがリストアイテムよりはみ出しているのでそれを含める
            return _itemList.Where(item =>
                {
                    var itemTransform = item.transform;
                    var itemRectTransform = itemTransform as RectTransform;
                    var itemY = itemTransform.localPosition.y;
                    var itemTop = itemY + itemRectTransform.rect.yMax + ITEM_BALLOON_OFFSET;
                    var itemBot = itemY + itemRectTransform.rect.yMin;
                    if (viewportTopY < itemBot) return false;
                    if (viewportBotY > itemTop) return false;
                    return true;
                }).Select(i => i.gameObject).ToList();
        }


        /// <summary>
        /// 戻るボタンアニメの開始までの遅延時間取得（画面Inオブジェクト数で可変する） 
        /// </summary>
        public override float GetBackButtonAnimationDelayTime()
        {
            return GetActiveItemList().Count * UIUtil.VIEW_IN_PARTS_INTERVAL;
        }

        /// <summary>
        /// 画面OUT
        /// </summary>
        public override IEnumerator PlayOutView()
        {
            //対抗戦レーストップへ遷移する際にリスト要素ハケと同時にロード演出を表示
            if(SceneManager.Instance.GetNextViewId() == SceneDefine.ViewId.SingleModeScenarioTeamRaceTop)
            {
                NowLoading.Instance.Show(NowLoading.Type.WhiteOutWithHorseShoe);
            }
           
            // アニメーション再生
            var complete = false;

            _view.ScrollRect.StopMovement(); // スクロール中の慣性停止
            // リストフェード
            LoopScroll.PlayOutAnimation(GetActiveItemList(), () => complete = true, _view.AnimationTargetBottom);

            // 全体
            var animDataList = new List<ViewBase.InOutAnimationData>();
            foreach (var gameObject in _view.AnimationTargetList)
            {
                if (gameObject == null)
                {
                    continue;
                }

                animDataList.Add(new ViewBase.InOutAnimationData(TweenAnimation.PresetType.PartsOutFade, gameObject));
            }
            UIUtil.PlayOutViewParts(animDataList, null, null);

            if (_isSelectedDialog != null)
            {
                yield return new WaitUntil(() => _isSelectedDialog == null || (!_isSelectedDialog.IsOpen && !_isSelectedDialog.IsCloseAnimation));
            }

            yield return new WaitUntil(() => complete);
        }

        /// <summary>
        /// 戻るボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {
            // 選択中 or レベル変更 あるか
            var isSelected = _skillInfoList.Exists(info => info.IsSelectedOrLevelChange());

            if (isSelected)
            {
                // 戻っていいか確認する
                var dialogData = new DialogCommon.Data().SetSimpleTwoButtonMessage(
                    TextId.Common0009.Text(),
                    TextId.SingleMode0230.Text(),
                    onRight: dialog => { ChagePrevView(); },
                    leftTextId: TextId.Common0004,
                    rightTextId: TextId.Common0003);
                _isSelectedDialog = DialogManager.PushDialog(dialogData);
            }
            else
            {
                ChagePrevView();
            }
        }

        /// <summary>
        /// 前の画面に戻る
        /// </summary>
        private void ChagePrevView()
        {
            if (IsResultState())
            {
                // 育成リザルト
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.SingleModeConfirmComplete);
            }
            else if (SceneManager.Instance.GetPrevViewId() == SceneDefine.ViewId.SingleModeScenarioTeamRaceTop) 
            {
                //対抗戦レーストップ画面へ
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.SingleModeScenarioTeamRaceTop);
            }
            else
            {
                // 育成TOPから加算画面された前提で、前の画面に戻す
                SceneManager.Instance.SubtractionView();
            }
        }

        /// <summary>
        /// OSのバックキー押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            // 戻るボタンと同じ挙動
            OnClickBackButton();
        }

        #endregion

        #region Method

        /// <summary>
        /// 表示準備
        /// </summary>
        private void Setup()
        {
            Cleanup();
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            RemainingPoint = workChara.SkillPoint;

            var mdm = MasterDataManager.Instance;
            var masterAvailableSkillSet = mdm.masterAvailableSkillSet;

            var availableSkillSet = masterAvailableSkillSet.GetListWithAvailableSkillSetIdOrderByIdAsc(workChara.CardData.AvailableSkillSetId);
            // 素質レベル達成しているスキルIDをリストアップ
            var availableSkillIdList = availableSkillSet.Where(a => a.NeedRank <= workChara.TalentLevel).Select(a => a.SkillId).ToList();
            var skillIdList = availableSkillIdList;
            
            // 育成チュートリアルで習得させたいスキルが含まれてなければ挿入
            if (TutorialSingleMode.IsTutorial)
            {
                var tutorialSkillId = TutorialSingleMode.GetSkillLeaningSkillId();
                if (skillIdList.Contains(tutorialSkillId) == false)
                {
                    skillIdList.Add(tutorialSkillId);
                }
            }

            _skillInfoList = SingleModeUtils.CreateSkillInfoBySkillIdList(skillIdList);

            // レアスキルのスキルポイントを計算する
            SingleModeUtils.CalcNeedRareSkillPoint(_skillInfoList);

            // ソート
            _skillInfoList.Sort((a, b) =>
            {
                var orderA = a.GetDispOrder();
                var orderB = b.GetDispOrder();
                return orderA - orderB;
            });

            // リスト生成
            UIUtil.CreateScrollItem(_view.Item, _itemList, _skillInfoList, (item, data) =>
            {
                item.UpdateItem(data.SkillList, OnClickPlusListItem, OnClickMinusListItem, () => RemainingPoint);
            });
            
            // アニメーション対象をスクロールビュー内のものに限定するために整列
            LayoutRebuilder.ForceRebuildLayoutImmediate(_view.ScrollRect.content);

            //アニメーション用のリスト登録
            SetupAnimationRectTransformList();

            UpdateSkillPoint();
            UpdateDecideButton();
        }

        /// <summary>
        /// LoopScroll.Animation()で使用するためのRectTransformListを準備する
        /// </summary>
        private void SetupAnimationRectTransformList()
        {
            _itemRectTransformList.Clear();
            
            foreach (var item in _itemList)
            {
                _itemRectTransformList.Add(item.BaseImage.rectTransform);
            }
        }

        /// <summary>
        /// 育成リザルト中か
        /// </summary>
        private bool IsResultState()
        {
            return SceneManager.Instance.GetPrevViewId() == SceneDefine.ViewId.SingleModeConfirmComplete ||
                   SingleModeChangeViewManager.Instance.NowStateId == SingleModeChangeViewManager.SingleModeStateId.Result;
        }


        /// <summary>
        /// データリストから指定スキルのデータ取得（派生先も検索）
        /// </summary>
        private PartsSingleModeSkillLearningListItem.Info GetInfo(int skillId)
        {
            foreach (var skillInfo in _skillInfoList)
            {
                foreach (var info in skillInfo.SkillList)
                {
                    if (info.SkillId == skillId) return info;
                }
            }

            return null;
        }

        /// <summary>
        /// 決定ボタンが有効になるかどうか
        /// </summary>
        /// <returns></returns>
        private bool IsValidDecideButton()
        {
            return _skillInfoList.Exists(skillInfo => skillInfo.IsEnableDecide());
        }

        /// <summary>
        /// 決定ボタンの状態更新
        /// </summary>
        private void UpdateDecideButton()
        {
            // 選択あれば決定ボタン押せる
            var valid = IsValidDecideButton();
            _view.DecideButton.SetInteractable(valid);
            _view.DecideButton.SetNotificationMessage(valid ? string.Empty : TextId.SingleMode0231.Text());
        }

        /// <summary>
        /// 所持pt更新
        /// </summary>
        private void UpdateSkillPoint()
        {
            // 使用ポイント合計
            int usePoint = 0;
            _skillInfoList.ForEach(skillInfo =>
           {
               usePoint += skillInfo.GetNeedSkillPoint();
           });

            // 消費後のポイント更新
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var afterPoint = workChara.SkillPoint - usePoint;
            RemainingPoint = afterPoint;

            // スキルポイント
            _view.SkillPointText.text = afterPoint.ToString();

        }

        /// <summary>
        /// リスト要素プラスボタンタップ時
        /// </summary>
        private void OnClickPlusListItem(PartsSingleModeSkillLearningListItem item)
        {
            var info = item.GetTopInfo();
            info.IsSelected = true;

            OnSelectUpdate(info);
        }

        /// <summary>
        /// リスト要素マイナスボタンタップ時
        /// </summary>
        private void OnClickMinusListItem(PartsSingleModeSkillLearningListItem item)
        {
            var info = item.GetTopInfo();

            // すでに解除状態からさらに解除する場合、前提スキルを解除
            if (info.IsSelected == false)
            {
                var preInfo = item.GetPreInfo();
                if (preInfo != null) info = preInfo;
            }
            info.IsSelected = false;

            OnSelectUpdate(info);
        }

        /// <summary>
        /// リスト選択状態変更時の共通更新 
        /// </summary>
        private void OnSelectUpdate(PartsSingleModeSkillLearningListItem.Info info)
        {
            var groupId = info.MasterData.GroupId; // スキルグループ
            var groupRate = info.MasterData.GroupRate; // スキルレート
            // レアだった場合
            if (PartsSingleModeSkillLearningListItem.IsRareSkill(info.MasterData))
            {
                // 選択時
                if (info.IsSelected)
                {
                    // 下位互換スキルが他のリストアイテムに存在する場合、選択状態変更
                    foreach (var otherInfo in _skillInfoList)
                    {
                        foreach (var otherItemInfo in otherInfo.SkillList)
                        {
                            if (info == otherItemInfo) continue;                    // 自分除外
                            if (groupId != otherItemInfo.MasterData.GroupId) continue;    // グループ違い
                            if (groupRate < otherItemInfo.MasterData.GroupRate) continue; // 上位スキルは対象外
                            if (otherItemInfo.IsGet) continue; //取得済み
                            otherItemInfo.IsSelected = info.IsSelected;
                        }
                    }
                }
            }
            else
            {
                // ノーマルスキル

                // 選択解除時
                if (info.IsSelected == false)
                {
                    // 上位互換スキルが他のリストアイテムに存在する場合、選択状態解除（下位を解除して上位だけ習得はできないように）
                    foreach (var otherInfo in _skillInfoList)
                    {
                        foreach (var otherItemInfo in otherInfo.SkillList)
                        {
                            if (info == otherItemInfo) continue;                    // 自分除外
                            if (groupId != otherItemInfo.MasterData.GroupId) continue;    // グループ違い
                            if (groupRate > otherItemInfo.MasterData.GroupRate) continue; // 下位スキルは対象外
                            otherItemInfo.IsSelected = info.IsSelected;
                        }
                    }
                }
            }

            OnSelectUpdate();
        }
        private void OnSelectUpdate()
        {
            // レアスキルの習得必要ポイント合算値を再計算
            SingleModeUtils.CalcNeedRareSkillPoint(_skillInfoList);

            UpdateSkillPoint();

            UpdateDecideButton();

            foreach (var item in _itemList)
            {
                item.UpdateItem();
            }
        }

        /// <summary>
        /// 決定ボタン
        /// </summary>
        private void OnClickDecideButton()
        {
            List<GainSkillInfo> gainSkillInfoList = new List<GainSkillInfo>();
            _skillInfoList.ForEach(skillInfo =>
            {
                skillInfo.SkillList.ForEach(info =>
                {
                    if (info.IsSelected && !info.IsGet)
                    {
                        gainSkillInfoList.Add(new GainSkillInfo
                        {
                            skill_id = info.SkillId,
                            level = SkillDefine.SKILL_LEVEL_MIN
                        });
                    }
                });
            });

            // 獲得スキルを表示して画面を戻す
            _view.StartCoroutine(CoroutineSkillGetChangeView(gainSkillInfoList));
        }

        /// <summary>
        /// 能力詳細表示
        /// </summary>
        private void OnClickDetailButton()
        {
            DialogSingleModeMainCharacterDetail.Open(WorkDataManager.Instance.SingleMode.Character);
        }

        /// <summary>
        /// 獲得スキルを表示して画面を戻す
        /// </summary>
        private IEnumerator CoroutineSkillGetChangeView(List<GainSkillInfo> gainSkillList)
        {
            bool isDecide = false;
            bool isComplete = false;
            DialogSingleModeAcquisitionSkillList.PushDialog(gainSkillList,
                TextId.SingleMode0045, () => isComplete = true, () => isDecide = true);

            yield return new WaitUntil(() => isComplete);

            //確定しないなら、通信処理は行わない
            if (!isDecide)
            {
                yield break;
            }

            if (TutorialSingleMode.IsTutorial)
            {
                // 育成チュートリアル時、固定スキルを付与
                TutorialSingleMode.OnSkillLearning();
            }
            else
            {
                //スキル習得API
                isComplete = false;
                SingleModeAPI.SendGainSkills(gainSkillList.ToArray(), ()=> isComplete = true);
                yield return new WaitUntil(() => isComplete);
            }

            // 選択全解除
            ClearSelected();

            //リストを再設定する
            Setup();

            //スキル獲得SE
            PlaySkillGetSe(gainSkillList);
            
            if (TutorialSingleMode.IsTutorial)
            {
                // ダイアログ閉じ待ち
                yield return new WaitWhile(() => DialogManager.IsExistDialog);

                // チュートリアルショートストーリー再生
                TutorialSingleMode.PlayShortStory(TutorialSingleMode.GetShortStoryIdWhenSkillLearningAfter(), () =>
                {
                    // 戻るガイド
                    TutorialSingleMode.SetGuideOnSkillLearningComplete(_view);
                });
            }
            else
            {
                //獲得完了
                DialogSingleModeSkillLearningComplete.PushDialog(gainSkillList);
            }
        }

        /// <summary>
        /// スキル獲得SE。優先度：固有＞虹＞金＞ノーマル
        /// </summary>
        private void PlaySkillGetSe(List<GainSkillInfo> gainSkillList)
        {
            var masterSkillData = MasterDataManager.Instance.masterSkillData;
            if (gainSkillList.Exists(a => masterSkillData.Get(a.skill_id).IsLevelUp))
            {
                // 固有スキル
                SingleModeUtils.PlayUniqueSkillGetSe();
            }
            else
            {
                // 一番レアリティが高いSE
                var gainData = gainSkillList.OrderByDescending(a => masterSkillData.Get(a.skill_id).Rarity).FirstOrDefault();
                SingleModeUtils.PlaySkillGetSe(gainData.skill_id, true);
            }
        }

        /// <summary>
        /// リセットボタン
        /// </summary>
        private void OnClickResetButton()
        {
            ClearSelected();
            OnSelectUpdate();
        }

        /// <summary>
        /// 選択中全解除
        /// </summary>
        private void ClearSelected()
        {
            _skillInfoList.ForEach(skillInfo => skillInfo.ClearSelected());
        }

        #endregion

        /// <summary>
        /// 片付け
        /// </summary>
        private void Cleanup()
        {
            foreach (var item in _itemList)
            {
                Object.Destroy(item.gameObject);
            }
            _itemList.Clear();
        }

    }
}
