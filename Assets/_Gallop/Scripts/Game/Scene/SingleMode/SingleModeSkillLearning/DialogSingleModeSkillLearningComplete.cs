using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// スキル獲得完了ダイアログ
    /// </summary>
    public sealed class DialogSingleModeSkillLearningComplete : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField]
        private Transform _skillIconRoot;

        public static void PushDialog(List<GainSkillInfo> gainSkillList)
        {
            var copy = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_SINGLE_MODE_SKILL_LEARNING_COMPLETE));
            var component = copy.GetComponent<DialogSingleModeSkillLearningComplete>();
            var dialogData = component.CreateDialogData();
            
            dialogData.Title = TextId.SingleMode0228.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.FooterText = TextId.SingleMode0229.Text();

            DialogManager.PushDialog(dialogData);
            component.Setup(gainSkillList);
        }

        private void Setup(List<GainSkillInfo> gainSkillList)
        {
            var skillIconPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_SKILL_ICON_PATH);
            foreach (var skillInfo in gainSkillList)
            {
                CreateSkillIcon(skillInfo, skillIconPrefab);
            }
        }

        private void CreateSkillIcon(GainSkillInfo skillInfo, GameObject skillIconPrefab)
        {
            // スキルアイコン生成
            var skillIconObj = Instantiate(skillIconPrefab, _skillIconRoot);
            var skillIcon = skillIconObj.GetComponent<SkillIcon>();
            skillIcon.MyButton.enabled = false;
            skillIcon.Setup(skillInfo.skill_id, false);
            if (skillInfo.level > 1)
            {
                skillIcon.SetLevel(skillInfo.level);
            }
        }
    }
}