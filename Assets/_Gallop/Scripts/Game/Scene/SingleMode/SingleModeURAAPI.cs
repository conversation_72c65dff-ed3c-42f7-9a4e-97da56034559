using System;
using System.Collections;
using System.Collections.Generic;
using Cute.Http;

namespace Gallop
{
    public sealed class SingleModeURAAPI : ISingleModeAPIBase
    {

        /// <summary>
        /// 育成開始API
        /// </summary>
        public void SendStart(
            int scenarioId,
            SingleModeStartChara startChara,
            TpInfo tpInfo,
            int useTp,
            int currentMoney,
            Action<SingleModeStartChara, TpInfo, UserItem[], TrainedChara[], int, UserMission[], UserMission[]> onSendSuccess)
        {
            var req = new SingleModeStartRequest
            {
                start_chara = startChara,
                current_money = currentMoney,
                tp_info = tpInfo,
                use_tp = useTp,
            };
            req.Send(res =>
            {
                WorkDataManager.Instance.SingleMode.ClearAllPlayingData();
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSendSuccess(req.start_chara, res.data.tp_info, res.data.user_item_array, res.data.add_trained_chara_array, res.data.chara_info.card_id, res.data.mission_list, res.data.story_event_mission_list);
            });
        }

        /// <summary>
        /// 育成再開API
        /// </summary>
        public IEnumerator SendLoadAsync(
            Action<UserMission[],UserMission[]> onSendSuccess,
            Action<ErrorType, int> onSendError)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            yield return SingleModeSceneController.SendAsync<SingleModeLoadRequest, SingleModeLoadResponse>(
                new SingleModeLoadRequest(),
                res =>
                {
                    workSingleMode.Apply(res.data);
                    onSendSuccess?.Invoke(res.data.mission_list, res.data.story_event_mission_list);
                    // #58607 エラー時にフラグを下しているが、リトライ成功した時に備えてここでフラグON
                    workSingleMode.IsPlaying = true;
                }, onSendError);
        }

        /// <summary>
        /// コマンド実行API
        /// </summary>
        public IEnumerator SendExecCommandAsync(
            SingleModeDefine.CommandType commandType,
            TrainingDefine.TrainingCommandId commandId,
            int commandGroupId,
            int selectId,
            Action<SingleModeCommandResult, int> onSendSuccess,
            Action<ErrorType, int> onSendError)
        {
            var workSingleModeData = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeExecCommandRequest
            {
                command_type = (int)commandType,
                command_id = (int)commandId,
                command_group_id = commandGroupId,
                select_id = selectId,
                current_turn = workSingleModeData.GetCurrentTurn(),
                current_vital = workSingleModeData.Character.Hp
            };
            yield return SingleModeSceneController.SendAsync<SingleModeExecCommandRequest, SingleModeExecCommandResponse>(
                req,
                res =>
                {
                    workSingleModeData.ApplyCommandResponse(res.data);
                    onSendSuccess(res.data.command_result, res.data.chara_info.turn);
                }, onSendError);
        }

        /// <summary>
        /// イベント既読API
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="charaId"></param>
        /// <param name="gainId"></param>
        /// <param name="onSendSuccess"></param>
        public void SendCheckEvent(int eventId, int charaId, int gainId, Action onSendSuccess)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeCheckEventRequest
            {
                event_id = eventId,
                chara_id = charaId,
                choice_number = gainId,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
            };
            req.Send(res =>
            {
                workSingleMode.Apply(res.data);
                onSendSuccess.Invoke();
            });
        }


        private SingleModeAPI.OnSendFinishSuccessDataContainer CreateOnSendSuccessDataContainer(SingleModeFinishResponse.CommonResponse res)
        {
            return new SingleModeAPI.OnSendFinishSuccessDataContainer()
            {
                trainedChara = res.trained_chara,
                directoryCardArray = res.directory_card_array,
                trainedCharaId = res.trained_chara_id,
                charaInfo = res.chara_info,
                lovePointInfo = res.love_point_info,
                rewardItemInfo = res.reward_item_info,
                supportCardDataArray = res.support_card_data_array,
                limitedShopInfo = res.limited_shop_info,
                updateUserCharaInfo = res.update_user_chara_info,
                releaseItemFlag = res.release_item_flag,
                circlePoint = res.circle_point,
                newCharaProfileArray = res.new_chara_profile_array,
                campaignIdArray = res.campaign_id_array,
                scenarioRecordArray = res.scenario_record_array,
                scenarioRecordRewardArray = res.scenario_record_reward_array,
                storyEventInfo = res.story_event_info,
                difficultyDataSet = res.difficulty_data_set,
                transferEventInfo = res.transfer_event_info,
                TrainingChallengeResult = res.training_challenge_result,
            };
        }

        /// <summary>
        /// 育成終了API
        /// </summary>
        public void SendFinish(bool isForceDelete, int factorLotteryId, Action<SingleModeAPI.OnSendFinishSuccessDataContainer> onSendSuccess)
        {
            var req = new SingleModeFinishRequest
            {
                is_force_delete = isForceDelete,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                factor_lottery_id = factorLotteryId,
            };
            req.Send(response =>
            {
                //特別移籍のワークを更新
                WorkDataManager.Instance.TransferEventData.Update(response.data.transfer_event_info);
                onSendSuccess(CreateOnSendSuccessDataContainer(response.data));
            });
        }

        /// <summary>
        /// 因子選択画面API
        /// </summary>
        public void SendFactorSelect(Action<SingleModeFactorSelectCommon> onSuccess)
        {
            var req = new SingleModeFactorSelectRequest
            {
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
            };

            req.Send(response =>
            {
                onSuccess(response.data.single_mode_factor_select_common);
            });
        }

        /// <summary>
        /// 因子再抽選API
        /// </summary>
        public void SendFactorLottery(int lotteryCount, TpInfo tpInfo, int useTp, Action<SingleModeFactorLotteryCommon> onSuccess)
        {
            var req = new SingleModeFactorLotteryRequest
            {
                lottery_count = lotteryCount,
                tp_info = tpInfo,
                use_tp = useTp
            };

            req.Send(response =>
            {
                onSuccess(response.data.single_mode_factor_lottery_common);
            });
        }

        /// <summary>
        /// レース出走登録API
        /// </summary>
        public IEnumerator SendRaceEntryAsync(int programId, Action onSuccess)
        {
            var req = new SingleModeRaceEntryRequest
            {
                program_id = programId,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
            };
            yield return SingleModeSceneController.SendAsync<SingleModeRaceEntryRequest, SingleModeRaceEntryResponse>(
                req,
                res =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(res.data);
                    onSuccess?.Invoke();
                }
            );
        }

        /// <summary>
        /// レース終了（結果確定）
        /// </summary>
        public IEnumerator SendRaceEndAsync(Action onSuccess, Action<CharaRaceReward, LoginUserTrophyInfo, RaceRewardData, RewardSummaryInfo> onSendSuccess)
        {
            yield return SingleModeSceneController.SendAsync<SingleModeRaceEndRequest, SingleModeRaceEndResponse>(
                new SingleModeRaceEndRequest()
                {
                    current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
                },
                res =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(res.data);
                    onSendSuccess(res.data.race_reward_info, res.data.add_trophy_info, res.data.trophy_reward_info, res.data.reward_summary_info);
                });
        }

        /// <summary>
        /// レース退出API
        /// </summary>
        public void SendRaceOut(Action onSuccess)
        {
            var req = new SingleModeRaceOutRequest();
            req.current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            req.Send(res =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// コンティニューAPI
        /// </summary>
        public IEnumerator SendContinueAsync(int continueType, Action onSuccess)
        {
            var req = new SingleModeContinueRequest()
            {
                continue_type = continueType,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
            };
            yield return SingleModeSceneController.SendAsync<SingleModeContinueRequest, SingleModeContinueResponse>(
                req,
                response =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(response.data);
                    WorkDataManager.Instance.ItemData.Update(response.data.user_item);
                    onSuccess?.Invoke();
                });
        }

        /// <summary>
        /// スキル習得API
        /// </summary>
        public void SendGainSkills(GainSkillInfo[] gainSkillArray, Action onSuccess)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeGainSkillsRequest
            {
                gain_skill_info_array = gainSkillArray,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
            };
            req.Send(res =>
            {
                workSingle.ApplyCharacter(res.data.chara_info);
                workSingle.ApplyHomeInfo(res.data.home_info);
                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// 短縮設定変更API
        /// </summary>
        public void SendChangeShortCut(SingleModeDefine.EventShortCutType shortcutType, Action onSuccess)
        {
            var req = new SingleModeChangeShortCutRequest()
            {
                short_cut_state = (int)shortcutType,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
            };
            req.Send((res) =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSuccess?.Invoke();
            }, stallOneSecond : true);
        }

        /// <summary>
        /// ミニゲーム結果送信API
        /// </summary>
        public void SendMiniGameEnd(MinigameResult result, Action onSuccess)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeMinigameEndRequest()
            {
                result = result,
                current_turn = workSingle.GetCurrentTurn()
            };
            req.Send(res =>
            {
                //通信成功したなら保存不要
                SaveDataManager.Instance.SaveLoader.ClearLastCraneGameValues();
                SaveDataManager.Instance.Save();
                workSingle.Apply(res.data);
                onSuccess.Invoke();
            });
        }

        /// <summary>
        /// 育成レース開始リクエスト
        /// </summary>
        public void SendRaceStart(bool isShort, System.Action<string> onComplete)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var isShortValue = isShort ? 1 : 0;
            var currentTurn = workSingle.GetCurrentTurn();
            var reqRaceStart = new SingleModeRaceStartRequest();
            reqRaceStart.is_short = isShortValue; // 短縮レースフラグ（タスクキル再開地点がレースリザルトになるように）
            reqRaceStart.current_turn = currentTurn;
            HttpManager.Instance.Send<SingleModeRaceStartRequest, SingleModeRaceStartResponse>(
                reqRaceStart,
                res =>
                {
                    SingleModeSceneController.CreateRaceInfoByRaceStartInfo(res.data.race_start_info, res.data.race_scenario);
                    onComplete?.Invoke(res.data.race_scenario);
                });
        }

        /// <summary>
        /// レース結果保存リクエスト
        /// </summary>
        public IEnumerator SendSaveRaceResultAsync(RaceSimulateData simData, SingleModeRaceResult raceResult, System.Action onComplete)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeSaveRaceResultRequest
            {
                race_result = raceResult,
                race_scenario = simData.SerializeToCompressedBase64(false),
                auto_continue_num = RaceManager.RaceInfo.SingleRaceRetryCount,
                random_seed = RaceManager.RaceInfo.RandomSeed,
                current_turn = workSingle.GetCurrentTurn(),
            };
            //RaceEndはレース結果が確定（コンティニューしない事が確定）したタイミングで叩くように修正
            yield return SingleModeSceneController.SendAsync<SingleModeSaveRaceResultRequest, SingleModeSaveRaceResultResponse>(
                req,
                res => { onComplete?.Invoke(); }
            );
        }

        /// <summary>
        /// レース分析リクエスト
        /// </summary>
        public void SendAnalyzeRequest(int programId, int current_turn, int year, Action<RaceHorseData[]> onAnalyzed = null)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new RaceAnalyzeRequest()
            {
                program_id = programId,
                current_turn = current_turn,
            };
            Cute.Http.HttpManager.Instance.Send<RaceAnalyzeRequest, RaceAnalyzeResponse>(
                req,
                res =>
                {
                    // 馬番で昇順にソート。
                    var horseDataArray = res.data.race_horse_data_array;
                    RaceUtil.SortRaceHorseDataAsc(horseDataArray);
                    onAnalyzed(horseDataArray);
                });
        }

        /// <summary>
        /// 走法変更リクエスト
        /// </summary>
        public void SendChangeRunningStyleRequest(int raceProgramId, RaceDefine.RunningStyle runningStyle, int current_turn, System.Action onCompleteRequest, System.Action onError)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeChangeRunningStyleRequest()
            {
                program_id = raceProgramId,
                running_style = (int)runningStyle,
                current_turn = current_turn,
            };

            HttpManager.Instance.Send<SingleModeChangeRunningStyleRequest, SingleModeChangeRunningStyleResponse>(
                req,
                (res) =>
                {
                    onCompleteRequest?.Invoke();
                },
                (error, val) =>
                {
                    onError?.Invoke();
                }
            );
        }

        /// <summary>
        /// レース予約リクエスト
        /// </summary>
        /// <returns></returns>
        public void SendRaceReserve(int programId, Action onSuccess = null, Action<ErrorType, int> onError = null)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var request = new SingleModeRaceReserveRequest
            {
                program_id = programId,
                current_turn = workSingle.GetCurrentTurn(),
            };
            request.Send(res =>
            {
                workSingle.Character.ReservedRaceProgramId = programId;
                onSuccess?.Invoke();
            }, onError);
        }

        /// <summary>
        /// 予約キャンセルリクエスト
        /// </summary>
        /// <param name="onSuccess"></param>
        /// <param name="onError"></param>
        public void SendCancelRaceReserve(Action onSuccess = null, Action<ErrorType, int> onError = null)
        {
            const int PROGRAM_ID = SingleModeDefine.NO_RESERVE_PROGRAM_ID; //未設定のID

            var workSingle = WorkDataManager.Instance.SingleMode;
            var request = new SingleModeRaceReserveRequest
            {
                program_id = PROGRAM_ID,
                current_turn = workSingle.GetCurrentTurn(),
            };
            request.Send(res =>
            {
                workSingle.Character.ReservedRaceProgramId = PROGRAM_ID; //未設定に戻す
                onSuccess?.Invoke();
            }, onError);
        }
    }
}