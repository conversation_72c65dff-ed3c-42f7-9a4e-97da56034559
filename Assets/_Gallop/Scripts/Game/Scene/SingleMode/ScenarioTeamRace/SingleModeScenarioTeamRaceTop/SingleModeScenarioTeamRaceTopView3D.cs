using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Gallop.Model.Component;
using UnityEngine.Rendering;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    using static SingleModeScenarioTeamRaceDeckInfo;

    /// <summary>
    /// アオハル杯レーストップ画面でエースキャラを表示する
    /// </summary>
    public partial class SingleModeScenarioTeamRaceTopViewController : ViewControllerBase<SingleModeScenarioTeamRaceTopView>
    {
        //NOTE:パラメーターほとんどは競技場と同じ
#region const
        /// <summary>1チームの人数</summary>
        private const int TEAM_MEMBER_COUNT = 5;

        /// <summary>
        /// マスター指定のモーションを再生する
        /// </summary>
        private const int CHARA_DEFAULT_MOTION_ID = 9;

        //DMM版においてShadow_distance == 15になりますとシャドウマップに予想外の影が入り、、
        //キャラのシャドウレシーバーメッシュのエッジに影の色が載せてしまう
        private const int SHADOW_DISTANCE = 8;

#endregion

        private MemberInfo[] _aceCharaData = null;

        private MasterCharaMotionSet.CharaMotionSet[] _myStandMotionMasterArray = null;

        private List<SimpleModelController> _character3DList = new List<SimpleModelController>();

        private List<SimpleModelController> _hideChara3DList = new List<SimpleModelController>();

        private string _bgPath = "";

        private SingleModeScenarioTeamRaceTopViewCharaParam _paramData = null;

        private Vector3 _focusCameraDefaultPos = Vector3.zero;

        private float _focusCameraDefaultFOV = 0f;
        private float _defaultLowResCameraFovFactor = 1f;
        private int _focusCameraDefaultCullingMask;

        private GallopCharacterImageEffect _imageEffect = null;

        private GameObject _charaShadowObject;
        private Transform _charaShadowReceiverTransform;
        private Light _charaShadowLight;
        private LowResolutionCamera _lowResCamera = null;
        private RenderTexture _bgRenderTexture = null;
        private CommandBuffer _bgToLowResoCameraBlitCommand = null;

        /// <summary>
        /// 3D関係のダウンロード
        /// </summary>
        /// <param name="register"></param>
        private void RegisterDownload3D(DownloadPathRegister register)
        {
            //表示設定のデータダウンロード
            TeamStadium3DController.RegisterDownload(register);

            //背景は競技場と同じものを表示
            _bgPath = ResourcePath.GetTeamStadiumDeckBgPath(BG_ID, BG_SUB_ID);

            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_CHARA_PARAM_FILE_DATA);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_TEAM_RACE_TOP_VIEW_IMAGE_EFFECT_DATA);
            register.RegisterPathWithoutInfo(_bgPath);

            //足元影板のPrefabDL
            register.RegisterPathWithoutInfo(ResourcePath.CHARACTER_SHADOW_RECEIVER_PREFAB_PATH);
        }

        /// <summary>
        /// DirectionalLightの設定を行う
        /// </summary>
        private void SetupDirectionalLight(SingleModeScenarioTeamRaceTopViewCharaParam paramData)
        {
            const LightShadows DEFAULT_SHADOW = LightShadows.Soft;
            const UnityEngine.Rendering.LightShadowResolution SHADOW_RESOLUTION_DEFAULT = UnityEngine.Rendering.LightShadowResolution.Medium;

            if (paramData == null)
                return;

            if (!DirectionalLightManager.HasInstance())
            {
                return;
            }
            var directionalLightManager = DirectionalLightManager.Instance;
            //ライト設定
            directionalLightManager.SetEnable(true);
            directionalLightManager.Reset();    //チーム編成などで設定が変わる

            //影設定
            _charaShadowLight.enabled = true;
            _charaShadowLight.type = LightType.Directional;
            _charaShadowLight.shadowStrength = paramData.ShadowStrength;
            _charaShadowLight.shadowResolution = SHADOW_RESOLUTION_DEFAULT;
            _charaShadowLight.shadows = DEFAULT_SHADOW;
            _charaShadowLight.cullingMask = GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3D)
                                          | GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.LayerCHAR);
            _charaShadowLight.transform.localRotation = Quaternion.Euler(paramData.ShadowLightRotation);    //実機だとセットアップ時の１回のみなので角度で保持しておく
            _charaShadowReceiverTransform.localPosition = paramData.ShadowRecieverPosition;
            _charaShadowReceiverTransform.localRotation = Quaternion.Euler(paramData.ShadowRecieverRotation);

#if UNITY_EDITOR
            directionalLightManager.transform.localRotation = Quaternion.Euler(paramData.CharacterColorData.LightDir.Value);
#endif
        }

        private void CreateCharaLight()
        {
            //Prefabの中に含ませても良いかも
            var shadowPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.CHARACTER_SHADOW_RECEIVER_PREFAB_PATH);
            _charaShadowObject = GameObject.Instantiate<GameObject>(shadowPrefab);

            _charaShadowLight = _charaShadowObject.GetComponentInChildren<Light>();
            var receiverRenderer = _charaShadowObject.GetComponentInChildren<Renderer>();
            _charaShadowReceiverTransform = receiverRenderer.transform; //動かす対象はここ
        }

        /// <summary>
        /// 3D設定
        /// </summary>
        private void Setup3D()
        {
            QualitySettings.shadowDistance = SHADOW_DISTANCE;

            //未初期化の状態があり得るので、エースメンバー取得前にセットアップしておく
            WorkDataManager.Instance.SingleMode.TeamRace.DeckBuilder.Setup();

            //ユーザーチームのエースキャラを取得
            var aceCharaData = WorkDataManager.Instance.SingleMode.TeamRace.TeamRaceDeckInfo.GetMemberList().Where(x => x.IsEmpty == false && x.IsAce);

            if (aceCharaData == null)
            {
                Debug.LogError("編成のキャラデータが正しく取得できませんでした");
            }
            else
            {
                _aceCharaData = aceCharaData.OrderByDescending(x => x.CharaId == WorkDataManager.Instance.SingleMode.Character.CharaId).ThenBy(x => x.DistanceType).ToArray();
            }

            _myStandMotionMasterArray = new MasterCharaMotionSet.CharaMotionSet[TEAM_MEMBER_COUNT];

            _paramData = SingleModeScenarioTeamRaceTopViewCharaParam.Load();

            var camera = _sceneController.FocusCamera.GetCamera();
            _focusCameraDefaultFOV = camera.fieldOfView;
            _focusCameraDefaultPos = camera.transform.localPosition;
            _focusCameraDefaultCullingMask = camera.cullingMask;

            //ライトに必要なオブジェクトを生成する
            CreateCharaLight();

            // キャラのロード
            Load3DCharacter();

            //ライト設定(キャラへの反映が必要なため、キャラロード後に行う)
            SetupDirectionalLight(_paramData);

            //カメラ設定
            camera.fieldOfView = _paramData.Camera.Fov;
            camera.transform.localPosition = _paramData.Camera.Position;
            camera.cullingMask |= GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3D);
            if(_lowResCamera == null)
            {
                _lowResCamera = camera.GetComponent<LowResolutionCamera>();
                _defaultLowResCameraFovFactor = _lowResCamera.FovFactor;
            }
            _lowResCamera.FovFactor = GallopUtil.GetFovFactorToIncrease();

            ApplyImageEffect();

            BGManager.Instance.SetMainBg(_bgPath);
            BGManager.Instance.SetMainBgLocalPos(_paramData.BackGround.Position);
            //BGの出力先がLowResoの背景となるが、iPadのMSAA問題(ResolveAAが入ると直前の描画結果が破棄される)により
            //直接描画する事は出来ない、また_sceneController.FocusCamera.EnableBackgroundTextureBlitFromUIを使用すると
            //BGCameraを使用せずに描画する事が出来るが、位置を動かしている+背景以外の花吹雪を散らしていて
            //EnableBackgroundTextureBlitFromUIだけでは賄いきれないので、独自にバッファを用意して描画する
            _sceneController.FocusCamera.EnableBackgroundTextureBlitFromUI(false);  //無効にしておく
            if (_lowResCamera.Texture != null)
            {
                _bgRenderTexture = new RenderTexture(_lowResCamera.Texture.width, _lowResCamera.Texture.height, 0);
#if CYG_DEBUG
                _bgRenderTexture.name = nameof(SingleModeScenarioTeamRaceTopViewController) + ".RT";
#endif
                _bgRenderTexture.Create();

                _bgToLowResoCameraBlitCommand = new CommandBuffer();
                _bgToLowResoCameraBlitCommand.Blit(_bgRenderTexture, BuiltinRenderTextureType.CurrentActive);

                _lowResCamera.Camera.AddCommandBuffer(CameraEvent.BeforeForwardOpaque, _bgToLowResoCameraBlitCommand);

                //EnableBackgroundTextureBlitFromUIでclearFlagがNothingになるので、設定を変更する必要がある
                var focusCamera = _sceneController.FocusCamera.GetCamera();
                focusCamera.clearFlags = CameraClearFlags.Color;
            }
            //作れなかった場合はnullになるので、UIに描画され正しい絵は出ないがエラーにもならない
            UIManager.Instance.SetBgCameraRenderTexture(_bgRenderTexture);
            BGManager.Instance.RecalcBgSize();
            BGManager.Instance.SetMainBgRightEdgeToCanvasRightEdge();

            //設定データを反映する
#if CYG_DEBUG && UNITY_EDITOR
            _view.EditorCharaMinMaxScale = _paramData.CharaMinMaxScale;
            _view.EditorBgPosition = _paramData.BackGround.Position;
#endif
        }

        /// <summary>
        /// キャラのロード
        /// </summary>
        /// <param name="isAsync"></param>
        private void Load3DCharacter()
        {
            //画質設定がノーマル以外は表示キャラ数を育成キャラだけ固定
            var gameQuality = WorkDataManager.Instance.SingleMode.TeamRace.GameQuality;
            var isNormalOrRich = gameQuality == GraphicSettings.GameQuality.Normal || gameQuality == GraphicSettings.GameQuality.Rich;
            int count = isNormalOrRich ? _aceCharaData.Length : 1;

            if (count > TEAM_MEMBER_COUNT)
            {
                // 来ないはず
                count = TEAM_MEMBER_COUNT;
                Debug.LogError("エースの数が想定以上です");
            }

            for (int i = 0; i < count; i++)
            {
                _character3DList.Add(CreateCharaModel(_aceCharaData[i].CharaId, i));
            }

            //育成キャラのセリフを出すために設定
            _view.CharaMessage.SetEnable(false);
            _view.CharaMessage.SetModel(_character3DList[0], false);
        }

        private SimpleModelController CreateCharaModel(int charaId, int index)
        {
            int charaMotionId = CHARA_DEFAULT_MOTION_ID;
            const int MASTER_TYPE = 1;

            SimpleModelController modelController = null;

            var dressId = 0;

            if (SingleModeScenarioTeamRaceUtils.IsSingleModeCharaByCharaId(_aceCharaData[index].CharaId))
            {
                dressId = WorkDataManager.Instance.SingleMode.Character.CardRarityData.RaceDressId;
                modelController = _sceneController.CreateModel(WorkDataManager.Instance.SingleMode.Character.CardId, dressId);
            }
            else
            {
                dressId = SingleModeScenarioTeamRaceUtils.GetPlayerTeamMemberDressId(_aceCharaData[index].TeamMember.SupportCardId);
                modelController = _sceneController.CreateBgCharaModel(_aceCharaData[index].TeamMember.SupportCardId, dressId).Controller;
            }

            int position = index + 1;
            MasterTeamStadiumStandMotion.TeamStadiumStandMotion master = MasterDataManager.Instance.masterTeamStadiumStandMotion.Get(charaId, MASTER_TYPE, dressId, position);
            var settingDataParam = _paramData.GetCharaSettingData(index);
            if (master == null)
            {
                // 使用するモーションのマスター
                _myStandMotionMasterArray[index] = MasterDataManager.Instance.masterCharaMotionSet.Get(charaMotionId);

                modelController.PlayMotion(_myStandMotionMasterArray[index]);
                if (settingDataParam != null)
                {
                    modelController.transform.localEulerAngles = settingDataParam.Rotation;
                }
            }
            else
            {
                // 使用するモーションのマスター
                _myStandMotionMasterArray[index] = MasterDataManager.Instance.masterCharaMotionSet.Get(master.MotionSet);

                modelController.PlayMotion(_myStandMotionMasterArray[index]);
                modelController.transform.localRotation = Quaternion.Euler(0, master.Rotation, 0);
            }

            if (settingDataParam != null)
            {
                modelController.transform.localPosition = settingDataParam.Position;
                modelController.transform.localScale = settingDataParam.Scale;
                GallopCharacterImageEffectParameter.ApplayCharacterColor(_paramData.CharacterColorData, modelController);
            }

            modelController.SetActiveWithCheck(false);
            //リアルタイムシャドウ有効
            modelController.SetShadow(ModelLoader.ShadowType.Normal);

            if (_paramData.RescaleModel)
            {
                //身長差丸め
                RescaleModel(modelController);
                //スケーリングした後にCySpringリセット
                modelController.ResetCyspring();
                modelController.ReserveWarmingUpCySpring();
            }

            return modelController;
        }

        /// <summary>
        /// キャラの吹き出し表示
        /// </summary>
        /// <param name="character"></param>
        private void ShowCharaMessage()
        {
            _view.CharaMessage.SetEnable(true);

            if (!_view.CharaMessage.IsPlaying)
            {
                _view.CharaMessage.PlayIdle(useSmoothFaceBlend: true);
            }

            _view.CharaMessage.SetActiveWithCheck(_view.CharaMessage.IsPlaying);
        }

        /// <summary>
        /// キャラの吹き出し非表示
        /// </summary>
        private void HideCharaMessage()
        {
            _view.CharaMessage.Stop();
            _view.CharaMessage.SetEnable(false);
        }

        /// <summary>
        /// モデルの表示を更新
        /// </summary>
        private void UpdateCharacterModelSetting()
        {
            if(_paramData != null && _paramData.ApplyCharacterColor)
            {
                foreach (var model in _character3DList)
                {
                    GallopCharacterImageEffectParameter.ApplayCharacterColor(_paramData.CharacterColorData, model);
                }
            }
#if UNITY_EDITOR
            //エディターで編集中のみDirectionalLightManagerの結果を使用する(Saveした時にここの値が使用されるため)
            if(DirectionalLightManager.HasInstance())
            {
                var directionalTransform = DirectionalLightManager.Instance.transform;
                foreach (var model in _character3DList)
                {
                    //自動編成のモデル作り直しにより、一時nullがあり得る
                    if (model == null)
                    {
                        continue;
                    }

                    model.SetAllLightRotation(true, directionalTransform.localRotation);
                }
            }
#endif
        }

        /// <summary>
        /// Scaleの平均化処理
        /// </summary>
        /// <param name="modelController"></param>
        private void RescaleModel(ModelController modelController)
        {
            float size = CameraController.Get3DCharaHeightScale(modelController.GetHeight(), ref _paramData.CharaMinMaxScale);
            Vector3 scale = modelController.PositionNode.localScale;
            scale.x = size;
            scale.y = size;
            scale.z = size;
            modelController.PositionNode.localScale = scale;

            if (modelController.TryGetModelComponent<EventTimelineLimbIK>(out var limbIK))
            {
                //スケールが変わってJoint距離が変わったので再計算する
                limbIK.RecalcJointDistance();

                //スケールが変わったのでコリジョンを作り直す必要がある
                CharaIKController.SetIKCollisionFromModelController(limbIK, modelController, size, IK.IKCollisionData.Parameter.Scene.Event);
            }
        }

        /// <summary>
        /// イメージエフェクト設定
        /// </summary>
        /// <param name="viewerType"></param>
        private void ApplyImageEffect()
        {
            if(_imageEffect != null)
            {
                return;
            }

            var imageEffectParam = ResourceManager.LoadOnView<GallopImageEffectParameter>(ResourcePath.SINGLE_MODE_SCENARIO_TEAM_RACE_TOP_VIEW_IMAGE_EFFECT_DATA);
            GraphicSettings.Instance.ColorCorrectionParameter = imageEffectParam.ColorCorrectionParam;
            _imageEffect = _sceneController.FocusCamera.GetCamera().gameObject.AddComponent<GallopCharacterImageEffect>();
            _imageEffect.Initialize();
            _imageEffect.ColorCorrectionParam.Initialize();
            _imageEffect.LoadShader();
            _imageEffect.CheckResources();
            if (imageEffectParam != null)
            {
                imageEffectParam.CopyTo(_imageEffect);
            }
        }

        /// <summary>
        /// 破棄
        /// </summary>
        private void Destroy3D()
        {
            foreach(var model in _character3DList)
            {
                GameObject.Destroy(model);
            }

            foreach(var model in _hideChara3DList)
            {
                GameObject.Destroy(model);
            }

            var camera = _sceneController.FocusCamera.GetCamera();
            camera.fieldOfView = _focusCameraDefaultFOV;
            camera.transform.localPosition = _focusCameraDefaultPos;
            camera.cullingMask = _focusCameraDefaultCullingMask;
            if(_lowResCamera != null)
            {
                _lowResCamera.FovFactor = _defaultLowResCameraFovFactor;
                if (_bgToLowResoCameraBlitCommand != null)
                {
                    _lowResCamera.Camera.RemoveCommandBuffer(CameraEvent.BeforeForwardOpaque, _bgToLowResoCameraBlitCommand);
                }
            }
            UIManager.Instance.SetBgCameraRenderTexture(null);
            if(_bgRenderTexture != null)
            {
                _bgRenderTexture.Release();
                GameObject.Destroy(_bgRenderTexture);
                _bgRenderTexture = null;
            }
            if(_bgToLowResoCameraBlitCommand != null)
            {
                _bgToLowResoCameraBlitCommand.Release();
                _bgToLowResoCameraBlitCommand = null;
            }

            //ライト設定をリセット
            DirectionalLightManager.Instance.Reset();

            _charaShadowLight = null;
            _charaShadowReceiverTransform = null;
            if (_charaShadowObject != null)
            {
                GameObject.Destroy(_charaShadowObject);
            }
            _charaShadowObject = null;

            if (_imageEffect != null)
            {
                GameObject.Destroy(_imageEffect);
                _imageEffect = null;
            }

            _character3DList.Clear();

            _hideChara3DList.Clear();

            _sceneController.ClearModelCache();
            GraphicSettings.SetPixelLightCount(0);

            QualitySettings.shadowDistance = GameDefine.DEFAULT_SHADOW_DISTANCE;
        }


#if UNITY_EDITOR
        public void EditorSaveSettingData()
        {
            var paramData = SingleModeScenarioTeamRaceTopViewCharaParam.Load();
            if (paramData != null && _character3DList != null && _character3DList.Count > 0)
            {
                SingleModeScenarioTeamRaceTopViewCharaParam.SaveContext context;
                context.Camera = _sceneController.FocusCamera.GetCamera();
                context.CharaMinMaxScale = _view.EditorCharaMinMaxScale;
                context.BackGroundImagePosition = _view.EditorBgPosition;
                context.ModelRootArray = _character3DList.Select(x => x.transform).ToArray();
                context.DirectionalLightRotation = DirectionalLightManager.Instance.DirectionalLightTransform.localRotation;
                context.ShadowLightRotation = _charaShadowLight.transform.localRotation;
                context.ShadowReceivePosition = _charaShadowReceiverTransform.localPosition;
                context.ShadowReceiveRotation = _charaShadowReceiverTransform.localRotation;
                context.ShadowStrength = _charaShadowLight.shadowStrength;
                paramData.Save(ref context);
            }
        }

        public void EditorLoadSettingData()
        {
            var paramData = SingleModeScenarioTeamRaceTopViewCharaParam.Load();
            if (paramData == null)
                return;

            if (_character3DList != null)
            {
                var count = _character3DList.Count;
                for (int i = 0; i < count; i++)
                {
                    var settingData = _paramData.GetCharaSettingData(i);
                    if (settingData != null)
                    {
                        var modelController = _character3DList[i];

                        settingData.Apply(modelController.transform);

                        if (paramData.RescaleModel)
                        {
                            //身長差丸め
                            RescaleModel(modelController);
                            //スケーリングした後にCySpringリセット
                            modelController.ResetCyspring();
                            modelController.ReserveWarmingUpCySpring();
                        }
                        else
                        {
                            modelController.SetScale(1f);

                            if (modelController.TryGetModelComponent<EventTimelineLimbIK>(out var limbIK))
                            {
                                //スケールが変わってJoint距離が変わったので再計算する
                                limbIK.RecalcJointDistance();

                                //スケールが変わったのでコリジョンを作り直す必要がある
                                CharaIKController.SetIKCollisionFromModelController(limbIK, modelController, 1f, IK.IKCollisionData.Parameter.Scene.Event);
                            }

                            //スケーリングした後にCySpringリセット
                            modelController.ResetCyspring();
                            modelController.ReserveWarmingUpCySpring();
                        }
                    }
                }
            }

            //カメラ設定
            var camera = _sceneController.FocusCamera.GetCamera();
            camera.fieldOfView = paramData.Camera.Fov;
            camera.transform.localPosition = paramData.Camera.Position;
            if(_lowResCamera == null)
            {
                _lowResCamera = camera.GetComponent<LowResolutionCamera>();
                _defaultLowResCameraFovFactor = _lowResCamera.FovFactor;
            }
            _lowResCamera.FovFactor = GallopUtil.GetFovFactorToIncrease();

            SetupDirectionalLight(paramData);

            _paramData = paramData;
        }

        public void EditorApplyBgPosition()
        {
            //背景を設定
            BGManager.Instance.SetMainBgLocalPos(_view.EditorBgPosition);
            _sceneController.FocusCamera.EnableBackgroundTextureBlitFromUI(false);
        }
#endif

    }

#if UNITY_EDITOR
    // カスタマイズするクラスを設定
    [CustomEditor(typeof(SingleModeScenarioTeamRaceTopView))]
    // Editorクラスを継承してクラスを作成
    public class SingleModeScenarioTeamRaceTopViewEditor : Editor
    {
        private SingleModeScenarioTeamRaceTopView _view;

        private void OnEnable()
        {
            _view = (SingleModeScenarioTeamRaceTopView)target;
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            if (_view == null)
                return;

            EditorGUILayout.Space();

            _view.EditorIsEdit = EditorGUILayout.Toggle("編集有効", _view.EditorIsEdit);
            GUI.enabled = _view.EditorIsEdit;
            _view.EditorCharaMinMaxScale = EditorGUILayout.Vector2Field("キャラのサイズ均等化", _view.EditorCharaMinMaxScale);
            _view.EditorBgPosition = EditorGUILayout.Vector2Field("背景位置", _view.EditorBgPosition);

            GUIUtil.Separator();

            if (GUILayout.Button("Save"))
            {
                if(_view.EditorViewController != null)
                {
                    _view.EditorViewController.EditorSaveSettingData();
                }
               
            }
            if (GUILayout.Button("Load"))
            {
                if(_view.EditorViewController != null)
                {
                    _view.EditorViewController.EditorLoadSettingData();
                }
            }

            if (GUILayout.Button("背景座標適用"))
            {
                _view.EditorViewController.EditorApplyBgPosition(); 
            }
            GUI.enabled = true;
        }
    }
#endif
}

