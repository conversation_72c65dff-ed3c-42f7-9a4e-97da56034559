using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ストーリーイベント報酬一覧ダイアログのリストアイテム
    /// </summary>
    public class PartsStoryEventRewardListItem : LoopScrollItemBase
    {
        #region 定義

        /// <summary>
        /// セットアップ用パラメータ
        /// </summary>
        public class SetupParameter : IPartsSetupParameter
        {
            /// <summary> アイテムカテゴリ </summary>
            public GameDefine.ItemCategory ItemCategory;

            /// <summary> アイテムId </summary>
            public int ItemId;

            /// <summary> アイテムの個数 </summary>
            public int ItemNum;

            /// <summary> 必要pt </summary>
            public int NeedPoint;

            /// <summary> 達成済みか </summary>
            public bool IsClear = false;

            /// <summary> 次の報酬か </summary>
            public bool IsNext = false;
        }

        #endregion

        #region SerializeField

        /// <summary> アイテムアイコン </summary>
        [SerializeField]
        private ItemIcon _itemIcon;

        /// <summary> 必要イベントPt </summary>
        [SerializeField]
        private TextCommon _eventPt;

        /// <summary> アイテムの名前 </summary>
        [SerializeField]
        private TextCommon _itemName;

        /// <summary> グレーアウトする要素のルート </summary>
        [SerializeField]
        private ColorSender _colorSender;

        /// <summary> スタンプ </summary>
        [SerializeField]
        private ImageCommon _stampImage;

        /// <summary> 次の報酬を示すリボン </summary>
        [SerializeField]
        private ImageCommon _nextImage;

        #endregion

        #region Method

        /// <summary>
        /// 要素の更新
        /// </summary>
        /// <param name="param"></param>
        public void UpdateItem(SetupParameter param)
        {
            StoryEventUtil.SetupRewardItemIcon(_itemIcon, param.ItemCategory, param.ItemId, param.ItemNum);
            _eventPt.text = StoryEventUtil.GetEventPtText(param.NeedPoint);
            _itemName.text = _itemIcon.ItemName;

            // 次の報酬
            _nextImage.SetActiveWithCheck(param.IsNext);

            // クリア済みフラグ
            _stampImage.SetActiveWithCheck(param.IsClear);
            _colorSender.SendColor = param.IsClear ? ButtonCommon.NO_INTERACTERABLE_COLOR : ButtonCommon.DEFAULT_COLOR_WHITE;
        }

        #endregion
    }
}
