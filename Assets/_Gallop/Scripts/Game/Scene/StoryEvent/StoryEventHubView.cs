using System.Collections;
using Cute.Http;

namespace Gallop
{
    /// <summary>
    /// ストーリーイベントHubView
    /// </summary>
    public class StoryEventHubView : ViewBase
    {
    }

    /// <summary>
    /// ストーリーイベントHubViewの情報
    /// </summary>
    public class StoryEventHubViewInfo : HubViewControllerBase.HubViewInfo
    {
        public StoryEventTopViewInfo TopViewInfo = new StoryEventTopViewInfo();
        public StoryEventMissionViewController.ViewInfo MissionViewInfo;
    }

    /// <summary>
    /// ストーリーイベントHubViewコントローラー
    /// </summary>
    public class StoryEventHubViewController : HubViewControllerBase
    {
        #region Variable

        private bool _isBlackOut;
        private bool _isHorseShoeLoading;

        #endregion

        #region HubViewControllerBase

        /// <summary>
        /// 子ビュー定義
        /// </summary>
        private static readonly SceneDefine.ViewId[] VIEW_ID_ARRAY =
        {
            SceneDefine.ViewId.StoryEventTop,
            SceneDefine.ViewId.StoryEventMission,
            SceneDefine.ViewId.RouletteDerby,
        };

        /// <summary>
        /// 子ビュー配列取得
        /// </summary>
        public override SceneDefine.ViewId[] GetChildViewIdArray()
        {
            return VIEW_ID_ARRAY;
        }

        /// <summary>
        /// 子ビューに渡すViewInfo選定
        /// </summary>
        protected override IViewInfo GetChildViewInfo(SceneDefine.ViewId viewId, IViewInfo viewInfo)
        {
            var hubViewInfo = viewInfo as StoryEventHubViewInfo;

            if (hubViewInfo != null)
            {
                switch (viewId)
                {
                    case SceneDefine.ViewId.StoryEventTop: return hubViewInfo.TopViewInfo;
                    case SceneDefine.ViewId.StoryEventMission: return hubViewInfo.MissionViewInfo;
                }
            }

            return viewInfo;
        }

        public override IEnumerator PlayInView()
        {
            // イベントトップ -> ルーレット の遷移
            if (_isBlackOut)
            {
                _isBlackOut = false;
                NowLoading.Instance.Hide();
            }
            // StoryEventHubView内の遷移
            else if (_isHorseShoeLoading)
            {
                _isHorseShoeLoading = false;
                var isFadeFinish = false;
                NowLoading.Instance.Hide(() => isFadeFinish = true);

                yield return base.PlayInView();
                
                while (!isFadeFinish)
                {
                    yield return null;
                }
                yield break;
            }

            yield return base.PlayInView();
        }

        public override IEnumerator PlayOutView()
        {
            var currentViewId = ChildCurrentController.GetViewId();
            var nextViewId = SceneManager.Instance.GetNextViewId();
            // イベントトップ -> ルーレット の遷移
            if (currentViewId == SceneDefine.ViewId.StoryEventTop &&
                nextViewId == SceneDefine.ViewId.RouletteDerby)
            {
                while (HttpManager.Instance.IsConnecting)
                {
                    yield return null;
                }
                
                _isBlackOut = true;
                bool isShowEnd = false;
                NowLoading.Instance.Show(NowLoading.Type.BlackFade, () => isShowEnd = true);
                
                yield return base.PlayOutView();
                
                while (!isShowEnd)
                {
                    yield return null;
                }
                
                yield break;
            }
            // ルーレット -> イベントトップ の遷移
            else if (currentViewId == SceneDefine.ViewId.RouletteDerby &&
                     nextViewId == SceneDefine.ViewId.StoryEventTop)
            {
                _isBlackOut = true;
                bool isShowEnd = false;
                const float BLACK_FADE_DURATION = 0.2f;
                NowLoading.Instance.Show(NowLoading.Type.BlackFade, () => isShowEnd = true, BLACK_FADE_DURATION);

                yield return base.PlayOutView();

                while (!isShowEnd)
                {
                    yield return null;
                }

                yield break;
            }
            // それ以外のStoryEventHubView間の遷移
            else if (IsChildView(nextViewId))
            {
                _isHorseShoeLoading = true;
                var isFadeFinishWait = false;
                NowLoading.Instance.Show(NowLoading.Type.WhiteOutWithHorseShoe, () =>
                {
                    isFadeFinishWait = true;
                });
                yield return base.PlayOutView();

                while (!isFadeFinishWait)
                {
                    yield return null;
                }
                yield break;
            }
            
            // それ以外の遷移
            yield return base.PlayOutView();
        }

        #endregion
    }
}
