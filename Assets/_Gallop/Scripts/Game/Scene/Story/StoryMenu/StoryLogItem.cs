using System;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// ストーリーログのリストアイテム
    /// </summary>
    public class StoryLogItem : LoopScrollItemBase
    {
        private const string BASE_SPRITE_CHARA = "utx_frm_storylog_00_sl";
        private const string BASE_SPRITE_TRAINER = "utx_frm_list_base_02_sl";

        private const int LEFT_PADDING_CHARA = 48;
        private const int LEFT_PADDING_TRAINER = 24;
        private const int LEFT_PADDING_FOR_LOG = 24;
        private const int RIGHT_PADDING_FOR_LOG = 0;

        private const int HORIZONTAL_SPACING_CHARA = 0;
        private const int HORIZONTAL_SPACING_TRAINER = 24;

        /// <summary>
        /// UIチーム指定の文字列が3行以内だった時（通常時）の縦幅
        /// </summary>
        private const float LINE_3_HEIGHT = 216f;

        /// <summary>
        /// UIチーム指定の文字列が4行目だった時の縦幅
        /// </summary>
        private const float LINE_4_HEIGHT = 252f;
        private const float LINE_4_MESSAGE_PREFERRED_HEIGHT = 172f;

        // 育成ログで1行に表示できる文字数
        private const int SINGLE_MODE_LINE_CHARACTER_MAX = 20;

        // ストーリー・ストーリーレースのログで1行に表示できる文字数
        private const int STORY_LINE_CHARACTER_MAX = StoryViewTextControllerPortraitBase.LINE_CHARACTER_MAX;

        // モブアイコン
        private const string ICON_MOB = "utx_ico_story_chara_00";

        // 複数人アイコン
        private const string ICON_ALL = "utx_ico_story_chara_01";

        // 吹き出しアニメーションの入りモーション
        private const string IN_STATE_NAME = "in00";

        // 吹き出しアニメーションのLayerIndex
        private const int BASE_LAYER_INDEX = 0;

        // 吹き出しアニメーションに合わせたサイズ変更の継続時間
        public const float TWEEN_DURATION = 0.2f;

        #region SerializeField
        [field: SerializeField, RenameField]
        protected RawImageCommon CharaIcon { get; set; }

        [field: SerializeField, RenameField]
        protected ImageCommon OtherIcon { get; set; }

        [field: SerializeField, RenameField]
        public TextCommon NameText { get; protected set; }

        [field: SerializeField, RenameField]
        public GameObject Separator { get; protected set; }

        [field: SerializeField, RenameField]
        public TextCommon MessageText { get; protected set; }

        [field: SerializeField, RenameField]
        public ButtonCommon VoiceButton { get; protected set; }

        [field: SerializeField, RenameField]
        protected ImageCommon MessageBase { get; set; }

        [field: SerializeField, RenameField]
        protected LayoutElement MessageLayoutElement { get; set; }

        [field: SerializeField, RenameField]
        protected GameObject NextCursor { get; set; }

        [field: SerializeField, RenameField]
        protected HorizontalLayoutGroup LayoutGroup { get; set; }

        [field: SerializeField, RenameField]
        protected VerticalLayoutGroup MessageLayoutGroup { get; set; }

        [field: SerializeField, RenameField]
        protected Animator BalloonAnimator { get; set; }

        #endregion SerializeField

        #region Private Member
        private bool _isAnimating = false;
        private string _voiceSheetId = string.Empty;
        private int _voiceIndex = AudioManager.INVALID_VOICE_ID;
        private string _selectorLabel = null;
        private System.Action _onClickVoice = null;
        private StoryLogInfo.SoundType _soundType = StoryLogInfo.SoundType.VoiceStory;
        private Vector2 _messageDeltaSize;
        private AnimatorOverrideController _overrideController = null;
        private StoryTimelineController.DisplayMode _displayMode = StoryTimelineController.DisplayMode.Portrait;
        private Color _colorSenderToColor = GameDefine.COLOR_WHITE;
        private Tweener _colorSenderTweener = null;
        private Tweener _balloonSizeTweener = null;
        private Tweener _moveTween = null;
        #endregion Private Member

        #region Property

        private RectTransform _cacheLayoutGroupRectTransform = null;
        public RectTransform CacheLayoutGroupRectTransform
        {
            get
            {
                if (_cacheLayoutGroupRectTransform == null)
                {
                    _cacheLayoutGroupRectTransform = LayoutGroup.transform as RectTransform;
                }
                return _cacheLayoutGroupRectTransform;
            }
        }

        private ColorSender _colorSender = null;
        public ColorSender ColorSender
        {
            get
            {
                if (_colorSender == null)
                {
                    _colorSender = GetComponent<ColorSender>();
                    if (_colorSender == null)
                    {
                        _colorSender = this.gameObject.AddComponent<ColorSender>();
                    }
                }
                return _colorSender;
            }
        }

        #endregion Property

        #region MonoBehaviour

        protected void Awake()
        {
            // prefabで切ってあるけど念のため
            BalloonAnimator.enabled = false;
            _messageDeltaSize = MessageText.rectTransform.sizeDelta;
        }

        protected void OnDestroy()
        {
            _colorSenderTweener?.Kill();
            _colorSenderTweener = null;
            _balloonSizeTweener?.Kill();
            _balloonSizeTweener = null;
            _moveTween?.Kill();
            _moveTween = null;
        }

        #endregion MonoBehaviour

        /// <summary>
        /// リストアイテム更新
        /// </summary>
        /// <param name="onClickVoice"></param>
        public void UpdateItem(StoryTimelineController.DisplayMode displayMode, StoryLogInfo info, System.Action onClickVoice)
        {
            _displayMode = displayMode;
            // 設定
            SetupByLogData(displayMode, info);
            SetupSize(info.Text);
            SetupSizeForLog();

            // コールバック設定
            _onClickVoice = onClickVoice;
        }

        /// <summary>
        /// Logダイアログで表示する場合のサイズ調整 (StoryViewTextControllerPortraitでは使わない)
        /// </summary>
        private void SetupSizeForLog()
        {
            // サイズ調整というよりマージン調整
            LayoutGroup.padding.left = LEFT_PADDING_FOR_LOG;
            LayoutGroup.padding.right = RIGHT_PADDING_FOR_LOG;
        }

        /// <summary>
        /// テキスト内の改行数によってサイズを調整する
        /// </summary>
        public void SetupSize(string text)
        {
            //改行数を数える
            var num = TextUtil.GetCountChar(text, '\n');
            if (_displayMode == StoryTimelineController.DisplayMode.SingleMode)
            {
                // １イベント効果のメッセージが20文字を超える場合があるので20文字ごとに行数カウント
                var planeText = TextUtil.RemoveRitchTextTag(text); // カラータグなど除去してからカウント
                var textArray = planeText.Split('\n');
                foreach (var str in textArray)
                {
                    num += str.Length / SINGLE_MODE_LINE_CHARACTER_MAX;
                }
            }

            //更新対象のTransform
            //階層構造が増えたので２つとも更新が必要
            //Balloonアニメーション対応でLayoutGroup.transformはStretch設定になっていない点に注意
            var rectTransform = transform as RectTransform;
            var childRectTransform = LayoutGroup.transform as RectTransform;

            // 通常のテキスト表示可能行数
            const int TEXT_LINE_COUNT = 3;
            //改行が３つ（文字列が4行）以上　＆　名前表記中の時は、縦幅を広げる
            if (num >= TEXT_LINE_COUNT && NameText.gameObject.activeSelf)
            {
                var sizeDelta = new Vector2(rectTransform.sizeDelta.x, LINE_4_HEIGHT);
                rectTransform.sizeDelta = sizeDelta;
                childRectTransform.sizeDelta = sizeDelta;
                MessageLayoutElement.preferredHeight = LINE_4_MESSAGE_PREFERRED_HEIGHT;
            }
            //改行が4つ（文字列が5行）以上　＆　名前なし
            else if (num > TEXT_LINE_COUNT && !NameText.gameObject.activeSelf)
            {
                float addSize = (MessageText.FontSize * (num - TEXT_LINE_COUNT));
                var sizeDelta = new Vector2(rectTransform.sizeDelta.x, LINE_4_HEIGHT + addSize);
                rectTransform.sizeDelta = sizeDelta;
                childRectTransform.sizeDelta = sizeDelta;
                MessageLayoutElement.preferredHeight = LINE_4_MESSAGE_PREFERRED_HEIGHT + addSize;
            }
            //改行が3つ未満（文字列が3行以下）
            else if (num < TEXT_LINE_COUNT)
            {
                var sizeDelta = new Vector2(rectTransform.sizeDelta.x, LINE_3_HEIGHT);
                rectTransform.sizeDelta = sizeDelta;
                childRectTransform.sizeDelta = sizeDelta;
                MessageLayoutElement.preferredHeight = 0f;
            }
            else
            {
                MessageLayoutElement.preferredHeight = 0f;
            }
        }

        /// <summary>
        /// ログ情報を使って表示に必用な設定をする
        /// </summary>
        public void SetupByLogData(StoryTimelineController.DisplayMode displayMode, StoryLogInfo logInfo, bool forceDisableButton = false, bool isSetMessage = true)
        {
            if (logInfo == null)
            {
                return;
            }

            // アイコンの設定
            SetupIcon(logInfo);

            NameText.text = logInfo.Name;

            // メッセージの設定
            if (isSetMessage)
            {
                SetMessageSize(displayMode, logInfo);
                SetMessage(displayMode, logInfo);
            }

            if (!WorkDataManager.Instance.GalleryData.IsPlayingEventGallery)
            {
                // ボイス関係の設定
                SetVoiceInfo(logInfo, forceDisableButton);
            }
            else
            {
                VoiceButton.gameObject.SetActive(false);
            }
        }

        private void SetMessageSize(StoryTimelineController.DisplayMode displayMode, StoryLogInfo logInfo)
        {
            // 育成ストーリー会話ウインドウでは1行21文字
            // 育成中のパラメータ上昇などのシステムテキストは基本の1行20文字
            // メインストーリー、キャラストーリーは今まで通りの1行20文字数改行なしを維持する
            if (displayMode == StoryTimelineController.DisplayMode.SingleMode)
            {
                // 育成の時だけ処理する

                if (logInfo.LogType == StoryLogInfo.InfoType.SYSTEM)
                {
                    //システムメッセージ
                    MessageText.rectTransform.sizeDelta = _messageDeltaSize + new Vector2(MessageText.FontSize / 2, 0);//20文字だが半角文字含むと誤差があるので少し広げる
                }
                else
                {
                    //育成ストーリー
                    MessageText.rectTransform.sizeDelta = _messageDeltaSize + new Vector2(MessageText.FontSize, 0);//21文字目が入るサイズ
                }
            }
        }

        private void SetMessage(StoryTimelineController.DisplayMode displayMode, StoryLogInfo logInfo)
        {
            if (!string.IsNullOrEmpty(logInfo.ColorText))
            {
                // フォントカラーを使用する場合は整形済みのテキストを使う
                MessageText.text = logInfo.ColorText;
                return;
            }

            if (displayMode == StoryTimelineController.DisplayMode.SingleMode)
            {
                MessageText.text = logInfo.Text;
            }
            else
            {
                // メインストーリー、キャラストーリー、ストーリーレースの場合は禁則処理を行う
                MessageText.text = GallopUtil.LineHeadWrapForStory(TextUtil.RemoveNewLineForStory(logInfo.Text), STORY_LINE_CHARACTER_MAX);
            }
        }

        private void SetVoiceInfo(StoryLogInfo logInfo, bool forceDisableButton)
        {
            // ボイスが有効なら再生ボタンを表示
            _voiceSheetId = logInfo.SheetId;
            _voiceIndex = logInfo.VoiceIndex;
            _selectorLabel = logInfo.SelectorLabel;
            _soundType = logInfo.soundType;

            if (forceDisableButton)
            {
                // Voiceボタンは強制非表示
                VoiceButton.gameObject.SetActive(false);
            }
            else
            {
                bool isActive = false;
                switch (_soundType)
                {
                    case StoryLogInfo.SoundType.VoiceStory:
                        isActive = (_voiceIndex > AudioManager.INVALID_VOICE_ID) && (AudioManager.IsPartVoice(_voiceSheetId) == false);
                        break;
                    case StoryLogInfo.SoundType.SeStoryRace:
                        isActive = (_voiceIndex > AudioManager.INVALID_VOICE_ID);
                        break;
                    default:
                        break;
                }
                VoiceButton.gameObject.SetActive(isActive);
            }

            VoiceButton.SetOnClick(OnClickVoice);
        }

        /// <summary>
        /// ボイス再生ボタンのコールバック
        /// </summary>
        private void OnClickVoice()
        {
            if (_voiceIndex < 0)
            {
                return;
            }

            var audioManager = AudioManager.Instance;

            switch (_soundType)
            {
                case StoryLogInfo.SoundType.VoiceStory:
                    // ボイス再生中だったら止める
                    StoryManager.Instance.StopStoryVoice();
                    audioManager.SetBusParam(BusParamSet.SEQ_LOG, true);
                    // 該当するボイスを再生
                    audioManager.PlayVoiceStory(_voiceSheetId, _voiceIndex, selectorLabel: _selectorLabel);
                    break;
                case StoryLogInfo.SoundType.SeStoryRace:
                    // 特定の条件のSEを止める。
                    StoryRaceUI.StopSeAllVoice();
                    audioManager.PlaySe(_voiceSheetId, _voiceIndex);
                    break;
                default:
                    return;
            }

            _onClickVoice?.Invoke();
        }

        /// <summary>
        /// キャラアイコン設定
        /// </summary>
        /// <param name="logInfo"></param>
        /// <remarks>
        /// オブジェクトが使いまわされるので
        /// 表示・非表示の制御は都度行う必要がある.
        /// </remarks>
        private void SetupIcon(StoryLogInfo logInfo)
        {
            // CharaID, Nameで表示の制御を行う
            // キャラIDの仕様についても確認すること
            // https://xxxxxxxxxx/pages/viewpage.action?pageId=73435127

            bool isMonologue = false;

            if (logInfo.ForceSetMobIcon)
            {
                // 強制指定: キャラ指定があっても汎用アイコンSpriteを使う
                SetupOtherIcon(ICON_MOB);
            }
            else if (logInfo.CharaIDList == null || logInfo.CharaIDList.Count == 0)
            {
                // キャラ指定がない
                // 応答や選択肢の場合はここに来るのでトレーナー確定
                // 汎用アイコンSpriteを使う
                SetupOtherIcon(ICON_MOB);
            }
            else if (logInfo.CharaIDList.Count == 1)
            {
                // 話者が単一キャラ
                int charaId = logInfo.CharaIDList[0];
                SetupIconForSingleCharacter(logInfo.Name, charaId, out isMonologue);
            }
            else
            {
                // 話者が複数キャラ同時
                SetupOtherIcon(ICON_ALL);
            }

            // モノローグの場合は吹き出しアイコンのSpriteが異なる
            // Spriteサイズが変わることによるPading調整とSpacing調整も行う
            // また, 名前ラベルも非表示になるのでその調整もする
            SetupLayout(isMonologue);
        }

        /// <summary>
        /// 汎用アイコンまたは複数キャラアイコンを設定
        /// </summary>
        private void SetupOtherIcon(string spriteName)
        {
            CharaIcon.gameObject.SetActive(false);
            OtherIcon.gameObject.SetActive(true);
            OtherIcon.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Story, spriteName);
        }

        /// <summary>
        /// 単体キャラのアイコン設定
        /// </summary>
        private void SetupIconForSingleCharacter(string name, int charaId, out bool isMonologue)
        {
            isMonologue = false;

            if (string.IsNullOrEmpty(name))
            {
                // 名前記載がない場合はモノローグ
                // このフラグを立てることでSetupLayout()時にレイアウトが変化する
                isMonologue = true;

                // アイコンは非表示
                CharaIcon.gameObject.SetActive(false);
                OtherIcon.gameObject.SetActive(false);

                return;
            }

            var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (charaData != null)
            {
                // キャラクターアイコンを使う
                CharaIcon.gameObject.SetActive(true);
                OtherIcon.gameObject.SetActive(false);

                // キャラ毎のアイコンTextureをロード
                var path = ResourcePath.GetCharaThumbnailIconPath(charaId);
                CharaIcon.texture = ResourceManager.LoadOnView<Texture2D>(path);
                CharaIcon.rectTransform.sizeDelta = IconBase.GetSizeTypeVector2(IconBase.SizeType.Chara_63);

                return;
            }

            var mobData = MasterDataManager.Instance.masterMobData.Get(charaId);
            if (mobData != null)
            {
                // キャラクターアイコンを使う
                CharaIcon.gameObject.SetActive(true);
                OtherIcon.gameObject.SetActive(false);

                // モブウマ娘のアイコンTextureをロード
                var path = ResourcePath.GetMobCharaIconPath(charaId);
                CharaIcon.texture = ResourceManager.LoadOnView<Texture2D>(path);
                CharaIcon.rectTransform.sizeDelta = IconBase.GetSizeTypeVector2(IconBase.SizeType.Common_M);

                return;
            }

            // モブ or トレーナー
            // 汎用アイコンSpriteを使う
            CharaIcon.gameObject.SetActive(false);
            OtherIcon.gameObject.SetActive(true);
            OtherIcon.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Story, ICON_MOB);
        }

        /// <summary>
        /// 吹き出しアイコンの設定とそれに伴うレイアウト調整
        /// </summary>
        /// <param name="isMonologue"></param>
        private void SetupLayout(bool isMonologue)
        {
            // 吹き出し用Spriteの差し替え, 名前(と区切り画像)の表示制御
            if (isMonologue)
            {
                MessageBase.sprite = UIManager.PreInAtlas.GetSprite(BASE_SPRITE_TRAINER);
                NameText.gameObject.SetActive(false);
                Separator.SetActive(false);
            }
            else
            {
                MessageBase.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Story, BASE_SPRITE_CHARA);
                NameText.gameObject.SetActive(true);
                Separator.SetActive(true);
            }

            // Paddingの調整
            MessageLayoutGroup.padding.left = isMonologue ? LEFT_PADDING_TRAINER : LEFT_PADDING_CHARA;

            // Alignmentの調整
            MessageLayoutGroup.childAlignment = isMonologue ? TextAnchor.MiddleLeft : TextAnchor.UpperLeft;

            // Spacingの調整
            LayoutGroup.spacing = isMonologue ? HORIZONTAL_SPACING_TRAINER : HORIZONTAL_SPACING_CHARA;
        }

        /// <summary>
        /// 次へカーソルの表示を変更
        /// </summary>
        public void SetActiveNextCursor(bool isActive)
        {
            NextCursor.SetActive(isActive);
        }

        #region 吹き出し表示アニメーション
        /// <summary>
        /// 吹き出しの入りアニメーションを再生
        /// </summary>
        public void PlayBalloonAnimation()
        {
            // 横持ちやSingleModeの時は非アクティブなので何もしない
            if (!isActiveAndEnabled)
            {
                return;
            }

            _isAnimating = true;

            // アニメーション準備
            SetupBalloonAnimation();

            // 再生
            BalloonAnimator.enabled = true;
            BalloonAnimator.Play(IN_STATE_NAME);

            // アニメーションと同時にオブジェクトの縦幅も変更する
            // 徐々に大きくすることでリストがゆっくり下がるように見せる
            _balloonSizeTweener.Play();
        }

        private void SetupBalloonAnimation()
        {
            // Clipをロード
            if (_overrideController == null)
            {
                var clip = ResourceManager.LoadOnView<AnimationClip>(ResourcePath.STORY_LOG_BALLOON_ANIMATION_CLIP);
                var animator = ResourceManager.LoadOnView<RuntimeAnimatorController>(ResourcePath.STORY_LOG_BALLOON_ANIMATOR);
                _overrideController = new AnimatorOverrideController() { runtimeAnimatorController = animator };
                _overrideController[IN_STATE_NAME] = clip;
                BalloonAnimator.runtimeAnimatorController = _overrideController;
            }

            // Tweenを準備
            if (_balloonSizeTweener == null)
            {
                var rectTransform = transform as RectTransform;
                var endSize = rectTransform.sizeDelta;
                float width = rectTransform.sizeDelta.x;
                rectTransform.sizeDelta = new Vector2(width, 0f);
                _balloonSizeTweener = rectTransform.DOSizeDelta(endSize, TWEEN_DURATION).SetEase(Ease.OutCubic);
            }
        }

        /// <summary>
        /// アニメーション終了処理
        /// </summary>
        public void FinishBalloonAnimation()
        {
            if (!_isAnimating)
            {
                return;
            }

            _isAnimating = false;

            // アニメーションをNormalizeTime=1で再生
            BalloonAnimator.Play(IN_STATE_NAME, BASE_LAYER_INDEX, 1f);

            // Tweenはコンプリートで最終状態に持っていく
            _balloonSizeTweener.Complete();
        }
        #endregion 吹き出し表示アニメーション

        #region 移動アニメーション
        /// <summary>
        /// 移動アニメーションのTweenerを破棄
        /// </summary>
        private void KillMoveTween()
        {
            if (_moveTween != null)
            {
                _moveTween.Kill();
                _moveTween = null;
            }
        }

        /// <summary>
        /// アニメーションを中断して位置を設定する
        /// </summary>
        /// <param name="pos"></param>
        public void ForceSetPosition(Vector3 pos,Action endAction)
        {
            KillMoveTween();
            CacheRectTransform.anchoredPosition = pos;
            endAction?.Invoke();
        }

        /// <summary>
        /// 移動アニメーションを再生する
        /// </summary>
        public void PlayMoveAnimation(Vector3 pos0, Vector3 pos1, float duration, Ease ease,Action endAction)
        {
            KillMoveTween();

            CacheRectTransform.anchoredPosition = pos0;
            _moveTween = CacheRectTransform.DOAnchorPos(pos1, duration).SetEase(ease);
            _moveTween.onComplete = () =>
            {
                endAction?.Invoke();
            };
        }

        /// <summary>
        /// 移動アニメーションを終了する
        /// </summary>
        public void FinishMoveAnimation()
        {
            if (_moveTween == null)
            {
                return;
            }

            // Completeで最終位置に移動させる
            _moveTween.Complete();
        }
        #endregion　移動アニメーション

        public void SetColorSenderColor(Color toColor, float duration)
        {
            if (duration < Math.EPSILON)
            {
                _colorSenderToColor = toColor;
                ColorSender.SendColor = _colorSenderToColor;
                _colorSenderTweener?.Kill();
                _colorSenderTweener = null;
                return;
            }

            if (_colorSenderToColor == toColor)
            {
                return;
            }

            _colorSenderToColor = toColor;

            _colorSenderTweener = DOTween.To(() => ColorSender.SendColor, col =>
            {
                _colorSender.SendColor = col;
            }, _colorSenderToColor, duration);
        }
    }

#if CYG_DEBUG && UNITY_EDITOR
    [CustomEditor(typeof(StoryLogItem))]
    public class StoryLogItemEditor : LoopScrollItemBaseEditor
    {
    }
#endif
}
