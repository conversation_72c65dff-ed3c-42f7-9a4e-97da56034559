using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// シングルモードでの会話画面文字表示
    /// 衣装ストーリー(Gacha)や才能開花ストーリー(LimitBreak)でも利用するモード
    /// </summary>
    public sealed class StoryViewTextControllerSingleMode : StoryViewTextControllerBase
    {
        // フォントサイズの定数
        // SetFontSizeをoverrideする場合は宣言する
        // ** モードによってサイズが違う可能性があるので注意
        // ** 変更する場合はUIチームとも相談すること
        public const int FONT_SIZE_DEFAULT = 42;
        private const int FONT_SIZE_LARGE = 84;
        private const int FONT_SIZE_SMALL = 32;

        [SerializeField]
        private TextFrame _textFrame = null;

        public TextFrame TextFrame => _textFrame;

        public override bool IsAvailable(StoryTimelineController.DisplayMode mode)
        {
            return mode == StoryTimelineController.DisplayMode.SingleMode;
        }

        public override void Initialize(System.Action onClick)
        {
            _textFrame.Initialize();
            _textFrame.SetCursorType(TextFrame.CursorType.Big); // 大きいカーソル
            _textFrame.ShakeTextFrame.Initialize(StoryUIShake.Target.Text);
        }

        public override void Shake(float normalizedTime)
        {
            _textFrame.ShakeTextFrame.Shake(normalizedTime);
        }

        public override void StopShake()
        {
            _textFrame.ShakeTextFrame.StopShake();
        }

        public override void DoOpen( System.Action onComplete )
        {
            _textFrame.DoOpen(onComplete);
        }

        public override void DoClose( System.Action onComplete )
        {
            _textFrame.DoClose(onComplete);
        }

        public override void SetAlpha(float alpha)
        {
            _textFrame.SetAlpha(alpha);
        }

        public override void SetFontSize(StoryTimelineTextClipData.FontSize fontSize)
        {
            switch (fontSize)
            {
                case StoryTimelineTextClipData.FontSize.Large:
                    _textFrame.TextLabel.fontSize = FONT_SIZE_LARGE;
                    break;

                case StoryTimelineTextClipData.FontSize.Small:
                    _textFrame.TextLabel.fontSize = FONT_SIZE_SMALL;
                    break;

                default:
                    _textFrame.TextLabel.fontSize = FONT_SIZE_DEFAULT;
                    break;
            }
        }

        /// <summary>
        /// TextCommonへの参照を取得
        /// </summary>
        public override TextCommon GetTextLabel()
        {
            return _textFrame.TextLabel;
        }

        /// <summary>
        /// 名前を設定
        /// </summary>
        public override void SetNameLabel(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                _textFrame.SetNameLabelVisible(false);
                return;
            }

            _textFrame.SetNameLabelVisible(true);
            _textFrame.NameLabel.text = name;
        }

        /// <summary>
        /// テキストフレームの色指定
        /// </summary>
        public override void SetCharaColor(int charaId)
        {
            // 話者が育成キャラならキャラカラー適用、それ以外はデフォルト
            _textFrame.SetCharaColor(charaId);
        }

        /// <summary>
        /// ウインドウ形状設定
        /// </summary>
        public override void SetupWindowForm(bool hideCharaPosition, string standPosition)
        {
            if (!hideCharaPosition && !string.IsNullOrEmpty(standPosition))
            {
                // 吹き出し口をキャラ位置に設定
                _textFrame.SetCharacterPosition(standPosition);
            }
            else
            {
                // 吹き出し口OFF
                _textFrame.HideCharacterPosition();
            }
        }

        /// <summary>
        /// 新しいTextLabelを作成する
        /// </summary>
        public override void CreateNewTextLabel()
        {
            //１つのTextLabelを使い回すので、何もしない

            //カーソルを非表示化
            _textFrame.SetCursorActive(false);
        }

        public override void PlayAnimation()
        {
        }

        public override void SetActiveNextCursor(bool isActive)
        {
            _textFrame.SetCursorActive(isActive);
        }
    }
}
