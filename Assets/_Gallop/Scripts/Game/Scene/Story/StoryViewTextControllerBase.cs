using System;
using UnityEngine;
using DG.Tweening;

namespace Gallop
{
    /// <inheritdoc />
    /// <summary>
    /// StoryViewでのText表示に関わるクラスの基底クラス
    /// </summary>
    public abstract class StoryViewTextControllerBase : MonoBehaviour
    {
        private const int NO_ACHIEVEMENT_ID = 0;

        protected readonly System.Text.StringBuilder _builder = new System.Text.StringBuilder();

        protected int _textCount = 0;
        protected string _fullText = null;
        protected bool _isTypewriteFinished = false;
        protected Tween _textTween = null;
        private Action _onStartTypewriteOnce;

        public virtual void OnDestroy()
        {
            AbortTypewrite();
        }

        public abstract bool IsAvailable(StoryTimelineController.DisplayMode mode);

        public abstract void Initialize(Action onClick);

        // DisplayModeに依存した初期化がある場合はoverride
        public virtual void Initialize(StoryTimelineController.DisplayMode mode)
        {
        }

        public virtual void Shake(float normalizedTime)
        {
        }

        public virtual void StopShake()
        {
        }

        public virtual void DoOpen(Action onComplete)
        {
            //何もしなかったとしても完了処理は呼んでおかなければならない
            onComplete?.Invoke();
        }

        public virtual void DoClose(Action onComplete)
        {
            //何もしなかったとしても完了処理は呼んでおかなければならない
            onComplete?.Invoke();
        }

        public virtual void SetAlpha(float alpha)
        {
        }

        public virtual void SetFontSize(StoryTimelineTextClipData.FontSize fontSize)
        {
        }

        public virtual int GetFontSize()
        {
            return 0;
        }

        public virtual void SetExtraIcon(int extraId)
        {
        }

        public string GetFullText() => _fullText;

        /// <summary>
        /// テキスト全文を設定する
        /// </summary>
        public virtual void SetFullText(string text)
        {
            _isTypewriteFinished = false;
            _fullText = CreateFullText(text);

            // 事前にRitchTextTagが入る前の文字数を控えておく
            _textCount = _fullText.Length;
        }

        /// <summary>
        /// 入力された文字列に改行除去などの整形を行ってからテキスト全文を作成する
        /// </summary>
        protected virtual string CreateFullText(string text)
        {
            // ストーリーモード横持ちとシングルモードは改行・禁則処理を行わない
            // フールプルーフのため、文末改行の削除とCarriage Returnの除去行う
            return GallopUtil.RemoveLastLineBreak(TextUtil.Replace(text, "\r", ""));
        }

        /// <summary>
        /// TextClipで指定されたカラーフォーマットを付ける (タグ変換しない場合)
        /// </summary>
        public void SetColorFormatWithoutTag(StoryTimelineTextClipData textClipData)
        {
            if (textClipData.ColorTextInfoList.Count == 0)
            {
                return;
            }

            _fullText = ApplyColorFormat(_fullText, textClipData.ColorTextArray, textClipData.FontColorArray);
        }

        /// <summary>
        /// TextClipで指定されたカラーフォーマットを付ける (キャラ名タグの変換が必要な場合はこちら)
        /// </summary>
        public void SetColorFormat(StoryTimelineTextClipData textClipData, int charaId, int supportId, int itemId, bool isMulti, int achievementId)
        {
            if (textClipData.ColorTextInfoList.Count == 0)
            {
                return;
            }

            _fullText =  ApplyColorFormat(_fullText, textClipData.ColorTextArray, textClipData.FontColorArray, charaId, supportId, itemId, isMulti, achievementId);
        }

        /// <summary>
        /// TextClipで指定されたカラーフォーマットを付けた文字列を返す (Skipされたテキストをログに残す場合に使用する)
        /// </summary>
        public string CreateColorText(StoryTimelineTextClipData textClip, int charaId, int supportId, int itemId, bool isMulti, int achievementId)
        {
            // この関数は事前にカラーフォーマットが設定されている事をチェックしてから呼び出す

            var fullText = CreateFullText(textClip.DisplayText);
            return ApplyColorFormat(fullText, textClip.ColorTextArray, textClip.FontColorArray, charaId, supportId, itemId, isMulti, achievementId);
        }

        /// <summary>
        /// 改行を含むテキストの指定された文字列に対してカラーフォーマットを付ける
        /// </summary>
        private string ApplyColorFormat(
            string text,
            string[] targetArray,
            FontColorType[] colorTypeArray,
            int charaId = 0,
            int supportId = 0,
            int itemId = 0,
            bool isMulti = false,
            int achievementId = NO_ACHIEVEMENT_ID)
        {
            var split = text.Split('\n');
            int lineCount = split.Length;

            var lastCharArray = new char[lineCount];
            var countArray = new int[lineCount];
            _builder.Length = 0;
            for (int i = 0; i < lineCount - 1; i++)
            {
                // 各行の末尾の文字を保存する
                _builder.Append(split[i]);
                var last = _builder[_builder.Length - 1];
                lastCharArray[i] = last;

                // 今までのテキスト中に何回出現したか保存する
                countArray[i] = TextUtil.GetCountChar(_builder.ToString(), last);
            }

            // 改行を取り除いてからカラータグを適用
            _builder.Length = 0;
            _builder.Append(TextUtil.RemoveNewLine(text));
            for (int i = 0; i < targetArray.Length; i++)
            {
                // 対象文字列にキャラ名や景品名のタグがあったら変換しておく
                var target = ReplaceCharaNameTag(targetArray[i], charaId);
                target = ReplaceSupportNameTag(target, supportId, isMulti);
                target = ReplaceEventItemNameTag(target, itemId);
                target = ReplaceAchievementTag(target, achievementId);
                var newText = TextUtil.AddColorFormatCode(colorTypeArray[i], target);
                _builder.Replace(target, newText);
            }

            // 改行を復活する
            for (int i = 0; i < lineCount - 1; i++)
            {
                int count = 0;
                for (int j = 0; j < _builder.Length; j++)
                {
                    if (_builder[j] == lastCharArray[i])
                    {
                        // 出現数が一致するまで探す
                        count++;
                        if (count == countArray[i])
                        {
                            // 見つかった文字の後ろに改行を入れる
                            _builder.Insert(j + 1, '\n');
                            break;
                        }
                    }
                }
            }

            return _builder.ToString();
        }

        /// <summary>
        /// タイプライトを開始する
        /// </summary>
        public void StartTypewrite(float speed, Action onFinish)
        {
            var textLabel = GetTextLabel();
            if (textLabel == null)
            {
                _onStartTypewriteOnce?.Invoke();
                _onStartTypewriteOnce = null;
                return;
            }

            // 一度クリアする
            textLabel.text = string.Empty;
            AbortTypewrite();

            float duration = _textCount / speed;
            _textTween = textLabel.DOText(_fullText, duration)
                .SetEase(Ease.Linear)
                .OnUpdate(OnUpdateTypewrite)
                .OnComplete(() => onFinish?.Invoke());

            _onStartTypewriteOnce?.Invoke();
            _onStartTypewriteOnce = null;
        }

        /// <summary>
        /// タイプライト中の処理
        /// </summary>
        protected virtual void OnUpdateTypewrite() {}

        /// <summary>
        /// タイプライトを中断させる (OnCompleteは実行しないので注意)
        /// </summary>
        public void AbortTypewrite()
        {
            if (_textTween != null)
            {
                _textTween.Kill();
                _textTween = null;
            }
        }

        /// <summary>
        /// タイプライトを一時停止
        /// </summary>
        public void PauseTypewrite()
        {
            if (_textTween?.IsComplete() != false) return;

            _textTween.Pause();
        }

        /// <summary>
        /// タイプライトを再開
        /// </summary>
        public void ResumeTypewrite()
        {
            if (_textTween?.IsComplete() != false) return;

            _textTween.Play();
        }

        /// <summary>
        /// ルビ表示を予約する
        /// 通常テキストと同期しながら表示される
        /// </summary>
        public virtual void ShowRequestRuby(TextRubyData.RubyBlockData rubyBlockData, StoryTimelineTextClipData clip)
        {
        }

        /// <summary>
        /// 全てのテキストを表示する
        /// </summary>
        public virtual void ShowFullText()
        {
            if (_isTypewriteFinished)
            {
                return;
            }
            _isTypewriteFinished = true;

            // OnCompleteが実行されるようにKillはしない
            if (_textTween != null)
            {
                _textTween.Complete();
                _textTween = null;
            }

            SetText(_fullText);
            SetActiveNextCursor(true);
        }

        // TextCommonへの参照を取得
        public abstract TextCommon GetTextLabel();
        // テキスト設定
        protected void SetText(string text)
        {
            var textLabel = GetTextLabel();
            if (textLabel != null) textLabel.text = text;
        }

        /// <summary>
        /// 新しいテキストを表示する時の共通設定
        /// </summary>
        public void SetupForNewText(
            StoryLogInfo logInfo,
            int charaId,
            int charaColorId,
            string name,
            string text,
            StoryTimelineTextClipData.FontSize fontSize = StoryTimelineTextClipData.FontSize.Default,
            bool hideCharaPosition = false,
            string standPosition = null)
        {
            CreateNewTextLabel();
            SetLogData(logInfo);
            SetExtraIcon(charaId);
            SetNameLabel(name);
            SetCharaColor(charaColorId);
            SetFontSize(fontSize);
            // 禁則処理に影響するのでFontSize設定より後に実行
            SetFullText(text);
            //窓サイズ設定で文章がほしいのでSetFullTextの後に実行
            SetupWindowSize();
            SetupWindowForm(hideCharaPosition, standPosition);
        }

        public abstract void SetNameLabel(string name);
        public abstract void CreateNewTextLabel();
        public abstract void PlayAnimation();

        // ログの追加
        public virtual void SetLogData(StoryLogInfo logInfo) {}

        // テキストフレームの色指定
        public virtual void SetCharaColor(int charaId) { }

        //ウィンドウ幅を設定
        public virtual void SetupWindowSize()
        {
        }

        /// <summary>
        /// ウインドウ形状設定
        /// </summary>
        public virtual void SetupWindowForm(bool hideCharaPosition, string standPosition)
        {
        }

        public abstract void SetActiveNextCursor(bool isActive);

        public virtual void OnChangeOrientation()
        {
        }

        public virtual void OnChangeAutoHighSpeedSetting()
        {
        }

        #region 表示用のテキストを取得
        /// <summary>
        /// タグを変換した表示用のテキストを取得
        /// </summary>
        public static string GetDisplayText(
            string originalText,
            int trainingCharaId,
            int supportId,
            int itemId = 0,
            bool isMulti = false,
            int achievementId = NO_ACHIEVEMENT_ID)
        {
            string text = ReplaceCharaNameTag(originalText, trainingCharaId);
            text = ReplaceSupportNameTag(text, supportId, isMulti);
            text = ReplaceUserNameTag(text);
            text = ReplaceEventItemNameTag(text, itemId);
            return ReplaceAchievementTag(text, achievementId);
        }

        /// <summary>
        /// キャラ名のタグを変換した文字列を取得
        /// </summary>
        private static string ReplaceCharaNameTag(string text, int trainingCharaId)
        {
            if (!MasterCharaData.IsCharaId(trainingCharaId) || !text.Contains(TextUtil.CHARA_NAME_TAG))
            {
                // 変換不要
                return text;
            }

            return TextUtil.ToCharacterName(text, trainingCharaId);
        }

        /// <summary>
        /// サポートキャラ名のタグを変換した文字列を取得
        /// </summary>
        private static string ReplaceSupportNameTag(string text, int supportId, bool isMulti)
        {
            if (!MasterCharaData.IsCharaId(supportId) || !text.Contains(TextUtil.SUPPORT_NAME_TAG))
            {
                // 変換不要
                return text;
            }

            return TextUtil.ToSupportCharaName(text, supportId, isMulti);
        }

        /// <summary>
        /// ユーザ名のタグを変換した文字列を取得 (育成キャラ名タグ変換を使わないと分かっている場合はこちら)
        /// </summary>
        public static string ReplaceUserNameTag(string text)
        {
            if (!text.Contains(TextUtil.USER_NAME_TAG))
            {
                // 変換不要
                return text;
            }

            return TextUtil.ToUserName(text);
        }

        /// <summary>
        /// ふくびき景品名のタグを変換した文字列を取得
        /// </summary>
        private static string ReplaceEventItemNameTag(string text, int itemId)
        {
            if(!StoryManager.HasInstance())
            {
                //変換できない
                return text;
            }

            var eventData = StoryManager.Instance.EventProduction;
            if (eventData == null)
            {
                // 変換不要
                return text;
            }

            if (!text.Contains(TextUtil.EVENT_ITEM_NAME_TAG))
            {
                // 変換不要
                return text;
            }

            return TextUtil.ToEventItemName(text, eventData.EventCategoryId, itemId);
        }

        /// <summary>
        /// 達成した業績のタグを変換した文字列を取得（フリー編：〇冠達成イベント）
        /// </summary>
        private static string ReplaceAchievementTag(string text, int achievementId)
        {
            if (achievementId <= NO_ACHIEVEMENT_ID)
            {
                // 変換不要
                return text;
            }

            if (text.Contains(TextUtil.ACHIEVEMENT_NAME_TAG))
            {
                text = TextUtil.ToAchievementText(text, achievementId, MasterString.Category.SingleModeAchievementName, TextUtil.ACHIEVEMENT_NAME_TAG);
            }

            if (text.Contains(TextUtil.ACHIEVEMENT_INFO_TAG))
            {
                text = TextUtil.ToAchievementText(text, achievementId, MasterString.Category.SingleModeAchievementInfo, TextUtil.ACHIEVEMENT_INFO_TAG);
            }

            return text;
        }
        #endregion 表示用のテキストを取得

        /// <summary>
        /// 名前とテキストをリセットする
        /// </summary>
        public void ResetText()
        {
            SetNameLabel(string.Empty);
            ResetTextOnly();
        }

        /// <summary>
        /// テキストをリセットする
        /// </summary>
        public void ResetTextOnly()
        {
            SetText(string.Empty);
        }

        /// <summary>
        /// タイプライト開始時のコールバックを設定（1度限り実行）
        /// </summary>
        /// <param name="onStartTypewriteOnce"></param>
        public void SetOnStartTypewriteOnce(Action onStartTypewriteOnce)
        {
            _onStartTypewriteOnce = onStartTypewriteOnce;
        }
    }
}
