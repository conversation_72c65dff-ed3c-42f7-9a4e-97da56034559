using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Gallop.Tutorial;

namespace Gallop
{
    /// <summary>
    /// マイページ：ホームトップ
    /// </summary>
    public class MyPageHomeTopUI : HomeTopUIBase
    {
        #region class, enum, const

        private const float DELAY_VOICE_DURATION = 0.2f;

        #endregion

        #region SerializeField, Property

        ///<summary> タッチ操作エリア </summary>
        [field: SerializeField, RenameField]
        public UITouchArea TouchArea { get; private set; } = null;

        ///<summary> 拡張セーフエリアのヘッダーの親オブジェクト </summary>
        [field: SerializeField, RenameField]
        public RectTransform OverSafeAreaHeaderRoot { get; private set; } = null;

        ///<summary> 吹き出し </summary>
        [field: SerializeField, RenameField]
        public PartsHomeCharaMessage CharaMessage { get; private set; } = null;

        ///<summary> バナー </summary>
        [field: SerializeField, RenameField]
        public BannerUI Banner { get; private set; } = null;

        ///<summary> イベントロゴ </summary>
        [field: SerializeField, RenameField]
        public MyPageEventLogoCurousel EventLogoCurousel { get; private set; } = null;

        /// <summary> ウマ娘変更ボタン  </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon ChangeCharaButton { get; private set; } = null;

        /// <summary> ウマ娘変更ボタン（ロックアイコン） </summary>
        [field: SerializeField, RenameField]
        public ImageCommon LockChangeCharaImage = null;
        
        /// <summary> キャンペーンボタン（ルート）  </summary>
        [field: SerializeField, RenameField]
        public GameObject CampaignButtonRoot { get; private set; } = null;
        
        /// <summary> キャンペーンボタン  </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon CampaignButton { get; private set; } = null;

        /// <summary> バレンタイン専用キャンペーンボタン  </summary>
        [field: SerializeField, RenameField]
        public GameObject ValentineCampaignButtonRoot { get; private set; } = null;
        
        /// <summary> バレンタイン専用キャンペーンボタン  </summary>
        [field: SerializeField, RenameField]
        public PartsHomeValentineButton ValentineCampaignButton { get; private set; } = null;
        
        ///<summary> 育成ボタンの親 </summary>
        [field: SerializeField, RenameField]
        public RectTransform TrainingButtonRoot { get; private set; } = null;

        ///<summary> 育成ボタン </summary>
        [field: SerializeField, RenameField]
        public HomeTrainingButton ButtonTraining { get; private set; } = null;
        
        ///<summary> ショップ </summary>
        [field: SerializeField, RenameField]
        public PartsShopButton ButtonShop { get; private set; } = null;

        ///<summary> ミッション </summary>
        [field: SerializeField, RenameField]
        public PartsMissionButton ButtonMission { get; private set; } = null;
        
        ///<summary> ミッションキャンペーン </summary>
        [field: SerializeField, RenameField]
        public GameObject MissionCampaignObj { get; private set; } = null;

        ///<summary> サークル </summary>
        [field: SerializeField, RenameField]
        public PartsCircleButton ButtonCircle { get; private set; } = null;

        ///<summary> お知らせ </summary>
        [field: SerializeField, RenameField]
        public PartsNewsButton ButtonNews { get; private set; } = null;

        ///<summary> プレゼント </summary>
        [field: SerializeField, RenameField]
        public PartsPresentButton ButtonPresent { get; private set; } = null;
        
        ///<summary> ライブシアター </summary>
        [field: SerializeField, RenameField]
        public PartsLiveTheaterButton ButtonTheater { get; private set; } = null;

        /// <summary> 会話アイコン表示 </summary>
        [SerializeField, RenameField]
        public HomeTalkIconLayout TalkIconLayout = null;
        
        #endregion

        #region 変数

        // イベント購読
        private readonly CompositeDisposable _subscription = new CompositeDisposable();

        // ホーム開始時ダイアログ表示チェック
        private HomeStartCheckSequence _checkSeq = null;

        // 遷移時遅延ボイスシーケンス
        private Tween _tweenDelayVoice;

        private bool _isPlayOuting;
        private bool _isCameraResetPos = false;

        private List<HomeDefine.StoryArea> _unReadStoryArea;

        private long _playInTime = 0;

        #endregion

        #region override
        
        public static void RegisterDownloadStatic(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.HOME_TRAINING_BUTTON_EFFECT_NAME);
            register.RegisterPathWithoutInfo(ResourcePath.HOME_TRAINING_BUTTON_TAT_NAME);
            register.RegisterPathWithoutInfo(ResourcePath.BADGE_ICON_TAT_PATH);

            //キャンペーンの開催でタイトルロゴ変更の必要があればダウンロードする
            var saveDataManager = SaveDataManager.Instance.SaveLoader;
            var campaignData = MasterDataManager.Instance.masterCampaignData.GetActiveChangeTitleLogoData();
            if (campaignData != null)
            {
                //データが存在していればダウンロード登録。（ただし現在はログイン時に全て落とす設定になっているので不要）
                register.RegisterPathWithoutInfo(ResourcePath.GetCampaginTitleLogo(campaignData.CampaignId));
                
                //切り替え用にデータを保存しておく
                if (saveDataManager.CampaignTitleLogoChangeId != campaignData.CampaignId || saveDataManager.CampaignTitleLogoChangeEndTime != campaignData.EndTime)
                {
                    //保存しているIDが異なっていた場合は、保存する
                    saveDataManager.CampaignTitleLogoChangeId = campaignData.CampaignId;
                    saveDataManager.CampaignTitleLogoChangeEndTime = campaignData.EndTime;
                    saveDataManager.Save();
                }
            }
            //そうでない場合でセーブデータが残っていたらリセットする
            else if (saveDataManager.CampaignTitleLogoChangeId > 0)
            {
                saveDataManager.ResetCampaignTitleLogoData();
            }
            
            //ストーリー解放ダイアログのリソースを落とす
            DialogStoryReleaseNotice.RegisterDownload(register);
            
            //バナーのダウンロードを行う
            BannerUI.RegisterDownload(register);
            
            //チュートリアル中はここから先はしない
            if (TutorialManager.IsTutorialExecuting())
            {
                return;
            }

            // ストーリーイベント開催中または開催予定
            if (WorkDataManager.Instance.StoryEventData.StoryEventId > 0)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetStoryEventLogoPath(WorkDataManager.Instance.StoryEventData.StoryEventId));
            }

            // チャレンジマッチボタンを表示すべきならリソースを落とす
            PartsChallengeMatchButton.RegisterDownloadIfNeed(register);
            // 特別移籍イベントボタンを表示すべきならリソースを落とす
            PartsTransferEventButton.RegisterDownloadIfNeed(register);

            // 育成チャレンジ
            PartsTrainingChallengeButton.RegisterDownloadIfNeeded(register);

            // ショップボタン用ダウンロード登録
            PartsShopButton.RegisterDownloadStatic(register);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public override IEnumerator InitializeView()
        {
            // プロローグチュートリアル中は動的要素を排除する
            bool isPrologue = TutorialManager.IsStepPrologueHome();
            
            // イベント購読
            _subscription.Clear();
            _subscription.Add(SceneController.TouchCharacterSubject.Subscribe(OnTouchCharacterEvent));
            _subscription.Add(SceneController.UpdateFreeCameraSubject.Subscribe(OnUpdateFreeCameraEvent));
            _subscription.Add(SceneController.StartStorySubject.Subscribe(OnStartStoryEvent));
            _subscription.Add(SceneController.UpdateStoryIconEventInfoSubject.Subscribe(OnUpdateStoryIconEvent));

            //ダイアログ表示チェッククラス作成
            var view = ViewController.GetView();
            _checkSeq = new HomeStartCheckSequence(view);

            //拡張セーフエリアベースのオブジェクトを設定する。いったん子オブジェクトに置いてポジションを設定した後に戻す
            var baseParent = OverSafeAreaHeaderRoot.parent;
            OverSafeAreaHeaderRoot.SetParent(view.OverSafeAreaContentsRoot);
            OverSafeAreaHeaderRoot.anchoredPosition = Math.VECTOR2_ZERO;
            OverSafeAreaHeaderRoot.SetParent(baseParent);

            // ボタン設定
            SetupButtons();

            // タッチ操作設定
            TouchArea.SetClickCallback(OnClickTouchArea);
            TouchArea.SetScrollCallback(OnScrollTouchArea);
            TouchArea.SetPointerDownCallback(OnPointerDownTouchArea);
            TouchArea.SetPointerUpCallback(OnPointerUpTouchArea);
            TouchArea.SetIsTouchEffectReaction(IsTouchEffectReaction);

            // プロローグ中は非表示にするものもある
            if (isPrologue)
            {
                CharaMessage.SetActiveWithCheck(false);
                CharaMessage.SetEnable(false);
                Banner.SetActiveWithCheck(false);
                EventLogoCurousel.SetActiveWithCheck(false);
                TalkIconLayout.SetVisibleWithAnim(false, immediate: true);
            }
            else
            {
                // 吹き出し設定
                var chara = ViewController.Director.GetStandModelController(HomeDefine.StandPos.Mypage);
                CharaMessage.SetModel(chara, false);
                CharaMessage.SetTime(ViewController.BgSeason, ViewController.Time, ViewController.EventId);
                CharaMessage.Stop();
                CharaMessage.SetEnable(false);
                
                //バナー設定
                Banner.Init();
                Banner.OnShow();
                Banner.SetAnnounceEventAction(OnAnnounceEventDialogOpen, OnAnnounceEventDialogClose);

                // イベントロゴ設定
                EventLogoCurousel.InitializeView();
            }
            
            //育成ボタンにエフェクトを追加する
            var trainingButtonEffect = ResourceManager.LoadOnView<GameObject>(ResourcePath.HOME_TRAINING_BUTTON_EFFECT_NAME , SceneDefine.ViewId.HomeHub);
            var buttonEffect = GameObject.Instantiate(trainingButtonEffect, TrainingButtonRoot);
            buttonEffect.transform.SetAsLastSibling();
            
            var trainingButtonTat = ResourceManager.LoadOnView<GameObject>(ResourcePath.HOME_TRAINING_BUTTON_TAT_NAME, SceneDefine.ViewId.HomeHub);
            var buttonTat = GameObject.Instantiate(trainingButtonTat, TrainingButtonRoot);
            buttonTat.transform.SetAsFirstSibling();

            // 会話アイコン
            TalkIconLayout.Init(
                ViewController.Director.GetTalkIconTransform(),
                ViewController.Director.GetPosterBoardIconPosition(),
                ViewController.Director.GetJukeboxIconPosition(),
                view.ContentsRoot,                
                ViewController.Director.GetPosterArray());
            TalkIconLayout.SetVisibleWithAnim(false, true);
            _unReadStoryArea = ViewController.Director.CurrentStoryDict
                .Where(x => !WorkDataManager.Instance.AlreadyReadData.IsAlreadyReadHomeStory(x.Value.Id))
                .Select(x => HomeUtil.GetStoryAreaFromCameraPos(x.Key))
                .ToList();
            TalkIconLayout.UpdateColor(_unReadStoryArea);
            
            yield return base.InitializeView();
        }

        public override IEnumerator PlayInView()
        {
            _isPlayOuting = false;
            _isCameraResetPos = true;
            _playInTime = TimeUtil.GetServerTimeStamp();

            //機能別メンテなどでホームに強制的に戻った際にフラグが立ったままになるのを防ぐために消す
            if (WorkDataManager.Instance.SingleMode.IsPlaying)
            {
                Debug.LogWarning("想定外のフローで育成データが削除されました");
                SingleModeUtils.ClearPlayData();
            }
            
            // プロローグ中はキャンペーン情報などを非表示
            bool isPrologue = TutorialManager.IsStepPrologueHome();
            if (isPrologue)
            {
                CampaignButtonRoot.SetActiveWithCheck(false);
                ValentineCampaignButtonRoot.SetActiveWithCheck(false);
                MissionCampaignObj.SetActiveWithCheck(false);
                yield return base.PlayInView();
                TutorialOutGame.PlayShortStoryOnMypageTab(this);
                yield break;
            }

            Banner.OnShow();
            EventLogoCurousel.PlayInView();
            UpdateTrainingButtonImage();//育成ボタンの表示状況が変わっている可能性があるので更新
            
            // 通常キャンペーンボタン
            bool isExistCampaignDialog = MasterDataManager.Instance.masterCampaignData.IsExistActiveCampaign(_playInTime, true); ;
            CampaignButtonRoot.SetActiveWithCheck(isExistCampaignDialog);
            if (isExistCampaignDialog)
            {
                CampaignButton.SetOnClick(() => { DialogCampaignList.PushDialog(_playInTime, true, UpdateTrainingButtonImage); });
            }
            
            // バレンタイン専用キャンペーンボタン
            ValentineCampaignButton.UpdateOnPlayIn(_playInTime);
            ValentineCampaignButtonRoot.SetActiveWithCheck(ValentineCampaignButton.IsActive);

            // ミッションボタン上の「限定ミッション」アイコンの有無をセット
            {
                // 「永続ではないミッション系キャンペーン」の一覧を取得
                var activeMissionCampaignList = MasterDataManager.Instance.masterCampaignData.GetActiveList(_playInTime, MasterCampaignData.TargetCategory.Mission);
                bool isExistMissionCampaign = activeMissionCampaignList != null && activeMissionCampaignList.Count > 0;

                // ミッション画面の「限定ミッション」の数を取得
                var viewLimitedMissionNum = TempData.Instance.ViewLimitedMissionNum;
                bool isExistLimitedMission = (viewLimitedMissionNum >= 1);

                bool isActiveMissionCampaignObj = isExistMissionCampaign || isExistLimitedMission;
                MissionCampaignObj.SetActive(isExistLimitedMission);
            }

            PlayInButton(true);
            
            yield return base.PlayInView();

            // 画面遷移後

            // ミッション「ホーム設定を変更しよう」への「挑戦」による遷移なら
            if (WorkDataManager.Instance.MissionData.HasChallangeAction(WorkMissionData.ChallangeAction.OpenDialogHomeChangeFavorite))
            {
                // ホーム設定ダイアログを開く
                DialogHomeChangeFavorite.Open();
            }
            WorkDataManager.Instance.MissionData.ResetChallangeAction();
            
            // ホームトップ表示時のダイアログ表示チェック（お知らせなどを開く）
            // ----
            // 以下の条件であればスキップ
            // ・ミッションの「挑戦」による遷移ならば表示したくないのでスキップする
            // ・チュートリアル実行中の場合
            if (!WorkDataManager.Instance.MissionData.HasChallangeAction(WorkMissionData.ChallangeAction.OpenDialogHomeChangeFavorite) &&
                !TutorialManager.IsTutorialExecuting())
            {
                _checkSeq.OnShowHomeTopUI(StartHomeActiveEvent);
            }
            else
            {
                // ダイアログ表示シーケンスが無いのですぐに能動イベントを開始する
                StartHomeActiveEvent();
            }
        }

        /// <summary>
        /// ホーム側で能動的に動作する機能を開始する
        /// </summary>
        /// <remarks>
        /// 対象はキャラの吹き出し表示等。
        /// ホームの初期化シーケンスが完了してから開始させたいのでタイミングをそろえる
        /// </remarks>
        private void StartHomeActiveEvent()
        {
            void PlayVoiceSequence()
            {
                _tweenDelayVoice?.Kill();
                _tweenDelayVoice = DOVirtual.DelayedCall(DELAY_VOICE_DURATION, () =>
                {   
                    // PlayIn後のディレイ中にユーザ操作があった場合は自動で喋らせない
                    if (_isCameraResetPos)
                    {
                        CharaMessage.SetEnable(true);
                        CharaMessage.PlaySet(useSmoothFaceBlend:true);
                    }
                });
            }

            void CheckAndReOpenValentineDialog()
            {
                // Note: HomeStartCheckSequenceでのチェックが困難だったためここでチェック。
                // ダイアログからの他画面の導線が複数存在する（今後も増える可能性もある）ため、画面遷移時コールバック（onChangeView）の引き渡しが現実的ではないのを考慮。
                if (ValentineCampaignButton.IsActive &&
                    ViewController.ViewInfo != null &&
                    ViewController.ViewInfo.NeedValentineSelectDialog)
                {
                    ViewController.ViewInfo.NeedValentineSelectDialog = false;
                    ValentineCampaignButton.ExecuteClickButtonAction();
                }
            }

            if (TutorialManager.IsTutorialExecuting())
            {
                TutorialOutGame.OpenGuideByCurrentState(PlayVoiceSequence);
            }
            else
            {
                // バレンタイン選択ダイアログの再表示が必要なら開く
                CheckAndReOpenValentineDialog();
                
                PlayVoiceSequence();

                // 必要ならショップボタンリボンのアニメーション開始
                ButtonShop.PlayRibbonAnimationIfNeed();

                // ミッション達成通知が残っているならここで表示
                UIManager.Instance.NoticeMissionClearUI.Show();
            }
        }        

        /// <summary>
        /// 非表示
        /// </summary>
        public override IEnumerator PlayOutView()
        {
            // マイページから移動したらキャラはあいさつしなくてよい
            TempData.Instance.HomeData.NeedHelloVoice = false;
            
            if (_tweenDelayVoice != null)
            {
                _tweenDelayVoice.Kill();
                _tweenDelayVoice = null;
            }

            CharaMessage.Stop();
            CharaMessage.SetEnable(false);
            TalkIconLayout.SetVisibleWithAnim(false);

            _isPlayOuting = true;

            if (SceneManager.Instance.IsRunChangeView)
            {
                EventLogoCurousel.PlayOutAnimation();

                yield return ExecTweenAnimation(isPlayIn: false, () =>
                {
                    if (gameObject != null)
                    {
                        gameObject.SetActive(false);
                    }
                });   
            }
            else
            {
                yield return base.PlayOutView();
            }
        }

        public override void UpdateView()
        {
            Banner.AlterUpdate();
            EventLogoCurousel.UpdateView();
            base.UpdateView();
        }

        public override void LateUpdateView()
        {
            // プロローグ中はキャンペーン情報などを非表示
            bool isPrologue = TutorialManager.IsStepPrologueHome();
            if (isPrologue) return;
            
            if (!TalkIconLayout.IsShow && !_isPlayOuting)
            {
                TalkIconLayout.SetVisibleWithAnim(true);
            }
            TalkIconLayout.AlterLateUpdate(ViewController.Director.GetCamera(), _unReadStoryArea);
        }

        public override IEnumerator FinalizeView()
        {
            TalkIconLayout.OnFinalize();
            _subscription?.Dispose();
            yield return base.FinalizeView();
        }
        
        /// <summary>
        /// 破棄処理
        /// </summary>
        private void OnDestroy()
        {
            _subscription?.Dispose();
        }

        /// <summary>
        /// 戻るボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {   
            if (GameDefine.AppChannel_BUMA == GameDefine.Channel_BUMA.BILIUO)
            {
                //游戏退出 uo渠道
                SDKManager_BUMA.Instance.Exit();
                return;
            }
            //アプリ終了確認ダイアログを開く（GoogleFBでバックキーではアプリ終了してほしいとのこと）
            var dialog = new DialogCommon.Data();
            dialog.FormType = DialogCommon.FormType.SMALL_TWO_BUTTON;
            dialog.Title = TextId.Title0040.Text();
            dialog.Text = TextId.Title0041.Text();
            dialog.RightButtonText = TextId.Common0003.Text();
            dialog.RightButtonCallBack = (dialogCommon) =>
            {
#if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
#elif UNITY_ANDROID || DMM
                    GameSystem.ApplicationQuit();
#endif
            };
            dialog.LeftButtonText = TextId.Common0004.Text();
            DialogManager.PushDialog(dialog);
        }

        #endregion

        #region メンバー関数

        /// <summary>
        /// ボタン設定
        /// </summary>
        private void SetupButtons()
        {
            // ボタン設定
            ButtonShop.Setup();
            ButtonMission.Setup();
            ButtonCircle.Setup();
            ButtonPresent.Setup(() => PlayInButton(false));
            ButtonTheater.Setup();
            ButtonNews.Setup(() => PlayInButton(false));
            ButtonTraining.Setup();

            //ウマ娘変更ボタン
            ChangeCharaButton.SetOnClick(OnClickChangeCharaButton);
        }

        /// <summary>
        /// ボタンのPlayInを呼び出し
        /// </summary>
        public void PlayInButton(bool needPlayInAnim)
        {
            ButtonShop.PlayIn();
            ButtonMission.PlayIn();
            ButtonCircle.PlayIn();
            ButtonPresent.PlayIn();
            ButtonTheater.Setup(); // ミッション報酬で楽曲を入手するケースもあるので、ここでもボタン表示を更新
            ButtonTheater.PlayIn();
            ButtonNews.PlayIn();
            ButtonTraining.PlayIn();

            // HomeTopUIBase準拠のアニメでない場合はここでイリアニメを再生する
            if (needPlayInAnim)
            {
                EventLogoCurousel.PlayInAnimation();
            }
        }

        /// <summary>
        /// 最新の育成状況に合わせてボタンを更新する（キャンペーンなどで外部から育成情報消された用）
        /// </summary>
        public void UpdateTrainingButtonImage()
        {
            ButtonTraining.UpdateButtonImage(false);
        }
        
        #region イベントコールバック

        /// <summary>
        /// キャラクタータッチのイベント発火時
        /// </summary>
        /// <param name="info"></param>
        private void OnTouchCharacterEvent(HomeTouchCharacterEventInfo info)
        {
            TouchCharacter(info.StandPos);
        }
        
        /// <summary>
        /// フリーカメラ更新のイベント発火時
        /// </summary>
        /// <param name="info"></param>
        private void OnUpdateFreeCameraEvent(HomeUpdateFreeCameraEventInfo info)
        {
            switch (info.Type)
            {
                case HomeUpdateFreeCameraEventInfo.EventType.MoveOnResetPosition:
                    // フリーカメラ動作時でもカメラリセット位置に近ければ何もしない
                    break;
                case HomeUpdateFreeCameraEventInfo.EventType.Move:
                {
                    // フリーカメラ動作時は吹き出しを消す
                    if (CharaMessage.IsPlaying)
                    {
                        CharaMessage.HideUI();   
                    }

                    _isCameraResetPos = false;
                    CharaMessage.SetEnable(false);
                    break;
                }
                case HomeUpdateFreeCameraEventInfo.EventType.Reset:
                {
                    _isCameraResetPos = true;
            
                    // フリーカメラがリセットされたら吹き出しを表示
                    if (CharaMessage.IsPlaying)
                    {
                        CharaMessage.ReShowUI();
                    }
            
                    CharaMessage.SetEnable(true);
                    break;
                }
            }
        }

        /// <summary>
        /// 会話開始のイベント発火時
        /// </summary>
        /// <param name="info"></param>
        private void OnStartStoryEvent(HomeStartStoryEventInfo info)
        {
            var storyDict = ViewController.Director.CurrentStoryDict;
            if (storyDict.TryGetValue(info.CameraPos, out var storyTrigger))
            {
                int storyId = storyTrigger.Id;
                bool isFirstRead = WorkDataManager.Instance.AlreadyReadData.IsAlreadyReadHomeStory(storyId) == false;
                if (isFirstRead)
                {
                    WorkDataManager.Instance.TalkGalleryData.AddNewInfo(storyId);
                }
                
                WorkDataManager.Instance.AlreadyReadData.AddReadHomeStory(storyId);
            }
        }

        /// <summary>
        /// 会話アイコンの更新イベント発火時
        /// </summary>
        /// <param name="info"></param>
        private void OnUpdateStoryIconEvent(HomeUpdateStoryIconEventInfo info)
        {
            _unReadStoryArea = ViewController.Director.CurrentStoryDict
                .Where(x => !WorkDataManager.Instance.AlreadyReadData.IsAlreadyReadHomeStory(x.Value.Id))
                .Select(x => HomeUtil.GetStoryAreaFromCameraPos(x.Key))
                .ToList();
            TalkIconLayout.UpdateColor(_unReadStoryArea);
        }

        #endregion

        /// <summary>
        /// キャラクターが選択された時のコールバック
        /// </summary>
        /// <param name="standPos"></param>
        private void TouchCharacter(HomeDefine.StandPos standPos)
        {
            if (standPos != HomeDefine.StandPos.Mypage)
            {
                return;
            }

            CharaMessage.SetEnable(true);
            CharaMessage.PlayTap();
        }

        /// <summary>
        /// タッチ操作エリアのクリック時コールバック
        /// </summary>
        private void OnClickTouchArea(Vector2 screenPosition)
        {
            if (TouchArea.IsLock())
                return;
            
            // イベント発火
            var sceneController = SceneManager.Instance.GetCurrentSceneController<HomeSceneController>();
            sceneController.TriggerTapScreenEvent(screenPosition);
        }

        /// <summary>
        /// タッチ操作エリアのスクロール時コールバック
        /// </summary>
        /// <param name="delta"></param>
        private void OnScrollTouchArea(Vector2 delta)
        {
            if (TouchArea.IsLock())
                return;

            SceneController.TriggerPreUpdateFreeCameraEvent(HomePreUpdateFreeCameraEventInfo.EventType.Drag, delta);
        }

        /// <summary>
        /// タッチ操作エリアのDown時
        /// </summary>
        private void OnPointerDownTouchArea(Vector2 screenPosition)
        {
            if (TouchArea.IsLock())
                return;

            SceneController.TriggerPreUpdateFreeCameraEvent(HomePreUpdateFreeCameraEventInfo.EventType.Drag, Math.VECTOR2_ZERO);
        }

        /// <summary>
        /// タッチ操作エリアのUp時
        /// </summary>
        private void OnPointerUpTouchArea(Vector2 screenPosition)
        {
            // 念のため入れておくが、UP時にReturnして困るケースがあれば消してもいい
            if (TouchArea.IsLock())
                return;

            SceneController.TriggerPreUpdateFreeCameraEvent(HomePreUpdateFreeCameraEventInfo.EventType.PointerUp);
        }

        /// <summary>
        /// タッチエフェクト出すか？
        /// </summary>
        /// <param name="screenPosition"></param>
        /// <returns></returns>
        private bool IsTouchEffectReaction(Vector2 screenPosition)
        {
            return ViewController.Director.CheckTouch(screenPosition, null);
        }

        /// <summary>
        /// ウマ娘変更ボタン押下
        /// </summary>
        private void OnClickChangeCharaButton()
        {
            DialogHomeChangeFavorite.Open();
        }

        /// <summary>
        /// コールバック：イベント告知ダイアログを開いた時
        /// </summary>
        private void OnAnnounceEventDialogOpen()
        {
            CharaMessage.Stop();
            CharaMessage.SetEnable(false);
        }

        /// <summary>
        /// コールバック：イベント告知ダイアログを閉じた時
        /// </summary>
        private void OnAnnounceEventDialogClose()
        {
            // フリーカメラモードではないならキャラ台詞を有効にする
            if (_isCameraResetPos)
            {
                CharaMessage.SetEnable(true);   
            }
        }

        #endregion

        #region mono
        /// <summary>
        /// google play point 付与処理
        /// ゲーム起動してる状態ならplay ストアからゲームに切り替えた時点で付与する。
        /// </summary>
        /// <param name="focus"></param>
        private void OnApplicationFocus(bool focus)
        {
#if UNITY_ANDROID
            if (focus)
            {
                //他のダイアログなくて、通常課金処理なくて、チュートリアル完了してる
                if (!PaymentUtility.Instance.IsProceedingPayment() && SaveDataManager.Instance.SaveLoader.IsTutorialFinished)
                {
                    Debug.Log("OnApplicationFocus====PaymentUtility====PaymentProcess");
#if ENABLE_PLATFORM_FUNCTIONS
                    PaymentUtility.Instance.PaymentProcess(()=> PaymentUtility.Instance.FinishPlatformPurchase(), null);
#endif
                }
            }
#endif
        }
        #endregion
    }
}
