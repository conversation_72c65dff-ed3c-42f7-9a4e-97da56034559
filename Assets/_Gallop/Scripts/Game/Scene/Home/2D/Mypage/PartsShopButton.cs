using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ホーム：ショップボタン
    /// </summary>
    public class PartsShopButton : MonoBehaviour
    {
        #region 定数

        /// <summary>
        /// 限定セールスバッジ位置
        /// </summary>
        private static readonly Vector2 LIMITED_SELES_BADGE_POS = new Vector2(0, 73);

        /// <summary>
        /// 特設ショップリボン用定数
        /// </summary>
        private static readonly string RIBBON_TEXT_SPRITE = "utx_txt_specialshop_now_00";
        private static readonly string RIBBON_ANIME_IN = "in";
        private static readonly string RIBBON_ANIME_LOOP = "loop";
        private static readonly string RIBBON_SPRITE_ROOT = "PLN_dum_img_text00";
        private static readonly int RIBBON_SORT_OFFSET = 10;

        #endregion

        [SerializeField]
        private ButtonCommon _button = null;
        
        [SerializeField]
        private ImageCommon _lockImage = null;

        [SerializeField]
        private Transform _ribbonRoot = null;

        [SerializeField]
        private CanvasRenderer _canvasRenderer = null;


        /// <summary>
        /// 「限定セールス」バッジ
        /// </summary>
        private FlashPlayer _limitedSalesBadge;
        private bool _limitedSalesBadgeActive = false;

        /// <summary>
        /// 通知バッジ：「ショップの解放条件付きアイテムの条件をクリア」or「サプチケ有り」
        /// </summary>
        private FlashPlayer _notifyBadge;
        private bool _notifyBadgeActive = false;

        /// <summary>
        /// 特設ショップリボン
        /// </summary>
        private FlashActionPlayer _specialShopRibbon;

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownloadStatic(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.HOME_SHOP_RIBBON_EFFECT_FLASH);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup()
        {
            _button.SetInteractable(true);
            _button.SetNotificationMessage(string.Empty);
            _button.SetOnClick(OnClick);
            _lockImage.SetActiveWithCheck(false);
            
            // 限定セールスバッジ
            if (_limitedSalesBadge == null)
            {
                _limitedSalesBadge = UIUtil.CreateLimitedSalesBadgeIconFlash(transform, (int)UIManager.CanvasSoringOrder.Main, _button.image.canvasRenderer);
                _limitedSalesBadge.transform.localPosition = LIMITED_SELES_BADGE_POS;
            }
            
            // 通知バッジ
            if (_notifyBadge == null)
            {
                _notifyBadge = UIUtil.CreateNotifyBadgeIconFlash(transform, UIManager.CanvasSoringOrder.Main, _button.image.canvasRenderer);
                _notifyBadge.transform.localPosition = HomeUtil.BOTTOM_ICON_BADGE_POS;
            }

            // リボン
            SetupRibbon(MasterDataManager.Instance.masterItemExchangeTop.IsInTermAnyAnnivShop);

            UpdateBadgeActiveStatus();
            
            _limitedSalesBadge.SetActiveWithCheck(_limitedSalesBadgeActive);
            _notifyBadge.SetActiveWithCheck(_notifyBadgeActive);
        }

        public void PlayIn()
        {
            // バッジ表示状態の更新
            UpdateBadgeActiveStatus();
            
            if (_limitedSalesBadge != null)
            {
                _limitedSalesBadge.SetActiveWithCheck(_limitedSalesBadgeActive);
                if (_limitedSalesBadgeActive)
                    UIUtil.ReplayBadgeIconFlashText(_limitedSalesBadge);
            }
            
            if (_notifyBadge != null)
            {
                _notifyBadge.SetActiveWithCheck(_notifyBadgeActive);
                if (_notifyBadgeActive)
                    UIUtil.ReplayBadgeIconFlashText(_notifyBadge);
            }

            // リボン表示状態の更新
            SetupRibbon(MasterDataManager.Instance.masterItemExchangeTop.IsInTermAnyAnnivShop);
        }

        public void PlayRibbonAnimationIfNeed()
        {
            if (_specialShopRibbon != null && _specialShopRibbon.isActiveAndEnabled)
            {
                _specialShopRibbon.Play(RIBBON_ANIME_LOOP);
            }
        }

        /// <summary>
        /// バッジアクティブ状態を
        /// </summary>
        private void UpdateBadgeActiveStatus()
        {
            _limitedSalesBadgeActive = NeedsShowLimitedBadge();
            
            if (_limitedSalesBadgeActive)
            {
                // 限定セールスバッジが表示されていたら、通知バッジを非表示
                _notifyBadgeActive = false;
            }
            else
            {
                // 通知バッジを表示：「ショップの解放条件付きアイテムの条件をクリア」or「サプチケ有り」
                var haveSurpriseCardTicket = WorkDataManager.Instance.ItemData.GetListByCategory(GameDefine.ItemCategory.SURPRISE_CARD_TICKET).Count > 0;
                var haveSurpriseSupportTicket = WorkDataManager.Instance.ItemData.GetListByCategory(GameDefine.ItemCategory.SURPRISE_SUPPORT_TICKET).Count > 0;
                var isReleaseItem = WorkDataManager.Instance.LimitedSalesData.IsReleaseItem;
                _notifyBadgeActive = haveSurpriseCardTicket || haveSurpriseSupportTicket || isReleaseItem;
            }
        }

        /// <summary>
        /// リボンの表示状態更新
        /// </summary>
        private void SetupRibbon(bool show)
        {
            if (_specialShopRibbon != null)
            {
                _specialShopRibbon.SetActiveWithCheck(show);
                if (show)
                {
                    _specialShopRibbon.Play(RIBBON_ANIME_IN);
                }
                return;
            }

            if (show)
            {
                InstantiateAndShowRibbon();
            }
        }

        private void OnClick()
        {
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.MenuShop);
        }

        /// <summary>
        /// 限定バッジ表示が必要な場合true
        /// </summary>
        private bool NeedsShowLimitedBadge()
        {
            if (WorkDataManager.Instance.LimitedSalesData.IsOpen) return true;

            return false;
        }

        /// <summary>
        /// 特設ショップリボン　アニメーションオブジェクト生成
        /// </summary>
        private void InstantiateAndShowRibbon()
        {
            var flash = ResourceManager.LoadOnScene<GameObject>(ResourcePath.HOME_SHOP_RIBBON_EFFECT_FLASH);
            if (flash == null)
            {
                Debug.LogWarningFormat("特設ショップリボンのFlashがない");
                return;
            }
            var obj = Instantiate(flash, _ribbonRoot);
            var sprite = UIManager.Instance.LoadAtlas(TargetAtlasType.Home).GetSprite(RIBBON_TEXT_SPRITE);

            _specialShopRibbon = obj.GetComponent<FlashActionPlayer>();
            if (_specialShopRibbon == null || sprite == null)
            {
                return;
            }

            _specialShopRibbon.LoadFlashPlayer();
            _specialShopRibbon.FlashPlayer.SetSprite(RIBBON_SPRITE_ROOT, sprite);
            _specialShopRibbon.SetSortOffset(RIBBON_SORT_OFFSET);

            // フェード用にアルファを追従させる
            var reciever = _specialShopRibbon.FlashPlayer.gameObject.AddComponent<A2UAlphaReceiver>();
            reciever.Set(_canvasRenderer, _specialShopRibbon.FlashPlayer.Motion);

            _specialShopRibbon.Play(RIBBON_ANIME_IN);
        }
    }
}