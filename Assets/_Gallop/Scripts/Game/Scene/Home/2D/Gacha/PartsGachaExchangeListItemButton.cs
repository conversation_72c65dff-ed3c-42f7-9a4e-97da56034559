using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 交換アイテムのリストアイテムのボタン
    /// </summary>
    public class PartsGachaExchangeListItemButton : MonoBehaviour
    {
        #region SerializeField

        /// <summary>
        /// 残り時間テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _remainTimeText;

        /// <summary>
        /// 交換ボタン(交換可能時)
        /// </summary>
        [SerializeField]
        private ButtonCommon _exchangeableButton;

        /// <summary>
        /// 交換ボタン(交換不可時)
        /// </summary>
        [SerializeField]
        private ButtonCommon _unexchangeableButton;

        #endregion

        #region Member

        /// <summary>
        /// ガチャ交換
        /// </summary>
        private GachaExchangeableUnit _exchangeableUnit;

        /// <summary>
        /// 交換可能か
        /// </summary>
        private bool _isExchangeable;

        /// <summary>
        /// 選択のコールバック
        /// </summary>
        private Action _onSelected;

        #endregion

        #region Method

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        /// <param name="onSelected"></param>
        public void Setup(GachaExchangeableUnit exchangeableUnit, Action onSelected)
        {
            _exchangeableUnit = exchangeableUnit;
            _isExchangeable = exchangeableUnit.IsExchangeable;
            _onSelected = onSelected;

            SetupRemainTimeText();
            SetupButton();
        }

        /// <summary>
        /// 交換期間をセットアップ
        /// </summary>
        private void SetupRemainTimeText()
        {
            // 期限なし
            var endDate = _exchangeableUnit.EndDate;
            if (endDate > TimeUtil.INDEFINITE_UNIX_TIME)
            {
                _remainTimeText.SetActiveWithCheck(false);
                return;
            }

            _remainTimeText.SetActiveWithCheck(true);

            var remainTime = endDate - TimeUtil.GetServerTimeStamp();
            var remainTimeParam = new Cute.Core.TimeUtil.TimeLeftParam(remainTime, 0);

            // 終了
            if (remainTimeParam.isEnd)
            {
                _remainTimeText.text = string.Format(TextId.Common0069.Text(), 0);
                return;
            }

            // あとX日
            if (remainTimeParam.day > 0)
            {
                _remainTimeText.text = string.Format(TextId.Common0067.Text(), remainTimeParam.day);
                _remainTimeText.FontColor = FontColorType.Brown;
                return;
            }

            // あとX時間
            if (remainTimeParam.hour > 0)
            {
                _remainTimeText.text = string.Format(TextId.Common0068.Text(), remainTimeParam.hour);
                _remainTimeText.FontColor = FontColorType.Orange;
                return;
            }

            // あとX分
            _remainTimeText.text = string.Format(TextId.Common0069.Text(), remainTimeParam.minute);
            _remainTimeText.FontColor = FontColorType.Orange;
        }

        /// <summary>
        /// ボタンをセットアップ
        /// </summary>
        private void SetupButton()
        {
            _exchangeableButton.SetActiveWithCheck(_isExchangeable);
            _unexchangeableButton.SetActiveWithCheck(!_isExchangeable);
            if (_isExchangeable)
            {
                _exchangeableButton.SetOnClick(OnClickButton);
            }
            else
            {
                var text = string.Format(TextId.Shop0054.Text(), TextId.Gacha0040.Text());
                _unexchangeableButton.SetNotificationMessage(text);
                _unexchangeableButton.SetInteractable(false);
            }
        }

        /// <summary>
        /// 交換ボタン押下時
        /// </summary>
        private void OnClickButton()
        {
            _onSelected?.Invoke();
        }

        #endregion
    }
}
