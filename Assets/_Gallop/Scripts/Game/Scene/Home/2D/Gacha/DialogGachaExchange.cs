using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャ交換ダイアログ
    /// </summary>
    public class DialogGachaExchange : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// ループスクロール
        /// </summary>
        [SerializeField]
        private LoopScroll _loopScroll;

        /// <summary>
        /// 天井ポイント名テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _limitPointTitleText;

        /// <summary>
        /// 所持天井ポイントテキスト
        /// </summary>
        [SerializeField]
        private TextCommon _limitPointText;

        /// <summary>
        /// 注意文言
        /// </summary>
        [SerializeField]
        private TextCommon _noticeText;

        #endregion

        #region Member

        /// <summary>
        /// ガチャ
        /// </summary>
        private Gacha _gacha;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(Gacha gacha , System.Action onSuccessCallback)
        {
            var content = LoadAndInstantiatePrefab<DialogGachaExchange>(ResourcePath.DIALOG_GACHA_EXCHANGE_PATH);
            var dialogData = content.CreateDialogData();
            dialogData.Title = GetDialogTitle(gacha.CardType);
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.CenterButtonCallBack = OnCenterButtonClick;
            dialogData.AutoClose = false;

            content.Setup(gacha , onSuccessCallback);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// ダイアログのタイトルを取得
        /// </summary>
        /// <param name="cardType"></param>
        /// <returns></returns>
        private static string GetDialogTitle(GameDefine.CardType cardType)
        {
            switch (cardType)
            {
                case GameDefine.CardType.Card:
                    return $"{TextId.Outgame0231.Text()}{TextId.Gacha0011.Text()}";
                case GameDefine.CardType.SupportCard:
                    return $"{TextId.Common0186.Text()}{TextId.Gacha0011.Text()}";
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="gacha"></param>
        public void Setup(Gacha gacha , System.Action onSuccessCallback)
        {
            _gacha = gacha;

            SetupNoticeText();
            SetupPointTitle();
            UpdateCurrentPoint();

            var listItemArray = CreateListItemList();
            _loopScroll.Setup(listItemArray.Length, item =>
            {
                var listItem = item as PartsGachaExchangeListItem;
                if (listItem == null)
                    return;
                listItem.UpdateItem(_gacha, listItemArray[listItem.ItemIndex] , onSuccessCallback);
            });
        }

        /// <summary>
        /// 注意文言をセットアップ
        /// </summary>
        private void SetupNoticeText()
        {
            switch (_gacha.CardType)
            {
                case GameDefine.CardType.Card:
                    _noticeText.text = TextId.Gacha0076.Text();
                    break;
                case GameDefine.CardType.SupportCard:
                    _noticeText.text = TextId.Gacha0077.Text();
                    break;
            }
        }

        /// <summary>
        /// ポイントのタイトルを更新
        /// </summary>
        private void SetupPointTitle()
        {
            _limitPointTitleText.text = GachaLimitPointCount.GetLimitPointTextByCardType(_gacha.CardType);
        }

        /// <summary>
        /// 現在の所持ポイントを更新
        /// </summary>
        private void UpdateCurrentPoint()
        {
            var currentPoint = TempData.Instance.GachaData.LimitPointCount.GetPointByGachaId(_gacha.Id);
            _limitPointText.text = currentPoint.ToCommaSeparatedString();
        }

        /// <summary>
        /// ウマ娘Pt交換リスト生成
        /// </summary>
        private PartsGachaExchangeListItem.ItemInfo[] CreateListItemList()
        {
            return MasterDataManager.Instance.masterGachaExchange.GetListWithGachaId(_gacha.Id)
                .Where(exchange => exchange != null)
                .Select(CreateItemInfo)
                .OrderBy(item => item.ExchangeableUnit.DispOrder)
                .ThenBy(item => item.ExchangeableUnit.CardId)
                .ToArray();
        }

        /// <summary>
        /// リストアイテムの情報を作成
        /// </summary>
        /// <param name="exchange"></param>
        /// <returns></returns>
        private PartsGachaExchangeListItem.ItemInfo CreateItemInfo(MasterGachaExchange.GachaExchange exchange)
        {
            var exchangeableUnit = new GachaExchangeableUnit(exchange);
            var itemInfo = new PartsGachaExchangeListItem.ItemInfo {ExchangeableUnit = exchangeableUnit};
            switch (exchangeableUnit.CardType)
            {
                case GameDefine.CardType.Card:
                    itemInfo.CardButtonInfo = new CharacterButtonInfo
                    {
                        IdType = CharacterButtonInfo.IdTypeEnum.Card,
                        Id = exchange.CardId,
                        Rarity = exchangeableUnit.Rarity,
                        EnableRarity = true,
                        OnLongTap = button => DialogCharacterCardDetail.Open(button.Info.MasterCardData.Id),
                        Level = GameDefine.MIN_TALENT_LEVEL
                    };
                    break;
                case GameDefine.CardType.SupportCard:
                    itemInfo.SupportCardButtonInfo = new CharacterButtonInfo
                    {
                        IdType = CharacterButtonInfo.IdTypeEnum.Support,
                        Id = exchange.CardId,
                        Rarity = exchangeableUnit.Rarity,
                        EnableRarity = true,
                        OnLongTap = button =>
                        {
                            var supportCardData = new WorkSupportCardData.SupportCardData(button.Info.Id);
                            DialogSupportCardDetail.Open(supportCardData, null, true);
                        }
                    };
                    break;
            }

            return itemInfo;
        }

        /// <summary>
        /// 左ボタン押下時
        /// </summary>
        /// <param name="dialog"></param>
        private static void OnCenterButtonClick(DialogCommon dialog)
        {
            dialog.Close();
        }

        #endregion
    }
}
