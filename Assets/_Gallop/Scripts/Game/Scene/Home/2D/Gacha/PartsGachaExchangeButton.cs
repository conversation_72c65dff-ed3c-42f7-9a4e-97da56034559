using System;
using UnityEngine;

namespace Gallop
{
    public class PartsGachaExchangeButton : MonoBehaviour
    {
        #region SerializeField

        /// <summary>
        /// 交換ポイント名
        /// </summary>
        [SerializeField]
        private TextCommon _pointNameText;

        /// <summary>
        /// ポイントテキスト
        /// </summary>
        [SerializeField]
        private TextCommon _pointText;

        /// <summary>
        /// 交換ボタン(交換可能時)
        /// </summary>
        [SerializeField]
        private ButtonCommon _exchangeableButton;


        /// <summary>
        /// 交換ボタン(交換不可時)
        /// </summary>
        [SerializeField]
        private ButtonCommon _unexchangeableButton;

        /// <summary>
        /// バッヂのルート
        /// </summary>
        [SerializeField]
        private RectTransform _badgeRoot;

        #endregion

        #region Member

        /// <summary>
        /// ガチャ
        /// </summary>
        private Gacha _gacha;

        /// <summary>
        /// 交換完了時のコールバック
        /// </summary>
        private Action _onCompleteExchanged;

        /// <summary>
        /// 交換可能か
        /// </summary>
        private bool _isExchangeable;

        /// <summary>
        /// 通知バッジ
        /// </summary>
        private FlashPlayer _notifyBadge;

        #endregion

        #region Method

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="gacha"></param>
        /// <param name="onCompleteExchange"></param>
        public void Setup(Gacha gacha, Action onCompleteExchange)
        {
            _gacha = gacha;
            _onCompleteExchanged = onCompleteExchange;

            _isExchangeable = TempData.Instance.GachaData.LimitPointCount.IsExchangeable(_gacha.Id);

            SetupPointName();
            SetupPoint();
            SetupButton();
            SetupNotifyBadge();
        }

        /// <summary>
        /// 交換ポイント名を
        /// </summary>
        private void SetupPointName()
        {
            switch (_gacha.CardType)
            {
                case GameDefine.CardType.Card:
                    _pointNameText.text = TextId.Gacha0048.Text();
                    return;
                case GameDefine.CardType.SupportCard:
                    _pointNameText.text = TextId.Gacha0049.Text();
                    return;
            }
        }

        /// <summary>
        /// 交換ポイントのテキストを設定
        /// </summary>
        private void SetupPoint()
        {
            var point = Mathf.Min(
                GachaDefine.MAX_LIMIT_POINT,
                TempData.Instance.GachaData.LimitPointCount.GetPointByGachaId(_gacha.Id)
            );
            _pointText.text = point.ToCommaSeparatedString();
        }

        /// <summary>
        /// ボタンをセットアップ
        /// </summary>
        private void SetupButton()
        {
            _exchangeableButton.SetOnClick(OnClickButton);
            _exchangeableButton.SetActiveWithCheck(_isExchangeable);
            _unexchangeableButton.SetOnClick(OnClickButton);
            _unexchangeableButton.SetActiveWithCheck(!_isExchangeable);
        }

        /// <summary>
        /// ボタンに通知バッジを設定
        /// </summary>
        private void SetupNotifyBadge()
        {
            if (_notifyBadge == null)
            {
                var badge = UIUtil.CreateNotifyBadgeIconFlash(_badgeRoot, (int) UIManager.CanvasSoringOrder.Main,
                    _exchangeableButton.image.canvasRenderer);
                _notifyBadge = badge;
            }

            if (!_isExchangeable)
            {
                // 足りない場合
                _notifyBadge.SetActiveWithCheck(false);
                return;
            }

            // 足りる場合
            _notifyBadge.Play("in");
            _notifyBadge.SetActiveWithCheck(true);
        }

        /// <summary>
        /// ボタン押下時のコールバック
        /// </summary>
        private void OnClickButton()
        {
            DialogGachaExchange.Open(_gacha , _onCompleteExchanged);
        }

        #endregion
    }
}
