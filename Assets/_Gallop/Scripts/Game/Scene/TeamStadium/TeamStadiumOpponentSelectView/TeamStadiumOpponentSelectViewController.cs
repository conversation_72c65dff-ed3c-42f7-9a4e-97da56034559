using DG.Tweening;
using System;
using System.Collections;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static WorkTeamStadiumData;

    /// <summary>
    /// チーム競技場相手選択のViewInfo
    /// </summary>
    public class TeamStadiumOpponentSelectViewInfo : IViewInfo
    {
        /// <summary>
        /// メニューダイアログが開くか (true: そう, false: 違う)
        /// </summary>
        public bool IsMenuOpened { get; set; }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public TeamStadiumOpponentSelectViewInfo()
        {
            
        }
    }

    /// <summary>
    /// チーム競技場相手選択トップ
    /// Viewコントローラー
    /// </summary>
    public class TeamStadiumOpponentSelectViewController : ViewControllerBase<TeamStadiumOpponentSelectView>
    {
        // ミリ秒から秒に変換する際の係数
        private const float SEC_TO_MS = 0.001f;
        private const float DURATION = 0.2f;

        private float _reloadButtonPosX;
        private Tween _reloadButtonCoolTimer = null;

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            PartsTeamStadiumOpponent.RegisterDownload(register);
        }

        #region BGM設定

        /// <summary>
        ///  BGM操作を無視する。前の画面のBGMをそのままにしたい場合にtrue
        /// </summary>
        public override bool IgnoreBgm()
        {
            return TeamStadiumUtil.GetIsSelectedBgm();
        }

        public override AudioId GetDynamicBgmId()
        {
            return AudioId.INVALID;
        }

        /// <summary>
        /// 前の画面で設定されているBGMでそのまま再生
        /// </summary>
        public override AudioManager.CueSheetCueNameInfo GetDynamicBgmCueInfo()
        {
            return TeamStadiumUtil.GetTeamStadiumMenuBgmData();
        }

        #endregion  

        /// <summary>
        /// 初期化処理
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            _view.ReloadButton.SetOnClick(() =>
            {
                SetButtonsEnabled(false);
                SendApi(() => 
                {
                    base.GetViewBase().StartCoroutine(Setup());
                });
            });

            yield return base.InitializeView();
        }

        public override IEnumerator PlayInView()
        {
            var bgmInfo = WorkDataManager.Instance.TeamStadiumData.TeamStadiumMenuBgmInfo;
            if (TeamStadiumUtil.GetIsSelectedBgm() == false)
            {
                TeamStadiumUtil.InitializeMenuBGM();
                TeamStadiumUtil.PlayMenuBgm();
            }
            else if (TeamStadiumUtil.GetIsSelectedBgm() == true
                && !AudioManager.IsPlayBgmInternal(bgmInfo.CueSheet, bgmInfo.CueName))
            {
                TeamStadiumUtil.PlayMenuBgm();
            }

            var viewInfo = GetViewInfo();
            if (viewInfo is TeamStadiumOpponentSelectViewInfo teamStadiumOpponentSelectViewInfo)
            {
                if (teamStadiumOpponentSelectViewInfo.IsMenuOpened)
                {
                    DialogHomeMenuMain.Open(null);
                }
                teamStadiumOpponentSelectViewInfo.IsMenuOpened = false;
            }


            if (!_view.ReloadButton.IsActive())
            {
                _view.TitleOpponentSelectCanvasGroup.DOFade(0f, DURATION).From().SetEase(Ease.OutCubic);
            }

            _reloadButtonPosX = (_view.ReloadButton.transform as RectTransform).anchoredPosition.x;
            _view.ReloadButton.SetActiveWithCheck(false);
            (_view.ReloadButton.transform as RectTransform).SetAnchoredPositionX(_reloadButtonPosX);

            var prevMemberList = WorkDataManager.Instance.TeamStadiumData.PrevMemberList;
            var currentMemberList = WorkDataManager.Instance.TeamStadiumData.TeamStadiumDeckInfo.GetMemberList();
            var isSameMemberAsLastMember = prevMemberList.SequenceEqual(currentMemberList);

            // 前回取得したデータがあり、デッキ編成に変更がなければ通信しない
            if (WorkDataManager.Instance.TeamStadiumData.OpponentDataList.Any() && isSameMemberAsLastMember)
            {
                yield return GetViewBase().StartCoroutine(Setup());
                yield break;
            }

            // 今回のデッキ編成メンバーを一時的に保持
            WorkDataManager.Instance.TeamStadiumData.SetMemberListTemporary();

            bool success = false;
            SendApi(() => {
                success = true;
            });
            yield return new WaitUntil(() => success);
            yield return GetViewBase().StartCoroutine(Setup());

            yield return base.PlayInView();
        }

        /// <summary>
        /// 対戦相手取得APIを呼ぶ
        /// </summary>
        /// <param name="onSuccess"></param>
        private void SendApi(Action onSuccess = null)
        {
            _view.ReloadButton.SetInteractable(false);
            var req = new TeamStadiumOpponentListRequest();
            var sendTime = Time.unscaledTime;
            req.Send(res =>
            {
                if (res.data != null)
                {
                    // 対戦相手リストを更新
                    WorkDataManager.Instance.TeamStadiumData.UpdateOpponentList(res.data.opponent_info_array);
                    onSuccess?.Invoke();
                }

                float coolTime = ServerDefine.TeamStaduimReloadCoolTime * SEC_TO_MS;
                _reloadButtonCoolTimer = _view.Timer(coolTime, () =>
                {
                    // タイミングによっては画面遷移で破棄されてる場合があるのでNullチェック
                    if (_view.ReloadButton != null)
                    {
                        _view.ReloadButton.SetInteractable(true);
                    }
                });

            }, (errorType, errorCode) =>
           {
               TeamStadiumErrorHandler.Handle(errorType, errorCode);
           });
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private IEnumerator Setup()
        {
            /// 枠番確定APIでサーバー側がどの対戦相手情報が送られてきたか判定するのに
            /// パネルの並び順（Strength + EvaluationPoint）を元に判定するので
            /// 不用意にパネルの順番を変えないでください。
            /// 変える必要が出た場合は、サーバー側の担当者へもご連絡をお願いいたします。
            var opponentDataList = WorkDataManager.Instance.TeamStadiumData.OpponentDataList;
            for (var i = 0; i < _view.OpponentItemArray.Length; i++)
            {
                var item = _view.OpponentItemArray[i];
                if(opponentDataList.Count <= i)
                {
                    //配列外参照対策
                    item.SetActiveWithCheck(false);
                    continue;
                }

                item.SetActiveWithCheck(true);
                var data = opponentDataList[i];
                if (data != null)
                {
                    item.Setup(data, i, OnOpponentInEnd, OnOpponentSelect);
                }
            }

            yield return base.GetViewBase().StartCoroutine(PlayIn());
        }

        /// <summary>
        /// 対戦相手選択パネルの出現演出が完了した時に実行されるコールバック
        /// </summary>
        /// <param name="index"></param>
        private void OnOpponentInEnd(int index)
        {
            // 一番下のパネルの出現演出が完了した際に一気にウイニング報酬アイコンの表示アニメーションを再生する。
            if (index == _view.OpponentItemArray.Length - 1)
            {
                var isPlaySe = false;
                for (var i = 0; i < _view.OpponentItemArray.Length; i++)
                {
                    if (_view.OpponentItemArray[i].PlayInWinConfirmIcon())
                    {
                        isPlaySe = true;
                    }
                }
                // ウイニング報酬アイコンの表示アニメーションが再生された場合はSEを鳴らす。
                if (isPlaySe)
                {
                    AudioManager.Instance.PlaySe(AudioId.SFX_TEAMRACE_DEFINITE_REWARD);
                }
            }
        }

        /// <summary>
        /// 相手決定コールバック
        /// </summary>
        /// <param name="opponentData"></param>
        private void OnOpponentSelect(OpponentData opponentData)
        {
            var status = WorkDataManager.Instance.TeamStadiumData.TeamStadiumStatus;
            status.SetOpponentData(opponentData);

            // 今回使用するデッキ編成を確定する
            //（ここ以降はデッキ編成で変更してもレース終了するまで変更が反映されない）
            var deckInfo = WorkDataManager.Instance.TeamStadiumData.TeamStadiumDeckInfo;
            status.SetMyDeckInfo(deckInfo);

            // 殿堂入りウマ娘一覧で移籍させない為に出走済の殿堂入りウマ娘IDを保存
            WorkDataManager.Instance.TeamStadiumData.UpdateInRaceCharaIdArray(status.MyDeckInfo.GetTrainedCharaDataList().Select(x => x.Id).ToArray());

            // 枠番確定API呼んだ後に遷移する。（画面遷移をスムーズにするためにここで通信しておく）
            var req = new TeamStadiumDecideFrameOrderRequest();
            req.opponent_info = opponentData.ServerData;
            req.Send(res =>
            {
                var viewInfo = new TeamStadiumDecideViewInfo();
                viewInfo.FrameOrderArray = res.data.frame_order_info_array;

                // レース進行状態をウマ番確定に進めておく
                WorkDataManager.Instance.TeamStadiumData.TeamStadiumInfo.UpdateRaceStatus(TeamStadiumDefine.RaceStatusType.Decide);

                PlayOut(SceneDefine.ViewId.TeamStadiumDecide, viewInfo);
            }, TeamStadiumErrorHandler.Handle);

        }

        /// <summary>
        /// バックボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {
            Back();
        }

        /// <summary>
        /// OSバックボタン押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            Back();
        }

        /// <summary>
        /// バックキー/戻るボタンの共通処理
        /// </summary>
        private void Back()
        {
            PlayOut(SceneDefine.ViewId.TeamStadium);
        }

        /// <summary>
        /// In再生
        /// </summary>
        private IEnumerator PlayIn()
        {
            SetButtonsEnabled(false);

            for (var i = 0; i < _view.OpponentItemArray.Length; i++)
            {
                yield return new WaitForSeconds(0.03f);
                _view.OpponentItemArray[i].PlayIn();
            }

            if (!_view.ReloadButton.IsActive())
            {
                const float DELAY = 0.15f;
                const float POS_X = 50f;
                UIManager.Instance.LockGameCanvas();

                yield return new WaitForSeconds(DELAY);

                UIManager.Instance.OpenFooterBackButton(0f);
                    _view.ReloadButton.SetActiveWithCheck(true);
                    _view.ReloadButton.CanvasGroup.DOFade(1f, DURATION);
                
                (_view.ReloadButton.transform as RectTransform)
                    .DOAnchorPosX(POS_X, DURATION)
                    .SetEase(Ease.OutCubic)
                    .From(true)
                    .OnComplete(()=> 
                    {
                        if (UIManager.Instance.IsLockGameCanvas())
                        {
                            UIManager.Instance.UnlockGameCanvas();
                        }
                    });
            }

            while (true)
            {
                bool isReady = IsReady();

                if (isReady)
                {
                    SetButtonsEnabled(true);
                    break;
                }
                else
                {
                    yield return new WaitForSeconds(0.03f);
                }
            }

            
        }

        /// <summary>
        /// ボタンが有効かを設定
        /// </summary>
        /// <param name="isEnabled">ボタンが有効か (true: 有効, false: 無効)</param>
        private void SetButtonsEnabled(bool isEnabled)
        {
            for (var i = 0; i < _view.OpponentItemArray.Length; i++)
            {
                _view.OpponentItemArray[i].SetButtonEnabled(isEnabled);
            }
        }

        /// <summary>
        /// ボタンが準備できているかを取得
        /// </summary>
        /// <returns>ボタンが準備できているか (true: いる, false: いない)</returns>
        private bool IsReady()
        {
            for (var i = 0; i < _view.OpponentItemArray.Length; i++)
            {
                if (!_view.OpponentItemArray[i].IsReady)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Out再生
        /// </summary>
        /// <returns></returns>
        private void PlayOut(SceneDefine.ViewId nextViewId, IViewInfo viewInfo = null)
        {
            if (_reloadButtonCoolTimer != null && _reloadButtonCoolTimer.IsPlaying())
            {
                _reloadButtonCoolTimer.Kill(false);
            }

            UIManager.Instance.LockGameCanvas();
            for (var i = 0; i < _view.OpponentItemArray.Length; i++)
            {
                _view.OpponentItemArray[i].PlayOut();
            }

            const float DURATION = 0.2f;
            const float POS_X = 50f;
            UIManager.Instance.CloseFooterBackButton();
            UIManager.Instance.SetVisibleHeaderTitleWithAnim(false);
            UIManager.Instance.HideFooter();
            _view.TitleOpponentSelectCanvasGroup.DOFade(0f, DURATION).SetEase(Ease.OutCubic);
            _view.ReloadButton.CanvasGroup.DOFade(0f, DURATION);
            if (nextViewId == SceneDefine.ViewId.TeamStadiumDecide)
            {
                UIManager.Instance.SetVisibleHeaderWithAnim(false);
            }
            (_view.ReloadButton.transform as RectTransform)
                .DOAnchorPosX(_reloadButtonPosX + POS_X, DURATION)
                .SetEase(Ease.OutCubic)
                .OnComplete(() => ChangeView(nextViewId, viewInfo));
        }

        /// <summary>
        /// 次のViewに遷移
        /// </summary>
        /// <param name="nextViewId"></param>
        private void ChangeView(SceneDefine.ViewId nextViewId, IViewInfo viewInfo = null)
        {
            UIManager.Instance.UnlockGameCanvas();
            SceneManager.Instance.ChangeView(nextViewId, viewInfo);
        }
    }
}