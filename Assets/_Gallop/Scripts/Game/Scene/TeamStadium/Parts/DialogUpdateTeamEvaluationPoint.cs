using Cute.Cri;
using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Gallop.Tutorial;
using UnityEngine;

namespace Gallop
{
    using static Gallop.MasterTeamStadiumRank;
    using static Gallop.TeamStadiumDefine;

    /// <summary>
    /// 最高チーム評価点ダイアログ
    /// https://xxxxxxxxxx/app/#/projects/5e68d0c586e4657e324e656c/screens/5e8337a986e46516e59fc4e8
    /// </summary>
    public class DialogUpdateTeamEvaluationPoint : DialogInnerBase
    {
        #region SerializeFields

        [SerializeField] private RectTransform _contentsRoot = null;
        [SerializeField] private RectTransform _frameRoot = null;
        [SerializeField] private RectTransform _backgroundEmblemFlashRoot = null;
        [SerializeField] private RectTransform _titleFlashRoot = null;
        [SerializeField] private RectTransform _gaugeFlashRoot = null;
        [SerializeField] private RectTransform _rankIconFlashRoot = null;
        [SerializeField] private ButtonCommon _rewardsListButton = null;
        [SerializeField] private PartsTeamEvaluationRewardItem _teamEvaluationRewardItem = null;
        [SerializeField] private ButtonCommon _closeButton = null;
        [SerializeField] private ButtonCommon _tapButton = null;
        [SerializeField] private GameObject _tapImage = null;
        [SerializeField] private CanvasGroup _rewardItemTitleCanvasGroup = null;
        [SerializeField] private CanvasGroup _rewardItemIconCanvasGroup = null;
        [SerializeField] private ItemIcon _rewardItemIcon = null;
        [SerializeField] private List<CanvasGroup> _detailFadeCanvasGroupList = null;
        [SerializeField] private Canvas _updateRankContentsCanvas = null;
        [SerializeField] private Canvas _detailContentsCanvas = null;
        [SerializeField] private ButtonCommon _skipButton = null;
        [SerializeField] private Transform _rewardItemsRoot = null;
        [SerializeField] private PartsTeamEvaluationRankupRewardsRow _rewardRowBase = null;
        [SerializeField] private RectTransform _updateTeamRankRewardTitle = null;
        
        #endregion

        #region Variables, Constants

        private TeamRank _beforeBestTeamRank;
        private TeamRank _currentRank;
        private TeamRank _nextRank;
        private int _beforeBestRankPoint;
        private int _currentRankPoint;
        private int _currentRankPointMin;
        private int _nextRankPointMin;
        private bool _isRankUp;
        private bool _isSkip = false;
        private bool _isPlayingRankIcons = false;
        private bool _isLimitRank;

        private RewardItemRow _rewardItemRow;

        private DialogCommon _dialog;
        private Sequence _sequence;
        private Animator _animatorInstance;
        private FlashPlayer _backgroundEmblemFlashPlayer;
        private FlashPlayer _titleFlashPlayer;
        private FlashPlayer _gaugeFlashPlayer;
        private FlashActionPlayer _rankIconFlashActionPlayer;
        private Canvas _parentCanvas;
        private List<PartsTeamEvaluationRankupRewardsRow> _rewardItemIconList;
        private IEnumerable<List<TeamStadiumRank>> _rankupRewardsChunkedList;
        private int _rankupRewardsChunkSize;
        private Coroutine _playGaugeCoroutine = null;
        private AudioPlayback _pointupSePlayback;

        private const int GAUGE_INDEX_MAX = 9;
        private const string ROOT_MOTION = "MOT_root";
        private const string RANK_FRAME_PLN = "PLN_dum_rank_frm00";
        private const string RANK_ICON_PLN = "PLN_dum_rank_icon00";
        private const string RANK_POINT_TXT = "TXT_num_strength00";
        private const float RANK_UP_BAR_DELAY = 0.06666f;

        private enum RewardItemRow
        {
            OneRow = 1,
            TwoRows,
            ThreeRows,
            FourRows
        }
        
        public ButtonCommon CloseButton { get { return _closeButton; } }

        private System.Action<int> _onClose = null;

        #endregion

#if CYG_DEBUG
        public static bool Debug_Play = false;
        public static TeamRank Debug_CurrentRank = TeamRank.None;
        public static TeamRank Debug_BeforeBestTeamRank = TeamRank.None;
#endif


        #region override

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.WITHOUT_FRAME;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.SingleMode0132.Text();
            data.CenterButtonText = TextId.Common0083.Text();
            data.EnableOutsideClick = false;
            return data;
        }

        #endregion

        public static void Open(System.Action<int> onClose)
        {
            //ダイアログの表示
            var component = LoadAndInstantiatePrefab<DialogUpdateTeamEvaluationPoint>(ResourcePath.DIALOG_UPDATE_TEAM_EVALUATION_POINT);
            var dialogData = component.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.OnPushBackKey = () =>
            {
                // 評価詳細のゲージ演出再生有無で判断
                if (component._playGaugeCoroutine != null)
                {
                    component.CloseDialog();
                };
                return true;
            };
            dialogData.CancelSe = AudioId.INVALID;
            
            // チュートリアル中の場合ダイアログを閉じた時にガイド表示を仕込むのでコールバック設定
            if (TutorialManager.IsTutorialExecuting())
            {
                UIManager.Instance.LockGameCanvas();        // ゲージ上昇アニメーションが完了するまで押下禁止
                dialogData.DestroyCallBack = () =>
                {
                    TutorialOutGame.SetGuideTeamRaceDeckEditBackButton();
                };
            }
            
            var dialog = DialogManager.PushDialog(dialogData);
            component.Setup(dialog);

            component._onClose = onClose;
        }

        /// <summary>
        /// 必要なリソースのダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.COMMON_UNITYANIMATION_FRAME_00);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_EMBLEM_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_GAUGE_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_ACTION_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_GLITTER_PARTICLE_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_TEXT_FLASH_PATH);
            ItemIcon.RegisterPath(register);
        }

        /// <summary>
        /// 初期化処理
        /// </summary>
        /// <param name="dialog"></param>
        private void Setup(DialogCommon dialog)
        {
            UIManager.Instance.AdjustContentsRootRect(_contentsRoot);

            _dialog = dialog;
            _parentCanvas = transform.GetComponentInParent<Canvas>();

            // 前の最高チーム評価
            _beforeBestRankPoint = WorkDataManager.Instance.UserData.BestTeamEvaluationPoint.GetDecrypted();
            _beforeBestTeamRank = TeamStadiumUtil.GetTeamRank(_beforeBestRankPoint);

            // 現在のチームランク
            var teamStadiumData = WorkDataManager.Instance.TeamStadiumData;
            _currentRankPoint = teamStadiumData.TeamStadiumDeckInfo.GetTeamRankPoint();
            _currentRank = TeamStadiumUtil.GetTeamRank(_currentRankPoint);

            var masterTeamStadiumRank = MasterDataManager.Instance.masterTeamStadiumRank;
#if CYG_DEBUG
            if ( Debug_Play == true )
            {
                _currentRank = Debug_CurrentRank;
                _currentRankPoint = masterTeamStadiumRank.Get((int)_currentRank).TeamMinValue;
                _beforeBestTeamRank = Debug_BeforeBestTeamRank;

                // デバッグ後に戻しておかないとずっとデバッグ状態になる。
                Debug_Play = false;
            }
#endif
            
            _currentRankPointMin = masterTeamStadiumRank.Get((int)_currentRank).TeamMinValue;

            _isRankUp = (_beforeBestTeamRank < _currentRank) && (0 < teamStadiumData.TeamEvaluationUpdateRankRewardArray.Count());

            // 過去最高チーム評価点更新
            WorkDataManager.Instance.UserData.UpdateBestTeamEvaluationPoint(_currentRankPoint);

            _isLimitRank = TeamRank.SS <= _currentRank;

            var nextTeamRank = _isLimitRank ? masterTeamStadiumRank.Get((int)TeamRank.SS) : TeamStadiumUtil.GetNextTeamRank(_currentRankPoint);
            var itemName = MasterDataManager.Instance.masterItemData.Get(nextTeamRank.ItemId).Name;

            // 最大ランクの場合は表示が変わる
            if (_isLimitRank)
            {
                _teamEvaluationRewardItem.Setup();

                Vector3 pos = Math.VECTOR3_ZERO;
                pos = _updateTeamRankRewardTitle.localPosition;
                pos.y = 6;
                _updateTeamRankRewardTitle.localPosition = pos;

                RectTransform rectTransform = _teamEvaluationRewardItem.GetComponent<RectTransform>();
                pos = rectTransform.localPosition;
                pos.y = -149;
                rectTransform.localPosition = pos;

                rectTransform = _rewardsListButton.GetComponent<RectTransform>();
                pos = rectTransform.localPosition;
                pos.y = -321;
                rectTransform.localPosition = pos;
            }
            else
            {
                _teamEvaluationRewardItem.Setup(nextTeamRank, itemName, false, _isLimitRank);
            }

            _updateRankContentsCanvas.overrideSorting = true;
            _updateRankContentsCanvas.sortingLayerID = _parentCanvas.sortingLayerID;
            _updateRankContentsCanvas.sortingOrder = _parentCanvas.sortingOrder + 1;
            _detailContentsCanvas.overrideSorting = true;
            _detailContentsCanvas.sortingLayerID = _parentCanvas.sortingLayerID;
            _detailContentsCanvas.sortingOrder = _parentCanvas.sortingOrder + 1;


            _rewardsListButton.SetOnClick(OnClickRewardsListButton);
            _closeButton.SetOnClick(CloseDialog);

            _rewardItemTitleCanvasGroup.gameObject.SetActiveWithCheck(false);
            _rewardItemIcon.gameObject.SetActiveWithCheck(false);
            _tapImage.SetActiveWithCheck(false);
            _tapButton.SetActiveWithCheck(false);
            _skipButton.SetActiveWithCheck(false);

            for (int i = 0, n = _detailFadeCanvasGroupList.Count; i < n; i++)
            {
                _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(false);
            }


            if (_isRankUp)
            {
                var teamStadiumRankList = new List<TeamStadiumRank>();
                for (int i = ((int)_beforeBestTeamRank + 1); i <= (int)_currentRank; i++)
                {
                    teamStadiumRankList.Add(masterTeamStadiumRank.Get(i));
                }

                // 一行当たりの最大アイテム数
                _rankupRewardsChunkSize = ChunkSize(teamStadiumRankList.Take(20).ToList());
                // 4個 or 5個づつのリストを作る
                _rankupRewardsChunkedList = teamStadiumRankList.Take(20).Select((rank, index) => new { rank, index })
                    .GroupBy(x => x.index / _rankupRewardsChunkSize)
                    .Select(group => group.Select(x => x.rank).ToList());

                _rewardItemRow = (RewardItemRow)_rankupRewardsChunkedList.Count();

                if (RewardItemRow.OneRow < _rewardItemRow)
                {
                    _titleFlashRoot.localPosition = GetFrameInUiPartsPosition(_titleFlashRoot);
                    _backgroundEmblemFlashRoot.localPosition = GetFrameInUiPartsPosition(_backgroundEmblemFlashRoot);
                    _rankIconFlashRoot.localPosition = GetFrameInUiPartsPosition(_rankIconFlashRoot);
                    _rewardItemTitleCanvasGroup.transform.localPosition = GetFrameInUiPartsPosition(_rewardItemTitleCanvasGroup.transform);
                    _rewardItemsRoot.localPosition = GetFrameInRewardItemsRootPosition(_rewardItemsRoot.transform);
                }

                StartCoroutine(PlayRankUp());
            }
            else
            {
                _backgroundEmblemFlashRoot.SetAnchoredPositionY(0f);
                _rankIconFlashRoot.SetAnchoredPositionY(GetFrameExpandUiPartsHeight());
                PlayUpdateEvaluation();
            }
        }

        /// <summary>
        /// ランクアップ表示再生
        /// </summary>
        private IEnumerator PlayRankUp()
        {
            _sequence = DOTween.Sequence();
            _sequence.OnStart(() => FlameIn());
            _sequence.InsertCallback(0.23f, () =>
            {
                TitleIn();
                _isPlayingRankIcons = true;
                RankIconIn();
            });
            _sequence.InsertCallback(0.33f, () => BackgroundEmblemIn());
            _sequence.InsertCallback(0.5f, () =>
            {
                if(!TutorialSingleMode.IsTutorial)
                {
                    _skipButton.SetActiveWithCheck(true);
                    _skipButton.SetOnClick(() => { _isSkip = true; });
                }
            });
            _sequence.InsertCallback(0.81f, () =>
            {
                _rewardItemTitleCanvasGroup.gameObject.SetActiveWithCheck(true);
                _rewardItemTitleCanvasGroup.DOFade(1f, 0.2f); // 27f(30fps)
            });
            _sequence.Play();

            yield return new WaitUntil(() => _isSkip);
            yield return new WaitUntil(() => !_isPlayingRankIcons);

            _dialog.Lock();
            _skipButton.SetActiveWithCheck(false);
            _tapImage.SetActiveWithCheck(true);
            _tapButton.SetActiveWithCheck(true);
            _tapButton.SetOnClick(PlayRankUpExpand);
            _dialog.Unlock();
        }

        /// <summary>
        /// ランクアップ詳細表示再生
        /// </summary>
        private void PlayRankUpExpand()
        {
            AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMRANK_APPEAR);

            _dialog.Lock();
            _sequence = DOTween.Sequence();
            _sequence.OnStart(() => FrameExpand());
            _sequence.InsertCallback(0.13f, () =>
            {
                const float DURATION = 0.15f;
                _rewardItemIconCanvasGroup.DOFade(0, DURATION);
                _rewardItemTitleCanvasGroup.DOFade(0, DURATION);

                float endHeight = GetFrameExpandUiPartsHeight2();
                _backgroundEmblemFlashPlayer.transform.DOLocalMoveY(endHeight, 0.2f).SetEase(Ease.InCubic);
                _rankIconFlashActionPlayer.transform.DOLocalMoveY(endHeight, 0.2f).SetEase(Ease.InCubic);
            });
            _sequence.InsertCallback(0.16f, () =>
            {
                _titleFlashPlayer.transform.DOLocalMoveY(GetFrameExpandUiPartsHeight(), 0.2f).SetEase(Ease.InCubic);
            });
            if (_isLimitRank == false)
            {
                _sequence.InsertCallback(0.23f, () => GaugeIn());
            }
            _sequence.OnComplete(() =>
            {
                for (int i = 0, n = _detailFadeCanvasGroupList.Count; i < n; i++)
                {
                    if (i == 2)
                    {
                        if (_isLimitRank == true)
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(false);
                        }
                        else
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(true);
                            _detailFadeCanvasGroupList[i].DOFade(0, 0.2f).From();
                            (_detailFadeCanvasGroupList[i].transform as RectTransform).DOAnchorPosY(-20f, 0.2f).From(true);
                        }
                    }
                    else if (i == 5)
                    {
                        if (_isLimitRank == false)
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(false);
                        }
                        else
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(true);
                            _detailFadeCanvasGroupList[i].DOFade(0, 0.2f).From();
                            (_detailFadeCanvasGroupList[i].transform as RectTransform).DOAnchorPosY(-20f, 0.2f).From(true);
                        }
                    }
                    else
                    {
                        _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(true);
                        _detailFadeCanvasGroupList[i].DOFade(0, 0.2f).From();
                        (_detailFadeCanvasGroupList[i].transform as RectTransform).DOAnchorPosY(-20f, 0.2f).From(true);
                    }
                }

                _rewardItemTitleCanvasGroup.gameObject.SetActiveWithCheck(false);
                _rewardItemIcon.gameObject.SetActiveWithCheck(false);
                _rewardItemsRoot.gameObject.SetActiveWithCheck(false);
                _tapImage.SetActiveWithCheck(false);
                _tapButton.SetActiveWithCheck(false);

                _dialog.Unlock();
            });
            _sequence.Play();
        }

        /// <summary>
        /// 評価点アップ詳細表示再生
        /// </summary>
        private void PlayUpdateEvaluation()
        {
            AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMRANK_APPEAR);

            _dialog.Lock();
            _sequence = DOTween.Sequence();
            _sequence.OnStart(() => FlameIn());
            _sequence.InsertCallback(0.167f, () => TitleIn());
            _sequence.InsertCallback(0.18f, () =>
            {
                if (_isLimitRank == false) GaugeIn();
            });
            _sequence.InsertCallback(0.3f, () => 
            {
                RankIconIn();
                BackgroundEmblemIn();
            });
            _sequence.OnComplete(() =>
            {
                for (int i = 0, n = _detailFadeCanvasGroupList.Count; i < n; i++)
                {
                    if (i == 2)
                    {
                        if (_isLimitRank == true)
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(false);
                        }
                        else
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(true);
                            _detailFadeCanvasGroupList[i].DOFade(0, 0.2f).From();
                            (_detailFadeCanvasGroupList[i].transform as RectTransform).DOAnchorPosY(-20f, 0.2f).From(true);
                        }
                    }
                    else if (i == 5)
                    {
                        if (_isLimitRank == false)
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(false);
                        }
                        else
                        {
                            _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(true);
                            _detailFadeCanvasGroupList[i].DOFade(0, 0.2f).From();
                            (_detailFadeCanvasGroupList[i].transform as RectTransform).DOAnchorPosY(-20f, 0.2f).From(true);
                        }
                    }
                    else
                    {
                        _detailFadeCanvasGroupList[i].gameObject.SetActiveWithCheck(true);
                        _detailFadeCanvasGroupList[i].DOFade(0, 0.2f).From();
                        (_detailFadeCanvasGroupList[i].transform as RectTransform).DOAnchorPosY(-20f, 0.2f).From(true);
                    }
                }

                _titleFlashPlayer.transform.DOLocalMoveY(GetFrameExpandUiPartsHeight(), 0.2f).SetEase(Ease.InOutCubic);
                
                _dialog.Unlock();
            });
            _sequence.Play();
        }

        /// <summary>
        /// FrameIn
        /// </summary>
        private void FlameIn()
        {
            var frame = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COMMON_UNITYANIMATION_FRAME_00, _dialog.DialogData.DialogHash);
            _animatorInstance = Instantiate(frame, _frameRoot).GetComponent<Animator>();
            _animatorInstance.GetComponent<AnimationEventSender>().Initialize(this);
            _animatorInstance.transform.SetSiblingIndex(0);

            string label = string.Empty;
            if (_isRankUp)
            {
                switch (_rewardItemRow)
                {
                    case RewardItemRow.OneRow:
                        label = "in01";
                        break;
                    case RewardItemRow.TwoRows:
                        label = "in04";
                        break;
                    case RewardItemRow.ThreeRows:
                        label = "in05";
                        break;
                    case RewardItemRow.FourRows:
                        label = "in06";
                        break;
                }
            }
            else
            {
                if (_currentRank == TeamRank.SS)
                {
                    label = "in04";
                }
                else
                {
                    label = "in02";
                }
            }

            _animatorInstance.Play(label);
        }

        /// <summary>
        /// FrameExpand
        /// </summary>
        private void FrameExpand()
        {
            string label = string.Empty;
            if (_isRankUp)
            {
                if (_currentRank == TeamRank.SS)
                {
                    switch (_rewardItemRow)
                    {
                        case RewardItemRow.OneRow:
                            label = "reduction04";
                            break;
                        case RewardItemRow.TwoRows:
                            break;
                        case RewardItemRow.ThreeRows:
                            label = "reduction05";
                            break;
                        case RewardItemRow.FourRows:
                            label = "reduction06";
                            break;
                    }
                }
                else
                {
                    switch (_rewardItemRow)
                    {
                        case RewardItemRow.OneRow:
                            label = "expand03";
                            break;
                        case RewardItemRow.TwoRows:
                            label = "reduction01";
                            break;
                        case RewardItemRow.ThreeRows:
                            label = "reduction02";
                            break;
                        case RewardItemRow.FourRows:
                            label = "reduction03";
                            break;
                    }

                }
            }
            else
            {
                if (_currentRank != TeamRank.SS)
                {
                    label = "expand03";
                }
            }

            if(label == string.Empty)
            {
                return;
            }

            _animatorInstance.Play(label);
        }

        /// <summary>
        /// FrameExpandOut
        /// </summary>
        private void FrameExpandOut()
        {
            string label = string.Empty;
            if (_isRankUp)
            {
                if (_currentRank == TeamRank.SS)
                {
                    switch (_rewardItemRow)
                    {
                        case RewardItemRow.OneRow:
                        case RewardItemRow.TwoRows:
                            label = "reduction_out02";
                            break;
                        case RewardItemRow.ThreeRows:
                        case RewardItemRow.FourRows:
                            label = "reduction_out01";
                            break;
                    }
                }
                else
                {
                    switch (_rewardItemRow)
                    {
                        case RewardItemRow.OneRow:
                            label = "expand_out03";
                            break;
                        case RewardItemRow.TwoRows:
                        case RewardItemRow.ThreeRows:
                        case RewardItemRow.FourRows:
                            label = "reduction_out01";
                            break;
                    }
                }
            }
            else
            {
                if (_currentRank == TeamRank.SS)
                {
                    label = "reduction_out02";
                }
                else
                {
                    label = "expand_out03";
                }
            }

            _animatorInstance.Play(label);
        }

        /// <summary>
        /// フレームイン再生時のUI座標取得
        /// </summary>
        /// <param name="transform"></param>
        /// <returns></returns>
        private Vector3 GetFrameInUiPartsPosition(Transform transform)
        {
            var newPosition = transform.localPosition;
            switch (_rewardItemRow)
            {
                case RewardItemRow.OneRow:
                    break;
                case RewardItemRow.TwoRows:
                    newPosition.Set(transform.localPosition.x, transform.localPosition.y + 135f, transform.localPosition.z);
                    break;
                case RewardItemRow.ThreeRows:
                    newPosition.Set(transform.localPosition.x, transform.localPosition.y + 250f, transform.localPosition.z);
                    break;
                case RewardItemRow.FourRows:
                    newPosition.Set(transform.localPosition.x, transform.localPosition.y + 360f, transform.localPosition.z);
                    break;
            }
            return newPosition;
        }

        /// <summary>
        /// フレームイン再生時の報酬アイコン親オブジェクト座標取得
        /// </summary>
        /// <param name="transform"></param>
        /// <returns></returns>
        private Vector3 GetFrameInRewardItemsRootPosition(Transform transform)
        {
            var newPosition = transform.localPosition;
            switch (_rewardItemRow)
            {
                case RewardItemRow.OneRow:
                    newPosition.Set(transform.localPosition.x, -195f, transform.localPosition.z);
                    break;
                case RewardItemRow.TwoRows:
                    newPosition.Set(transform.localPosition.x, transform.localPosition.y + 35f, transform.localPosition.z);
                    break;
                case RewardItemRow.ThreeRows:
                    newPosition.Set(transform.localPosition.x, transform.localPosition.y + 55f, transform.localPosition.z);
                    break;
                case RewardItemRow.FourRows:
                    newPosition.Set(transform.localPosition.x, transform.localPosition.y + 65f, transform.localPosition.z);
                    break;
            }
            return newPosition;
        }

        /// <summary>
        /// フレーム展開時のUI高さ取得
        /// </summary>
        /// <returns></returns>
        private float GetFrameExpandUiPartsHeight()
        {
            const float movingDistance = 170f;
            const float movingDistanceLimitRank = 190;
            var height = _isLimitRank ? movingDistanceLimitRank : movingDistance;
            if (!_isRankUp)
            {
                return height;
            }

            switch (_rewardItemRow)
            {
                case RewardItemRow.OneRow:
                    break;
                case RewardItemRow.TwoRows:
                    height += -135f;
                    break;
                case RewardItemRow.ThreeRows:
                    height += -250f;
                    break;
                case RewardItemRow.FourRows:
                    height += -360f;
                    break;
            }
            return height;
        }

        /// <summary>
        /// フレーム展開時のUI高さ取得
        /// </summary>
        /// <returns></returns>
        private float GetFrameExpandUiPartsHeight2()
        {
            const float movingDistance = 170f;
            const float movingDistanceLimitRank = 140f;
            var height = _isLimitRank ? movingDistanceLimitRank : movingDistance;
            if (!_isRankUp)
            {
                return height;
            }

            switch (_rewardItemRow)
            {
                case RewardItemRow.OneRow:
                    break;
                case RewardItemRow.TwoRows:
                    height += -135f;
                    break;
                case RewardItemRow.ThreeRows:
                    height += -250f;
                    break;
                case RewardItemRow.FourRows:
                    height += -360f;
                    break;
            }
            return height;
        }

        /// <summary>
        /// 背景演出再生
        /// </summary>
        /// <returns></returns>
        private void BackgroundEmblemIn()
        {
            if (_backgroundEmblemFlashPlayer == null)
            {
                _backgroundEmblemFlashPlayer = FlashLoader.LoadOnHash(
                    ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_EMBLEM_FLASH_PATH, _backgroundEmblemFlashRoot, ROOT_MOTION, _dialog.DialogData.DialogHash);
                _backgroundEmblemFlashPlayer.SetLayer(_backgroundEmblemFlashRoot.gameObject.layer);
                _backgroundEmblemFlashPlayer.SortLayer = _dialog.ParentCanvasSortingLayerName;
                _backgroundEmblemFlashPlayer.SortOffset = _dialog.GetSortingOrder();
            }

            if (_isRankUp == false && _isLimitRank == true)
            {
                _backgroundEmblemFlashRoot.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y - 40f, transform.localPosition.z);
            }

            _backgroundEmblemFlashPlayer.SortOffset = _parentCanvas.sortingOrder + 1;
            _backgroundEmblemFlashPlayer.SortLayer = _parentCanvas.sortingLayerName;
            _backgroundEmblemFlashPlayer.GetMotion(ROOT_MOTION).SetMotionPlay("in");
        }

        /// <summary>
        /// タイトル演出再生
        /// </summary>
        private void TitleIn()
        {
            if (_titleFlashPlayer == null)
            {
                _titleFlashPlayer = FlashLoader.LoadOnHash(
                   ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_TEXT_FLASH_PATH, _titleFlashRoot, ROOT_MOTION, _dialog.DialogData.DialogHash);
                _titleFlashPlayer.SetLayer(_titleFlashRoot.gameObject.layer);
                _titleFlashPlayer.SortLayer = _dialog.ParentCanvasSortingLayerName;
                _titleFlashPlayer.SortOffset = _dialog.GetSortingOrder();
            }

            _titleFlashPlayer.SortOffset = _parentCanvas.sortingOrder + 1;
            _titleFlashPlayer.SortLayer = _parentCanvas.sortingLayerName;
            _titleFlashPlayer.GetMotion(ROOT_MOTION).SetMotionPlay("in");
            AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMPOINT_UPDATE);
        }

        /// <summary>
        /// ランクアイコン演出再生
        /// </summary>
        private void RankIconIn()
        {
            if (_rankIconFlashActionPlayer == null)
            {
                var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_ACTION_PATH, _dialog.DialogData.DialogHash);
                var obj = Instantiate(prefab, _rankIconFlashRoot);
                _rankIconFlashActionPlayer = obj.GetComponent<FlashActionPlayer>();
                _rankIconFlashActionPlayer.LoadFlashPlayer();
                _rankIconFlashActionPlayer.gameObject.SetLayerRecursively(gameObject.layer);
                _rankIconFlashActionPlayer.SetSortOffset(_parentCanvas.sortingOrder + 2);
                _rankIconFlashActionPlayer.SetSortLayer(_parentCanvas.sortingLayerName);
                _rankIconFlashActionPlayer.FlashPlayer.Init();
            }

            if (_isRankUp)
            {
                StartCoroutine(PlayRankIcons());
            }
            else
            {
                SetupRankIcon(_currentRank, _currentRankPoint, isUnder: true);
                SetupRankIcon(_currentRank, _currentRankPoint);

                if (_isLimitRank)
                {
                    _rankIconFlashRoot.localPosition = new Vector3(transform.localPosition.x, transform.localPosition.y + 140f, transform.localPosition.z);
                }

                _rankIconFlashActionPlayer.Play("expand_in");
            }
        }

        /// <summary>
        /// ランクアップ演出表示
        /// </summary>
        /// <returns></returns>
        private IEnumerator PlayRankIcons()
        {
            var masterTeamStadiumRank = MasterDataManager.Instance.masterTeamStadiumRank;
            var isMultiRankup = (1 < (_currentRank - _beforeBestTeamRank));
            SetupRankIcon(_beforeBestTeamRank, _beforeBestRankPoint, isUnder: true);
            SetupRankIcon((_beforeBestTeamRank + 1), (isMultiRankup) ? masterTeamStadiumRank.Get((int)(_beforeBestTeamRank + 1)).TeamMinValue : _currentRankPoint);

            var teamStadiumRankList = new List<TeamStadiumRank>();
            for (int i = ((int)_beforeBestTeamRank + 1); i <= (int)_currentRank; i++)
            {
                var teamRankRewardItem = masterTeamStadiumRank.Get(i);
                teamStadiumRankList.Add(teamRankRewardItem);
            }

            _rewardRowBase.SetActiveWithCheck(true);
            _rewardItemIconList = new List<PartsTeamEvaluationRankupRewardsRow>();
            for (int i = 0, n = _rankupRewardsChunkedList.Count(); i < n; i++)
            {
                var rewardsRow = Instantiate(_rewardRowBase, _rewardItemsRoot);
                rewardsRow.Setup(_rankupRewardsChunkedList.ElementAt(i));
                _rewardItemIconList.Add(rewardsRow);
            }
            _rewardRowBase.SetActiveWithCheck(false);

            _rankIconFlashActionPlayer.Play("in");

            yield return new WaitForSeconds(0.7f);

            AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMPOINT_RANKUP);
            _rewardItemIconList.FirstOrDefault().PlayIcon((TeamRank)(_beforeBestTeamRank + 1), _parentCanvas.sortingOrder);

            yield return new WaitUntil(() => (_rankIconFlashActionPlayer.FlashPlayer.GetCurrentLabel() == "in_end"));

            if (isMultiRankup)
            {
                int roopCount = 0;
                for (int i = ((int)_beforeBestTeamRank + 2); i <= (int)_currentRank; i++)
                {
                    roopCount++;
                    AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMPOINT_RANKUP);

                    if (_isSkip)
                    {
                        SetupRankIcon(_currentRank, _currentRankPoint);
                        _rankIconFlashActionPlayer.Play("continuous");

                        yield return PlaySkipRankUpRewards(teamStadiumRankList);

                        break;
                    }

                    SetupRankIcon(((TeamRank)i), (i == (int)_currentRank) ? _currentRankPoint : masterTeamStadiumRank.Get(i).TeamMinValue);

                    _rankIconFlashActionPlayer.Play("continuous");

                    if (roopCount < 20)
                    {
                        for (int index = 0, n = _rewardItemIconList.Count; index < n; index++)
                        {
                            bool isContain = _rewardItemIconList[index].IsContainReward((TeamRank)i);
                            if (isContain)
                            {
                                _rewardItemIconList[index].PlayIcon((TeamRank)i, _parentCanvas.sortingOrder + i);
                            }
                        }
                    }
                    else
                    {
                        // 報酬アイテムが20個以上なら、最新のアイテムを最後尾に追加し
                        // 最初の報酬を削除して再度アイコンを並べた上でアニメーション再生

                        var startRank = (i - 19);
                        var rankIndex = teamStadiumRankList.FindIndex(x => x.TeamRank == startRank);
                        var rankList = teamStadiumRankList.GetRange(rankIndex, 20);
                        _rankupRewardsChunkSize = ChunkSize(rankList);
                        _rankupRewardsChunkedList = rankList.Select((rank, index) => new { rank, index })
                            .GroupBy(x => x.index / _rankupRewardsChunkSize)
                            .Select(group => group.Select(x => x.rank).ToList());

                        for (int index = 0, n = _rankupRewardsChunkedList.Count(); index < n; index++)
                        {
                            _rewardItemIconList[index].UpdateIcons(_rankupRewardsChunkedList.ElementAt(index));
                        }

                        _rewardItemIconList.LastOrDefault().PlayIcon(((TeamRank)i), _parentCanvas.sortingOrder);
                    }

                    yield return new WaitForSeconds(0.5f);
                }

            }

            _isSkip = true;
            _isPlayingRankIcons = false;
        }

        /// <summary>
        /// ランクアップ報酬演出のスキップ処理
        /// </summary>
        /// <returns></returns>
        private IEnumerator PlaySkipRankUpRewards(List<TeamStadiumRank> teamStadiumRankList)
        {
            var rewardsCount = teamStadiumRankList.Count;
            if (20 < rewardsCount)
            {
                var startRank = ((int)_currentRank - 19);
                var rankIndex = teamStadiumRankList.FindIndex(x => x.TeamRank == startRank);
                var rankList = teamStadiumRankList.GetRange(rankIndex, 20);
                _rankupRewardsChunkSize = ChunkSize(rankList);
                _rankupRewardsChunkedList = rankList.Select((rank, index) => new { rank, index })
                    .GroupBy(x => x.index / _rankupRewardsChunkSize)
                    .Select(group => group.Select(x => x.rank).ToList());

                for (int index = 0, n = _rankupRewardsChunkedList.Count(); index < n; index++)
                {
                    _rewardItemIconList[index].UpdateIcons(_rankupRewardsChunkedList.ElementAt(index));
                }
            }

            for (int index = 0; index < _rankupRewardsChunkedList.Count(); index++)
            {
                yield return StartCoroutine(_rewardItemIconList[index].ShowIcons(_parentCanvas.sortingOrder));
            }
        }

        /// <summary>
        /// 報酬アイテムのチャンクサイズ
        /// ４列か５列なのか判定
        /// </summary>
        /// <param name="teamStadiumRankList"></param>
        /// <returns></returns>
        private int ChunkSize(List<TeamStadiumRank> teamStadiumRankList)
        {
            var count = teamStadiumRankList.Count;

            if (count <= 5 || (8 < count && count <= 10) || 12 < count && count <= 15 || 16 < count)
            {
                return 5;
            }
            else
            {
                return 4;
            }
        }

        /// <summary>
        /// ランクアップアイコンセットアップ
        /// </summary>
        /// <param name="teamRank"></param>
        /// <param name="point"></param>
        /// <param name="isUnder"></param>
        private void SetupRankIcon(TeamRank teamRank, int point, bool isUnder = false)
        {
            var rankFrameTexture = ResourceManager.LoadOnHash<Texture>(ResourcePath.GetTeamRankFrameSizeM(teamRank.Group()), _dialog.DialogData.DialogHash);
            var rankIconTexture = ResourceManager.LoadOnHash<Texture>(ResourcePath.GetTeamRankIconSizeM(teamRank), _dialog.DialogData.DialogHash);

            var objRoot = (isUnder) ? "OBJ_mc_dum_rank_under00/{0}" : "OBJ_mc_dum_rank_stomp00/{0}";

            _rankIconFlashActionPlayer.FlashPlayer.SetTexture(string.Format(objRoot, RANK_FRAME_PLN), rankFrameTexture);
            _rankIconFlashActionPlayer.FlashPlayer.SetTexture(string.Format(objRoot, RANK_ICON_PLN), rankIconTexture);
            _rankIconFlashActionPlayer.FlashPlayer.SetText(point.ToString(), string.Format(objRoot, RANK_POINT_TXT));
        }

        /// <summary>
        /// ゲージ演出再生
        /// </summary>
        private void GaugeIn()
        {
            if (_gaugeFlashPlayer == null)
            {
                _gaugeFlashPlayer = FlashLoader.LoadOnHash(
                    ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_GAUGE_FLASH_PATH, _gaugeFlashRoot, ROOT_MOTION, _dialog.DialogData.DialogHash);
                _gaugeFlashPlayer.SetLayer(_gaugeFlashRoot.gameObject.layer);
                _gaugeFlashPlayer.SortLayer = _dialog.ParentCanvasSortingLayerName;
                _gaugeFlashPlayer.SortOffset = _dialog.GetSortingOrder();
            }

            const string currentRankMotFormat = "MOT_mc_dum_rank_current00/{0}";
            const string nextRankMotFormat = "MOT_mc_dum_rank_next00/{0}";

            // 次のチームランク
            var nextTeamRank = (_isLimitRank) ? MasterDataManager.Instance.masterTeamStadiumRank.Get((int)TeamRank.SS) : TeamStadiumUtil.GetNextTeamRank(_currentRankPoint);
            _nextRankPointMin = nextTeamRank.TeamMinValue;
            _nextRankPointMin = nextTeamRank.TeamMinValue;
            _nextRank = TeamStadiumUtil.GetTeamRank(_nextRankPointMin);

            _gaugeFlashPlayer.SetResetMode(AnimateToUnity.AnMotion.ResetModeTypes.None);
            _gaugeFlashPlayer.Init();
            _gaugeFlashPlayer.SortOffset = _parentCanvas.sortingOrder + 1;
            _gaugeFlashPlayer.SortLayer = _parentCanvas.sortingLayerName;
            _gaugeFlashPlayer.SetTexture(string.Format(currentRankMotFormat, RANK_FRAME_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankFrame(_currentRank.Group())));
            _gaugeFlashPlayer.SetTexture(string.Format(currentRankMotFormat, RANK_ICON_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankIcon(_currentRank)));
            _gaugeFlashPlayer.SetTexture(string.Format(nextRankMotFormat, RANK_FRAME_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankFrame(_nextRank.Group())));
            _gaugeFlashPlayer.SetTexture(string.Format(nextRankMotFormat, RANK_ICON_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankIcon(_nextRank)));

            _gaugeFlashPlayer.SetText(_currentRankPointMin.ToString(), string.Format(currentRankMotFormat, RANK_POINT_TXT));
            _gaugeFlashPlayer.SetText((_isLimitRank) ? nextTeamRank.TeamMaxValue.ToString() : _nextRankPointMin.ToString(), string.Format(nextRankMotFormat, RANK_POINT_TXT));
            _gaugeFlashPlayer.Play("in");

            PlayRankGaugeProgress();
        }

        /// <summary>
        /// ゲージ再生
        /// </summary>
        private void PlayRankGaugeProgress()
        {
            _playGaugeCoroutine = StartCoroutine(ShowGaugeProgress());
        }

        /// <summary>
        /// ゲージ表示
        /// </summary>
        /// <returns></returns>
        private IEnumerator ShowGaugeProgress()
        {
            var step = GAUGE_INDEX_MAX;
            var ratio = 1.0f;
            if (_currentRank != TeamRank.SS)
            {
                var currentMaster = MasterDataManager.Instance.masterTeamStadiumRank.Get((int)_currentRank);
                ratio = (float)(_currentRankPoint - currentMaster.TeamMinValue) / (float)(currentMaster.TeamMaxValue - currentMaster.TeamMinValue);
                step = Mathf.FloorToInt(ratio * 10);
            }

            const string rankGaugeLabelFormat = "rank_{0}";
            string rankGaugeLabel = string.Empty;
            switch (_currentRank.Group())
            {
                case TeamRankGroup.F:
                case TeamRankGroup.E:
                    rankGaugeLabel = string.Format(rankGaugeLabelFormat, TeamRankGroup.E.ToString().ToLower());
                    break;
                case TeamRankGroup.D:
                case TeamRankGroup.C:
                case TeamRankGroup.B:
                case TeamRankGroup.A:
                    rankGaugeLabel = string.Format(rankGaugeLabelFormat, _currentRank.Group().ToString().ToLower());
                    break;
                case TeamRankGroup.S:
                case TeamRankGroup.SS:
                    rankGaugeLabel = string.Format(rankGaugeLabelFormat, TeamRankGroup.S.ToString().ToLower());
                    break;
            }

            var count = Mathf.Min(step, GAUGE_INDEX_MAX);
            var barMotions = new List<AnimateToUnity.AnMotion>();
            var barAddMotions = new List<AnimateToUnity.AnMotion>();
            for (int i = 1; i <= count; ++i)
            {
                var barMotionRoot = _gaugeFlashPlayer.GetObj($"OBJ_mc_gau_evalution_bar{i}", true).ChildMotion;
                barMotionRoot.SetResetModeType(AnimateToUnity.AnMotion.ResetModeTypes.None);
                barMotions.Add(barMotionRoot);
                _gaugeFlashPlayer.GetMotion("MOT_img_gauge_evaluation_bar", rootGameObject: barMotionRoot.GameObject).SetMotionPlay(rankGaugeLabel);

                var barAddMotionRoot = _gaugeFlashPlayer.GetObj($"OBJ_mc_gau_evalution_bar{i}_add", true).ChildMotion;
                barAddMotionRoot.SetResetModeType(AnimateToUnity.AnMotion.ResetModeTypes.None);
                barAddMotions.Add(barAddMotionRoot);

                for (int ii = 0, n = barAddMotionRoot.ObjectList.Count; ii < n; ii++)
                {
                    _gaugeFlashPlayer.GetMotion("MOT_img_gauge_evaluation_bar", rootGameObject: barAddMotionRoot.ObjectList[ii].GameObject).SetMotionPlay(rankGaugeLabel);
                }
            }

            // ゲージが広がるのを待つ
            yield return new WaitForSeconds(0.5f);

            // バーのアニメーション
            int barMotionCount = barMotions.Count;
            if (barMotionCount > 0)
            {
                // 0個目のバーのin待ち
                yield return new WaitForSeconds(RANK_UP_BAR_DELAY);

                // グレード上昇SE開始
                _pointupSePlayback = AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMRANK_POINTUP, true);

                for (int i = 0; i < barMotionCount; ++i)
                {
                    barMotions[i].SetMotionPlay("in");
                    barAddMotions[i].SetMotionPlay("in");

                    // ラストならDelayなし
                    if (i != barMotionCount - 1)
                    {
                        yield return new WaitForSeconds(RANK_UP_BAR_DELAY);
                    }
                }

                // グレード上昇SE停止
                AudioManager.Instance.StopSe(_pointupSePlayback);
            }
            
            // チュートリアル対応 : 評価値ゲージの増加アニメーション完了後に、「閉じる」ボタンをガイドで掴む
            if (TutorialManager.IsTutorialExecuting())
            {
                // 演出待ちを入れる
                yield return new WaitForSeconds(0.5f);

                UIManager.Instance.UnlockGameCanvas();
                TutorialOutGame.SetGuideUpdateTeamEvaluationPointDialog(this);
            }
        }

        /// <summary>
        /// ダイアログを閉じる
        /// </summary>
        private void CloseDialog()
        {
            _rewardsListButton.SetEnable(false);
            _closeButton.SetEnable(false);

            // ゲージの演出が動作中なら止める
            if (_playGaugeCoroutine != null)
            {
                StopCoroutine(_playGaugeCoroutine);
            }

            // 再生中ならSEを止める
            if (AudioManager.IsPlaySe(_pointupSePlayback))
            {
                AudioManager.Instance.StopSe(_pointupSePlayback);
            }

            _backgroundEmblemFlashPlayer.Play("out");
            if (_gaugeFlashPlayer != null)
            {
                _gaugeFlashPlayer.Play("out");
            }
            _rankIconFlashActionPlayer.Play("out");
            _titleFlashPlayer.Play("out");
            _animatorInstance.SetTrigger("Close");
            FrameExpandOut();

            const float DURATION = 0.16f;
            for (int i = 0, n = _detailFadeCanvasGroupList.Count; i < n; i++)
            {
                _detailFadeCanvasGroupList[i].DOFade(0, DURATION);
            }
            
            DOVirtual.DelayedCall(DURATION, () => _dialog.Close());

            _onClose?.Invoke(_beforeBestRankPoint);
        }

        /// <summary>
        /// 報酬一覧ボタン
        /// </summary>
        private void OnClickRewardsListButton()
        {
            DialogTeamEvaluationRewardList.Open();
        }
    }
}
