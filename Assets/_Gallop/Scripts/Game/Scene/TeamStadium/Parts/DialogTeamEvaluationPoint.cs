using Cute.Cri;
using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using Cute.Core;
using UnityEngine;

namespace Gallop
{
    using static Gallop.MasterTeamStadiumRank;
    using static Gallop.TeamStadiumDefine;

    /// <summary>
    /// チーム評価点ダイアログ
    /// https://xxxxxxxxxx/app/#/projects/5e68d0c586e4657e324e656c/screens/5e83388186e465294b9fc324
    /// </summary>
    public class DialogTeamEvaluationPoint : DialogInnerBase
    {
        #region SerializeFields

        [SerializeField] private CanvasGroup _title = null;
        [SerializeField] private ButtonCommon _teamEditButton = null;
        [SerializeField] private PartsTeamEvaluationRewardItem _teamEvaluationRewardItem = null;
        [SerializeField] private ImageCommon _teamRankUpdateRewardTitle = null;
        [SerializeField] private ButtonCommon _rewardsListButton = null;
        [SerializeField] private RectTransform _backgroundEmblemFlashRoot = null;
        [SerializeField] private RectTransform _gaugeFlashRoot = null;
        [SerializeField] private RectTransform _rankIconFlashRoot = null;

        [SerializeField] private ButtonCommon _closeButton = null;
        [SerializeField] private CanvasGroup _buttonCanvasGroup = null;
        [SerializeField] private RectTransform _frameRoot = null;
        [SerializeField] private List<CanvasGroup> _fadeInCanvasGroupList = null;
        [SerializeField] private Canvas _contentsRootCanvas = null;
        [SerializeField] private ImageCommon _nextReward = null;
        [SerializeField] private TextCommon _limitRankText = null;

        #endregion

        #region Variables, Constants

        private TeamRank _currentRank;
        private TeamRank _nextRank;
        private int _currentRankPoint;
        private int _currentRankPointMin;
        private int _nextRankPointMin;
        private bool _isLimitRank;
        private TeamStadiumRank _nextTeamRank;

        private DialogCommon _dialog;
        private Sequence _sequence;
        private Animator _animatorInstance;
        private FlashPlayer _backgroundEmblemFlashPlayer;
        private FlashPlayer _gaugeFlashPlayer;
        private FlashPlayer _rankIconFlashPlayer;
        private Canvas _parentCanvas;
        private Coroutine _playGaugeCoroutine = null;
        private AudioPlayback _pointupSePlayback;

        private const int GAUGE_INDEX_MAX = 9;
        private const string ROOT_MOTION = "MOT_root";
        private const string RANK_FRAME_PLN = "PLN_dum_rank_frm00";
        private const string RANK_ICON_PLN = "PLN_dum_rank_icon00";
        private const string RANK_POINT_TXT = "TXT_num_strength00";
        private const float RANK_UP_BAR_DELAY = 0.06666f;

        #endregion

        #region override

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.WITHOUT_FRAME;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.CenterButtonText = TextId.Common0083.Text();
            data.EnableOutsideClick = false;
            return data;
        }

        #endregion

#if CYG_DEBUG
        public static bool Debug_Play = false;
        public static TeamRank Debug_CurrentRank = TeamRank.None;
#endif

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open()
        {
            var component = LoadAndInstantiatePrefab<DialogTeamEvaluationPoint>(ResourcePath.DIALOG_TEAM_EVALUATION_POINT);
            var dialogData = component.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.OnPushBackKey = () =>
            {
                UpdateDispatcher.StartCoroutine(component.CloseDialog());
                return true;
            };
            dialogData.CancelSe = AudioId.INVALID;
            var dialog = DialogManager.PushDialog(dialogData);

            // チームランク詳細ダイアログ用のSEを鳴らす
            AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMRANK_APPEAR);

            component.Setup(dialog);
        }

        /// <summary>
        /// 必要なリソースのダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.COMMON_UNITYANIMATION_FRAME_00);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_EMBLEM_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_GAUGE_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_PATH);
        }

        /// <summary>
        /// 初期化処理
        /// </summary>
        /// <param name="dialog"></param>
        private void Setup(DialogCommon dialog)
        {
            _dialog = dialog;
            _parentCanvas = transform.GetComponentInParent<Canvas>();

            // 現在のチームランク
            var teamStadiumData = WorkDataManager.Instance.TeamStadiumData;
            _currentRankPoint = teamStadiumData.TeamStadiumDeckInfo.GetTeamRankPoint();
            _currentRank = TeamStadiumUtil.GetTeamRank(_currentRankPoint);

            var masterTeamStadiumRank = MasterDataManager.Instance.masterTeamStadiumRank;
            _currentRankPointMin = masterTeamStadiumRank.Get((int)_currentRank).TeamMinValue;

#if CYG_DEBUG
            if (Debug_Play == true)
            {
                _currentRank = Debug_CurrentRank;

                // デバッグ後に戻しておかないとずっとデバッグ状態になる。
                Debug_Play = false;
            }
#endif

            _isLimitRank = TeamRank.SS <= _currentRank;

            // 次のチームランク
            _nextTeamRank = _isLimitRank ? masterTeamStadiumRank.Get((int)TeamRank.SS) : TeamStadiumUtil.GetNextTeamRank(_currentRankPoint);
            _nextRankPointMin = _nextTeamRank.TeamMinValue;
            _nextRank = TeamStadiumUtil.GetTeamRank(_nextRankPointMin);

            // 次に貰える報酬の情報
            var bestEvaluationPoint = WorkDataManager.Instance.UserData.BestTeamEvaluationPoint;
            var nextRewardTeamRank = (TeamRank.SS <= TeamStadiumUtil.GetTeamRank(bestEvaluationPoint)) ? masterTeamStadiumRank.Get((int)TeamRank.SS) : TeamStadiumUtil.GetNextTeamRank(bestEvaluationPoint);
            var itemName = MasterDataManager.Instance.masterItemData.Get(nextRewardTeamRank.ItemId).Name;
            var isRewardLimitRank = TeamRank.SS < (TeamRank)nextRewardTeamRank.TeamRank;

            // 最大ランクの場合は表示が変わる
            if (_isLimitRank)
            {
                _limitRankText.SetActiveWithCheck(true);
                _nextReward.SetActiveWithCheck(false);
                _teamEvaluationRewardItem.Setup();

                Vector3 pos;
                RectTransform rectTransform = _title.GetComponent<RectTransform>();
                pos = rectTransform.localPosition;
                pos.y = 674;
                rectTransform.localPosition = pos;

                rectTransform = _backgroundEmblemFlashRoot;
                pos = rectTransform.localPosition;
                pos.y = -39;
                rectTransform.localPosition = pos;

                rectTransform = _rankIconFlashRoot;
                pos = rectTransform.localPosition;
                pos.y = 139;
                rectTransform.localPosition = pos;

                rectTransform = _teamRankUpdateRewardTitle.rectTransform;
                pos = rectTransform.localPosition;
                pos.y = -29;
                rectTransform.localPosition = pos;

                rectTransform = _teamEvaluationRewardItem.GetComponent<RectTransform>();
                pos = rectTransform.localPosition;
                pos.y = -182;
                rectTransform.localPosition = pos;

                rectTransform = _rewardsListButton.GetComponent<RectTransform>();
                pos = rectTransform.localPosition;
                pos.y = -358;
                rectTransform.localPosition = pos;

                rectTransform = _teamEditButton.GetComponent<RectTransform>();
                pos = rectTransform.localPosition;
                pos.y = 65;
                rectTransform.localPosition = pos;
            }
            else
            {
                _limitRankText.SetActiveWithCheck(false);
                _nextReward.SetActiveWithCheck(true);
                _teamEvaluationRewardItem.Setup(nextRewardTeamRank, itemName, false, isRewardLimitRank);
            }

            _contentsRootCanvas.overrideSorting = true;
            _contentsRootCanvas.sortingLayerID = _parentCanvas.sortingLayerID;
            _contentsRootCanvas.sortingOrder = _parentCanvas.sortingOrder + 1;
            _teamEditButton.SetOnClick(OnClickTeamEditButton);
            _rewardsListButton.SetOnClick(OnClickRewardsListButton);
            _closeButton.SetOnClick(() =>
            {
                UpdateDispatcher.StartCoroutine(CloseDialog());
            });
            _closeButton.SetActiveWithCheck(false);

            PlayIn();
        }

        /// <summary>
        /// 表示シーケンス再生
        /// </summary>
        private void PlayIn()
        {
            _sequence = DOTween.Sequence();
            _sequence.OnStart(() => FlameIn());
            _sequence.InsertCallback(0.167f, () => TitleIn());
            _sequence.InsertCallback(0.18f, () => GaugeIn());
            _sequence.InsertCallback(0.3f, () =>
            {
                RankIconIn();
                BackgroundEmblemIn();
            });
            _sequence.OnComplete(() =>
            {
                ShowCloseButton();
            });
            _sequence.Play();
        }


        /// <summary>
        /// FrameIn
        /// </summary>
        private void FlameIn()
        {
            var frame = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COMMON_UNITYANIMATION_FRAME_00, _dialog.DialogData.DialogHash);
            _animatorInstance = Instantiate(frame, _frameRoot).GetComponent<Animator>();
            _animatorInstance.GetComponent<AnimationEventSender>().Initialize(this);
            _animatorInstance.transform.SetSiblingIndex(0);

            if (_isLimitRank)
            {
                _animatorInstance.Play("in04");
            }
            else
            {
                _animatorInstance.Play("in02");
            }
        }

        /// <summary>
        /// タイトル演出再生
        /// </summary>
        private void TitleIn()
        {
            const float DURATION = 0.2f;
            _title.DOFade(0f, DURATION).From();
            _title.transform.DOLocalMoveY(170f, DURATION).SetEase(Ease.OutCubic).From();
        }

        /// <summary>
        /// BackgroundEmblemIn
        /// </summary>
        private void BackgroundEmblemIn()
        {
            if (_backgroundEmblemFlashPlayer == null)
            {
                _backgroundEmblemFlashPlayer = FlashLoader.LoadOnHash(
                    ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_EMBLEM_FLASH_PATH, _backgroundEmblemFlashRoot, ROOT_MOTION, _dialog.DialogData.DialogHash);
                _backgroundEmblemFlashPlayer.SetLayer(_backgroundEmblemFlashRoot.gameObject.layer);
                _backgroundEmblemFlashPlayer.SortLayer = _dialog.ParentCanvasSortingLayerName;
                _backgroundEmblemFlashPlayer.SortOffset = _dialog.GetSortingOrder();
            }

            _backgroundEmblemFlashPlayer.SortOffset = _parentCanvas.sortingOrder + 1;
            _backgroundEmblemFlashPlayer.SortLayer = _parentCanvas.sortingLayerName;
            _backgroundEmblemFlashPlayer.GetMotion(ROOT_MOTION).SetMotionPlay("in");
        }

        /// <summary>
        /// RankIconIn
        /// </summary>
        /// <returns></returns>
        private void RankIconIn()
        {
            if (_rankIconFlashPlayer == null)
            {
                _rankIconFlashPlayer = FlashLoader.LoadOnHash(
                    ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_PATH, _rankIconFlashRoot, ROOT_MOTION, _dialog.DialogData.DialogHash);
                _rankIconFlashPlayer.SetLayer(_rankIconFlashRoot.gameObject.layer);
                _rankIconFlashPlayer.SortLayer = _dialog.ParentCanvasSortingLayerName;
                _rankIconFlashPlayer.SortOffset = _dialog.GetSortingOrder();
            }

            _rankIconFlashPlayer.SetResetMode(AnimateToUnity.AnMotion.ResetModeTypes.None);
            _rankIconFlashPlayer.Init();
            _rankIconFlashPlayer.SortOffset = _parentCanvas.sortingOrder + 2;
            _rankIconFlashPlayer.SortLayer = _parentCanvas.sortingLayerName;

            var frameTexture = ResourceManager.LoadOnHash<Texture>(ResourcePath.GetTeamRankFrameSizeM(_currentRank.Group()), _dialog.DialogData.DialogHash);
            var iconTexture = ResourceManager.LoadOnHash<Texture>(ResourcePath.GetTeamRankIconSizeM(_currentRank), _dialog.DialogData.DialogHash);

            const string underObjFormat = "OBJ_mc_dum_rank_under00/{0}";
            _rankIconFlashPlayer.SetTexture(string.Format(underObjFormat, RANK_FRAME_PLN), frameTexture);
            _rankIconFlashPlayer.SetTexture(string.Format(underObjFormat, RANK_ICON_PLN), iconTexture);
            _rankIconFlashPlayer.SetText(_currentRankPoint.ToString(), string.Format(underObjFormat, RANK_POINT_TXT));

            const string stompObjRoot = "OBJ_mc_dum_rank_stomp00";
            _rankIconFlashPlayer.GetObj(stompObjRoot).GameObject.SetActiveWithCheck(false);
            _rankIconFlashPlayer.Play("expand_in");
        }

        /// <summary>
        /// GaugeIn
        /// </summary>
        private void GaugeIn()
        {
            if( _isLimitRank == true )
            {
                // SSランク時は次がないのでやらない
                return;
            }

            if (_gaugeFlashPlayer == null)
            {
                _gaugeFlashPlayer = FlashLoader.LoadOnHash(
                    ResourcePath.TEAM_STADIUM_TEAM_EVALUATION_GAUGE_FLASH_PATH, _gaugeFlashRoot, ROOT_MOTION, _dialog.DialogData.DialogHash);
                _gaugeFlashPlayer.SetLayer(_gaugeFlashRoot.gameObject.layer);
                _gaugeFlashPlayer.SortLayer = _dialog.ParentCanvasSortingLayerName;
                _gaugeFlashPlayer.SortOffset = _dialog.GetSortingOrder();
            }

            const string currentRankMotFormat = "MOT_mc_dum_rank_current00/{0}";
            const string nextRankMotFormat = "MOT_mc_dum_rank_next00/{0}";

            _gaugeFlashPlayer.SetResetMode(AnimateToUnity.AnMotion.ResetModeTypes.None);
            _gaugeFlashPlayer.Init();
            _gaugeFlashPlayer.SortOffset = _parentCanvas.sortingOrder + 1;
            _gaugeFlashPlayer.SortLayer = _parentCanvas.sortingLayerName;
            _gaugeFlashPlayer.SetTexture(string.Format(currentRankMotFormat, RANK_FRAME_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankFrame(_currentRank.Group())));
            _gaugeFlashPlayer.SetTexture(string.Format(currentRankMotFormat, RANK_ICON_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankIcon(_currentRank)));
            _gaugeFlashPlayer.SetTexture(string.Format(nextRankMotFormat, RANK_FRAME_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankFrame(_nextRank.Group())));
            _gaugeFlashPlayer.SetTexture(string.Format(nextRankMotFormat, RANK_ICON_PLN), ResourceManager.LoadOnView<Texture>(ResourcePath.GetTeamRankIcon(_nextRank)));

            _gaugeFlashPlayer.SetText(_currentRankPointMin.ToString(), string.Format(currentRankMotFormat, RANK_POINT_TXT));
            _gaugeFlashPlayer.SetText((_isLimitRank) ? _nextTeamRank.TeamMaxValue.ToString() : _nextRankPointMin.ToString(), string.Format(nextRankMotFormat, RANK_POINT_TXT));
            _gaugeFlashPlayer.Play("in");
            PlayRankGaugeProgress();
        }

        /// <summary>
        /// ゲージ再生
        /// </summary>
        private void PlayRankGaugeProgress()
        {
           _playGaugeCoroutine = StartCoroutine(ShowGaugeProgress());
        }

        /// <summary>
        /// ゲージ表示
        /// </summary>
        /// <returns></returns>
        private IEnumerator ShowGaugeProgress()
        {
            var step = GAUGE_INDEX_MAX;
            var ratio = 1.0f;
            if (_currentRank != TeamRank.SS)
            {
                var currentMaster = MasterDataManager.Instance.masterTeamStadiumRank.Get((int)_currentRank);
                ratio = (float)(_currentRankPoint - currentMaster.TeamMinValue) / (float)(currentMaster.TeamMaxValue - currentMaster.TeamMinValue);
                step = Mathf.FloorToInt(ratio * 10);
            }

            const string rankGaugeLabelFormat = "rank_{0}";
            string rankGaugeLabel = string.Empty;
            switch (_currentRank.Group())
            {
                case TeamRankGroup.F:
                case TeamRankGroup.E:
                    rankGaugeLabel = string.Format(rankGaugeLabelFormat, TeamRankGroup.E.ToString().ToLower());
                    break;
                case TeamRankGroup.D:
                case TeamRankGroup.C:
                case TeamRankGroup.B:
                case TeamRankGroup.A:
                    rankGaugeLabel = string.Format(rankGaugeLabelFormat, _currentRank.Group().ToString().ToLower());
                    break;
                case TeamRankGroup.S:
                case TeamRankGroup.SS:
                    rankGaugeLabel = string.Format(rankGaugeLabelFormat, TeamRankGroup.S.ToString().ToLower());
                    break;
            }

            var count = Mathf.Min(step, GAUGE_INDEX_MAX);
            var barMotions = new List<AnimateToUnity.AnMotion>();
            var barAddMotions = new List<AnimateToUnity.AnMotion>();
            for (int i = 1; i <= count; ++i)
            {
                var barMotionRoot = _gaugeFlashPlayer.GetObj($"OBJ_mc_gau_evalution_bar{i}", true).ChildMotion;
                barMotionRoot.SetResetModeType(AnimateToUnity.AnMotion.ResetModeTypes.None);
                barMotions.Add(barMotionRoot);
                _gaugeFlashPlayer.GetMotion("MOT_img_gauge_evaluation_bar", rootGameObject: barMotionRoot.GameObject).SetMotionPlay(rankGaugeLabel);

                var barAddMotionRoot = _gaugeFlashPlayer.GetObj($"OBJ_mc_gau_evalution_bar{i}_add", true).ChildMotion;
                barAddMotionRoot.SetResetModeType(AnimateToUnity.AnMotion.ResetModeTypes.None);
                barAddMotions.Add(barAddMotionRoot);

                for (int ii = 0, n = barAddMotionRoot.ObjectList.Count; ii < n; ii++)
                {
                    _gaugeFlashPlayer.GetMotion("MOT_img_gauge_evaluation_bar", rootGameObject: barAddMotionRoot.ObjectList[ii].GameObject).SetMotionPlay(rankGaugeLabel);
                }
            }

            // ゲージが広がるのを待つ
            yield return new WaitForSeconds(0.5f);

            // バーのアニメーション
            int barMotionCount = barMotions.Count;
            if (barMotionCount > 0)
            {
                // 0個目のバーのin待ち
                yield return new WaitForSeconds(RANK_UP_BAR_DELAY);

                // グレード上昇SE開始
                _pointupSePlayback = AudioManager.Instance.PlaySe(AudioId.SFX_SYS_TEAMRANK_POINTUP, true);

                for (int i = 0; i < barMotionCount; ++i)
                {
                    barMotions[i].SetMotionPlay("in");
                    barAddMotions[i].SetMotionPlay("in");

                    // ラストならDelayなし
                    if (i != barMotionCount - 1)
                    {
                        yield return new WaitForSeconds(RANK_UP_BAR_DELAY);
                    }
                }

                // グレード上昇SE停止
                AudioManager.Instance.StopSe(_pointupSePlayback);
            }
        }

        /// 閉じるボタン表示
        /// </summary>
        private void ShowCloseButton()
        {
            const float DURATION = 0.2f;
            _closeButton.SetActiveWithCheck(true);
            _buttonCanvasGroup.DOFade(0f, DURATION).From();
            (_closeButton.transform as RectTransform).DOAnchorPosY(-20f, DURATION).From(true);
        }

        /// <summary>
        /// ダイアログを閉じる
        /// </summary>
        private IEnumerator CloseDialog(System.Action finishCallback = null)
        {
            //閉じている最中にボタンが反応しないようにロックをかけておく
            UIManager.Instance.LockGameCanvas();
            _rewardsListButton.SetEnable(false);
            _closeButton.SetEnable(false);
            _teamEditButton.SetEnable(false);

            // ゲージの演出が動作中なら止める
            if (_playGaugeCoroutine != null)
            {
                StopCoroutine(_playGaugeCoroutine);
            }
            
            // 再生中ならSEを止める
            if (AudioManager.IsPlaySe(_pointupSePlayback))
            {
                AudioManager.Instance.StopSe(_pointupSePlayback);
            }

            const float DIALOG_WAIT_DURATION = 0.1f;
            const float DURATION = 0.15f;

            //ダイアログは閉じ始めからDelayで0.1秒の待機状態がある。そのため先にクローズを呼んでそれに合わせてアニメーションさせる
            _dialog.Close();
            yield return new WaitForSeconds(DIALOG_WAIT_DURATION);


            for (int i = 0, count = _fadeInCanvasGroupList.Count; i < count; i++)
            {
                if (_fadeInCanvasGroupList[i] != null)
                {
                    _fadeInCanvasGroupList[i].DOFade(0, DURATION);
                }
            }

            if (_backgroundEmblemFlashPlayer != null)
            {
                _backgroundEmblemFlashPlayer.Play("out");
            }
            if (_gaugeFlashPlayer != null)
            {
                _gaugeFlashPlayer.Play("out");
            }

            if (_rankIconFlashPlayer != null)
            {
                if (_rankIconFlashPlayer.IsExistLabel("expand_out"))
                {
                    // ver1.2以降用のラベルがあるならそちらを使います.
                    _rankIconFlashPlayer.Play("expand_out");
                }
                else
                {
                    _rankIconFlashPlayer.Play("out");
                }
            }

            if (_animatorInstance != null)
            {
                _animatorInstance.SetTrigger("Close");
                if (_isLimitRank)
                {
                    _animatorInstance.Play("reduction_out02");
                }
                else
                {
                    _animatorInstance.Play("expand_out03");
                }
            }

            UIManager.Instance.UnlockGameCanvas();
            finishCallback?.Invoke();
        }

        /// <summary>
        /// 報酬一覧ボタン
        /// </summary>
        private void OnClickRewardsListButton()
        {
            DialogTeamEvaluationRewardList.Open();
        }

        /// <summary>
        /// チーム編成ボタン
        /// </summary>
        private void OnClickTeamEditButton()
        {
            UpdateDispatcher.StartCoroutine(CloseDialog(() =>
            {
                SceneManager.Instance.StackCurrentViewForBack();
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.TeamStadiumDeckHub, new HubViewControllerBase.HubViewInfo() { DefaultViewId = SceneDefine.ViewId.TeamStadiumDeck });
            }));

        }
    }
}
