using System;
using System.Linq;
using UnityEngine;
using Cute.UI;

namespace Gallop
{
    /// <summary>
    /// アイテム入手導線ダイアログのリストアイテム
    /// </summary>
    public class PartsDialogReleaseContentPlaceListItem : LoopScrollItemBase
    {
        #region 定義

        /// <summary>
        /// セットアップ用パラメータ
        /// </summary>
        public class SetupParameter : IPartsSetupParameter
        {
            /// <summary> タイトル </summary>
            public string Title = string.Empty;

            /// <summary> アイコン </summary>
            public Sprite IconSprite;

            /// <summary> ボタンコールバック </summary>
            public Action OnClick = null;

            /// <summary>
            /// 場所データから作成
            /// </summary>
            /// <param name="itemPlaceData"></param>
            /// <param name="onClick"></param>
            public SetupParameter(ReleaseContentType type, Action onChangeView = null, Action onFinishRaceDelete = null)
            {
                // 遷移先で分岐
                switch (type)
                {
                    case ReleaseContentType.DailyRace: // デイリーレース
                        SetupDailyRace(onChangeView, onFinishRaceDelete);
                        break;

                    case ReleaseContentType.Circle: // サークル
                        SetupCircle(onChangeView);
                        break;

                    case ReleaseContentType.Champions: // チャンピオンズミーティング
                        SetupChampions(onChangeView);
                        break;

                    case ReleaseContentType.RoomMatch: // ルームマッチ
                        SetupRoomMatch(onChangeView);
                        break;

                    case ReleaseContentType.PracticeRace: // 練習
                        SetupPracticeRace(onChangeView);
                        break;
                }
            }

            private void SetupDailyRace(Action onChangeView = null, Action onFinishRaceDelete = null)
            {
                Title = TextId.Race0043.Text();
                IconSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.DailyRace));

                OnClick = () =>
                {
                    // 中断データがあるなら
                    if (WorkDataManager.Instance.RaceStateData.HasDailyRaceState())
                    {
                        // 復帰処理
                        RaceUtil.DailyRaceResumeProcess(onGoToPaddock: onChangeView, onFinishDelete: onFinishRaceDelete);
                        return;
                    }

                    onChangeView?.Invoke();

                    //TeamStadiumがスタックをもってしまっているので、消しておく。本来であればスタック自体が不要なはずだが、影響範囲を考えて個別対応
                    SceneManager.Instance.ClearBackStack();
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.DailyRace);
                };
            }

            private void SetupCircle(Action onChangeView = null)
            {
                Title = TextId.Circle0321.Text();
                IconSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.ReleaseCircle));

                OnClick = () =>
                {
                    onChangeView?.Invoke();

                    //TeamStadiumがスタックをもってしまっているので、消しておく。本来であればスタック自体が不要なはずだが、影響範囲を考えて個別対応
                    SceneManager.Instance.ClearBackStack();
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.Circle);
                };
            }

            private void SetupChampions(Action onChangeView = null)
            {
                Title = TextId.Champions0034.Text();
                IconSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.ReleaseChampions));

                OnClick = () =>
                {
                    onChangeView?.Invoke();

                    //TeamStadiumがスタックをもってしまっているので、消しておく。本来であればスタック自体が不要なはずだが、影響範囲を考えて個別対応
                    SceneManager.Instance.ClearBackStack();
                    // HomeHubView内に遷移する際はフッター用の遷移（Footer.Transition、ChangeViewをラップしている）を使った方が良い。
                    // ※ChangeViewでの遷移もコメントアウトして残しておく。
                    //SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub, new HomeViewInfo(HomeTopState.Race, (int)RaceHomeTopUI.DisplayType.Event));
                    UIManager.Footer.Transition(Footer.ButtonType.Race, (int)RaceHomeTopUI.DisplayType.Event);
                };
            }

            private void SetupRoomMatch(Action onChangeView = null)
            {
                Title = TextId.RoomMatch0001.Text();
                IconSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.RoomMatch));
                
                OnClick = () =>
                {
                    onChangeView?.Invoke();

                    //TeamStadiumがスタックをもってしまっているので、消しておく。本来であればスタック自体が不要なはずだが、影響範囲を考えて個別対応
                    SceneManager.Instance.ClearBackStack();
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.RoomMatchHub);
                };
            }

            private void SetupPracticeRace(Action onChangeView = null)
            {
                Title = TextId.PracticeRace400101.Text();
                IconSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.PracticeRace));

                OnClick = () =>
                {
                    onChangeView?.Invoke();

                    //TeamStadiumがスタックをもってしまっているので、消しておく。本来であればスタック自体が不要なはずだが、影響範囲を考えて個別対応
                    SceneManager.Instance.ClearBackStack();
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.PracticeRaceHub);
                };
            }
        }


        #endregion

        #region Enum

        /// <summary>
        /// コンテンツ解放種類
        /// </summary>
        public enum ReleaseContentType
        {
            None,
            DailyRace,        // デイリーレース
            Circle,           // サークル
            Champions,        // チャンピオンズミーティング
            RoomMatch,        // ルームマッチ
            PracticeRace,     // 練習
        };

        #endregion

        #region SerializeField

        /// <summary> アイコン </summary>
        [SerializeField]
        private ImageCommon _iconImage = null;

        /// <summary> コンテンツ名 </summary>
        [SerializeField]
        private TextCommon _contentNameTitle = null;

        /// <summary> ボタン </summary>
        [SerializeField]
        private ButtonCommon _button = null;

        #endregion

        /// <summary>
        /// パラメータ更新
        /// </summary>
        /// <param name="param"></param>
        public void UpdateItem(SetupParameter param)
        {
            // テキスト設定
            _contentNameTitle.text = param.Title;

            // アイコン
            _iconImage.sprite = param.IconSprite;

            _button.SetOnClick(() => param.OnClick?.Invoke());
        }
    }
}