using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// チーム競技場スコア詳細ダイアログのキャラ毎のスコア表示。
    /// </summary>
    /// <remarks>
    /// https://xxxxxxxxxx/app/#/projects/5e44a4f586e4655b009a56d3/screens/5e44ac7a86e4653dd99a548c
    /// </remarks>
    //-------------------------------------------------------------------
    public class PartsTeamStadiumCharaScoreDetail : MonoBehaviour
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        [Header("キャラ名")] 
        /// <summary>キャラ名。</summary>
        [SerializeField] 
        private TextCommon _charaNameText = null;

        [Header("キャラ情報")] 
        /// <summary>キャラ情報のルート。</summary>
        [SerializeField] 
        private RectTransform _charaInfoRoot = null;
        /// <summary>キャラアイコン。</summary>
        [SerializeField] 
        private CharacterButton _charaButton = null;
        /// <summary>着順。</summary>
        [SerializeField] 
        private ImageCommon _finishOrderImage = null;
        /// <summary>合計スコア。</summary>
        [SerializeField] 
        private TextCommon _totalScoreText = null;

        /// <summary>スコアのプレートをぶら下げるところ。</summary>
        [Header("スコア詳細")] 
        [SerializeField] 
        private GameObject _scorePlateRoot = null;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。
        /// </summary>
        public void Setup(DialogTeamStadiumScoreDetail.InitDesc.HorseScore horseScore)
        {
            SetupCharaName(horseScore.CharaId);
            SetupCharaIcon(horseScore.TrainedCharaId, horseScore.CharaId, horseScore.DressId, horseScore.FinalRank, horseScore.TalentLevel);
            SetupFinishOrderImage(horseScore.FinishOrder);
            SetupTotalScoreText(horseScore.TotalScore);
            SetupScorePlate(horseScore.ScoreArray, _scorePlateRoot);
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(_charaInfoRoot);
            
            // 全ての要素の生成が終わったらレイアウトサイズ再計算。
            LayoutRebuilder.ForceRebuildLayoutImmediate(this.transform as RectTransform);
        }

        /// <summary>
        /// キャラ名初期化。
        /// </summary>
        private void SetupCharaName(int charaId)
        {
            var masterChara = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (masterChara == null)
            {
                return;
            }
            _charaNameText.text = masterChara.Name;
        }
        
        /// <summary>
        /// キャラアイコン初期化。
        /// </summary>
        private void SetupCharaIcon(int trainedCharaId, int charaId, int dressId, GameDefine.FinalTrainingRank trainingRank, int talentLevel)
        {
            var buttonInfo = new CharacterButtonInfo
            {
                Id = charaId,
                IdType = CharacterButtonInfo.IdTypeEnum.Chara,
                DressId = dressId,
                IconSizeType = IconBase.SizeType.CharaSmall,
                // ボタン機能必要。
                EnableButton = true,
                Interactable = true,
                // 育成ランク必要。
                FinalTrainingRank = trainingRank,
                RankImage = true,
                // キャラアイコン長押しで育成キャラ詳細開く。
                OnLongTap = (_) =>
                {
                     OnCharaIconLongTap(trainedCharaId);
                },
                Level = talentLevel,
            };
            _charaButton.Setup(buttonInfo);
        }

        private void OnCharaIconLongTap(int trainedCharaId)
        {
            if (!WorkDataManager.HasInstance())
            {
                return;
            }

            var trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(trainedCharaId);
            if (trainedChara == null)
            {
                return;
            }

            var setupParam = DialogTrainedCharacterDetail.CreateSetupParameter(trainedChara);

            // 二つ名変更ボタンを使用不可にする
            setupParam.GetPartsParameter<PartsTrainedCharacterDetailHeaderTitle.SetupParameter>((param) => {
                param.Own.NickNameChangeType = PartsTrainedCharacterDetailHeaderTitle.SetupParameter.ForMe.NickNameChange.Disable;
                param.Own.NickNameChangeDisableText = TextId.Race0670.Text();
            });

            DialogTrainedCharacterDetail.Open(setupParam);
        }

        /// <summary>
        /// 着順初期化。
        /// </summary>
        /// <param name="finishOrder">着順。0~</param>
        private void SetupFinishOrderImage(int finishOrder)
        {
            _finishOrderImage.sprite = RaceUtil.GetRankSprite(finishOrder);
        }

        /// <summary>
        /// 獲得スコア合計初期化。
        /// </summary>
        private void SetupTotalScoreText(int totalScore)
        {
            _totalScoreText.text = TextId.TeamStadium0020.Format(totalScore);
        }

        /// <summary>
        /// スコアの種類ごとのプレート初期化。
        /// </summary>
        private void SetupScorePlate(TeamStadiumResultScoreData[] scoreArray, GameObject plateParent)
        {
            var platePrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.TEAM_STADIUM_SCORE_DETAIL_PLATE_PATH);
            for (int i = 0; i < scoreArray.Length; ++i)
            {
                // プレートインスタンス生成。
                var plateObj = GameObject.Instantiate(platePrefab, plateParent.transform);
                var plateComponent = plateObj.GetComponent<PartsTeamStadiumScoreDetailPlate>();
                
                // プレートに表示する情報を設定。
                
                plateComponent.Setup(scoreArray[i]);
            }
            
            LayoutRebuilder.ForceRebuildLayoutImmediate(plateParent.GetComponent<RectTransform>());
        }
    }
}

