using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// ライブシアター：楽曲選択
    /// </summary>
    public class LiveTheaterMusicSelect : LiveTheaterUIBase
    {
        private const float CENTER_OBJ_SCALE = 1.2f;
        private const float MOVIE_ASPECT = 16f / 9f;    //ムービーアスペクト比
        private const float THUM_ASPECT = 2f / 1f;  //サムネ、横長
        private const float MOVIE_HIGHT_RATE = 0.5f;    //画面の縦の何割を占めるか
        private const float BOTTOM_BG_HIGHT_RATE = 0.6f;    //画面下部画像の縦割合
        private const float FADE_DURATION_SEC = 0.3f;

        public RectTransform MovieTarget => _movieTarget;
        public RawImageCommon ThumbImage => _thumbImage;
        public ButtonCommon DownloadButton => _downloadButton;
        public GameObject MusicListLockObj => _musicListLockObj;

        [SerializeField] private ButtonCommon _downloadButton = null;
        [SerializeField] private ButtonCommon _musicListButton = null;
        [SerializeField] private GameObject _musicListLockObj = null;
        [SerializeField] private RawImageCommon _thumbImage = null;
        [SerializeField] private RectTransform _movieRoot = null;
        [SerializeField] private RectTransform _movieTarget = null;
        [SerializeField] private RawImageCommon _bottomBg = null;
        [SerializeField] private RawImageCommon _bottomBgSub = null;
        [SerializeField] private LiveTheaterJacketList _jacketList= null;
        [SerializeField] private ButtonCommon _buttonLeft = null;
        [SerializeField] private ButtonCommon _buttonRight = null;
        [SerializeField] private LiveTheaterJacketListItem _single = null;

        private int _currentInfoIndex = 0;

        //前に見てた背景のパス
        private string _currentBgPath = null;
        private Tween _bgFadeTween = null;

        public override TextId GetNextButtonText()
        {
            return TextId.LiveTheater0001;
        }

        public override void Initialize(LiveTheaterViewController view)
        {
            base.Initialize(view);

            _currentInfoIndex = _view.DefaultIndex;

            if (view.InfoList.Count == 1)
            {
                //1曲しかない
                _single.UpdateItemForce(_view.CurrentInfo);
                _jacketList.SetActiveWithCheck(false);
                _buttonLeft.SetActiveWithCheck(false);
                _buttonRight.SetActiveWithCheck(false);
            }
            else
            {
                //２曲以上だとスクロール
                _jacketList.Initialize(view.InfoList, null, OnFlick);
                _jacketList.SetOnCenter(OnSelectJacket);    //中心に来たら切り替わり
                _jacketList.ScrollImmediately(_currentInfoIndex);  //中心要素の確定
                _jacketList.RefreshItems(); //各子要素のジャケット取得
                _single.SetActiveWithCheck(false);
                _buttonLeft.SetOnClick(_jacketList.ScrollToPrev);
                _buttonRight.SetOnClick(_jacketList.ScrollToNext);
            }
            
            //楽曲一覧
            _musicListButton.SetOnClick(view.OpenMusicList);

            //背景、ムービーの解像度合わせ
            EtoE();

            //GameViewSize変更
#if UNITY_EDITOR
            GameViewSizeChangeListner.TryAddComponent(gameObject, this, EtoE);
#endif
        }

        //背景などのEtoE処理
        public void EtoE()
        {
            //ムービーのEtoE
            var canvasSize = (_view.GetViewBase().transform as RectTransform).rect.size;
            var movieSize = _movieRoot.sizeDelta;
            movieSize.y = MOVIE_HIGHT_RATE * canvasSize.y;
            movieSize.x = movieSize.y * MOVIE_ASPECT;
            _movieRoot.sizeDelta = movieSize;
            _movieTarget.sizeDelta = movieSize;
            //サムネ
            var thumSize = _thumbImage.rectTransform.sizeDelta;
            thumSize.y = MOVIE_HIGHT_RATE * canvasSize.y;
            thumSize.x = thumSize.y * THUM_ASPECT;
            _thumbImage.rectTransform.sizeDelta = thumSize;
            //画面下部画像のEtoE
            var bottomSize = _bottomBg.rectTransform.sizeDelta;
            bottomSize.y = BOTTOM_BG_HIGHT_RATE * canvasSize.y;
            bottomSize.x = bottomSize.y * 2;    //１：２
            _bottomBg.rectTransform.sizeDelta = bottomSize;
            _bottomBgSub.rectTransform.sizeDelta = bottomSize;
        }

        public override void Final()
        {
            base.Final();
            _jacketList.Release();
            if (_bgFadeTween != null)
            {
                _bgFadeTween.Kill();
                _bgFadeTween = null;
            }
        }

        public override void OnUpdate()
        {
            //ムービーの再生サイズが合わなくなることがあったため、強制的に合わせる
            if(_movieTarget.childCount > 0)
            {
                var moviePlayer = _movieTarget.transform.GetChild(0) as RectTransform;
                moviePlayer.sizeDelta = _movieTarget.sizeDelta;
            }
            base.OnUpdate();
        }

        public override void Show()
        {
            //ムービーの表示
            _movieRoot.gameObject.SetActive(true);
            //初期背景の設定
            SetBg(_view.CurrentInfo, false);
            base.Show();
        }

        public override void Hide()
        {
            //ムービー非表示
            _movieRoot.gameObject.SetActive(false);
            base.Hide();
        }
        
        /// <summary>
        /// 直接楽曲を選択する
        /// </summary>
        public void SetSelectJacket(int index, LiveTheaterInfo info)
        {
            _currentInfoIndex = index;
            _jacketList.ScrollImmediately(index);
        }

        /// <summary>
        /// ジャケットが中心に来た時のデータ設定
        /// </summary>
        /// <param name="index"></param>
        /// <param name="info"></param>
        public void OnSelectJacket(int index, LiveTheaterInfo info)
        {
            if (_view.CurrentInfo == info)
                return;

            _view.SelectInfo(info);
            SetBg(info, false);
        }

        /// <summary>
        /// フリック時のコールバック
        /// </summary>
        /// <param name="status"></param>
        private void OnFlick(FlickHandler.FlickStatus status)
        {
            if (status.IsLeft) _jacketList.ScrollToNext();
            if (status.IsRight) _jacketList.ScrollToPrev();
        }

        //背景画像の設定
        private void SetBg(LiveTheaterInfo info, bool bgFade = true)
        {
            if (_bgFadeTween != null)
            {
                _bgFadeTween.Complete();
                _bgFadeTween = null;
            }

            var prevBgPath = _currentBgPath;
            _currentBgPath = info.MusicBgPath;
            if (prevBgPath == _currentBgPath)
                return; //同じなら変える必要ない

            if (bgFade)
            {
                _bottomBgSub.texture = _bottomBg.texture;
                _bottomBg.texture = ResourceManager.LoadOnView<Texture>(_currentBgPath);
                _bottomBg.SetAlpha(0);
                _bgFadeTween = _bottomBg.DOFade(1, FADE_DURATION_SEC).OnComplete(() => {
                    ResourceManager.UnloadFromView(prevBgPath);
                    _bgFadeTween = null;
                });
                _bottomBgSub.SetAlpha(1);
                _bottomBgSub.DOFade(0, FADE_DURATION_SEC);
            }
            else
            {
                _bottomBgSub.SetAlpha(0);
                _bottomBg.SetAlpha(1);
                ResourceManager.UnloadFromView(prevBgPath);
                _bottomBg.texture = ResourceManager.LoadOnView<Texture>(_currentBgPath);
            }
        }

        private void OnFlickJacket(FlickHandler.FlickStatus flickState)
        {
            if (flickState.IsRight)
                _jacketList.ScrollToPrev();
            if (flickState.IsLeft)
                _jacketList.ScrollToNext();
        }
    }
}
