using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using static Gallop.LiveTheaterInfo;

namespace Gallop
{
    /// <summary>
    /// ライブシアター：自動編成
    /// </summary>
    public class DialogLiveTheaterAutoSetting : DialogInnerBase
    {
        private const int GROUP_LENGTH = 4;
        private const int GROUP_BACK_CHARA = 2;
        private const int GROUP_BACK_DRESS = 3;

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.LiveTheater0005.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.RightButtonText = TextId.Common0003.Text();
            return data;
        }

        [SerializeField] private ToggleGroupCommon[] _toggleGroupArray;

        private bool _isExistBack;
        private AutoSetSetting _settingData;

        public static void PushDialog(bool isExistBack, AutoSetSetting settingData, System.Action<AutoSetSetting> onSelect)
        {
            var resource = ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_LIVE_THEATER_AUTO_SETTING);
            var ins = Instantiate(resource, UIManager.MainCanvas.transform).GetComponent<DialogLiveTheaterAutoSetting>();
            ins._settingData = settingData;
            ins._isExistBack = isExistBack;
            ins.Initialize();

            var data = ins.CreateDialogData();
            data.RightButtonCallBack = d =>
            {
                ins.Apply();
                onSelect(ins._settingData);
            };

            DialogManager.PushDialog(data);
        }

        //ToggleGroup配列のインデックス値から内部辞書のどれに対応するかを返す
        private System.Tuple<AutoSetPosition, AutoSetTarget> IndexToPosAndTarget(int index)
        {
            AutoSetPosition pos = AutoSetPosition.Main;
            AutoSetTarget target = AutoSetTarget.Chara;
            switch (index)
            {
                case 0: pos = AutoSetPosition.Main; target = AutoSetTarget.Chara; break;
                case 1: pos = AutoSetPosition.Main; target = AutoSetTarget.Dress; break;
                case 2: pos = AutoSetPosition.Back; target = AutoSetTarget.Chara; break;
                case 3: pos = AutoSetPosition.Back; target = AutoSetTarget.Dress; break;
                default: Debug.LogWarning("未対応 index = " + index);break;
            }

            return new System.Tuple<AutoSetPosition, AutoSetTarget>(pos, target);
        }

        //トグル配列のインデックス値から配置タイプのどれに対応するかを返す
        private AutoSetType IndexToAutoSetType(int index)
        {
            return (AutoSetType)index;
        }

        //表示初期化
        private void Initialize()
        {
            if(_toggleGroupArray.Length != GROUP_LENGTH)
            {
                Debug.LogError("長さおかしい length = " + _toggleGroupArray.Length);
                return;
            }
            for(int i= 0; i < GROUP_LENGTH; i++)
            {
                var group = _toggleGroupArray[i];
                var set = IndexToPosAndTarget(i);
                for(int j = 0; j < group.ToggleArray.Length; j++)
                {
                    var type = IndexToAutoSetType(j);
                    group.ToggleArray[j].isOn = _settingData.AutoSetDic[set.Item1][set.Item2] == type;
                }
            }

            //バックダンサーいないならトグル無効化
            if(_isExistBack == false)
            {
                var backCharaGroup = _toggleGroupArray[GROUP_BACK_CHARA];
                foreach(var t in backCharaGroup.ToggleArray)
                {
                    t.SetInteractable(false);
                    t.SetNotificationMessage(TextId.LiveTheater0028.Text());
                }
                var backDressGroup = _toggleGroupArray[GROUP_BACK_DRESS];
                foreach (var t in backDressGroup.ToggleArray)
                {
                    t.SetInteractable(false);
                    t.SetNotificationMessage(TextId.LiveTheater0028.Text());
                }
            }
        }

        //トグルを内部辞書に反映
        private void Apply()
        {
            if (_toggleGroupArray.Length != GROUP_LENGTH)
            {
                Debug.LogError("長さおかしい length = " + _toggleGroupArray.Length);
                return;
            }
            for (int i = 0; i < GROUP_LENGTH; i++)
            {
                var group = _toggleGroupArray[i];
                var set = IndexToPosAndTarget(i);
                var type = IndexToAutoSetType(group.GetOnIndex());
                _settingData.AutoSetDic[set.Item1][set.Item2] = type;
            }
        }

    }
}
