using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ピンクの下地にステート表示するコンポーネント
    /// ** 下地付きで左右にパディングのあるテキスト
    /// ** AutoLayoutはいつ反映されるか予測付かないので自前でやる
    /// </summary>
    public class ChampionsNoticeText : MonoBehaviour
    {
        private const float PADDING = 36;   //左右に18pxのパディング

        public TextId CurrentText { get; private set; } = TextId.None;

        [SerializeField] private TextCommon _text;

        private RectTransform _rectTransform;
        private float _width = 0;

        public void Set(long initTime, SceneDefine.ViewId viewId)
        {
            var work = WorkDataManager.Instance.ChampionsData;
            var detail = work.GetCurrentRoundDetailTimeBase(initTime);
            var schedule = work.GetCurrentRoundScheduleData(initTime);
            var state = work.GetState();
            var noEntry = work.NoEntry();
            if (detail == null ||
               schedule == null)
                return;

            var text = TextId.None;
            switch(detail.GetRound())
            {
                //決勝
                case ChampionsDefines.Round.Final:
                    switch(schedule.GetFinalState())
                    {
                        //エントリー
                        case ChampionsDefines.FinalState.Entry:
                            //エントリー済み
                            if(!noEntry)
                            {
                                text = TextId.Champions0108;
                            }
                            break;
                        //マッチング中
                        case ChampionsDefines.FinalState.Matching:
                            if (!noEntry)
                            {
                                text = TextId.Champions0107;
                            }
                            break;
                        //レース中
                        case ChampionsDefines.FinalState.Race:
                            switch (state)
                            {
                                case ChampionsDefines.RoundState.League:
                                case ChampionsDefines.RoundState.Entry: //来ることないけど一応
                                case ChampionsDefines.RoundState.Lobby:
                                case ChampionsDefines.RoundState.RaceStart:
                                    if (detail.GetTier() != ChampionsDefines.Tier.None)
                                    {
                                        //決勝進出者はTierがある
                                        text = TextId.Race0657;
                                    }
                                    break;
                            }
                            break;
                    }
                    break;
                //予選
                default:
                    switch(state)
                    {
                        case ChampionsDefines.RoundState.None:
                        case ChampionsDefines.RoundState.League:
                            //NOP
                            break;
                        case ChampionsDefines.RoundState.RaceStart:
                            text = TextId.Race0657;
                            break;
                        default:
                            text = TextId.Champions0108;
                            break;
                    }
                    break;
            }

            _text.TextId = text;
            CurrentText = text;
            gameObject.SetActive(text != TextId.None);
        }

        private void Update()
        {
            _text.UpdatePreferedWidth();
            var currentWidth = _text.rectTransform.rect.width;
            if (_width != currentWidth)
            {
                if (_rectTransform == null)
                    _rectTransform = transform as RectTransform;

                var sizeDelta = _rectTransform.sizeDelta;
                _rectTransform.sizeDelta = new Vector2(currentWidth + PADDING, sizeDelta.y);
            }

            _width = currentWidth;
        }
    }
}