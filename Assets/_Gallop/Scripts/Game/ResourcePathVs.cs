using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    using CutIn.Cutt;

    /// <summary>
    /// Vsカット関連のパス
    /// </summary>
    public static partial class ResourcePath
    {
        /// <summary>
        /// ルールに基づいた名前の置き換えをしたモーションパスの取得
        /// </summary>
        /// <param name="motionKey"></param>
        /// <param name="motionType"></param>
        /// <param name="personalityId"></param>
        /// <param name="replaceIndex"></param>
        /// <param name="bodyClipIndex"></param>
        /// <returns></returns>
        public static string GetVsCharaMotionPath(TimelineKeyCharacterMotionData motionKey, CutInHelper.MotionType motionType, int charaId, int personalityId, int replaceIndex, int bodyClipIndex = 0)
        {
            // 変更するモーションの元の名前を取得
            var originalClipName = GetCharaMotionName(motionKey, motionType, replaceIndex, bodyClipIndex);

            // 置き換えて返す
            return GetVsReplacedMotionPath(originalClipName, motionType, charaId, personalityId);
        }

        /// <summary>
        /// 元のモーション名からルールに基づいて置き換える
        /// </summary>
        /// <param name="clipName"></param>
        /// <param name="motionType"></param>
        /// <param name="personalityId"></param>
        /// <returns></returns>
        private static string GetVsReplacedMotionPath(string clipName, CutInHelper.MotionType motionType, int charaId, int personalityId)
        {
            // 置き換え後のパスを取得
            var replacedClipPath = ReplaceVsMotionPath(motionType, clipName, charaId, personalityId);

            // ファイル見つからなかった場合、代替モーションを探す
            //問題なのは、CutInHelper.COMMON_PERSONALITY_IDは性格値で、キャラ別にモーション分岐している体には使えないので意味がない。
            //もしかしたらChara00みたいな共通モーションが用意されるかもしれないがわからない
            return ResourceManager.IsExistAsset(replacedClipPath)
                ? replacedClipPath
                : ReplaceVsMotionPath(motionType, clipName, charaId, CutInHelper.COMMON_PERSONALITY_ID);
        }

        /// <summary>
        /// IDを差し替えた形でパスを返す
        /// </summary>
        /// <param name="motionType"></param>
        /// <param name="motionName"></param>
        /// <param name="personalityId"></param>
        /// <returns></returns>
        private static string ReplaceVsMotionPath(CutInHelper.MotionType motionType, string motionName, int charaId, int personalityId)
        {
            switch (motionType)
            {
                case CutInHelper.MotionType.Body:
                    {
                        // Bodyはキャラ別
                        var clipName = System.Text.RegularExpressions.Regex.Replace(motionName, "_chr[0-9]{4}", TextUtil.Format("_chr{0:D4}", charaId));
                        var format = GetVsMotionPathFormat(motionType);
                        return TextUtil.Format(format, charaId, clipName);
                    }

                case CutInHelper.MotionType.Facial:
                case CutInHelper.MotionType.Ear:
                    {
                        // フェイシャル系は性格値（CSVから持ってきたもの）
                        var clipName = System.Text.RegularExpressions.Regex.Replace(motionName, "_type[0-9]{2}", TextUtil.Format("_type{0:D2}", personalityId));
                        var format = GetVsMotionPathFormat(motionType);
                        return TextUtil.Format(format, personalityId, clipName);
                    }
                case CutInHelper.MotionType.Position:
                    // Position系は置き換えない
                    return string.Empty;

                default:
                    throw new ArgumentOutOfRangeException(nameof(motionType), motionType, null);
            }
        }

        /// <summary>
        /// モーションファイルのルートのフォーマットをモーションの種類ごとに返す
        /// </summary>
        /// <param name="motionType"></param>
        /// <returns></returns>
        private static string GetVsMotionPathFormat(CutInHelper.MotionType motionType)
        {
            switch (motionType)
            {
                case CutInHelper.MotionType.Body:
                    return EVENT_MOTION_BODY_PATH_FORMAT;
                case CutInHelper.MotionType.Facial:
                case CutInHelper.MotionType.Ear:
                    return VS_MOTION_FACIAL_FORMAT;
                default:
                    throw new ArgumentOutOfRangeException(nameof(motionType), motionType, null);
            }
        }
    }
}
