using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// カルーセルスクロール(タブ付き)
    /// </summary>
    public class CarouselScroll<TItemData> : NormalizedLoopScroll<CarouselScrollItemBase<TItemData>, TItemData>
    {
        #region SerializeField

        /// <summary>
        /// タブの間隔
        /// </summary>
        [SerializeField]
        protected float _tabSpacing = 0.0f;

        /// <summary>
        /// タブ選択時のスクロールの時間
        /// </summary>
        [SerializeField]
        protected float _tabScrollDuration = 0.35f;

        #endregion

        #region Member

        /// <summary>
        /// 最も近い要素のポジションに対する閾値
        /// </summary>
        private float _nearThreshold;

        /// <summary>
        /// オフセット位置の要素の隣の要素のローカル中心位置
        /// </summary>
        private float _nextItemLocalPosition;

        /// <summary>
        /// 位置調整で参照するスクロール位置
        /// </summary>
        private float _referencePosition;

        /// <summary>
        /// 位置調整で参照するローカル位置
        /// </summary>
        private float _referenceLocalPosition;

        /// <summary>
        /// 位置調整で参照するスクロール位置
        /// </summary>
        private float _referenceOffset;

        /// <summary>
        /// インデックス更新時のコールバック
        /// </summary>
        private Action<int, TItemData> _onUpdateIndex;

        #endregion

        #region Method

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="cellDataList"></param>
        /// <param name="onUpdateIndex"></param>
        /// <param name="onFlick"></param>
        public void Initialize(IEnumerable<TItemData> cellDataList,
            Action<int, TItemData> onUpdateIndex,
            Action<FlickHandler.FlickStatus> onFlick = null,
            bool isForceUpdateAllItems = false)
        {
            _onUpdateIndex = onUpdateIndex;
            CalculateNearThreshold();
            InitializeScroll(cellDataList, OnUpdateIndex, onFlick, isForceUpdateAllItems);
            SetupTabButtonCallback();
            CalculateNextTabLocalPosition();
        }

        /// <summary>
        /// 指定インデックスまでスクロール
        /// </summary>
        /// <param name="index"></param>
        public void ScrollTo(int index)
        {
            ScrollTo(index, _tabScrollDuration);
        }

        /// <summary>
        /// 即座に指定インデックスまでスクロールする
        /// </summary>
        /// <param name="index"></param>
        public void ScrollImmediately(int index)
        {
            ScrollTo(index, 0.0f);
            RefreshPosition();
        }

        /// <summary>
        /// 次の要素にスクロール
        /// </summary>
        public virtual void ScrollToNext()
        {
            ScrollToNext(_tabScrollDuration);
        }

        /// <summary>
        /// 前の要素にスクロール
        /// </summary>
        public virtual void ScrollToPrev()
        {
            ScrollToPrev(_tabScrollDuration);
        }

        /// <summary>
        /// スクロールオブジェクトがスワイプなどで動かされていないか
        /// </summary>
        /// <returns></returns>
        public bool IsScrollContentsAlined()
        {
            return Mathf.Approximately(_referenceLocalPosition,0);
        }

        /// <summary>
        /// タブボタンのコールバックを設定
        /// </summary>
        protected virtual void SetupTabButtonCallback()
        {
            foreach (var item in ObjectPool)
            {
                if (item.TabButton == null)
                {
                    continue;
                }
                item.TabButton.SetOnClick(() => _scroller.ScrollToActualIndex(item.ActualIndex, _tabScrollDuration));
            }
        }

        /// <summary>
        /// 閾値の計算
        /// </summary>
        private void CalculateNearThreshold()
        {
            _nearThreshold = _interval - _interval * 0.1f;
        }

        /// <summary>
        /// 要素の拡縮サイズと間隔を考慮した際の隣のタブのローカル位置
        /// </summary>
        private void CalculateNextTabLocalPosition()
        {
            var item = ObjectPool[0];
            var scaleOnOffset = item.TabScaleTween.Evaluate(_offset);
            var tabSizeOnOffset = item.TabOriginSize.x * scaleOnOffset * 0.5f;

            var scaleOnNext = item.TabScaleTween.Evaluate(_offset + _interval);
            var tabSizeOnNext = item.TabOriginSize.x * scaleOnNext * 0.5f;

            _nextItemLocalPosition = tabSizeOnOffset + _tabSpacing + tabSizeOnNext;
        }

        /// <summary>
        /// 位置の更新
        /// </summary>
        /// <param name="position"></param>
        protected override void UpdatePosition(float position)
        {
            base.UpdatePosition(position);
            RePosition();
        }

        /// <summary>
        /// 要素の拡縮サイズと間隔を考慮した位置の再計算
        /// </summary>
        private void RePosition()
        {
            UpdateReferences();

            var upperItem = ObjectPool.Where(item => item.Position > _referencePosition)
                .OrderBy(item => item.Position);
            UpdateTabLocalPosition(upperItem, _referenceOffset);

            var lowerItem = ObjectPool.Where(item => item.Position < _referencePosition)
                .OrderByDescending(item => item.Position);
            UpdateTabLocalPosition(lowerItem, _referenceOffset, false);
        }

        /// <summary>
        /// 位置再計算時の参照基準を更新
        /// </summary>
        private void UpdateReferences()
        {
            var nearestItems = FindNearestItems();
            if (nearestItems.Length > 1)
            {
                var diff = Mathf.Abs(nearestItems[1].Position - _offset);
                var ratio = diff / _interval;
                var x = Mathf.Lerp(0.0f, _nextItemLocalPosition, ratio);

                nearestItems[1].Tab.localPosition = new Vector3(
                    x,
                    nearestItems[1].Tab.localPosition.y,
                    nearestItems[1].Tab.localPosition.z
                );

                _referenceLocalPosition = nearestItems[1].Tab.localPosition.x;
                _referencePosition = nearestItems[1].Position;
                _referenceOffset = nearestItems[1].Tab.rect.width * 0.5f;
            }
            else
            {
                var nearestItemTab = nearestItems[0].Tab;
                _referencePosition = nearestItems[0].Position;
                _referenceLocalPosition = nearestItemTab.localPosition.x;
                _referenceOffset = nearestItemTab.rect.width * 0.5f;
            }
        }

        /// <summary>
        /// 近傍の要素を取得
        /// </summary>
        /// <returns></returns>
        private CarouselScrollItemBase<TItemData>[] FindNearestItems()
        {
            var nearestOffset = ObjectPool.Min(item => Mathf.Abs(item.Position - _offset));
            return ObjectPool
                .Where(item => Mathf.Abs(item.Position - _offset) - nearestOffset < _nearThreshold)
                .OrderBy(item => item.Position)
                .ToArray();
        }

        /// <summary>
        /// 要素の拡縮サイズと間隔を考慮した際のタブのローカル位置を更新
        /// </summary>
        /// <param name="items"></param>
        /// <param name="referenceOffset"></param>
        /// <param name="isUpper"></param>
        private void UpdateTabLocalPosition(IOrderedEnumerable<CarouselScrollItemBase<TItemData>> items,
            float referenceOffset, bool isUpper = true)
        {
            foreach (var item in items)
            {
                var tab = item.Tab;
                var halfWidth = tab.rect.width * 0.5f;
                referenceOffset += _tabSpacing + halfWidth;
                var localPosX = isUpper
                    ? _referenceLocalPosition + referenceOffset
                    : _referenceLocalPosition - referenceOffset;
                var localPos = tab.localPosition;
                tab.localPosition = new Vector3(localPosX, localPos.y, localPos.z);
                referenceOffset += halfWidth;
            }
        }

        /// <summary>
        /// インデックス更新時のコールバック
        /// </summary>
        /// <param name="index"></param>
        private void OnUpdateIndex(int index)
        {
            _onUpdateIndex?.Invoke(index, Items[index]);
        }

        public override void Release()
        {
            base.Release();
            _onUpdateIndex = null;
        }
        #endregion
    }
}
