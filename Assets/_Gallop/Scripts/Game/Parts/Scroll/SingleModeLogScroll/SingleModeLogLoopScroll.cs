using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成ログスクロールビュー
    /// </summary>
    public class SingleModeLogLoopScroll : LoopScroll
    {
        //Content最大サイズを保持、要素サイズ不定の場合に利用
        public bool KeepMaxContentSize { get; set; } = false;

        //要素サイズ不定の場合、要素間隔を更新する場合に利用
        public bool UseUpdateElementSpacing { get; set; } = false;

        private Dictionary<int, float> _ignoreElementIndexAndSizeDict = new Dictionary<int, float>();

        //動的に変動する要素サイズに合わせてスクロールContentのサイズを拡大する
        public void UpdateContentSizeByElementSize((Vector2 elementSize, int index) updateData)
        {
            var updateElementHeight = updateData.elementSize.y;

            var itemHeight = ItemSize - Spacing;

            var diff = updateElementHeight - itemHeight;

            if (diff > 0f && !_ignoreElementIndexAndSizeDict.ContainsKey(updateData.index))
            {
                var scrollRectSize = RectTransform.sizeDelta;
                scrollRectSize.y += diff;
                RectTransform.sizeDelta = scrollRectSize;
                _ignoreElementIndexAndSizeDict.Add(updateData.index, updateElementHeight);
            }
        }

        //要素ループによりサイズ不定の要素同士の間の間隔が正常になっていない問題を対応
        public override void UpdateElementSpacing()
        {
            if (ScrollDirection != Direction.Vertical)
            {
                Debug.LogWarning("この関数は現状縦スクロールだけに対応");
                return;
            }

            if (!UseUpdateElementSpacing)
            {
                return;
            }

            var itemList = ItemList.Where(x => x.isActiveAndEnabled).OrderBy(x => x.ItemIndex);

            var count = itemList.Count();

            for (int i = 1; i < count; i++)
            {
                var prevElement = itemList.ElementAt(i - 1);
                var prevElementPos = prevElement.CacheRectTransform.anchoredPosition;
                var prevElementSize = prevElement.CacheRectTransform.sizeDelta;

                var currentElement = itemList.ElementAt(i);
                var currentElementPos = currentElement.CacheRectTransform.anchoredPosition;

                var newPos = prevElementPos.y - prevElementSize.y - Spacing;

                currentElementPos.y = newPos;

                currentElement.CacheRectTransform.anchoredPosition = currentElementPos;
            }

            var newRectSize = RectTransform.sizeDelta;

            newRectSize.y = 0f;

            foreach (var item in itemList)
            {
                newRectSize.y += item.CacheRectTransform.sizeDelta.y;
            }

            if (newRectSize.y > 0f)
            {
                newRectSize.y += Spacing * count + Margin.Top;

                if (RectTransform.sizeDelta.y < newRectSize.y)
                {
                    RectTransform.sizeDelta = newRectSize;
                }
            }
        }

        /// <summary>
        /// 幅を調整する
        /// </summary>
        protected override void SetRectSize()
        {
            var delta = RectTransform.sizeDelta;
            delta.y = ItemSize * (MaxSize) + Margin.Top + Margin.Bottom;

            if (KeepMaxContentSize)
            {
                if (RectTransform.sizeDelta.y < delta.y)
                    RectTransform.sizeDelta = delta;
            }
        }
    }
}
