using System.Linq;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using UnityEngine.Serialization;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// 出走表のキャラ情報プレート１枚をフェードイン＆Y移動させる出現演出。
    /// </summary>
    //-------------------------------------------------------------------
    public class FadeScalePanel : MonoBehaviour
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        // メイン描画物gameObjectの矩形操作＆activate対象。※指定必須。
        [SerializeField]
        private RectTransform _mainContents = null;
        
        // メイン描画物gameObjectのactivate対象。複数activeにする必要がある場合用なのでnullでも可。
        [SerializeField]
        private GameObject[] _activateMainContentsArray = null;

        /// <summary>メイン描画物をフェードさせるためのCanvasGroup。</summary>
        [SerializeField]
        private CanvasGroup _mainContentsCanvasGroup = null;

        /// <summary>出現時に下から移動する移動量。</summary>
        [SerializeField]
        private float _mainContentsEnterOffsetY = -15;
        /// <summary>退場時に下に移動する移動量。</summary>
        [SerializeField]
        private float _mainContentsExitOffsetY = -15;

        /// <summary>出現時のアニメーション時間。</summary>
        [SerializeField]
        private float _animEnterTime = 0.27f;
        /// <summary>退場時のアニメーション時間。</summary>
        [SerializeField]
        private float _animExitTime = 0.27f;
        
        private float _startY;
        private float _finalY;

        private Sequence _seq = null;

        private List<string> _disableMainContentsList = new List<string>();

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        /// <summary>
        /// 出現アニメーション再生。
        /// </summary>
        public void PlayEnter()
        {
            _finalY = _mainContents.anchoredPosition.y;
            _startY = _finalY + _mainContentsEnterOffsetY;

            // 本来の表示位置より少し下に置いておく。
            SetActiveMainContents(true);
            _mainContents.anchoredPosition = new Vector2(_mainContents.anchoredPosition.x, _startY);

            _seq = DOTween.Sequence();
            
            // フェードイン。
            _mainContentsCanvasGroup.alpha = 0;
            _seq.Join(
                _mainContentsCanvasGroup.DOFade(
                    1,
                    _animEnterTime));
            
            // 本来のY位置に移動させる。
            _seq.Join(
                _mainContents
                    .DOAnchorPosY(_finalY, _animEnterTime)
                    .SetEase(Ease.OutCirc));

            _seq.Play();
        }
        
        /// <summary>
        /// 退場アニメーション再生。
        /// </summary>
        public void PlayExit()
        {
            if ((_mainContents == null) ||  (_mainContentsCanvasGroup == null))
            {
                return;
            }

            _finalY = _mainContents.anchoredPosition.y + _mainContentsExitOffsetY;
            _startY = _mainContents.anchoredPosition.y;

            _seq = DOTween.Sequence();
            
            // フェードアウト。
            _seq.Join(
                _mainContentsCanvasGroup.DOFade(
                    0,
                    _animExitTime));
            
            // 本来のY位置に移動させる。
            _seq.Join(
                _mainContents
                    .DOAnchorPosY(_finalY, _animExitTime)
                    .SetEase(Ease.OutCirc));

            _seq.Play();
        }

        /// <summary>
        /// スキップ処理
        /// </summary>
        public void Skip()
        {
            if(_seq != null)
            {
                _seq.Complete(true);
            }
        }

        /// <summary>
        /// メイン描画物のactive切替。
        /// </summary>
        private void SetActiveMainContents(bool isActive)
        {
            _mainContents.gameObject.SetActive(isActive);

            if (_activateMainContentsArray != null)
            {
                foreach (var obj in _activateMainContentsArray)
                {
                    if (_disableMainContentsList.Contains(obj.name))
                    {
                        obj.gameObject.SetActive(false);
                    }
                    else
                    {
                        obj.gameObject.SetActive(isActive);
                    }
                }
            }
        }

        /// <summary>
        /// 場合によってコンテンツを省きたい場合、Activeにならないようにする
        /// </summary>
        public void SetDisableMainContents( string objName )
        {
            _disableMainContentsList.Add(objName);
        }


        private void OnDestroy()
        {
            if (_seq != null && _seq.IsPlaying())
            {
                _seq.Kill();
                _seq = null;
            }
        }
    }
}
