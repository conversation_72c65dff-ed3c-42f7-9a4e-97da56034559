using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// レース：レースグループグレード詳細
    /// </summary>
    public class DialogRaceGroupGradeDetail : DialogInnerBase
    {

        #region override

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.CustomRace0068.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            return data;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }

        #endregion

        [SerializeField]
        private RawImageCommon _courseImage;
        [SerializeField]
        private RawImageCommon _titleImage;
        [SerializeField]
        private RawImageCommon _titleFrameImage;
        [SerializeField]
        private TextCommon _courseText;
        [SerializeField]
        private PartsEnvironmentConditions _environment;
        [SerializeField]
        private TextCommon _entryNumText;
        [SerializeField] 
        private ItemIcon _iconBase;
        [SerializeField]
        private RectTransform _mainRewardRoot;
        [SerializeField] 
        private RectTransform _firstRewardRoot;
        [SerializeField]
        private ButtonCommon _buttonItemDetail;
        [SerializeField]
        private ImageCommon _clearIconBase;
        [SerializeField]
        private PartsTrophyButton _trophyButton;

        private RaceGroupGradeInfo _gradeInfo;

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="gradeInfo"></param>
        public static void PushDialog(RaceGroupGradeInfo gradeInfo)
        {
            var instance = LoadAndInstantiatePrefab<DialogRaceGroupGradeDetail>(ResourcePath.DIALOG_RACE_GROUP_GRADE_DETAIL);
            var data = instance.CreateDialogData();
            data.ContentsObject = instance.gameObject;
            var dialog = DialogManager.PushDialog(data);
            instance.Setup(dialog, gradeInfo);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="dialog"></param>
        /// <param name="gradeInfo"></param>
        protected void Setup(DialogCommon dialog, RaceGroupGradeInfo gradeInfo)
        {
            _gradeInfo = gradeInfo;
            _courseImage.texture = _gradeInfo.GetCourseTex();

            //タイトル画像は難易度ごとに異なるので毎回読んで捨てる
            _titleImage.texture = ResourceManager.LoadOnHash<Texture>(_gradeInfo.RaceTitlePath, dialog.DialogData.DialogHash);
            _titleFrameImage.texture = ResourceManager.LoadOnHash<Texture>(_gradeInfo.RaceTitleFramePath, dialog.DialogData.DialogHash);

            _courseText.text = RaceUtil.GetCourseInfoTextLong(_gradeInfo.RaceInstance);
            if (gradeInfo.Season.HasValue && gradeInfo.Weather.HasValue && gradeInfo.GroundCondition.HasValue)
            {
                _environment.SetActiveWithCheck(true);
                _environment.Setup(
                    (RaceDefine.Weather) gradeInfo.Weather.Value,
                    (GameDefine.BgSeason) gradeInfo.Season.Value,
                    (RaceDefine.GroundCondition) gradeInfo.GroundCondition.Value
                );
            }
            else
            {
                _environment.SetActiveWithCheck(false);
            }
            var raceData = _gradeInfo.RaceInstance.GetRaceMaster();
            if (raceData != null)
            {
                _entryNumText.text = TextUtil.Format(TextId.Common0270.Text(), _gradeInfo.RaceInstance.GetRaceMaster().EntryNum);
            }
            
            CreateItems(_gradeInfo.FirstClearItemArray, _firstRewardRoot, gradeInfo.IsClear, true);
            CreateItems(_gradeInfo.PickupItemArray, _mainRewardRoot, false, false);
            _buttonItemDetail.SetOnClick(() => OpenRewardItemListDialog(_gradeInfo));

            if (_trophyButton != null)
            {
                _trophyButton.Setup(_gradeInfo.RaceInstance.Id);
            }
        }

        /// <summary>
        /// 報酬アイテム一覧ダイアログを開く
        /// </summary>
        /// <param name="legendRaceId"></param>
        private static void OpenRewardItemListDialog(RaceGroupGradeInfo gradeInfo)
        {
            gradeInfo.SendRewardListRequest(rewardArray =>
            {
                DialogRaceRewardItemList.PushDialog(rewardArray);
            });
        }

        /// <summary>
        /// アイテムアイコンを並べる
        /// </summary>
        /// <param name="itemArray"></param>
        /// <param name="root"></param>
        /// <param name="???"></param>
        /// <param name="isClear"></param>
        /// <param name="dispNum"></param>
        private void CreateItems(RaceRewardItemInfo[] itemArray, RectTransform root, bool isClear, bool dispNum)
        {
            foreach (var child in root.transform.GetChildren())
            {
                GameObject.Destroy(child);
            }
            
            _clearIconBase.SetActiveWithCheck(true);
            for (int i = 0; i < itemArray.Length; i++)
            {
                var newIcon = Instantiate(_iconBase, root);
                if (itemArray[i].IsNone)
                {
                    newIcon.gameObject.SetActive(false);
                }
                else
                {
                    newIcon.SetData(itemArray[i].Category, itemArray[i].Id, itemArray[i].Num, dispNum, isInfoPop: true);
                    newIcon.SetSize(IconBase.SizeType.Common_M);

                    // クリア済みなら
                    if (isClear)
                    {
                        // アイテムアイコンをグレーアウト
                        newIcon.Image.SetMulColor(ButtonCommon.NO_INTERACTERABLE_COLOR);

                        // CLEARアイコンを生成してアイテムアイコンに付ける
                        var clearIcon = Instantiate(_clearIconBase.gameObject, newIcon.Image.transform); // タップ時にアイテムアイコンと一緒にスケーリングするように親を指定
                        clearIcon.transform.localPosition = Math.VECTOR3_ZERO;
                    }
                }
            }
            _clearIconBase.SetActiveWithCheck(false);
        }
    }
}
