using UnityEngine;
using System.Collections;

namespace Gallop
{
    /// <summary>
    /// レースタイトル表示
    /// </summary>
    public class PartsRaceTitle : MonoBehaviour
    {
        [SerializeField]
        public RawImageCommon _raceTitleImage = null;
        [SerializeField]
        public RawImageCommon _raceTitleBaseImage = null;
        [SerializeField]
        public RawImageCommon _raceTitleG1Image = null;

        private string _raceNamePath;
        private string _raceTitlePath;
        public void Setup(MasterRace.Race race, ResourceManager.ResourceHash hash)
        {
            
            if(!string.IsNullOrEmpty(_raceNamePath))
            {
                _raceTitleG1Image.texture = null;
                _raceTitleImage.texture = null;
                ResourceManager.UnloadFromHash(_raceNamePath, hash);
            }

            if(!string.IsNullOrEmpty(_raceTitlePath))
            {
                _raceTitleBaseImage.texture = null;
                ResourceManager.UnloadFromHash(_raceTitlePath, hash);
            }

            //タイトル画像をセット。G1かそれ以外かでタイトルサイズが違うので注意
            int raceTitleId = race.FfAnim;
            int raceTitleSubId = race.FfSub;
            _raceNamePath = ResourcePath.GetRaceTitleRaceNameTexturePath(raceTitleId, raceTitleSubId);
            Texture2D titleTexture = ResourceManager.LoadOnHash<Texture2D>(_raceNamePath, hash);
            var isG1 = race.Grade == (int) RaceDefine.Grade.G1;
            _raceTitleImage.gameObject.SetActive(!isG1);
            _raceTitleBaseImage.gameObject.SetActive(!isG1);
            _raceTitleG1Image.gameObject.SetActive(isG1);
            if (isG1)
            {
                _raceTitleG1Image.texture = titleTexture;
            }
            else
            {
                _raceTitleImage.texture = titleTexture;

                _raceTitlePath = ResourcePath.GetRaceTitleFrameTexturePath((RaceDefine.Grade)race.Grade);
                if (race.Group == (int)RaceDefine.RaceGroup.RoomMatch || race.Group == (int)RaceDefine.RaceGroup.Practice)
                {
                    // G1じゃないルームマッチ、練習用のフレーム
                    _raceTitlePath = ResourcePath.GetRaceTitleFrameTexturePath(RaceUtil.GetRaceType((RaceDefine.RaceGroup)race.Group));
                }

                var titleFrameTexture = ResourceManager.LoadOnView<Texture2D>(_raceTitlePath);
                _raceTitleBaseImage.texture = titleFrameTexture;
            }
        }
    }
}