using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// メニューから1つ選択するシンプルなダイアログ
    /// </summary>
    public class ToggleMenuDialog : DialogInnerBase
    {
        private const int SCROLL_SIZE = 7;

        [SerializeField]
        private LoopScrollItemBase _itemBaseForLayout = null;
        [SerializeField]
        private LoopScrollItemBase _itemBaseForScroll = null;
        [SerializeField]
        private ScrollRectCommon _scrollRect = null;
        [SerializeField]
        private LoopScroll _loopScroll = null;
        [SerializeField]
        private UnityEngine.UI.VerticalLayoutGroup _layout = null;

        public int SelectedIndex { get; private set; }

        /// <summary>
        /// Enum指定でダイアログ開く
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="onDecide"></param>
        /// <param name="defaultIndex"></param>
        /// <param name="title"></param>
        /// <param name="rightText"></param>
        /// <param name="ignoreArray"></param>
        public static void OpenEnum<T>(System.Action<T> onDecide, T defaultVal, string title = "", string rightText = "", T[] ignoreArray = null)
        {
            var enumArray = EnumUtil.GetEnumArray<T>();
            if (enumArray == null)
                return;

            int defaultIndex = 0;
            var enumList = new List<T>();
            var nameList = new List<string>();
            for(var i = 0; i< enumArray.Length; i++)
            {
                if(ignoreArray != null && ignoreArray.Contains(enumArray[i]))
                {
                    continue;
                }

                var str = enumArray[i].GetEnumDisplayName();
                enumList.Add(enumArray[i]);
                nameList.Add(str);

                if(defaultVal.Equals(enumArray[i]))
                {
                    defaultIndex = enumList.Count - 1;
                }
            }

            Open(nameList, index => onDecide(enumList[index]), defaultIndex, title, rightText);
        }

        public static void Open(List<string> list, System.Action<int> onDecide, int defaultIndex = 0, string title = "", string rightText = "", bool scrollToDefaultIndex = false)
        {
            var obj = ResourceManager.LoadOnView<GameObject>(ResourcePath.TOGGLE_MENU_DIALOG_PATH);
            var ins = Instantiate(obj, UIManager.MainCanvas.transform).GetComponent<ToggleMenuDialog>();
            ins.Initialize(list, defaultIndex, scrollToDefaultIndex);
            var dialogdata = ins.CreateDialogData();
            dialogdata.Title = title == "" ? TextId.Common0077.Text() : title;
            dialogdata.LeftButtonText = TextId.Common0004.Text();
            dialogdata.RightButtonText = rightText == "" ? TextId.Common0003.Text() : rightText;
            dialogdata.RightButtonCallBack = d => onDecide(ins.SelectedIndex);
            dialogdata.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            DialogManager.PushDialog(dialogdata);
        }

        private void Initialize(List<string> list, int defaultIndex, bool scrollToDefaultIndex)
        {
            SelectedIndex = defaultIndex;

            if(list.Count >= SCROLL_SIZE)
            {
                _itemBaseForLayout.SetActiveWithCheck(false);
                _loopScroll.ItemBase = _itemBaseForScroll;
                _layout.enabled = false;
            }
            else
            {
                _loopScroll.ItemBase = _itemBaseForLayout;
            }

            _loopScroll.Setup<ToggleMenuItem>(list.Count, item => {
                item.Set(list[item.ItemIndex], SelectedIndex == item.ItemIndex, this);
            });

            var loopRect = _loopScroll.transform as RectTransform;
            var scrolRect = _scrollRect.transform as RectTransform;
            if (list.Count < SCROLL_SIZE)
            {
                //スクロールしないなら強制中央ぞろえ
                //LoopScrollSetupした後にRectを上書き
                _itemBaseForScroll.SetActiveWithCheck(false);
                _loopScroll.ItemBase = _itemBaseForLayout;
                _loopScroll.enabled = false;
                loopRect.sizeDelta = new Vector2(loopRect.sizeDelta.x, scrolRect.rect.height);
                _layout.enabled = true;

                _scrollRect.enabled = false;
                _scrollRect.verticalScrollbar.SetActiveWithCheck(false);
                _scrollRect.verticalScrollbar = null;
            }
            else if (scrollToDefaultIndex)
            {
                // defaultIndexまでスクロールする

                var scrollRectHeight = _loopScroll.ScrollRect.GetComponent<RectTransform>().rect.height;
                var itemHeight = _loopScroll.ItemBase.CacheRectTransform.rect.y;

                // 1アイテム当たりの比率
                var loopScrollRect = _loopScroll.RectTransform.rect.height;
                var itemRate = _loopScroll.ItemSize / (_loopScroll.RectTransform.rect.height - scrollRectHeight);

                // 比率と個数から位置を求める
                // フォーカスを中心に持ってくるため、可視範囲の合計数から半分くらいの量を足しておく
                var visibleItemNum = (scrollRectHeight - _loopScroll.Margin.Top + itemHeight / 2) / _loopScroll.ItemSize;
                var targetIndex = defaultIndex - visibleItemNum / 2 + 0.5f;
                var scrollPos = 1 - targetIndex * itemRate;

                // 0以下、1以上の対応
                scrollPos = Mathf.Max(0, Mathf.Min(1, scrollPos));

                _loopScroll.ScrollRect.verticalNormalizedPosition = scrollPos;
            }
        }

        public void OnSelected(int index)
        {
            SelectedIndex = index;
            _loopScroll.UpdateActiveItem();
        }

        #region override

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;
        }
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        #endregion

        #region Debug

#if CYG_DEBUG
        
        /// <summary>
        /// キャラ、衣装の組み合わせの選択
        /// </summary>
        /// <param name="onSelect"></param>
        /// <param name="defaultCharaId"></param>
        /// <param name="defaultDressId"></param>
        public static void DebugOpenCharaDress(System.Action<int, int> onSelect, int defaultCharaId, int defaultDressId)
        {
            var list = new List<string>();
            var charaDressList = new List<System.Tuple<int, int>>();
            var defaultIndex = 0;
            var masterCharaList = MasterDataManager.Instance.masterCharaData.dictionary.Values.OrderBy(d => d.Id).ToList();
            foreach (var chara in masterCharaList)
            {
                var dressList = GetUniqueDressAll(chara.Id);
                dressList.Insert(0, (int)ModelLoader.DressID.SRCommon);
                foreach (var d in dressList) 
                {
                    var dressData = MasterDataManager.Instance.masterDressData.Get(d);
                    var text = chara.Name + ":" + dressData.Name;
                    list.Add(text);
                    charaDressList.Add(new System.Tuple<int, int>(chara.Id, dressData.Id));
                    if (chara.Id == defaultCharaId && dressData.Id == defaultDressId)
                    {
                        defaultIndex = list.Count - 1;
                    }
                }
            }

            Open(list, index =>
            {
                var data = charaDressList[index];
                onSelect(data.Item1, data.Item2);
            });
        }

        //勝負服IDを全て取得
        public static List<int> GetUniqueDressAll(int charaId)
        {
            var list = new List<int>();
            foreach (var kv in MasterDataManager.Instance.masterDressData.dictionary)
            {
                var data = kv.Value;
                if (data.CharaId == charaId && data.UseRace == 1)
                {
                    //キャラID指定ならキャラ限
                    list.Add(data.Id);
                }
            }
            return list;
        }

#endif

        #endregion
    }
}
