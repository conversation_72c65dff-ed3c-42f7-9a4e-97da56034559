using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// アイテムスロット（アイテム選択の場面で「選択したアイテム」を表現するのに使う）
    /// </summary>
    public class PartsSelectedItemSlot : MonoBehaviour
    {
        [SerializeField]
        private ItemIcon _itemIcon = null;

        /// <summary>スロットにアイテムをセットした時のアニメ</summary>
        [SerializeField]
        private TweenAnimationTimelineComponent _selectInAnimTlComponent = null;
        public TweenAnimationTimelineComponent SelectInAnimTlComponent => _selectInAnimTlComponent;

        /// <summary>スロットにアイテムをセットした時のエフェクト</summary>
        private GameObject _selectInEffectObj;

        /// <summary>エフェクトのルートオブジェクト</summary>
        [SerializeField]
        private GameObject _effectRoot = null;

        /// <summary>
        /// 表示準備
        /// </summary>
        public void Setup()
        {
            // エフェクト読み込み
            var effectPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.UIEFFECT_COMMON_ITEMUSE_GLITTER00);
            _selectInEffectObj = GameObject.Instantiate(effectPrefab, _effectRoot.transform);
            _selectInEffectObj.SetActive(true);

            _effectRoot.SetActive(false);
        }

        /// <summary>
        /// アイテムアイコンを表示する
        /// </summary>
        public void SetItem(GameDefine.ItemCategory itemCategory, int itemId, int itemNum, System.Action onClick = null)
        {
            var isActiveItemIconPrev = _itemIcon.gameObject.activeSelf;
            _itemIcon.gameObject.SetActive(true);

            _itemIcon.SetData(
                itemCategory: itemCategory,
                itemId: itemId,
                number: itemNum,
                numDisp: false,
                showZero: false,
                opt: 0,
                isInfoPop: true,
                parentCanvas: UIManager.DialogCanvas,
                onClick: () =>
                {
                    onClick?.Invoke();
                });

            _itemIcon.SetSize(IconBase.SizeType.Large);

            // 空スロットにアイテムアイコンがセットされたらアニメ再生
            if (!isActiveItemIconPrev)
            {
                _selectInAnimTlComponent.Stop();
                _selectInAnimTlComponent.Play();

                _effectRoot.SetActive(false); // エフェクトについてはアニメの方でアクティブ/非アクティブの制御をしてくれるので、準備として非アクティブにしておくだけで良い

                #if false // パーティクルがダイアログより裏側に表示される問題の修正検証コード
                var particleRoot = _effectRoot.GetComponentInChildren<ParticleSystem>();
                if (particleRoot != null)
                {
                    foreach (var renderer in particleRoot.GetComponentsInChildren<Renderer>())
                    {
                        if (renderer == null)
                            continue;
                        renderer.sortingLayerName = "TapEffect";
                        renderer.sortingOrder = 30000;
                    }
                }
                #endif
            }
        }

        /// <summary>
        /// アイテムアイコンを消して空スロットを表示する
        /// </summary>
        public void SetEmpty()
        {
            _itemIcon.gameObject.SetActive(false);
            _effectRoot.SetActive(false);
        }

        private void OnDestroy()
        {
            if (_selectInAnimTlComponent == null)
                return;
            _selectInAnimTlComponent.Stop();
        }
    }
}