using UnityEngine;
#if UNITY_EDITOR
using System;
using UnityEditor;
#endif
namespace Gallop
{
    [ExecuteInEditMode]
    public class TextMeshProUguiCommon : TMPro.TextMeshProUGUI, ICustomTextComponent, IUIAssetReconnector
    {
        [SerializeField]
        private bool _isIgnoreParentColor = false;
        public bool IsIgnoreParentColor
        {
            get => _isIgnoreParentColor;
            set
            {
                if (_isIgnoreParentColor != value)
                {
                    _isIgnoreParentColor = value;
                    //親のDirtyフラグを立てる
                    ColorSender.SetDirtyParentSender(transform);
                }
            }
        }

        /// <summary>
        /// 受け取りカラー
        /// </summary>
        public Color recieveColor { get; set; } = Color.white;

        /// <summary>
        /// プログラム側からテキスト設定したかどうかのフラグ
        /// </summary>
        /// <remarks>
        /// 優先順位は プログラムでtextに設定 > Serializeで設定したtextID > Serializeで設定したtext
        /// </remarks>
        private bool _isOverwritten = false;

        public bool IsOverwritten => _isOverwritten;

        public new string text
        {
            get => base.text;

            set
            {
                base.text = value;

                if (Application.isPlaying)
                {//起動中だけ上書き確認する
                    _isOverwritten = true;
                    //つまり、既にTextIdは参照していないはずなのでOldはクリアしておく。（そうしないと、再度同じIDを指定された時に更新されないので）
                    _controller.TextIdStrOld = string.Empty;
                }
            }
        }


        public void SetTextIdFromController(TextId textId)
        {
            m_textid = textId;
            m_textid_str = textId.ToString();
        }

        public void SetTextFromController(string textFromTextId)
        {
            base.text = textFromTextId;
        }

        private TextId m_textid = TextId.None;
        public TextId TextId
        {
            get => m_textid;
            set
            {
                m_textid = value;
                m_textid_str = value.ToString();

                //新しいTextIdが指定された時点で上書き済みフラグを無効化する
                _isOverwritten = false;

                //起動中だけ変更時にUpdateする
                _controller.UpdateText(this);
            }
        }

        /// <summary>
        /// inspectorで設定できるtext_id（enum名を文字列でシリアライズ保存）
        /// </summary>
        [SerializeField]
        private string m_textid_str = null;

        public string TextIdString {
            get => m_textid_str;
            set => m_textid_str = value;
        }

        [SerializeField]
        private TextFormat.FontSize m_fontSizeFormat = TextFormat.FontSize.None;
        public TextFormat.FontSize FontSizeFormat
        {
            get => m_fontSizeFormat;
            set => m_fontSizeFormat = value;
        }

        public int FontSize
        {
            get => Mathf.FloorToInt(fontSize);
            set
            {
                fontSize = value;
                _controller.UpdateSize(this);
            }
        }

        /// <summary>
        /// フォント色
        /// 今後はこちらを使用
        /// </summary>
        [SerializeField]
        private FontColorType _fontColor = FontColorType.White;
        public FontColorType FontColor
        {
            get => _fontColor;
            set => _fontColor = value;
        }

        /// <summary>
        /// アウトライン色
        /// 今後はこちらを使用
        /// </summary>
        [SerializeField]
        private OutlineColorType _outlineColor = OutlineColorType.None;
        public OutlineColorType OutlineColor
        {
            get => _outlineColor;
            set => _outlineColor = value;
        }

        /// <summary>
        /// アウトラインサイズ
        /// </summary>
        [SerializeField]
        private OutlineSizeType _outlineSize = OutlineSizeType.M;
        public OutlineSizeType OutlineSize
        {
            get => _outlineSize;
            set => _outlineSize = value;
        }

        #region グラデーション

        public Color GradientColor1 { get; private set; }
        public Color GradientColor2 { get; private set; }

        /// <summary>
        /// グラデーション用の色
        /// None以外の場合FontColorを無視してこっちが優先される
        /// </summary>
        [SerializeField]
        private VerticalGradientColorType _verticalGradientColor = VerticalGradientColorType.None;
        public VerticalGradientColorType VerticalGradientColor { get { return _verticalGradientColor; } }

        public void SetGradientVertical(Color top, Color bottom)
        {
            GradientColor1 = top;
            GradientColor2 = bottom;

            var newGrad = new TMPro.VertexGradient();
            newGrad.topLeft = top;
            newGrad.topRight = top;
            newGrad.bottomLeft = bottom;
            newGrad.bottomRight = bottom;
            colorGradient = newGrad;
            SetVerticesDirty();
        }

        #endregion

        private readonly CustomTextController _controller = new CustomTextController();

        private Color _defaultColor = Color.white;
        public Color DefaultColor {
            get => _defaultColor;
            set => _defaultColor = value;
        }
        private bool _isSetupedDefaultColor;

        private Color _mulColor = Color.white;

        public bool IsActiveInHierarchy => gameObject.activeInHierarchy;

        protected override void Awake()
        {
            base.Awake();
            SetupDefaultColor();
            // Sprite参照復旧
            ReconnectReference();
            // OnUpdateが来ないケースがあったため強制的に一度アップデートコール.
            OnUpdate();
        }

        /// <summary>
        /// DefualtColorを準備する
        /// UpdateColorを呼ぶ前に呼ぶ必要がある
        /// </summary>
        public void SetupDefaultColor()
        {
            if (_isSetupedDefaultColor)
            {
                return;
            }

            _defaultColor = color;
            _isSetupedDefaultColor = true;
        }

#if UNITY_EDITOR
        private void Update()
        {
            if (Application.isPlaying)
            {
                return;
            }
            OnUpdate();
        }
#endif

        public void OnUpdate()
        {
            _controller.UpdateText(this);
            _controller.UpdateSize(this);

            UpdateColor();

            UpdateOutline();

            _controller.FontColorOld = _fontColor;
            _controller.OutlineColorOld = _outlineColor;
            _controller.OutlineSizeOld = _outlineSize;
        }

        public void UpdateColor()
        {
            CustomTextController.UpdateColor(this);
        }

        private void UpdateOutline()
        {
            if ( _controller.OutlineColorOld == _outlineColor && _controller.OutlineSizeOld == _outlineSize )
            {
                return;
            }

            var outlineColor = ColorPreset.OutlineColorDictionary[_outlineColor];
            var colorToNormal = GetComponent<ColorToNormalForTextMeshProUgui>();
            if ( outlineColor.a > 0)
            {
                if (colorToNormal == null)
                {
                    colorToNormal = gameObject.AddComponent<ColorToNormalForTextMeshProUgui>();
                }
                colorToNormal.Color = outlineColor;
#if UNITY_EDITOR
                var assetPath = AssetDatabase.GetAssetPath(font);
                const string ResourcesPath = "Assets/_Gallop/Resources/";
                assetPath = assetPath.Replace(ResourcesPath, string.Empty);
                assetPath = System.IO.Path.GetDirectoryName(assetPath) + "/" +
                            System.IO.Path.GetFileNameWithoutExtension(assetPath) + " Outline " + Enum.GetName(typeof(OutlineSizeType), _outlineSize);
                if (fontSharedMaterial.name != System.IO.Path.GetFileNameWithoutExtension(assetPath))
                {
                    if (Application.isPlaying)
                    {
                        //エディタでだけ、Materialをロードする
                        fontSharedMaterial = ResourceManager.LoadOnView<Material>(assetPath, SceneManager.Instance.GetCurrentViewId());
                    }
                    else
                    {
                        fontSharedMaterial = Resources.Load<Material>(assetPath);
                    }
                }
#endif
                return;
            }


            //アウトラインを止める処理
            fontSharedMaterial = font.material;

            if (colorToNormal != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(colorToNormal);
                }
                else
                {
                    DestroyImmediate(colorToNormal);
                }
            }

        }


        [SerializeField]
        private string _fontSdfBundleName = "";
        [SerializeField]
        private string _fontMaterialBundleName = "";

        /// <summary>
        /// 参照を貼り直す
        /// </summary>
        public void ReconnectReference()
        {
            if (font == null)
            {
                font = ResourceManager.LoadOnScene<TMPro.TMP_FontAsset>(_fontSdfBundleName);
            }

            if (fontSharedMaterial == null)
            {
                fontSharedMaterial = ResourceManager.LoadOnScene<Material>(_fontSdfBundleName);
            }

            #if BUMA_T
            /*
             *【需求】UI热更
             * 从Resources重新加载Shader
             * TextMesh Pro 的材质需要打入Bundle，引用关联丢失；如果 Shader 打入Bundle，会引起ShaderManager过多修改，切Shader无热更可能
             * by xuxinwei in 2022-02-0
             */
            var reconnectShader = Shader.Find(fontSharedMaterial.shader.name);
            if (reconnectShader != null) { fontSharedMaterial.shader = reconnectShader; }
            #endif
        }

        public void SetMulColor(Color color)
        {
            _mulColor = color;
            if (_colorToNormal == null)
            {
                _colorToNormal = GetComponent<ColorToNormalForTextMeshProUgui>();
            }
            if (_colorToNormal != null)
            {
                _colorToNormal.SetMulColor(_mulColor);
            }
            CustomTextController.UpdateColor(this);
        }

        private ColorToNormalForTextMeshProUgui _colorToNormal;

        public Color GetMulColor()
        {
            return _mulColor;
        }

#if UNITY_EDITOR
        /// <summary>
        /// AssetのImport時に必要な変換処理を実行
        /// 今設定されているSpriteから、コンバート用のAtlas/Sprite名を作る
        /// </summary>
        public bool ConvertOnAssetImport(string assetFilePath)
        {
            if (!GallopUtil.IsPathInGallopDir(assetFilePath))
            {
                // TextMeshProUguiCommonは_Gallop以下のアセットのみ張り替え対象
                return false;
            }
            
            var bundlePath = AssetDatabase.GetAssetPath(font);

            // NULL参照だが、リファレンス先情報が残っている ⇒ Missing
            // リソース環境無しではどうしてもMissingになるので、その場合はプロパティを変更しない
            SerializedObject serializeObject = new SerializedObject(this);
            SerializedProperty serializeProperty = serializeObject.FindProperty("_fontAsset");
            if (serializeProperty == null) return false;
            if (serializeProperty.objectReferenceValue == null)
            {
                if (serializeProperty.objectReferenceInstanceIDValue != 0)
                {
                    return false;
                }
            }

            var fontSdfBundleNameOld = _fontSdfBundleName;
            if (string.IsNullOrEmpty(bundlePath))
            {
                _fontSdfBundleName = string.Empty;
            }
            else
            {
                _fontSdfBundleName = System.IO.Path.GetDirectoryName(bundlePath) + "/" + System.IO.Path.GetFileNameWithoutExtension(bundlePath);
                _fontSdfBundleName = _fontSdfBundleName.Replace(ResourcePath.BundleResourcesAssetsPath, string.Empty);
            }

            bundlePath = AssetDatabase.GetAssetPath(fontSharedMaterial);
            serializeProperty = serializeObject.FindProperty("_sharedMaterial");
            if (serializeProperty.objectReferenceValue == null)
            {
                if (serializeProperty.objectReferenceInstanceIDValue != 0)
                {
                    return false;
                }
            }

            var fontMaterialBundleNameOld = _fontMaterialBundleName;
            if (string.IsNullOrEmpty(bundlePath))
            {
                _fontMaterialBundleName = string.Empty;
            }
            else
            {
                _fontMaterialBundleName = System.IO.Path.GetDirectoryName(bundlePath) + "/" + System.IO.Path.GetFileNameWithoutExtension(bundlePath);
                _fontMaterialBundleName = _fontMaterialBundleName.Replace(ResourcePath.BundleResourcesAssetsPath, string.Empty);
            }

            return fontSdfBundleNameOld != _fontSdfBundleName || fontMaterialBundleNameOld != _fontMaterialBundleName;
        }
#endif
    }
}
