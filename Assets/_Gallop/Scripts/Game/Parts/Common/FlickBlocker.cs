using UnityEngine.EventSystems;

namespace Gallop
{
    /// <summary>
    /// フリック用のイベントをブロックする
    /// </summary>
    public class FlickBlocker : <PERSON>IBeh<PERSON>our, IFlickable
    {
        public void OnBeginDrag(PointerEventData eventData)
        {
            // Do Nothing.
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            // Do Nothing.
        }

        public void OnPointerDown(PointerEventData eventData)
        {
            // Do Nothing.
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            // Do Nothing.
        }
    }
}
