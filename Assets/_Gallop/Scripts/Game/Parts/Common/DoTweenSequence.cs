using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.Serialization;

namespace Gallop
{
    [System.Serializable]
    public class DoTweenSequenceEntity
    {
        public enum EntityType
        {
            Callback,
            Interval,
        }
        public EntityType Type;
        public string ActionName;
        public float Duration;
    }

    /// <summary>
    /// DoTweenのシーケンスを登録し生成するクラス
    /// Editor上で編集することができる
    /// </summary>
    [System.Serializable]
    public class DoTweenSequence 
    {
        public List<DoTweenSequenceEntity> sequenceList = new List<DoTweenSequenceEntity>();

        /// <summary>
        /// 登録されている情報からシーケンスを生成する
        /// </summary>
        /// <param name="target"></param>
        /// <returns></returns>
        public Sequence GenerateSequence(GameObject target)
        {
            var seq  = DOTween.Sequence();
            foreach(var entity in sequenceList)
            {
                if(entity.Type == DoTweenSequenceEntity.EntityType.Interval)
                {
                    seq.AppendInterval(entity.Duration);
                }
                else
                {
                    seq.AppendCallback(()=>{target.SendMessage(entity.ActionName,entity.Duration,SendMessageOptions.DontRequireReceiver);});
                }
            }
            return seq;
        }
    }

    /// <summary>
    /// Editorで編集することができるような関数を識別するためのアトリビュート
    /// </summary>
    public class DoTweenAnimationCallback : System.Attribute
    {
        public string Label{get;private set;}
        public DoTweenAnimationCallback(string label){
            Label = label;
        }
    }
}