using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// スクロールバーの共通処理まとめクラス
    /// </summary>
    [ExecuteInEditMode]
    public class ScrollbarCommon : Scrollbar, ILockable
    {
        private const string COLLISION_NAME = "Collision";
        private const float COLLISION_SCALE = 4f;

        private RectTransform _slidingAreaRect;
        private string _handleRectName;

        private RectTransform rectTransform;

        private RectTransform _handleImageRect;

        private Graphic _handleImage;
        public Graphic HandleImage => _handleImage;

        public System.Action CallbackOnPointerUp { get; set; }
        public System.Action CallbackOnBeginDrag { get; set; }

        /// <summary>
        /// 計算用SizeDelta
        /// ハンドル幅がが最小サイズを下回った時に計算用として使用する
        /// </summary>
        private Vector2 _sizeDelta = Math.VECTOR2_ZERO;
        public Vector2 SizeDelta {
            get => _sizeDelta;
            set => _sizeDelta = value;
        }

        protected override void Update()
        {
            base.Update();
            SetupCollisionRect();
        }

        protected override void Start()
        {
            base.Start();
            _slidingAreaRect = handleRect.parent.GetComponent<RectTransform>();
            _handleRectName = handleRect.name;
        }

        /// <summary>
        /// Collisionの矩形サイズを補正
        /// </summary>
        private void SetupCollisionRect()
        {
            //スクロールバーの当たり判定を大きくする
            //もしすでにCollisionの子供だったらスルー
            if (!_handleRectName.Equals(COLLISION_NAME))
            {
                //当たり判定伸長用のオブジェクトを追加
                var collision = new GameObject(COLLISION_NAME, typeof(RectTransform), typeof(GraphicCast));
                var handleCollisionRect = collision.GetComponent<RectTransform>();
                handleCollisionRect.SetParent(handleRect.parent);
                handleCollisionRect.localScale = Math.VECTOR3_ONE;
                handleCollisionRect.anchoredPosition = Math.VECTOR2_ZERO;
                handleCollisionRect.anchorMin = Math.VECTOR2_ZERO;
                handleCollisionRect.anchorMax = Math.VECTOR2_ONE;

                //親子を入れ替える
                handleCollisionRect.SetParent(handleRect.parent);
                handleRect = handleCollisionRect;
                handleRect.SetParent(handleCollisionRect);
                _handleRectName = handleRect.name;
            }

            //ハンドル画像を取得する
            if (_handleImageRect == null)
            {
                //子供の中からHandleという名前の画像オブジェクトを探す
                var images = GetComponentsInChildren<Image>(true);
                var image = System.Array.Find(images, i => i.name.Equals("Handle"));
                if (image != null)
                {
                    _handleImageRect = image.transform as RectTransform;
                }

                if (_handleImageRect == null)
                {
                    return;
                }
                _handleImage = _handleImageRect.GetComponent<Graphic>();
            }

            //自分のrectTrasformをキャッシュ
            if (rectTransform == null)
            {
                rectTransform = GetComponent<RectTransform>();
                if (rectTransform == null)
                {
                    return;
                }
            }

            _handleImageRect.SetParent(handleRect);
            _handleImageRect.anchorMin = Math.VECTOR2_ZERO;
            _handleImageRect.anchorMax = Math.VECTOR2_ONE;
            _handleImageRect.anchoredPosition = Math.VECTOR2_ZERO;
            //親(Collision)が4倍になっているので、スクロールバーは1/4の大きさにする
            _handleImageRect.sizeDelta = new Vector2(-_handleImageRect.rect.width * (COLLISION_SCALE - 1), 0f);

            RectTransform slidingAreaRect;

            if (direction == Direction.BottomToTop || direction == Direction.TopToBottom)
            {//垂直方向スクロールバーなら

                //slidingArea調整
                slidingAreaRect = _slidingAreaRect;
                slidingAreaRect.anchorMin = Math.VECTOR2_ZERO;
                slidingAreaRect.anchorMax = Math.VECTOR2_ONE;
                slidingAreaRect.sizeDelta = new Vector2(-rectTransform.rect.width, slidingAreaRect.sizeDelta.y);

                handleRect.sizeDelta = new Vector2(rectTransform.rect.width * COLLISION_SCALE, 0f);
                if (_handleImageRect != null)
                {
                    //少し(4pxほど)下方向に画像を広げて欲しいとの事
                    const float BottomShiftSize = 4f;
                    _handleImageRect.sizeDelta = new Vector2(-rectTransform.rect.width * (COLLISION_SCALE - 1), BottomShiftSize) + SizeDelta;
                    _handleImageRect.anchoredPosition = new Vector2(0f,-BottomShiftSize * 0.5f);
                }

                return;
            }

            //水平方向スクロールバーなら

            //slidingArea調整
            slidingAreaRect = _slidingAreaRect;
            slidingAreaRect.anchorMin = Math.VECTOR2_ZERO;
            slidingAreaRect.anchorMax = Math.VECTOR2_ONE;
            slidingAreaRect.sizeDelta = new Vector2(slidingAreaRect.sizeDelta.x, -rectTransform.rect.height);

            //水平方向スクロールバーなら
            handleRect.sizeDelta = new Vector2(0f, rectTransform.rect.height * COLLISION_SCALE);
            if (_handleImageRect != null)
            {
                _handleImageRect.sizeDelta = new Vector2(0f, -rectTransform.rect.height * (COLLISION_SCALE - 1));
            }
        }

        /// <summary>
        /// UIがロックされているかをチェックする
        /// </summary>
        /// <remarks>
        /// UIManager.IsLockAllUI()は、親のLockableBehaviourをチェックする過程で自動的にチェックされる
        /// </remarks>
        /// <returns></returns>
        public bool IsLock()
        {
            var parentLockableBehaviour = GetComponentInParent<LockableBehaviour>();
            if (parentLockableBehaviour != null)
            {
                return parentLockableBehaviour.IsLock();
            }

            return false;
        }

        /// <summary>
        /// ロック状態に切り替わったときのコールバック
        /// </summary>
        /// <param name="lockTarget"></param>
        public void OnLock(LockableBehaviour lockTarget)
        {
        }

        /// <summary>
        /// アンロック状態に切り替わった時のコールバック
        /// </summary>
        /// <param name="lockTarget"></param>
        public void OnUnlock(LockableBehaviour lockTarget)
        {
        }

        /// <summary>
        /// ポインターダウン
        /// </summary>
        /// <param name="eventData"></param>
        public override void OnPointerDown(PointerEventData eventData)
        {
            if (!this.interactable)
            {
                return;
            }

            if (IsLock())
            {
                return;
            }
            base.OnPointerDown(eventData);
        }

        /// <summary>
        ///  ドラッグ
        /// </summary>
        /// <param name="eventData"></param>
        public override void OnDrag(PointerEventData eventData)
        {
            if (!this.interactable)
            {
                return;
            }

            if (IsLock())
            {
                return;
            }
            base.OnDrag(eventData);
        }

        /// <summary>
        /// ドラッグ開始
        /// </summary>
        /// <param name="eventData"></param>
        public override void OnBeginDrag(PointerEventData eventData)
        {
            if (!this.interactable)
            {
                return;
            }

            if (IsLock())
            {
                return;
            }

            base.OnBeginDrag(eventData);
            CallbackOnBeginDrag?.Invoke();
        }

        /// <summary>
        /// ポインターアップ
        /// </summary>
        /// <param name="eventData"></param>
        public override void OnPointerUp(PointerEventData eventData)
        {
            if (!this.interactable)
            {
                return;
            }

            if (IsLock())
            {
                return;
            }

            CallbackOnPointerUp?.Invoke();
            base.OnPointerUp(eventData);
        }

#if UNITY_EDITOR
        /// <summary>
        /// ScrollbarをScrollbarCommonに置き換える
        /// </summary>
        /// <param name="targetObj"></param>
        /// <returns></returns>
        public static ScrollbarCommon ChangeComponent(GameObject targetObj)
        {
            var scrollbar = targetObj.GetComponent<Scrollbar>();
            var scrollbarCommon = scrollbar as ScrollbarCommon;
            if (scrollbarCommon != null)
            {
                //すでに置き換え済みなら何もしない
                return scrollbarCommon;
            }


            var navigation = scrollbar.navigation;
            var transition = scrollbar.transition;
            var colors = scrollbar.colors;
            var spriteState = scrollbar.spriteState;
            var animationTriggers = scrollbar.animationTriggers;
            var interactable = scrollbar.interactable;
            var targetGraphic = scrollbar.targetGraphic;
            var handleRect = scrollbar.handleRect;
            var direction = scrollbar.direction;
            var value = scrollbar.value;
            var size = scrollbar.size;
            var numberOfSteps = scrollbar.numberOfSteps;
            var onValueChanged = scrollbar.onValueChanged;

            DestroyImmediate(scrollbar);

            scrollbarCommon = targetObj.AddComponent<ScrollbarCommon>();
            if (scrollbarCommon == null)
            {
                return scrollbarCommon;
            }

            scrollbarCommon.navigation = navigation;
            scrollbarCommon.transition = transition;
            scrollbarCommon.colors = colors;
            scrollbarCommon.spriteState = spriteState;
            scrollbarCommon.animationTriggers = animationTriggers;
            scrollbarCommon.interactable = interactable;
            scrollbarCommon.targetGraphic = targetGraphic;
            scrollbarCommon.handleRect = handleRect;
            scrollbarCommon.direction = direction;
            scrollbarCommon.value = value;
            scrollbarCommon.size = size;
            scrollbarCommon.numberOfSteps = numberOfSteps;
            scrollbarCommon.onValueChanged = onValueChanged;

            return scrollbarCommon;
        }
#endif
    }
}
