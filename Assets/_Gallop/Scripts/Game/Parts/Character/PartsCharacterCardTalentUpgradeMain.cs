using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    public class PartsCharacterCardTalentUpgradeMain : MonoBehaviour, IPartsCharacterCardManageMain
    {
        [SerializeField]
        private TextCommon _textCurrentLevel = null;
        [SerializeField]
        private TextCommon _textNextLevel = null;

        [SerializeField]
        private PartsCardPowerUpArrow _partsPowerUpArrow = null;

        public PartsLimitBreakSkillGroup PartsLimitBreakSkillGroup = null;
        
        [SerializeField]
        private ButtonCommon _buttonItemBefore = null;
        [SerializeField]
        private ButtonCommon _buttonItemNext = null;

        [SerializeField]
        private RectTransform _listRoot = null;
        
        [SerializeField]
        private PartsCharacterCardTalentUpgradeCarouselScroll _carouselScroll = null;
        [SerializeField]
        private SimpleScroller _scroller = null;
        
        [SerializeField]
        private TextCommon _textOwnMoney = null;

        [SerializeField]
        private TextCommon _textNeedMoney = null;

        [SerializeField]
        private TextCommon _textWarning = null;
        [SerializeField]
        private ParticleSystem _buttonParticle = null;

        [SerializeField] 
        private GameObject _maxTextGroupObject = null;

        [SerializeField] 
        private TextCommon _maxLevelText = null;

        [SerializeField]
        private GameObject _nextMaxLevelObject = null;

        [SerializeField] 
        private GameObject _maxLevelDescObject = null;
        
        // アイテムを生成する先のページ達
        [SerializeField]
        private List<Transform> _listItemPage = new List<Transform>();
        // スクロール数を表示するためのドット
        [SerializeField]
        private List<PageDot> _pageDots = new List<PageDot>();

        /// <summary>
        /// スクロール表示部分を構成するリスト以外(Dot等)のUIのRoot
        /// </summary>
        [SerializeField]
        private GameObject _otherScrollItemUIRoot = null;


        private CharacterCardHaveListViewController _controller = null;

        private List<ItemIconWithNeedNum> _itemList = new List<ItemIconWithNeedNum>();

        /// <summary>
        /// 次テキストの点滅設定
        /// </summary>
        private TweenAnimationTimelineComponent _textNextAnimation = null;
        private CanvasGroup _textNextCanvas = null;

        private const int ItemPerPage = 4;
        private int _currentItemPageIndex = 0;
        private int _itemPageCount = 0;

        private int _maxLevel = 0;
        private int _minLevel = 0;

        private int _ownMoney { get { return GallopUtil.GetHaveItemNum(GameDefine.ItemCategory.MONEY, GameDefine.MONEY_ITEM_ID); } }

        private int _needMoney
        {
            get
            {
                //必要マネーを調べる。
                return GallopUtil.GetMoneyForTalentUpgrade(_partsPowerUpArrow.CurrentValue , _controller.CardData.TalentLevel);
            }
        }

        private bool _alreadySetUpDone = false;
        
        public void Setup(CharacterCardHaveListViewController controller)
        {
            if (_alreadySetUpDone)
            {
                return;
            }
            
            _controller = controller;

            _buttonItemBefore.SetOnClick(OnClickPrev);
            _buttonItemNext.SetOnClick(OnClickNext);

            _alreadySetUpDone = true;
        }
        public void Release()
        {
            _partsPowerUpArrow.Release();
        }

        private void Initialize()
        {
            _minLevel = _controller.CardData.TalentLevel + 1;
            _maxLevel = GameDefine.MAX_TALENT_LEVEL;
            
            if (_textNextAnimation == null)
            {
                _textNextAnimation = _textNextLevel.GetComponent<TweenAnimationTimelineComponent>();
            }

            if (_textNextCanvas == null)
            {
                _textNextCanvas = _textNextLevel.GetComponent<CanvasGroup>();
            }

            _partsPowerUpArrow.Setup(_minLevel, _maxLevel, _maxLevel, OnUpdatePowerUpArrow, PartsCardPowerUpArrow.UIPageType.CharacterCardTalentUpgrade);
            
            OnUpdatePowerUpArrow(_minLevel);
            
            //最大かどうかチェック
            bool isMaxTalentLevel = _controller.CardData.IsMaxTalentLevel();
            
            //矢印ボタン切り替え
            _partsPowerUpArrow.gameObject.SetActive(!isMaxTalentLevel);
            //currentLevel表示切り替え
            _textCurrentLevel.gameObject.SetActive(!isMaxTalentLevel);
            _textNextLevel.gameObject.SetActive(!isMaxTalentLevel);
            //itemスクロール表示切替え
            _listRoot.gameObject.SetActive(!isMaxTalentLevel);
            _otherScrollItemUIRoot.SetActive(!isMaxTalentLevel);
            
            //Max説明オブジェクト表示切り替え
            _maxTextGroupObject.SetActive(isMaxTalentLevel);
            _maxLevelDescObject.SetActive(isMaxTalentLevel);
            //Maxレベル更新
            _maxLevelText.text = TextUtil.Format("{0} {1}", TextId.Outgame0084.Text(), _controller.CardData.TalentLevel);
            _maxLevelText.VerticalGradientColor = (VerticalGradientColorType.TalentLv1 - 1) + _controller.CardData.TalentLevel;
            _maxLevelText.UpdateColor();
        }

        /// <summary>
        /// カルーセルスクロールの初期化
        /// </summary>
        private void InitializeCarouselScroll()
        {
            var itemSetupList = new List<ItemIconWithNeedNum.SetupInfo>();
            var cardData = _controller.CardData;
            var itemDict = GallopUtil.GetCharacterCardTalentUpgradeItemDict(cardData.GetTalentGroupId(), cardData.TalentLevel, _partsPowerUpArrow.CurrentValue);
            foreach (var itemPair in itemDict)
            {
                itemSetupList.Add(new ItemIconWithNeedNum.SetupInfo(GameDefine.ItemCategory.TRAINING, itemPair.Key, itemPair.Value, animationEnabled: true, onClick: () =>
                {
                    DialogItemPlace.Open(GameDefine.ItemCategory.TRAINING, itemPair.Key, itemPair.Value,
                        new CharacterCardHaveListViewInfo(_controller.SelectedPageType, _controller.CardData.CardId, _controller.SelectedPageState, scrollPos: _controller.ScrollPos));
                }));
            }
            // 一ページ単位のサイズに分割
            var itemSetupChunkList = itemSetupList.Select((v, i) => new { v, i })
                .GroupBy(x => x.i / ItemPerPage)
                .Select(g => g.Select(x => x.v));

            var itemDataList = new List<PartsCharacterCardTalentUpgradeListItem.ItemData>();
            foreach (var chunk in itemSetupChunkList)
            {
                itemDataList.Add(new PartsCharacterCardTalentUpgradeListItem.ItemData(chunk.ToList()));
            }
            _carouselScroll.Initialize(itemDataList, OnSelect, OnFlick, true);
            _carouselScroll.ScrollImmediately(0);
            ScrollerUpdateIndexCallback(0);

            _itemPageCount = itemDataList.Count;
            if (_itemPageCount > 1)
            {
                // 複数ページあるとき
                _buttonItemBefore.SetActiveWithCheck(true);
                _buttonItemNext.SetActiveWithCheck(true);
                _carouselScroll.enabled = true;
                _scroller.enabled = true;

                // ページドットの個数
                for (int i = 0; i < _pageDots.Count(); ++i)
                {
                    _pageDots[i].SetActiveWithCheck(i < _itemPageCount);
                }
            }
            else
            {
                // 1ページしかないときは色々と表示を消す
                _buttonItemBefore.SetActiveWithCheck(false);
                _buttonItemNext.SetActiveWithCheck(false);
                _carouselScroll.enabled = false;
                _scroller.enabled = false;

                // ページドット全部消す
                for (int i = 0; i < _pageDots.Count(); ++i)
                {
                    _pageDots[i].SetActiveWithCheck(false);
                }
            }
        }

        public void Show()
        {
            Initialize();
        }

        private void OnUpdatePowerUpArrow(int level)
        {
            //テキスト更新
            _textCurrentLevel.text = TextUtil.Format("{0} {1}", TextId.Outgame0084.Text(), _controller.CardData.TalentLevel);
            _textCurrentLevel.VerticalGradientColor = (VerticalGradientColorType.TalentLv1 - 1) + _controller.CardData.TalentLevel;
            _textCurrentLevel.UpdateColor();
            
            _textNextLevel.text = TextUtil.Format("{0} {1}", TextId.Outgame0084.Text(), level);
            _textNextLevel.VerticalGradientColor = (VerticalGradientColorType.TalentLv1 - 1) + level;
            _textNextLevel.UpdateColor();
            
            if (!_controller.CardData.IsMaxTalentLevel())
            {
                //アイテム数更新
                InitializeCarouselScroll();
            }

            _partsPowerUpArrow.CheckButtonInteractable();
            UpdateLimitBreakStatus(_controller.CardData, _partsPowerUpArrow.CurrentValue);
            SetCenterButtonInteractable();

            if (!_controller.CardData.IsMaxTalentLevel())
            {
                _textOwnMoney.text = TextUtil.ToCommaSeparatedString(_ownMoney);
                _textNeedMoney.text = TextUtil.ToCommaSeparatedString(_needMoney);
                _textOwnMoney.FontColor = _needMoney > _ownMoney ? FontColorType.Warning : FontColorType.Brown;
            }

            if (level >= GameDefine.MAX_TALENT_LEVEL && !_controller.CardData.IsMaxTalentLevel())
            {
                _nextMaxLevelObject.gameObject.SetActive(true);
            }
            else
            {
                _nextMaxLevelObject.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 覚醒Lvスキル情報更新
        /// </summary>
        public void UpdateLimitBreakStatus(WorkCardData.CardData cardData, int value)
        {
            PartsLimitBreakSkillGroup.SetUp(cardData, value, _controller.UmamusumeAtlas);
        }
        
        /// <summary>
        /// 覚醒Lvスキル情報更新
        /// 解决强化觉醒，点击道具进入商店后返回强化界面，强化等级不在高亮显示  代码迁移120.6版本  by zw
        /// </summary>
        public void UpdateLimitBreakStatus(WorkCardData.CardData cardData)
        {
            UpdateLimitBreakStatus(cardData, _partsPowerUpArrow.CurrentValue);
        }
        
        private void ScrollerUpdateIndexCallback(int index)
        {
            _currentItemPageIndex = index;
            for (int i = 0; i < _pageDots.Count(); ++i)
            {
                _pageDots[i].SetOn(i == index, false);
            }
        }

        private void OnFlick(FlickHandler.FlickStatus status)
        {
            if (status.IsLeft) OnClickNext();
            if (status.IsRight) OnClickPrev();
        }

        private void OnSelect(int index, PartsCharacterCardTalentUpgradeListItem.ItemData itemData)
        {
            ScrollerUpdateIndexCallback(index);
        }

        /// <summary>
        /// 次へ
        /// </summary>
        private void OnClickNext()
        {
            _carouselScroll.ScrollToNext();
        }

        /// <summary>
        /// 前へ
        /// </summary>
        private void OnClickPrev()
        {
            _carouselScroll.ScrollToPrev();
        }
        
        /// <summary>
        /// CenterButtonのInteractable設定
        /// </summary>
        /// <returns></returns>
        private void SetCenterButtonInteractable()
        {
            // 先にお金が足りているか確認
            bool hasMoney = false;
            bool isInteractable = false;
            string notifyText = string.Empty;

            bool isMaxTalentLevel = _controller.CardData.IsMaxTalentLevel();
            if (isMaxTalentLevel)
            {
                notifyText = TextId.Character0197.Text();
            }
            else
            {
                // 先にお金が足りているか確認
                hasMoney = _needMoney <= _ownMoney;
                notifyText = string.Empty;
                if (!hasMoney)
                {
                    notifyText = TextId.Character0203.Text();
                    _textWarning.SetTextWithCustomTag(TextId.Outgame0250.Text());
                }
                
                //itemが足りてるか確認
                bool enoughItem = _carouselScroll.EnoughTalentUpgrade();
                if (!enoughItem)
                {
                    if (hasMoney)
                    {
                        notifyText = TextId.Character0202.Text();
                        _textWarning.SetTextWithCustomTag(TextId.Outgame0249.Text());
                    }
                    else
                    {
                        notifyText = TextId.Character0220.Text();
                        _textWarning.SetTextWithCustomTag(TextId.Outgame0251.Text());
                    }
                }
                
                //アイテムとお金が足りればボタン押せる
                isInteractable = hasMoney && enoughItem;
            }

            _controller.SetCenterButtonInteractable(isInteractable, notifyText);
            _textWarning.SetActiveWithCheck(!isMaxTalentLevel && !isInteractable);

            if (isInteractable)
            {
                //点滅アニメーションスタート
                if (!_textNextAnimation.IsPlaying())
                {
                    _textNextAnimation.PlayLoop();
                }

                _buttonParticle.gameObject.SetActive(true);
                _buttonParticle.Play();
            }
            else
            {
                //点滅アニメーションストップ
                _textNextAnimation.Stop();
                _textNextCanvas.alpha = 1.0f;
                OffParticle();
                _textNextLevel.VerticalGradientColor = VerticalGradientColorType.NotAvailable;
                _textNextLevel.UpdateColor();
            }
        }
        
        /// <summary>
        /// パーティクルシステムを外からセット
        /// </summary>
        /// <param name="particle"></param>
        public void SetParticle(ParticleSystem particle)
        {
            _buttonParticle = particle;
        }

        /// <summary>
        /// パーティクル止める
        /// </summary>
        public void OffParticle()
        {
            if (_buttonParticle != null)
            {
                _buttonParticle.Stop();
                _buttonParticle.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 覚醒レベルアップボタン押下
        /// </summary>
        public void OnClickCenterButton()
        {
            int beforeTalent = _controller.CardData.TalentLevel;

            Camera viewCamera = _controller.CharacterBg.ViewerCamera;
            DialogCharacterTalentUpgrade.Open(_controller.CardData, _partsPowerUpArrow.CurrentValue, () =>
            {
                //点滅アニメーションストップ
                _textNextAnimation.Stop();
                _textNextCanvas.alpha = 1.0f;
                
                _controller.PlayTalentLevelUp(beforeTalent, () =>
                {
                    //バッジ更新
                    _controller.ApplyBadge();

                    if (_controller.CardData.IsMaxTalentLevel())
                    {
                        // 最大だったら選択画面に戻す
                        _controller.ReturnToTop();
                    }
                    else
                    {
                        // 画面初期化
                        Initialize();
                        // refs #69476 サポカ上限解放などと同様にレベルアップ演出後にデータの更新を行う
                        _controller.ReSelectCharacter();
                    }
                });
            }
            );
        }

        private void OnDestroy()
        {
            _carouselScroll.Release();
        }
    }
}