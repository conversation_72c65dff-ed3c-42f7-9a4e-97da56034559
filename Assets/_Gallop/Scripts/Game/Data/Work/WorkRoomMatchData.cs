using System.Collections.Generic;
using System.Linq;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    /// <summary>
    /// ルームマッチ関連データ
    /// </summary>
    public class WorkRoomMatchData
    {
        #region クラス定義

        /// <summary>
        /// ルームマッチ：ルーム情報
        /// </summary>
        public class RoomData : WorkPracticeRaceData.ExhibitionRaceDataBase
        {
            #region Const

            /// <summary> サーバーの観戦許可のtrue値 </summary>
            public const int SERVER_CAN_WATCH = 1;

            /// <summary> サーバーのレースシミュレートのtrue値 </summary>
            public const int SERVER_SIMULATE_DONE = 1;

            /// <summary> ルーム公開設定のtrue値 </summary>
            public const int SERVER_ALLOW_DISPLAY = 1;

            #endregion

            #region Member, Property, Const

            /// <summary> ルームId </summary>
            public int RoomId => _roomId;
            private int _roomId;

            /// <summary> 部屋名 </summary>
            public string RoomName => _roomName;
            private ObscuredString _roomName = string.Empty;

            /// <summary> メッセージ </summary>
            public string Message => _message;
            private ObscuredString _message = string.Empty;

            /// <summary> 観戦可否 </summary>
            public bool CanWatch => _canWatch;
            private ObscuredBool _canWatch;

            /// <summary> キャラランクの制限 </summary>
            public int RankRestriction => _rankRestriction;
            private ObscuredInt _rankRestriction = (int)GameDefine.FinalTrainingRank.None;

            /// <summary> キャラランク制限の種類 </summary>
            public ExhibitionRaceDefine.RankRestrictionType RankRestrictionType => (ExhibitionRaceDefine.RankRestrictionType)_rankRestrictionType.GetDecrypted();
            private ObscuredInt _rankRestrictionType = (int)ExhibitionRaceDefine.RankRestrictionType.None;

            /// <summary> プライベート枠人数 </summary>
            public int PrivateEntryNum => _privateEntryNum;
            private ObscuredInt _privateEntryNum;
            /// <summary> 現在の合計出走人数 </summary>
            public int CurrentEntryNum => _currentEntryNum;
            private ObscuredInt _currentEntryNum;
            /// <summary> 現在のプライベート枠出走人数 </summary>
            public int CurrentPrivateEntryNum => _currentPrivateEntryNum;
            private ObscuredInt _currentPrivateEntryNum;
            /// <summary> プライベート枠設定情報 </summary>
            public ExhibitionRaceDefine.PrivateSetting PrivateSetting => (ExhibitionRaceDefine.PrivateSetting)_privateSetting.GetDecrypted();
            private ObscuredInt _privateSetting;

            /// <summary> プライベート枠で出走可能かの情報(参加時に利用) </summary>
            public ExhibitionRaceDefine.PrivateEntryStatus PrivateEntryStatus => (ExhibitionRaceDefine.PrivateEntryStatus)_privateEntryStatus.GetDecrypted();
            private ObscuredInt _privateEntryStatus = (int)ExhibitionRaceDefine.PrivateEntryStatus.None;

            /// <summary> 発走時間 </summary>
            public string StartTime => _startTime;
            private ObscuredString _startTime;

            /// <summary> 発走時間 </summary>
            public long StartUnixTime { get; private set; }

            /// <summary> シミュレート済みかどうか </summary>
            public bool IsRaceSimulateDone => _isRaceSimulate;
            private ObscuredBool _isRaceSimulate = false;

            /// <summary> 公開設定 </summary>
            public bool AllowDisplay => _allowDisplay;
            private ObscuredBool _allowDisplay = false;

            /// <summary> 主催 </summary>
            public UserData HostUser => _hostUser;
            private UserData _hostUser;

            /// <summary> レースID </summary>
            public int RaceId => MasterDataManager.Instance.masterRaceInstance.Get(_raceInstanceId).RaceId;

            /// <summary> 保存ルームID </summary>
            public int SavedRoomId => _savedRoomId;
            private ObscuredInt _savedRoomId;

            /// <summary> 登録ID </summary>
            // 保存時のサーバーDB容量削減の為にセーブルームIDと登録IDの2つが必要 
            public ulong RegisterId => _registerId;
            private ObscuredULong _registerId;

            /// <summary> お気に入り </summary>
            public bool IsFavorite => _isFavorite;
            private ObscuredBool _isFavorite;

            #endregion

            #region Method

            /// <summary>
            /// サーバーのデータから作成
            /// </summary>
            /// <param name="roomInfo"></param>
            /// <param name="hostUser"></param>
            public RoomData(RoomMatchRoomInfo roomInfo, RoomMatchUser hostUser)
            {
                UpdateRoomInfo(roomInfo);

                _hostUser = new UserData(hostUser);
            }

            /// <summary>
            /// サーバーのデータから作成
            /// </summary>
            /// <param name="roomInfo"></param>
            /// <param name="hostUser"></param>
            public RoomData(RoomMatchRoomInfo roomInfo, RoomMatchUserDetail hostUser)
            {
                UpdateRoomInfo(roomInfo);

                _hostUser = new UserData(hostUser);
            }

            /// <summary>
            /// サーバーのデータから作成(RoomMatchSavedRoomInfo)
            /// </summary>
            public RoomData(RoomMatchSavedRoomInfo saveRoomInfo, RoomMatchUser hostUser)
            {
                UpdateSaveRoomInfo(saveRoomInfo);

                _hostUser = new UserData(hostUser);
            }

            /// <summary>
            /// ルーム設定情報から作成
            /// </summary>
            /// <param name="settingInfo"></param>
            public RoomData(RoomMatchHostEntrySettingInfo settingInfo)
            {
                _raceInstanceId = settingInfo.CourseSetting.FindRaceInstance().Id;
                _roomName = settingInfo.RoomName;
                _message = settingInfo.Message;

                _season = (int)settingInfo.Season;
                _weather = (int)settingInfo.Weather;
                _groundCondition = (int)settingInfo.GroundCondition;
                _motivation = (int)settingInfo.Motivation;

                _entryNum = settingInfo.EntryNum;
                _privateEntryNum = settingInfo.PrivateEntryNum;
                _privateSetting = (int)settingInfo.PrivateSetting;

                _canWatch = settingInfo.CanWatch;
                _rankRestriction = (int)settingInfo.RankRestriction;
                _rankRestrictionType = (int)settingInfo.RankRestrictionType;

                _savedRoomId = 0;
                _isFavorite = false;

                // ホストは自分
                _hostUser = new UserData(WorkDataManager.Instance.UserData);
            }

            /// <summary>
            /// サーバーのデータで更新
            /// </summary>
            /// <param name="roomInfo"></param>
            public void UpdateRoomInfo(RoomMatchRoomInfo roomInfo)
            {
                _roomId = roomInfo.room_id;
                _raceInstanceId = roomInfo.race_instance_id;
                _roomName = roomInfo.room_name;
                _message = roomInfo.message;

                _season = roomInfo.season;
                _weather = roomInfo.weather;
                _groundCondition = roomInfo.ground_condition;
                _motivation = roomInfo.motivation;

                _entryNum = roomInfo.entry_num;
                _currentEntryNum = roomInfo.current_entry_num;
                _privateSetting = roomInfo.private_entry_type;
                _privateEntryNum = roomInfo.private_entry_num;
                _currentPrivateEntryNum = roomInfo.private_current_entry_num;
                _privateEntryStatus = roomInfo.private_entry_status;

                _canWatch = (roomInfo.is_allow_watching == SERVER_CAN_WATCH);
                _allowDisplay = (roomInfo.is_allow_display == SERVER_ALLOW_DISPLAY);
                _rankRestriction = roomInfo.trained_chara_restriction;
                _rankRestrictionType = roomInfo.restriction_type;
                _startTime = roomInfo.start_time;
                StartUnixTime = TimeUtil.ToUnixTimeFromJstString(_startTime);

                _isRaceSimulate = roomInfo.status == SERVER_SIMULATE_DONE;

                _savedRoomId = 0;
                _isFavorite = false;
            }

            /// <summary>
            /// 出走人数を変更
            /// </summary>
            /// <param name="currentEntryNum"></param>
            public void UpdateCurrentEntryNum(int currentEntryNum, int currentPrivateEntryNum)
            {
                _currentEntryNum = currentEntryNum;
                _currentPrivateEntryNum = currentPrivateEntryNum;
            }

            /// <summary>
            /// サーバーのデータで更新(RoomMatchSavedRoomInfo)
            /// </summary>
            public void UpdateSaveRoomInfo(RoomMatchSavedRoomInfo roomInfo)
            {
                _raceInstanceId = roomInfo.race_instance_id;
                _roomName = roomInfo.room_name;
                _message = roomInfo.message;

                _season = roomInfo.season;
                _weather = roomInfo.weather;
                _groundCondition = roomInfo.ground_condition;
                _motivation = roomInfo.motivation;

                _entryNum = roomInfo.entry_num;
                _currentEntryNum = roomInfo.current_entry_num;
                _privateSetting = roomInfo.private_entry_type;
                _privateEntryNum = roomInfo.private_entry_num;
                _currentPrivateEntryNum = roomInfo.private_current_entry_num;

                _canWatch = roomInfo.is_allow_watching != 0;
                _rankRestriction = roomInfo.trained_chara_restriction;
                _rankRestrictionType = roomInfo.restriction_type;
                _startTime = roomInfo.start_time;
                StartUnixTime = TimeUtil.ToUnixTimeFromJstString(_startTime);

                _savedRoomId = roomInfo.saved_room_id;
                _registerId = roomInfo.register_id;
                _isFavorite = (roomInfo.favorite_flag != 0);
            }

            /// <summary>
            /// ルーム名更新
            /// </summary>
            /// <param name="roomName"></param>
            public void UpdateRoomName(string roomName)
            {
                _roomName = roomName;
            }

            /// <summary>
            /// メッセージ更新
            /// </summary>
            /// <param name="message"></param>
            public void UpdateMessage(string message)
            {
                _message = message;
            }

            /// <summary>
            /// 開始時刻更新
            /// </summary>
            /// <param name="startTime"></param>
            public void UpdateStartTime(string startTime)
            {
                _startTime = startTime;
                StartUnixTime = TimeUtil.ToUnixTimeFromJstString(_startTime);
            }

            /// <summary>
            /// 開始時刻更新
            /// (ルーム開催時のみ使用。サーバーデータから更新する場合はstringの方を使うこと)
            /// </summary>
            /// <param name="startTime"></param>
            public void UpdateStartTime(long startTime)
            {
                StartUnixTime = startTime;
            }

            /// <summary>
            /// シミュレート済みかを更新
            /// </summary>
            /// <param name="isRaceSimulate"></param>
            public void UpdateIsRaceSimulate(int status)
            {
                _isRaceSimulate = status == SERVER_SIMULATE_DONE;
            }

            /// <summary>
            /// 公開設定を変更
            /// </summary>
            public void UpdateAllowDisplay(int allowDisplay)
            {
                _allowDisplay = (allowDisplay == SERVER_ALLOW_DISPLAY);
            }

            /// <summary>
            /// お気に入り状態変更.
            /// </summary>
            public void UpdateFavoite(bool isFavorite)
            {
                _isFavorite = isFavorite;
            }

            /// <summary>
            /// コースセット取得
            /// </summary>
            /// <returns></returns>
            public MasterRaceCourseSet.RaceCourseSet GetMasterRaceCourseSet()
            {
                return MasterDataManager.Instance.masterRaceInstance.Get(_raceInstanceId)?.GetRaceCourseSetMaster();
            }

            /// <summary>
            /// 残り枠数を取得
            /// (参加時に利用)
            /// </summary>
            /// <returns></returns>
            public int GetRemainEntryNum()
            {
                if (PrivateEntryStatus == ExhibitionRaceDefine.PrivateEntryStatus.None)
                {
                    Debug.Log("ここではプライベート枠参加状態は無効です。必要ならサーバーに依頼してください。");
                }

                if (PrivateEntryStatus == ExhibitionRaceDefine.PrivateEntryStatus.Disable)
                {
                    // プライベート枠に該当しない人
                    return (EntryNum - PrivateEntryNum) - (CurrentEntryNum - CurrentPrivateEntryNum);
                }

                return EntryNum - CurrentEntryNum;
            }

            #endregion
        }

        /// <summary>
        /// ユーザー情報
        /// </summary>
        public class UserData
        {
            #region Member, Property

            /// <summary> 称号が存在しないときのid </summary>
            public const int INVALID_HONOR_ID = -1;

            /// <summary> ViewerID </summary>
            public ObscuredLong ViewerId => _viewerId;
            private ObscuredLong _viewerId;

            /// <summary> ユーザ名 </summary>
            public string UserName => _userName;
            private ObscuredString _userName;

            /// <summary> リーダーキャラのID </summary>
            public int LeaderCharaId => _leaderCharaId;
            private ObscuredInt _leaderCharaId;

            /// <summary> リーダーキャラの衣装ID </summary>
            public int LeaderCharaDressId => _leaderCharaDressId;
            private ObscuredInt _leaderCharaDressId;

            /// <summary> 称号 </summary>
            public int HonorId => _honorId;
            private ObscuredInt _honorId;

            /// <summary> ユーザの種類 </summary>
            public ExhibitionRaceDefine.UserJoinType UserJoinType => (ExhibitionRaceDefine.UserJoinType)_userJoinType.GetDecrypted();
            private ObscuredInt _userJoinType = (int)ExhibitionRaceDefine.UserJoinType.Normal;

            /// <summary> フレンド状態 </summary>
            public WorkFriendData.FriendState FriendState => (WorkFriendData.FriendState)_friendState.GetDecrypted();
            private ObscuredInt _friendState = (int)WorkFriendData.FriendState.None;

            /// <summary> 出走キャラ </summary>
            public List<RoomMatchTrainedCharaData> TraindCharaList => _traindCharaList;
            private List<RoomMatchTrainedCharaData> _traindCharaList = new List<RoomMatchTrainedCharaData>();

            /// <summary> 登録時間 </summary>
            public string RegisterTime => _registerTime;
            private ObscuredString _registerTime;

            /// <summary> 登録時間 </summary>
            public long RegisterUnixTime { get; private set; }

            #endregion

            #region Method

            /// <summary>
            /// サーバーのデータから作成(軽量版)
            /// </summary>
            public UserData(RoomMatchUser user)
            {
                _viewerId = user.viewer_id;
                _userName = user.name;
                _leaderCharaId = user.leader_chara_id;
                _leaderCharaDressId = user.leader_chara_dress_id;
                _honorId = user.honor_id;
                _userJoinType = user.join_type;
            }

            /// <summary>
            /// サーバーのデータから作成
            /// </summary>
            public UserData(RoomMatchUserDetail user)
            {
                _viewerId = user.viewer_id;
                _userName = user.name;
                _leaderCharaId = user.leader_chara_id;
                _leaderCharaDressId = user.leader_chara_dress_id;
                _userJoinType = user.join_type;

                _honorId = user.honor_id;
                _friendState = user.user_friend_state;
                _registerTime = user.register_time;
                RegisterUnixTime = TimeUtil.ToUnixTimeFromJstString(_registerTime);

                var sortedTrainedCharaList = user.trained_chara_array.OrderBy(chara => chara.member_id);
                foreach (var trainedChara in sortedTrainedCharaList)
                {
                    _traindCharaList.Add(new RoomMatchTrainedCharaData(trainedChara));
                }
            }

            /// <summary>
            /// WorkUserDataから作成
            /// </summary>
            public UserData(WorkUserData userData)
            {
                _viewerId = userData.ViewerId;
                _userName = userData.UserName;
                _leaderCharaId = userData.LeaderCharaId;
                _leaderCharaDressId = userData.LeaderDressId;
                _honorId = userData.HonorId;
            }

            #endregion
        }


        public class RoomMatchTrainedCharaData : WorkTrainedCharaData.TrainedCharaData
        {
            #region Member, Property

            /// <summary>
            /// 参加種類
            /// </summary>
            public ExhibitionRaceDefine.JoinType JoinType => (ExhibitionRaceDefine.JoinType)_joinType.GetDecrypted();
            private ObscuredInt _joinType;

            /// <summary>
            /// 出走登録順
            /// </summary>
            public int MemberId => _memberId;
            private ObscuredInt _memberId;

            #endregion

            #region Method

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="serverParam"></param>
            public RoomMatchTrainedCharaData(RoomMatchTrainedChara serverParam)
                : base(serverParam)
            {
                _joinType = serverParam.join_type;
                _memberId = serverParam.member_id;
            }

            #endregion
        }

        /// <summary>
        /// シミュレート済レース結果
        /// </summary>
        public class RaceResultData
        {
            public int RoomId => _roomId;
            private ObscuredInt _roomId;
            public RaceHorseData[] RaceHorseDataArray => _raceHorseDataArray;
            private RaceHorseData[] _raceHorseDataArray;

            public string RaceScenario => _raceScenario;
            private ObscuredString _raceScenario;

            public int RandomSeed => _randomSeed;
            private ObscuredInt _randomSeed;

            public int Season => _season;
            private ObscuredInt _season;

            public int Weather => _weather;
            private ObscuredInt _weather;

            public int GroundCondition => _groundCondition;
            private ObscuredInt _groundCondition;

            public TrainedChara[] TrainedCharaArray => _trainedCharaArray;
            private TrainedChara[] _trainedCharaArray;

            public RaceResultData(RoomMatchRaceStartResponse.CommonResponse data, int roomId)
            {
                _roomId = roomId;
                _raceHorseDataArray = data.race_horse_data_array;
                _raceScenario = data.race_scenario;
                _randomSeed = data.random_seed;
                _season = data.season;
                _weather = data.weather;
                _groundCondition = data.ground_condition;
                _trainedCharaArray = data.trained_chara_array;
            }
        }

        /// <summary>
        /// 保存済レース結果
        /// </summary>
        public class SavedRaceResultData
        {
            public RoomMatchSavedRoomInfo RaceResult => _raceResult;
            private RoomMatchSavedRoomInfo _raceResult;

            public RaceHorseData[] RaceHorseDataArray => _raceHorseDataArray;
            private RaceHorseData[] _raceHorseDataArray;

            public string RaceScenario => _raceScenario;
            private ObscuredString _raceScenario;

            public int RandomSeed => _randomSeed;
            private ObscuredInt _randomSeed;

            public int Season => _season;
            private ObscuredInt _season;

            public int Weather => _weather;
            private ObscuredInt _weather;

            public int GroundCondition => _groundCondition;
            private ObscuredInt _groundCondition;

            public TrainedChara[] TrainedCharaArray => _trainedCharaArray;
            private TrainedChara[] _trainedCharaArray;

            public SavedRaceResultData(RoomMatchGetSavedRaceResultResponse.CommonResponse data)
            {
                _raceResult = data.race_result;
                _raceHorseDataArray = data.race_horse_data_array;
                _raceScenario = data.race_scenario;
                _randomSeed = data.race_result.random_seed;
                _season = data.season;
                _weather = data.weather;
                _groundCondition = data.ground_condition;
                _trainedCharaArray = data.trained_chara_array;
            }
        }

        #endregion

        #region Member, Property

        /// <summary> 参加可能な部屋一覧 </summary>
        public List<RoomData> GuestEntryRoomList => _guestEntryRoomList;
        private List<RoomData> _guestEntryRoomList = new List<RoomData>();

        /// <summary> 現在選択中の部屋 </summary>
        public RoomData CurrentRoomData => _currentRoomData;
        private RoomData _currentRoomData = null;

        /// <summary> 現在選択中の部屋のメンバー </summary>
        public List<UserData> CurrentRoomUserList => _currentRoomUserList;
        private List<UserData> _currentRoomUserList = new List<UserData>();

        /// <summary>
        /// 参加した部屋一覧
        /// ※追加や変更の際はJoinWatchRoomNumも変更すること
        /// </summary>
        public List<RoomData> MyEntryRoomList => _myEntryRoomList;
        private List<RoomData> _myEntryRoomList = new List<RoomData>();

        /// <summary>参加・観戦しているルーム数</summary>
        public int JoinWatchRoomNum => _joinWatchRoomNum;
        private ObscuredInt _joinWatchRoomNum = 0;

        /// <summary> 種類別登録済みルーム </summary>
        public List<RoomData> RegistHostList => _registHostList;
        private List<RoomData> _registHostList = new List<RoomData>();
        public List<RoomData> RegistEntryList => _registEntryList;
        private List<RoomData> _registEntryList = new List<RoomData>();
        public List<RoomData> RegistWatchList => _registWatchList;
        private List<RoomData> _registWatchList = new List<RoomData>();

        /// <summary> 保存した部屋一覧 </summary>
        // KeyにセーブルームIDと登録ID、Valueにルーム情報（RoomData, RaceType）
        public Dictionary<System.Tuple<int, ulong>, System.Tuple<RoomData, ExhibitionRaceDefine.RoomRaceType>> SaveRoomDictionary => _saveRoomDictionary;
        private Dictionary<System.Tuple<int, ulong>, System.Tuple<RoomData, ExhibitionRaceDefine.RoomRaceType>> _saveRoomDictionary = new Dictionary<System.Tuple<int, ulong>, System.Tuple<RoomData, ExhibitionRaceDefine.RoomRaceType>>();

        /// <summary> 最新の参加登録した部屋 </summary>
        public RoomData LatestEntryRoomData => _latestEntryRoomData;
        private RoomData _latestEntryRoomData = null;

        /// <summary> ポーリング間隔 </summary>
        public float PollingInterval => _pollingInterval;
        private float _pollingInterval = 0;

        /// <summary> ポーリングにおけるルーム情報差分チェック </summary>
        public bool ChangeRoomDataWithPolling { get; private set; } = false;

        /// <summary> ポーリングにおけるユーザー差分チェック </summary>
        public bool ChangeUserWithPolling => CurrentRoomJoinUserList.Any() || CurrentRoomLeaveUserList.Any();

        /// <summary> ポーリングユーザー差分(増加) </summary>
        public List<UserData> CurrentRoomJoinUserList => _currentRoomJoinUserList;
        private List<UserData> _currentRoomJoinUserList = new List<UserData>();

        /// <summary> ポーリングユーザー差分(減少) </summary>
        public List<UserData> CurrentRoomLeaveUserList => _currentRoomLeaveUserList;
        private List<UserData> _currentRoomLeaveUserList = new List<UserData>();

        /// <summary> レース情報 </summary>
        public RaceResultData RaceResultInfo => _raceResultInfo;
        private RaceResultData _raceResultInfo = null;


        /// <summary> 保存済レース結果情報 </summary>
        public SavedRaceResultData SavedRaceResultInfo => _savedRaceResultInfo;
        private SavedRaceResultData _savedRaceResultInfo = null;

        /// <summary> 確認可能なルームがあるかの通知 </summary>
        public bool NeedNotifyRoom { get; private set; } = false;

        /// <summary> 出走登録中キャラ一覧 </summary>
        private List<ObscuredInt> _roomMatchEntryCharaIdArray = new List<ObscuredInt>();

        public bool IsReplay => _isReplay;
        private ObscuredBool _isReplay = false;

        /// <summary>やる気演出スキップ対象レースリスト（int: RoomId）</summary>
        public List<int> SkipMotivationList => _skipMotivationList;
        private List<int> _skipMotivationList = new List<int>();

        /// <summary>フレンド情報一覧（フォロー選択パネルで使用）</summary>
        public List<UserInfoAtFriend> RoomUserFriendInfoList => _roomFriendInfoList;
        private List<UserInfoAtFriend> _roomFriendInfoList = new List<UserInfoAtFriend>();

        /// <summary>ルーム検索結果。ルームに参加してるユーザー情報</summary>
        public (int RoomId, List<UserData> UserArray) RoomSearchResultUsersInfo => _roomSearchResultUsersInfo;
        private (ObscuredInt RoomId, List<UserData> UserArray) _roomSearchResultUsersInfo = (0, null);
        
        #endregion

        #region Method

        /// <summary>
        /// 出走済みキャラ一覧を更新
        /// </summary>
        /// <param name="entry_chara_id_array"></param>
        public void ApplyEntryChara(int[] entry_chara_id_array)
        {
            _roomMatchEntryCharaIdArray.Clear();
            if (entry_chara_id_array != null)
            {
                foreach(var id in entry_chara_id_array)
                {
                    _roomMatchEntryCharaIdArray.Add(id);
                }
            }
        }

        /// <summary>
        /// ルーム作成レスポンス適用
        /// </summary>
        /// <param name="response"></param>
        public void ApplyCreateRoom(RoomMatchCreateRoomResponse.CommonResponse response)
        {
            _currentRoomData = new RoomData(response.room_info, response.room_user_detail_array[0]);

            _currentRoomUserList.Clear();
            _currentRoomUserList.Add(new UserData(response.room_user_detail_array[0]));

            InitWithPolling(response.polling_interval);
        }

        /// <summary>
        /// シンプル版ルーム作成レスポンス適用
        /// </summary>
        /// <param name="response"></param>
        public void ApplyCreateRoom(RoomMatchCreateRoomSimpleResponse.CommonResponse response)
        {
            _currentRoomData = new RoomData(response.room_info, response.room_user_detail_array[0]);

            _currentRoomUserList.Clear();
            _currentRoomUserList.Add(new UserData(response.room_user_detail_array[0]));

            InitWithPolling(response.polling_interval);
        }

        /// <summary>
        /// ルーム参加登録
        /// </summary>
        /// <param name="data"></param>
        public void ApplyEntryRoom(RoomMatchEntryRoomResponse.CommonResponse response)
        {
            // 参加した部屋と参加した自身の情報
            var roomData = new RoomData(response.room_info, response.room_user_detail_array[0]);
            _latestEntryRoomData = roomData;
            AddMyEntryRoomList(roomData);
        }

        /// <summary>
        /// ルーム情報追加
        /// </summary>
        public void AddMyEntryRoomList(RoomData roomData)
        {
            _myEntryRoomList.Add(roomData);
            _joinWatchRoomNum++;// _myEntryRoomListの数とは限らないので注意

            if (_roomSearchResultUsersInfo.RoomId == roomData.RoomId)
            {
                // 検索結果が既に存在する場合がある
                _roomSearchResultUsersInfo.UserArray.Add(new UserData(WorkDataManager.Instance.UserData));
            }
        }

        /// <summary>
        /// ルーム参加情報削除
        /// </summary>
        /// <param name="roomId"></param>
        public void RemoveMyEntryRoomList(int roomId)
        {
            _myEntryRoomList.RemoveAll(d => d.RoomId == roomId);
            _joinWatchRoomNum--;

            if (_roomSearchResultUsersInfo.RoomId == roomId)
            {
                // 検索結果が既に存在する場合がある
                _roomSearchResultUsersInfo.UserArray.RemoveAll(u => u.ViewerId == Certification.ViewerId);
            }
        }

        /// <summary>
        /// 待機ルーム入室
        /// </summary>
        /// <param name="response"></param>
        public void ApplyRoomEnter(RoomMatchEnterWaitingRoomResponse.CommonResponse response)
        {
            _currentRoomUserList.Clear();
            RoomMatchUserDetail host = null;
            for (var i = 0; i < response.room_user_detail_array.Length; i++)
            {
                _currentRoomUserList.Add(new UserData(response.room_user_detail_array[i]));

                if (response.room_user_detail_array[i].viewer_id == response.room_info.host_viewer_id)
                {
                    // ホスト
                    host = response.room_user_detail_array[i];
                }
            }
            _currentRoomUserList = _currentRoomUserList.OrderBy(u => u.RegisterUnixTime).ToList();

            if (host == null)
            {
                // 万が一ホストが見つからなかった場合は部屋が存在しないエラーを出して戻る
                // (本来は起きない想定だが、サーバーの対応漏れで起きていたので念のため)
                var resultCode = GallopResultCode.ROOM_MATCH_NO_ROOM_ERROR;
                var header = TextUtil.GetMasterText(MasterString.Category.ErrorHeader, resultCode);
                var message = TextUtil.GetMasterText(MasterString.Category.ErrorMessage, resultCode);
                DialogManager.PushErrorCommon(message, header, null, GallopResultCode.OpenErrorPopupType.RoomMatch);
                return;
            }

            _currentRoomData = new RoomData(response.room_info, host);

            InitWithPolling(response.polling_interval);
        }

        /// <summary>
        /// 現在のルーム情報を更新
        /// </summary>
        /// <param name="roomInfo"></param>
        public void UpdateCurrentRoomData(RoomMatchRoomInfo roomInfo)
        {
            _currentRoomData.UpdateRoomInfo(roomInfo);
        }

        /// <summary>
        /// 現在のルーム情報を更新
        /// </summary>
        /// <param name="roomInfo"></param>
        public void UpdateCurrentRoomData(WorkRoomMatchData.RoomData roomData)
        {
            _currentRoomData = roomData;
        }

        /// <summary>
        /// ポーリング初期化
        /// </summary>
        /// <param name="pollingInterval"></param>
        private void InitWithPolling(int pollingInterval)
        {
            _pollingInterval = pollingInterval;

            _currentRoomJoinUserList.Clear();
            _currentRoomLeaveUserList.Clear();

            ChangeRoomDataWithPolling = false;
        }

        /// <summary>
        /// 待機ルーム　ポーリング用の更新
        /// </summary>
        /// <param name="response"></param>
        public void UpdateWithPolling(RoomMatchPollingResponse.CommonResponse response)
        {
            // データ初期化
            InitWithPolling(response.polling_interval);

            // 部屋差分
            if (response.room_info != null)
            {
                ChangeRoomDataWithPolling = true;
                if (response.room_info.room_name != null)
                {
                    _currentRoomData.UpdateRoomName(response.room_info.room_name);
                }
                if (response.room_info.message != null)
                {
                    _currentRoomData.UpdateMessage(response.room_info.message);
                }
                if (response.room_info.start_time != null)
                {
                    _currentRoomData.UpdateStartTime(response.room_info.start_time);
                }
                _currentRoomData.UpdateIsRaceSimulate(response.room_info.status);
                _currentRoomData.UpdateAllowDisplay(response.room_info.is_allow_display);
            }

            // ユーザ差分
            if (response.room_user_detail_array != null && response.room_user_detail_array.Length != 0)
            {
                UpdateCurrentUserAndDiff(response.room_user_detail_array);
                
                if (ChangeUserWithPolling)
                {
                    _currentRoomUserList = _currentRoomUserList.OrderBy(u => u.RegisterUnixTime).ToList();

                    _currentRoomData.UpdateCurrentEntryNum(
                        _currentRoomUserList.SelectMany(u => u.TraindCharaList).Count(),
                        _currentRoomUserList.SelectMany(u => u.TraindCharaList).Where(c => c.JoinType == ExhibitionRaceDefine.JoinType.EntryPrivate).Count()
                        );
                }
            }
        }

        /// <summary>
        /// ユーザー一覧と差分を更新
        /// </summary>
        /// <param name="userArray"></param>
        private void UpdateCurrentUserAndDiff(RoomMatchUserDetail[] userArray)
        {
            var oldUserCount = _currentRoomUserList.Count;
            var checkedUserCount = 0;

            // 入室差分チェック
            {
                var isExist = false;
                int currentUserIndex;
                for (var i = 0; i < userArray.Length; i++)
                {
                    isExist = false;
                    
                    for (currentUserIndex = 0; currentUserIndex < oldUserCount; currentUserIndex++)
                    {
                        if (userArray[i].viewer_id == _currentRoomUserList[currentUserIndex].ViewerId
                            && userArray[i].register_time == _currentRoomUserList[currentUserIndex].RegisterTime)
                        {
                            isExist = true;
                            checkedUserCount++;
                            break;
                        }
                    }

                    if (!isExist)
                    {
                        _currentRoomJoinUserList.Add(new UserData(userArray[i]));
                    }
                }
            }
            // 退室差分チェック
            if (checkedUserCount != oldUserCount)
            {
                var isExist = false;
                int responseUserIndex;
                for (var i = 0; i < oldUserCount; i++)
                {
                    isExist = false;
                    for (responseUserIndex = 0; responseUserIndex < userArray.Length; responseUserIndex++)
                    {
                        if (userArray[responseUserIndex].viewer_id == _currentRoomUserList[i].ViewerId
                            && userArray[responseUserIndex].register_time == _currentRoomUserList[i].RegisterTime)
                        {
                            isExist = true;
                            break;
                        }
                    }
                    if (!isExist)
                    {
                        _currentRoomLeaveUserList.Add(_currentRoomUserList[i]);
                    }
                }
                // 差分をデータに適用
                var leaveUserCount = _currentRoomLeaveUserList.Count;
                for (var i = 0; i < leaveUserCount; i++)
                {
                    _currentRoomUserList.RemoveAll(user => user.ViewerId == _currentRoomLeaveUserList[i].ViewerId);
                }
            }
            // 入室差分をデータに適用
            if (_currentRoomJoinUserList.Any())
            {
                var joinUserCount = _currentRoomJoinUserList.Count;
                for (var i = 0; i < joinUserCount; i++)
                {
                    _currentRoomUserList.Add(_currentRoomJoinUserList[i]);
                }
            }
        }

        /// <summary>
        /// レース情報適用
        /// </summary>
        /// <param name="data"></param>
        public void ApplyRaceStart(RoomMatchRaceStartResponse.CommonResponse data)
        {
            _raceResultInfo = new RaceResultData(data, CurrentRoomData.RoomId);
        }

        /// <summary>
        /// 保存済レース情報適用
        /// </summary>
        /// <param name="data"></param>
        public void ApplySavedRaceResult(RoomMatchGetSavedRaceResultResponse.CommonResponse data)
        {
            _savedRaceResultInfo = new SavedRaceResultData(data);
        }

        /// <summary>
        /// ユーザーリストの更新
        /// </summary>
        /// <param name="response"></param>
        public void UpdateRoomUserList(RoomMatchUserDetail[] userDetail)
        {
            _currentRoomUserList.Clear();
            for (var i = 0; i < userDetail.Length; i++)
            {
                _currentRoomUserList.Add(new UserData(userDetail[i]));
            }
            _currentRoomUserList = _currentRoomUserList.OrderBy(u => u.RegisterUnixTime).ToList();
        }

        /// <summary>
        /// ユーザーリストの更新
        /// </summary>
        public void UpdateRoomUserList(UserData[] userData)
        {
            _currentRoomUserList.Clear();
            for (var i = 0; i < userData.Length; i++)
            {
                _currentRoomUserList.Add(userData[i]);
            }
        }

        /// <summary>
        /// 参加中のルームリストの更新
        /// </summary>
        public void UpdateMyRoomEnterList(RoomMatchRoomInfo[] roomInfo, RoomMatchUser[] userInfo)
        {
            if (roomInfo == null || userInfo == null)
                return;

            // サークルではRoomMatchGetEntryRoomListResponseではないので個別の引数で対応.
            MyEntryRoomList.Clear();

            for (int i = 0; i < roomInfo.Length; i++)
            {
                var hostId = roomInfo[i].host_viewer_id;
                RoomMatchUser hostUser = null;
                for (int j = 0; j < userInfo.Length; j++)
                {
                    if (hostId == userInfo[j].viewer_id)
                    {
                        hostUser = userInfo[j];
                    }
                }
                if (hostUser != null)
                {
                    var roomData = new WorkRoomMatchData.RoomData(roomInfo[i], hostUser);
                    MyEntryRoomList.Add(roomData);
                }
            }
            _joinWatchRoomNum = MyEntryRoomList.Count;
        }

        /// <summary>
        /// サーバー情報から部屋を開催・参加・観戦に分ける.
        /// </summary>
        public void UpdateRegistRoomList(RoomMatchGetEntryRoomListResponse res)
        {
            _myEntryRoomList.Clear();
            _registHostList.Clear();
            _registEntryList.Clear();
            _registWatchList.Clear();

            for (int i = 0; i < res.data.room_info_array.Length; i++)
            {
                // 参加する部屋の情報を割り出し.
                int roomId = res.data.room_info_array[i].room_id;
                long hostId = res.data.room_info_array[i].host_viewer_id;
                RoomMatchUser hostUser = null;
                ExhibitionRaceDefine.RoomRaceType raceType = ExhibitionRaceDefine.RoomRaceType.None;
                for (int j = 0; j < res.data.room_user_array.Length; j++)
                {
                    if (roomId != res.data.room_user_array[j].room_id)
                    {
                        continue;
                    }
                    if (hostId == res.data.room_user_array[j].viewer_id)
                    {
                        hostUser = res.data.room_user_array[j];
                    }

                    if (Certification.ViewerId == hostId)
                    {
                        raceType = ExhibitionRaceDefine.RoomRaceType.Host;
                    }
                    else if (Certification.ViewerId == res.data.room_user_array[j].viewer_id)
                    {
                        if (res.data.room_user_array[j].join_type == (int)ExhibitionRaceDefine.UserJoinType.Normal)
                        {
                            raceType = ExhibitionRaceDefine.RoomRaceType.Guest;
                        }
                        else
                        {
                            raceType = ExhibitionRaceDefine.RoomRaceType.Audience;
                        }
                    }
                }
                if (hostUser == null)
                {
                    continue;
                }

                RoomData roomData = new RoomData(res.data.room_info_array[i], hostUser);
                _myEntryRoomList.Add(roomData);

                if (raceType == ExhibitionRaceDefine.RoomRaceType.Host)
                {
                    _registHostList.Add(roomData);
                }
                else if (raceType == ExhibitionRaceDefine.RoomRaceType.Guest)
                {
                    _registEntryList.Add(roomData);
                }
                else if (raceType == ExhibitionRaceDefine.RoomRaceType.Audience)
                {
                    _registWatchList.Add(roomData);
                }
            }
            _joinWatchRoomNum = _myEntryRoomList.Count;
        }

        /// <summary>
        /// NotificationInfoから更新（APIレスポンス経由）
        /// </summary>
        /// <param name="info"></param>
        public void UpdateNotification(NotificationInfo info)
        {
            NeedNotifyRoom = (info != null) && (0 < info.room_match_race_result_num);
        }

        /// <summary>
        /// 出走登録されているか
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool ContainsEntryChara(int id)
        {
            return _roomMatchEntryCharaIdArray.Contains(id);
        }

        /// リプレイフラグ更新
        /// </summary>
        public void UpdateReplayFlag(bool isReplay)
        {
            _isReplay = isReplay;
        }

        /// <summary>
        /// やる気演出スキップ対象レースリスト追加
        /// </summary>
        /// <param name="roomId"></param>
        public void AddSkipMotivationList(int roomId)
        {
            var existsRace = _skipMotivationList.Exists(x => (x == roomId));
            
            if (existsRace || (roomId == 0))
            {
                return;
            }

            _skipMotivationList.Add(roomId);
        }

        /// <summary>
        /// やる気演出スキップ対象レースリスト削除
        /// </summary>
        /// <param name="roomId"></param>
        public void RemoveSkipMotivationList(int roomId)
        {
            var existsRace = _skipMotivationList.Exists(x => x == roomId);

            if (existsRace)
            {
                _skipMotivationList.Remove(roomId);
            }
        }

        /// <summary>
        /// サーバーから取得したルームのフレンド情報適用
        /// <param name=""></param>
        public void ApplyRoomUserFriendInfoList(RoomMatchGetFriendListResponse.CommonResponse data)
        {
            _roomFriendInfoList.Clear();
            
            var friendInfoArray = data.summary_user_info_array;

            if (friendInfoArray != null)
            {
                for (int i = 0; i < friendInfoArray.Length; i++)
                {
                    _roomFriendInfoList.Add(friendInfoArray[i]);
                }
            }
        }

        /// <summary>
        /// 参加・観戦しているルーム数を更新
        /// </summary>
        /// <param name="joinWatchRoomNum"></param>
        public void UpdateJoinWatchRoomNum(int joinWatchRoomNum)
        {
            _joinWatchRoomNum = joinWatchRoomNum;
        }
        
        /// <summary>
        /// 検索結果のルームに参加してるユーザー情報適用
        /// </summary>
        /// <param name="roomId"></param>
        /// <param name="searchResultUserArray"></param>
        public void ApplyRoomSearchResultUsersInfo(int roomId, RoomMatchUser[] searchResultUserArray)
        {
            if (searchResultUserArray != null)
            {
                var userArray = new List<UserData>(searchResultUserArray.Count());
                for (int i = 0, n = searchResultUserArray.Count(); i < n; i++)
                {
                    userArray.Add(new UserData(searchResultUserArray[i]));
                }

                _roomSearchResultUsersInfo = (roomId, userArray);
            }
        } 
            
        #endregion
    }
}
