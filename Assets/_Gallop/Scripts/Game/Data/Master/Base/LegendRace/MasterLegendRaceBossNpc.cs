// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: legend_race/legend_race_boss_npc
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterLegendRaceBossNpc : AbstractMasterData
    {
        public const string TABLE_NAME = "legend_race_boss_npc";

        MasterLegendRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, LegendRaceBossNpc> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, LegendRaceBossNpc> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterLegendRaceBossNpc");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterLegendRaceBossNpc(MasterLegendRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, LegendRaceBossNpc>();
            _db = db;
        }


        public LegendRaceBossNpc Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterLegendRaceBossNpc");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterLegendRaceBossNpc", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private LegendRaceBossNpc _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_LegendRaceBossNpc();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for LegendRaceBossNpc");
                return null;
            }

            // SELECT `chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            LegendRaceBossNpc orm = null;

            if (query.Step())
            {
                int charaId                  = (int)query.GetInt(0);
                int raceDressId              = (int)query.GetInt(1);
                int nicknameId               = (int)query.GetInt(2);
                int cardRarityDataId         = (int)query.GetInt(3);
                int post                     = (int)query.GetInt(4);
                int speed                    = (int)query.GetInt(5);
                int stamina                  = (int)query.GetInt(6);
                int pow                      = (int)query.GetInt(7);
                int guts                     = (int)query.GetInt(8);
                int wiz                      = (int)query.GetInt(9);
                int properDistanceShort      = (int)query.GetInt(10);
                int properDistanceMile       = (int)query.GetInt(11);
                int properDistanceMiddle     = (int)query.GetInt(12);
                int properDistanceLong       = (int)query.GetInt(13);
                int properRunningStyleNige   = (int)query.GetInt(14);
                int properRunningStyleSenko  = (int)query.GetInt(15);
                int properRunningStyleSashi  = (int)query.GetInt(16);
                int properRunningStyleOikomi = (int)query.GetInt(17);
                int properGroundTurf         = (int)query.GetInt(18);
                int properGroundDirt         = (int)query.GetInt(19);
                int skillSetId               = (int)query.GetInt(20);

                orm = new LegendRaceBossNpc(id, charaId, raceDressId, nicknameId, cardRarityDataId, post, speed, stamina, pow, guts, wiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, skillSetId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_LegendRaceBossNpc()) {
                while (query.Step()) {
                    int id                       = (int)query.GetInt(0);
                    int charaId                  = (int)query.GetInt(1);
                    int raceDressId              = (int)query.GetInt(2);
                    int nicknameId               = (int)query.GetInt(3);
                    int cardRarityDataId         = (int)query.GetInt(4);
                    int post                     = (int)query.GetInt(5);
                    int speed                    = (int)query.GetInt(6);
                    int stamina                  = (int)query.GetInt(7);
                    int pow                      = (int)query.GetInt(8);
                    int guts                     = (int)query.GetInt(9);
                    int wiz                      = (int)query.GetInt(10);
                    int properDistanceShort      = (int)query.GetInt(11);
                    int properDistanceMile       = (int)query.GetInt(12);
                    int properDistanceMiddle     = (int)query.GetInt(13);
                    int properDistanceLong       = (int)query.GetInt(14);
                    int properRunningStyleNige   = (int)query.GetInt(15);
                    int properRunningStyleSenko  = (int)query.GetInt(16);
                    int properRunningStyleSashi  = (int)query.GetInt(17);
                    int properRunningStyleOikomi = (int)query.GetInt(18);
                    int properGroundTurf         = (int)query.GetInt(19);
                    int properGroundDirt         = (int)query.GetInt(20);
                    int skillSetId               = (int)query.GetInt(21);

                    int key = (int)id;
                    LegendRaceBossNpc orm = new LegendRaceBossNpc(id, charaId, raceDressId, nicknameId, cardRarityDataId, post, speed, stamina, pow, guts, wiz, properDistanceShort, properDistanceMile, properDistanceMiddle, properDistanceLong, properRunningStyleNige, properRunningStyleSenko, properRunningStyleSashi, properRunningStyleOikomi, properGroundTurf, properGroundDirt, skillSetId);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class LegendRaceBossNpc
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: race_dress_id) </summary>
            public readonly int RaceDressId;
            /// <summary> (CSV column: nickname_id) </summary>
            public readonly int NicknameId;
            /// <summary> (CSV column: card_rarity_data_id) </summary>
            public readonly int CardRarityDataId;
            /// <summary> (CSV column: post) </summary>
            public readonly int Post;
            /// <summary> (CSV column: speed) </summary>
            public readonly int Speed;
            /// <summary> (CSV column: stamina) </summary>
            public readonly int Stamina;
            /// <summary> (CSV column: pow) </summary>
            public readonly int Pow;
            /// <summary> (CSV column: guts) </summary>
            public readonly int Guts;
            /// <summary> (CSV column: wiz) </summary>
            public readonly int Wiz;
            /// <summary> (CSV column: proper_distance_short) </summary>
            public readonly int ProperDistanceShort;
            /// <summary> (CSV column: proper_distance_mile) </summary>
            public readonly int ProperDistanceMile;
            /// <summary> (CSV column: proper_distance_middle) </summary>
            public readonly int ProperDistanceMiddle;
            /// <summary> (CSV column: proper_distance_long) </summary>
            public readonly int ProperDistanceLong;
            /// <summary> (CSV column: proper_running_style_nige) </summary>
            public readonly int ProperRunningStyleNige;
            /// <summary> (CSV column: proper_running_style_senko) </summary>
            public readonly int ProperRunningStyleSenko;
            /// <summary> (CSV column: proper_running_style_sashi) </summary>
            public readonly int ProperRunningStyleSashi;
            /// <summary> (CSV column: proper_running_style_oikomi) </summary>
            public readonly int ProperRunningStyleOikomi;
            /// <summary> (CSV column: proper_ground_turf) </summary>
            public readonly int ProperGroundTurf;
            /// <summary> (CSV column: proper_ground_dirt) </summary>
            public readonly int ProperGroundDirt;
            /// <summary> (CSV column: skill_set_id) </summary>
            public readonly int SkillSetId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public LegendRaceBossNpc(int id = 0, int charaId = 0, int raceDressId = 0, int nicknameId = 0, int cardRarityDataId = 0, int post = 0, int speed = 0, int stamina = 0, int pow = 0, int guts = 0, int wiz = 0, int properDistanceShort = 0, int properDistanceMile = 0, int properDistanceMiddle = 0, int properDistanceLong = 0, int properRunningStyleNige = 0, int properRunningStyleSenko = 0, int properRunningStyleSashi = 0, int properRunningStyleOikomi = 0, int properGroundTurf = 0, int properGroundDirt = 0, int skillSetId = 0)
            {
                this.Id                       = id;
                this.CharaId                  = charaId;
                this.RaceDressId              = raceDressId;
                this.NicknameId               = nicknameId;
                this.CardRarityDataId         = cardRarityDataId;
                this.Post                     = post;
                this.Speed                    = speed;
                this.Stamina                  = stamina;
                this.Pow                      = pow;
                this.Guts                     = guts;
                this.Wiz                      = wiz;
                this.ProperDistanceShort      = properDistanceShort;
                this.ProperDistanceMile       = properDistanceMile;
                this.ProperDistanceMiddle     = properDistanceMiddle;
                this.ProperDistanceLong       = properDistanceLong;
                this.ProperRunningStyleNige   = properRunningStyleNige;
                this.ProperRunningStyleSenko  = properRunningStyleSenko;
                this.ProperRunningStyleSashi  = properRunningStyleSashi;
                this.ProperRunningStyleOikomi = properRunningStyleOikomi;
                this.ProperGroundTurf         = properGroundTurf;
                this.ProperGroundDirt         = properGroundDirt;
                this.SkillSetId               = skillSetId;
            }
        }
    }
}
