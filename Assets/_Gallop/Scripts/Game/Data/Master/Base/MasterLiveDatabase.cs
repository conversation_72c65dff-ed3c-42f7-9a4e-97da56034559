// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: live
// Author: ykst <yukishi<PERSON><EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterLiveDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterLiveData masterLiveData                     { get; private set; }
        public MasterLivePermissionData masterLivePermissionData { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLiveData           = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLivePermissionData = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_livePermissionData_musicId = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterLiveDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterLiveData           = new MasterLiveData(this);
            this.masterLivePermissionData = new MasterLivePermissionData(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet live database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterLiveData != null) { _selectQuery_masterLiveData.Dispose(); _selectQuery_masterLiveData = null; }
            if (_selectQuery_masterLivePermissionData != null) { _selectQuery_masterLivePermissionData.Dispose(); _selectQuery_masterLivePermissionData = null; }
            if (_indexedSelectQuery_livePermissionData_musicId != null) { _indexedSelectQuery_livePermissionData_musicId.Dispose(); _indexedSelectQuery_livePermissionData_musicId = null; }
            if (this.masterLiveData != null) { this.masterLiveData.Unload(); }
            if (this.masterLivePermissionData != null) { this.masterLivePermissionData.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for live/live_data
        
        /// <summary>
        /// SELECT `sort`,`music_type`,`title_color_top`,`title_color_bottom`,`condition_type`,`song_chara_type`,`live_member_number`,`default_main_dress`,`default_main_dress_color`,`default_mob_dress`,`default_mob_dress_color`,`backdancer_order`,`backdancer_dress`,`backdancer_dress_color`,`has_live`,`start_date`,`end_date` FROM `live_data` WHERE `music_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LiveData()
        {
            if (_selectQuery_masterLiveData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLiveData = connection.PreparedQuery("SELECT `sort`,`music_type`,`title_color_top`,`title_color_bottom`,`condition_type`,`song_chara_type`,`live_member_number`,`default_main_dress`,`default_main_dress_color`,`default_mob_dress`,`default_mob_dress_color`,`backdancer_order`,`backdancer_dress`,`backdancer_dress_color`,`has_live`,`start_date`,`end_date` FROM `live_data` WHERE `music_id`=?;");
            }
        
            return _selectQuery_masterLiveData;
        }
        
        /// <summary>
        /// SELECT `music_id`,`sort`,`music_type`,`title_color_top`,`title_color_bottom`,`condition_type`,`song_chara_type`,`live_member_number`,`default_main_dress`,`default_main_dress_color`,`default_mob_dress`,`default_mob_dress_color`,`backdancer_order`,`backdancer_dress`,`backdancer_dress_color`,`has_live`,`start_date`,`end_date` FROM `live_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LiveData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `music_id`,`sort`,`music_type`,`title_color_top`,`title_color_bottom`,`condition_type`,`song_chara_type`,`live_member_number`,`default_main_dress`,`default_main_dress_color`,`default_mob_dress`,`default_mob_dress_color`,`backdancer_order`,`backdancer_dress`,`backdancer_dress_color`,`has_live`,`start_date`,`end_date` FROM `live_data`;");
        }
        
        // SQL statements for live/live_permission_data
        
        /// <summary>
        /// SELECT * FROM `live_permission_data` WHERE `music_id`=? AND `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LivePermissionData()
        {
            if (_selectQuery_masterLivePermissionData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLivePermissionData = connection.PreparedQuery("SELECT * FROM `live_permission_data` WHERE `music_id`=? AND `chara_id`=?;");
            }
        
            return _selectQuery_masterLivePermissionData;
        }
        
        /// <summary>
        /// SELECT `chara_id` FROM `live_permission_data` WHERE `music_id`=? ORDER BY `chara_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LivePermissionData_MusicId()
        {
            if (_indexedSelectQuery_livePermissionData_musicId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_livePermissionData_musicId = connection.PreparedQuery("SELECT `chara_id` FROM `live_permission_data` WHERE `music_id`=? ORDER BY `chara_id` ASC;");
            }
        
            return _indexedSelectQuery_livePermissionData_musicId;
        }
        
        /// <summary>
        /// SELECT `music_id`,`chara_id` FROM `live_permission_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LivePermissionData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `music_id`,`chara_id` FROM `live_permission_data`;");
        }
    }
}