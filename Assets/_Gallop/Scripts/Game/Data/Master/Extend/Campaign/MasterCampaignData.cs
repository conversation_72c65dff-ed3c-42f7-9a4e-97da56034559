using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// カードマスタ
    /// </summary>
    /// <remarks>
    /// </remarks>
    public partial class MasterCampaignData
    {
        public enum TransitionType
        {
            Mission = 9999, //ミッション画面
            SingleModeTop = 10001, //育成トップ
        }
        
        public enum TargetCategory
        {
            None = 0,
            SingleMode = 1,
            DailyRace = 2, 
            LegendRace = 3,
            Mission = 4,
            //キャラストーリー解放キャンペーン
            OpenCharacterStory = 5,
            EternalMission = 6,         // 永続ミッション
            TeamStadium = 7,            // チーム競技場
            Valentine = 8,              // バレンタイン
        }

        public enum EffectType
        {
            None = 0,
            SingleReward = 1,
            RaceCount = 2,
            Difficulty = 3,
            SingleUseTP = 4,
            SingleRentalCount = 5,
            PlusBonus = 6,  // 報酬の増加
            RacePiece = 9,              // 特定のレース報酬にピースを追加
        }

        public const int SINGLE_MODE_ALL_SCENARIO_TARGET_ID = 0;
        
        public enum ImageChangeType
        {
            None = 0,
            TitleLogo = 1, //タイトルロゴを変更する
        }

        // ミッション画面で表示するロゴがリッチタイプか簡易タイプか
        public enum LogoType
        {
            None = 0,       // ロゴ判定無し
            Rich = 1,       // リッチ版
            Simple = 2,     // 簡易版
        }

        /// <summary>
        /// 現在有効なキャンペーンがあるかどうか。ダイアログ用のフラグを立てると、キャンペーンではあるがリストとして表示しなくてよいものは省く
        /// </summary>
        public bool IsExistActiveCampaign(long checkTime , bool isUseDialog = false)
        {
            var targetList = dictionary.Values.ToList();
            int count = targetList.Count;
            for (int i = 0; i < count; ++i)
            {
                var data = targetList[i];
                //ダイアログ用の判定を行う際にはダイアログ表示が不要な場合は判定からスキップ
                if (isUseDialog && data.UserShow == 0)
                {
                    continue;
                }
                
                if (data.IsInTerm(checkTime))
                {
                    return true;
                }
            }
            return false;
        }
        
        /// <summary>
        /// 現在有効なキャンペーンのデータリストの中からタイトルロゴを変更するデータを取得する。複数取得しても仕方ないので１つ見つかったら返す。
        /// </summary>
        public CampaignData GetActiveChangeTitleLogoData(long checkTime = 0)
        {
            //値が入っていない時は現在の時間を取ってくる
            checkTime = checkTime == 0 ? TimeUtil.GetServerTimeStamp() : checkTime;

            List<CampaignData> targetList = GetListWithImageChangeOrderByCampaignIdAsc((int)ImageChangeType.TitleLogo);

            int count = targetList.Count;
            List<CampaignData> activeList = new List<CampaignData>();
            for (int i = 0; i < count; ++i)
            {
                var data = targetList[i];
                if (data.IsInTerm(checkTime))
                {
                    return data;
                }
            }
            return null;
        }

        /// <summary>
        /// 現在有効なキャンペーンのデータリストを取得する。ダイアログ用のフラグを立てると、キャンペーンではあるがリストとして表示しなくてよいものは省く
        /// </summary>
        public List<CampaignData> GetActiveList(long checkTime = 0, TargetCategory categoryType = TargetCategory.None , EffectType effectType = EffectType.None, bool isUseDialog = false, int targetId = 0)
        {
            //値が入っていない時は現在の時間を取ってくる
            checkTime = checkTime == 0 ? TimeUtil.GetServerTimeStamp() : checkTime;

            IEnumerable<CampaignData> campaignData;
            if (categoryType == TargetCategory.None)
            {
                campaignData = dictionary.Values;
            }
            else
            {
                campaignData = GetListWithTargetTypeOrderByCampaignIdAsc((int) categoryType);
            }

            var activeList = new List<CampaignData>();

            foreach (var data in campaignData)
            {
                //ダイアログ用の判定を行う際にはミッションは検索対象外
                if (isUseDialog && data.UserShow == 0)
                {
                    continue;
                }

                // TargetId指定判定
                if (data.TargetId != 0 &&
                    targetId != 0 &&
                    data.TargetId != targetId)
                {
                    continue;
                }

                // EffectType指定判定
                if (effectType != EffectType.None &&
                    data.EffectType1 != (int)effectType)
                {
                    continue;
                }

                // 有効期限判定
                if (data.IsInTerm(checkTime))
                {
                    activeList.Add(data);
                }
            }

            if (categoryType == TargetCategory.None)
            {
                // カテゴリが指定されている時のルールと同じく `campaign_id` でソートを行う
                return activeList
                    .OrderBy(data => data.CampaignId)
                    .ToList();
            }

            return activeList;
        }


        /// <summary>
        /// 育成ハードモードキャンペーンリストを取得
        /// </summary>
        /// <param name="checkTime"></param>
        /// <returns></returns>
        public List<CampaignData> GetActiveSingleModeDifficultyList(long checkTime = 0)
        {
            var campaignDataList = GetActiveList(checkTime, TargetCategory.SingleMode, EffectType.Difficulty);
            return campaignDataList;
        }

        /// <summary>
        /// 育成TPキャンペーンリストを取得
        /// </summary>
        /// <param name="checkTime"></param>
        /// <returns></returns>
        public List<CampaignData> GetSingleUseTpCampaignData(long checkTime = 0)
        {
            var campaignDataList = GetActiveList(checkTime, TargetCategory.SingleMode, EffectType.SingleUseTP);
            return campaignDataList;
        }

        /// <summary>
        /// 育成レンタル回数増加キャンペーンリストを取得
        /// </summary>
        /// <param name="checkTime"></param>
        /// <returns></returns>
        public List<CampaignData> GetSingleRentalCountCampaignData(long checkTime = 0)
        {
            var campaignDataList = GetActiveList(checkTime, TargetCategory.SingleMode);
            if (!campaignDataList.Any())
            {
                // 有効なキャンペーンがなければ終了
                return null;
            }

            return campaignDataList.Where(campaign => campaign.EffectType1 == (int)EffectType.SingleRentalCount).ToList();
        }

        /// <summary>
        /// バレンタインキャンペーンを取得
        /// </summary>
        /// <param name="checkTime"></param>
        /// <returns></returns>
        public CampaignData GetValentineCampaignData(long checkTime = 0)
        {
            var campaignDataList = GetActiveList(checkTime, TargetCategory.Valentine);
            if (!campaignDataList.Any())
            {
                return null;
            }

            return campaignDataList.First();
        }

        #region 特定のレース報酬にピースを追加キャンペーン

        /// <summary>
        /// 開催されている【特定のレース報酬にピースを追加キャンペーン】のリストを取得（開催されていないなら空のリストを取得）
        /// </summary>
        public List<CampaignData> GetActiveRacePieceCampaignDataList(long checkTime = 0)
        {
            var campaignDataList = GetActiveList(checkTime, TargetCategory.SingleMode);
            campaignDataList = campaignDataList.Where(d => d.EffectType1 == (int)EffectType.RacePiece).ToList();
            return campaignDataList;
        }

        /// <summary>
        /// 開催されている【特定のレース報酬にピースを追加キャンペーン】の内部データリストを取得（開催されていないなら空のリストを取得）
        /// </summary>
        public List<MasterCampaignSingleRaceAddData.CampaignSingleRaceAddData> GetActiveRacePieceCampaignSingleRaceAddDataList(long checkTime = 0)
        {
            var outList = new List<MasterCampaignSingleRaceAddData.CampaignSingleRaceAddData>();

            var campaignDataList = GetActiveRacePieceCampaignDataList(checkTime);
            foreach (var campaignData in campaignDataList)
            {
                var raceAdditionalId = campaignData.EffectValue1; // EffectValue1 == race_additional_id

                var campaignSingleRaceAddDataList = MasterDataManager.Instance.masterCampaignSingleRaceAddData.GetListWithRaceAdditionalIdOrderByIdAsc(raceAdditionalId);
                if (campaignSingleRaceAddDataList == null || campaignSingleRaceAddDataList.Count <= 0)
                    continue;

                outList.AddRange(campaignSingleRaceAddDataList);
            }

            return outList;
        }

        /// <summary>
        /// 開催されている【特定のレース報酬にピースを追加キャンペーン】の報酬リストを取得（開催されていないなら空のリストを取得）
        /// </summary>
        public List<MasterCampaignSingleRaceAddReward.CampaignSingleRaceAddReward> GetActiveRacePieceCampaignSingleRaceAddRewardList(out int outRaceAdditionalId, int targetRaceProgramId = 0, long checkTime = 0)
        {
            var outList = new List<MasterCampaignSingleRaceAddReward.CampaignSingleRaceAddReward>();
            outRaceAdditionalId = 0;

            var campaignSingleRaceAddDataList = GetActiveRacePieceCampaignSingleRaceAddDataList(checkTime);
            if (campaignSingleRaceAddDataList == null || campaignSingleRaceAddDataList.Count <= 0)
                return outList;

            // レースの指定があるなら、そのレースのデータだけ取り出す
            if (targetRaceProgramId != 0)
            {
                campaignSingleRaceAddDataList = campaignSingleRaceAddDataList.Where(c => c.RaceProgramId == targetRaceProgramId).ToList();

                if (campaignSingleRaceAddDataList.Count <= 0)
                    return outList;
            }

            outRaceAdditionalId = campaignSingleRaceAddDataList.FirstOrDefault().RaceAdditionalId;

            foreach (var campaignSingleRaceAddData in campaignSingleRaceAddDataList)
            {
                var raceAdditionalRewardId = campaignSingleRaceAddData.RaceAdditionalRewardId;

                var campaignSingleRaceAddRewardList = MasterDataManager.Instance.masterCampaignSingleRaceAddReward.GetListWithRaceAdditionalRewardIdOrderByIdAsc(raceAdditionalRewardId);
                if (campaignSingleRaceAddRewardList == null || campaignSingleRaceAddRewardList.Count <= 0)
                    continue;

                outList.AddRange(campaignSingleRaceAddRewardList);
            }

            return outList;
        }

        #endregion

        public partial class CampaignData
        {
            public string TitleText
            {
                get
                {
                    return TextUtil.GetMasterText(MasterString.Category.CampaignTitle, CampaignId);
                }
            }
            
            /// <summary>
            /// 正式タイトル（データがない場合はTitleTextを使う）
            /// </summary>
            public string FormalTitleText
            {
                get
                {
                    var title = TextUtil.GetMasterText(MasterString.Category.CampaignFormalTitle, CampaignId);
                    if (string.IsNullOrEmpty(title))
                    {
                        return TitleText;
                    }
                    return title;
                }
            }
            
            public string ExplainText
            {
                get
                {
                    return TextUtil.GetMasterText(MasterString.Category.CampaignExplain, CampaignId);
                }
            }
            
            public bool IsInTerm(long nowTimeStamp = 0)
            {
                long stampNow = nowTimeStamp == 0 ? TimeUtil.GetServerTimeStamp() : nowTimeStamp;
                if (StartTime <= stampNow && stampNow <= EndTime)
                {
                    return true;
                }
                return false;
            }

            /// <summary>
            /// カテゴリー名を取得
            /// </summary>
            public string GetTargetCategoryText()
            {
                string categoryText = String.Empty;
                switch ((TargetCategory)TargetType)
                {
                    case TargetCategory.SingleMode:
                        categoryText = TextId.Common0273.Text();
                        break;
                    case TargetCategory.DailyRace:
                        categoryText = TextId.Race0043.Text();
                        break;
                    case TargetCategory.LegendRace:
                        categoryText = TextId.Race0072.Text();
                        break;
                    case TargetCategory.Mission:
                        categoryText = TextId.Home0011.Text();
                        break;
                    case TargetCategory.TeamStadium:
                        categoryText = TextId.Race0583.Text();
                        break;
                }
                return categoryText;
            }

            /// <summary>
            /// ロゴを表示する時にリッチな画像を使うか
            /// </summary>
            public bool IsUseRichLogo()
            {
                return (this.LogoType == (int)MasterCampaignData.LogoType.Rich);
            }
        }
    }

    /// <summary>
    /// <see cref="MasterCampaignData"/>拡張メソッド
    /// </summary>
    public static class MasterCampaignDataExtensions
    {
        /// <summary>
        /// リストから<see cref="EffectType"/>で検索して最初に見つかったものを返す。見つからなければNull
        /// </summary>
        public static MasterCampaignData.CampaignData FindByEffectType(this List<MasterCampaignData.CampaignData> self, MasterCampaignData.EffectType effectType) =>
            self.Find(data => data.EffectType1 == (int)effectType);
    }
}
