using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using Cute.Core;
using Gallop.CutIn.Cutt;
using Gallop.Model.Component;
using JetBrains.Annotations;

namespace Gallop
{
    /// <summary>
    /// Resourcesに関するパス定義を集約する。
    /// レース用の定義は、ResourcePathRaceに集約されています。
    /// ライブ用の定義は、ResourcePathLiveに集約されています。
    /// 文字列定義は可能な限りconstで定義してください。（readonlyは実行時に初期化されるため）
    /// </summary>
    public static partial class ResourcePath
    {
        private static readonly StringBuilder _stringBuilder = new StringBuilder(256);

        /// <summary>
        /// AssetBundleRootDirectoryの値を返す
        /// AssetBundleRootDirectoryに設定されたフォルダ以下がアセバンビルドの対象となる。
        /// </summary>
        /// <returns></returns>
        public static string AssetBundleRoot
        {
            get
            {
                if (Cute.Core.ProjectPrefs.IsReady)
                {
                    return Cute.Core.ProjectPrefs.GetString(Cute.AssetBundle.AssetBundleManager.AssetBundleRootDirectory);
                }
                else
                {
                    Debug.LogWarning("ProjectPrefs wait ...");
                    return "";
                }
            }
        }

        //アプリ固有リソースフォルダのフォルダ名
        public const string GallopResourcesFolderName = "_GallopResources";

        //アプリ固有プリインリソースフォルダの仮想パス
        public const string GallopPreInResourcesAssetsPath = "Assets/_Gallop/Resources/";

        //アプリ固有リソースフォルダの仮想パス
        public const string GallopResourcesAssetsPath = "Assets/" + GallopResourcesFolderName + "/";

        //Assets配下でのリソースフォルダの相対パス
        public const string BUNDLE_RESOURCES_DIRECTORY = "Bundle/Resources/";

        //アセットバンドル生成元リソースを配置するフォルダの仮想パス
        public const string BundleResourcesAssetsPath = GallopResourcesAssetsPath + BUNDLE_RESOURCES_DIRECTORY;

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// デバッグ用アセバンを配置するフォルダの仮想パス
        /// </summary>
        public const string DEBUG_RESOURCES_ROOT = GallopResourcesAssetsPath + "Bundle/Debug/Resources/";
#endif

#if UNITY_EDITOR
        //ソースリソースフォルダの仮想パス
        public const string SourceResourcesAssetsPath = "Assets/" + GallopResourcesFolderName + "/SourceResources/";
#endif

        //_GallopResourcesフォルダへのフルパスを取得する
        public static string GetGallopResourcesFullPath()
        {
            return Application.dataPath + "/" + GallopResourcesFolderName + "/";
        }

        //3Dリソースを配置するフォルダ名
        public const string Root3d = "3d/";

        //アセットバンドル生成元リソースを配置するフォルダのフルパスを取得する
        public static string GetBundleResourcesFullPath()
        {
            return Application.dataPath + "/" + GallopResourcesFolderName + "/" + BUNDLE_RESOURCES_DIRECTORY;
        }

        // ストリーミングアセットのフォルダ名
        public const string NotPreinStreamingAssetFolderName = "NotPreinResource";

        // アセバン化するストリーミングアセットフォルダの仮想パス
        public const string NotPreinStreamingAssetsPath = "Assets/StreamingAssets/" + NotPreinStreamingAssetFolderName + "/";

        // サウンド
        public const string SOUND_PATH = "Sound";

        // サウンド
        public const string NOT_PREIN_SOUND_PATH = NotPreinStreamingAssetsPath + SOUND_PATH;

        // ムービー
        public const string NOT_PREIN_MOVIE_PATH = NotPreinStreamingAssetsPath + "Movie";

        #region System
        public const string SINGLETON_PREFABS_PATH = "prefabs/manager/systemsingleton";

        public const string NOTIFICATION_PATH = "UI/Parts/Notification";

        public const string NOTICE_MISSION_CLEAR_UI_PATH = "UI/Parts/NoticeMissionClearUI";
        public const string NOTICE_ANNOUNCE_UI_PATH = "UI/Parts/NoticeAnnounceUI";

        public const string BACKGROUND_DOWNLOAD_PROGRESS_PATH = "UI/Parts/LoadingCanvas/BackgroundDownloadProgress";
        
        #endregion

        #region タイトル
        
        /// <summary>
        /// スプラッシュの画像パス。
        /// </summary>
        public const string SPLASH_BG_PATH = "Title/splash_bg";

        /// <summary>
        /// ATTのBGパス.
        /// </summary>
        public const string TITLE_LOGO_PATH = "Title/utx_obj_title_logo_umamusume";

        /// <summary>
        /// タイトルのムービーパス。
        /// </summary>
        public const string TITLE_MOVIE_FORMAT = "Title/title_movie_{0:D2}";

        public static string GetTitleMoviePath()
        {
            var format = MovieHelper.MovieFormat.H264;
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TITLE_MOVIE_FORMAT, (int)format).ToString();
        }

        #endregion

        #region shader & ImageEffect

        // シェーダーのアセバンパス
        public const string ShaderAssetPath = "shader";

        // ImageEffectのルートディレクトリ
        public const string ImageEffectRootPath = "ImageEffect/";

        #endregion

        #region LightProbe

        /// <summary>
        /// LightProbeのパスを取得する
        /// </summary>
        /// <param name="courseId">コース番号</param>
        /// <param name="season">季節</param>
        /// <param name="weather">天候</param>
        /// <param name="groundType">芝とダートどっちを走るのか</param>
        /// <returns>アセットパス</returns>
        public static string GetLightProbePath(int courseId, GameDefine.BgSeason season, RaceDefine.Weather weather, RaceDefine.Time time)
        {
            int resourceId = CourseEnvParam.MakeID(season, weather, time);
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(LightProbePath, courseId, resourceId).ToString();
        }

        /// <summary>
        /// LightProbeのパスを取得する
        /// </summary>
        /// <param name="courseId">コース番号</param>
        /// <param name="resourceId">リソースID</param>
        /// <returns>アセットパス</returns>
        public static string GetLightProbePath(int courseId, int resourceId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(LightProbePath, courseId, resourceId).ToString();
        }

        #endregion

        #region UIAnimation ( Flash, UIEffect, UIUnityAnimation )

        public const string UIANIMATION_ROOT = "UIAnimation/";
        public const string FLASH_ROOT = UIANIMATION_ROOT + "Flash/";
        public const string FLASH_COMBINE_ROOT = UIANIMATION_ROOT + "FlashCombine/";
        public const string FLASH_COMIBINE_TIMELINE_ROOT = FLASH_COMBINE_ROOT + "Timeline/";
        public const string FLASH_COMBINE_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/";
        public const string UI_EFFECT_ROOT = UIANIMATION_ROOT + "UIEffect/";
        public const string UI_UNITY_ANIMATION_ROOT = UIANIMATION_ROOT + "UIUnityAnimation/";
        public const string UI_GACHA_ANIMATION_ROOT = UI_UNITY_ANIMATION_ROOT + "Gacha/";
        public const string UI_STORY_ANIMATION_ROOT = UI_UNITY_ANIMATION_ROOT + "Story/";

        public const string COMMON_FLASH_ROOT = FLASH_ROOT + "Common/";

        public const string COMMON_FLASH_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/Common/";

        // ヘッダータイトル
        public const string HEADER_TITLE_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_header_title00";

        // キャラ名リボン
        public const string CHARA_RIBBON_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_color_ribbonbase00";

        // アイテムアイコン登場演出
        public const string ITEM_EFFECT_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_itemopen00";
        // アイテムアイコンパーティクル用
        public const string ITEM_EFFECT_PARTICLE_PATH = UI_EFFECT_ROOT + "Common/pfb_uieff_cmn_itemopen_ss_00";

        public const string COMMON_TXT_EVENT_REWARD_00 = UI_EFFECT_ROOT + "Common/pfb_uieff_cmn_txt_event_reward00";
        // ダイアログのヘッダーのきらきらエフェクト
        public const string COMMON_DIALOG_HEADER_EFFECT_PARTICLE_PATH = UI_EFFECT_ROOT + "Common/pfb_uieff_cmn_txt_event_reward01";

        // 報酬ダイアログ演出用
        public const string CONFETTI_PARTICLE_PATH = UI_EFFECT_ROOT + "Common/pfb_uieff_confetti_particle_00";

        // 選択肢演出
        public const string STORY_CHOICE_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_selectballoon00";

        // Autoボタン演出
        public const string STORY_AUTO_BUTTON_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_autobutton00";

        // 縦持ちUIの吹き出し (AnimatorController)
        public const string STORY_LOG_BALLOON_ANIMATOR = UI_STORY_ANIMATION_ROOT + "StoryLogBalloon/story_balloon_animation00";

        // 縦持ちUIの吹き出し (AnimationClip)
        public const string STORY_LOG_BALLOON_ANIMATION_CLIP = UI_STORY_ANIMATION_ROOT + "StoryLogBalloon/story_balloon_animation00_in00";
        
        //ホームの育成ボタンのエフェクト
        public const string HOME_TRAINING_BUTTON_EFFECT_NAME = UI_EFFECT_ROOT + "Home/pfb_uieff_home_btn_training_glitter_00";
        public const string HOME_TRAINING_BUTTON_TAT_NAME = FLASH_COMBINE_ROOT + "Timeline/Home/tat_home_btn_training_00";

        // ホームのショップボタンのリボンのエフェクト
        public const string HOME_SHOP_RIBBON_EFFECT_FLASH = FLASH_COMBINE_ROOT + "Action/Home/fa_home_ribbon_specialshop_btn00";

        // ガチャ
        public const string GACHA_FLASH_COMBINE_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/Gacha/";
        public const string GACHA_RESULT_CARD_ICON_FLASH_ACTION = GACHA_FLASH_COMBINE_ACTION_ROOT + "fa_gacha_charaicon_open00";
        public const string GACHA_RESULT_SUPPORT_ICON_FLASH_ACTION = GACHA_FLASH_COMBINE_ACTION_ROOT + "fa_gacha_supportcard_open00";
        public const string GACHA_TOP_WHITE_GRADATION = UI_GACHA_ANIMATION_ROOT + "Texture/utx_gacha_white_alpha_gradation00";
        public const string GACHA_STAR_ICON_FLASH = COMMON_FLASH_ROOT + "pf_fl_cmn_eff_icon_strong_star00";

        // スキルカット
        public const string SKILL_CUT_FLASH_ROOT = FLASH_ROOT + "CutIn/Skill/";

        //キャラ・サポカ強化
        public const string SUPPORT_CHARACTER_FLASH_COMBINE_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/Chara/";

        public const string SUPPORT_CARD_FLASH = SUPPORT_CHARACTER_FLASH_COMBINE_ACTION_ROOT + "fa_chara_supportcard00";
        public const string SUPPORT_CARD_LIMITBREAK_ACTION = SUPPORT_CHARACTER_FLASH_COMBINE_ACTION_ROOT + "fa_chara_limit_break00";

        public const string SUPPORTCARD_NEXT_LIMITBREAK_STONE_ANIMATION = FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_icon_limitbreak00";
        public const string SUPPORTCARD_LIMITBREAK_SCREEN_EFFECT = FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_eff_limit_break_complete00";
        
        // ヒントLv強化ダイアログタイトル
        public const string CHARACTER_CARD_HINT_LV_UP_TITLE_ACTION = SUPPORT_CHARACTER_FLASH_COMBINE_ACTION_ROOT + "fa_chara_txt_hint_lvup00";
        
        //称号獲得
        public const string HONOR_GET_FLASH = COMMON_FLASH_ROOT + "pf_fl_cmn_get_honor00";

        // トロフィー
        public const string TROPHY_GLITTER_PATH_BASE = UI_EFFECT_ROOT + "TrophyRoom/pfb_uieff_trophyroom_glitter_particle{0:D2}";
        public static string GetTrophyGlitterPath(int size)
        {
            return TextUtil.Format(TROPHY_GLITTER_PATH_BASE, size);
        }

        //解放可能エフェクト
        public const string LIMITBREAK_AVAILABLE_BUTTON_UIEFFECT = UI_EFFECT_ROOT + "Chara/pfb_uieff_chara_btn_feasible00";

        // プレゼントボックスFlashパス
        public const string COMMON_PRESENTBOX_ACTION_FLASH_PATH = COMMON_FLASH_ACTION_ROOT + "fa_cmn_presentbox00";

        // レース結果「Win/Lose/Draw」Flashパス
        public const string COMMON_RACE_RESULT_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_txt_raceresult00";
        // レース結果「Win/Lose/Draw」FlashActionパス 
        public const string COMMON_RACE_RESULT_FLASH_ACTION_PATH = COMMON_FLASH_ACTION_ROOT + "fa_cmn_txt_raceresult00";                    // Scale:70%越え向け
        public const string COMMON_RACE_RESULT_SCALE_60_TO_70_FLASH_ACTION_PATH = COMMON_FLASH_ACTION_ROOT + "fa_cmn_txt_raceresult00_02";  // Scale:60%～70%向け
        public const string COMMON_RACE_RESULT_SMALL_FLASH_ACTION_PATH = COMMON_FLASH_ACTION_ROOT + "fa_cmn_txt_raceresult00_01";           // Scale:50%向け

        // リロードアニメーション
        public const string COMMON_RELOAD_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_icon_reload00";
        
        // 着順アニメーション（マスクを必要とする部分などに使用）
        public const string COMMON_RACE_RESULT_RANK_ANIMATION = FLASH_COMBINE_ROOT + "Timeline/Common/tat_cmn_race_result_rank00";

        // 衣装変更キャラ名プレートFlash
        public const string DRESS_CHANGE_NAME_PLATE_FLASH_PATH = FLASH_ROOT + "OutGame/prefab/pf_fl_dresschange_ribbonbase00";
        #endregion

        #region Jacket

        public enum JacketSize { M, L }

        public const string JACKET_ROOT = "Live/Jacket/";
        private const string JACKET_PATH = JACKET_ROOT + "jacket_icon_{0}_{1:0000}";

        /// <summary>
        /// CDジャケット絵のパス
        /// </summary>
        public static string GetJacketPath(int id, JacketSize size = JacketSize.M)
        {
            _stringBuilder.Length = 0;

            const string strM = "m";
            const string strL = "l";
            var sizeStr = size == JacketSize.M ? strM : size == JacketSize.L ? strL : "";
            return _stringBuilder.AppendFormat(JACKET_PATH, sizeStr, id).ToString();
        }

        #endregion

        #region Atlas

        /// <summary>
        /// アトラスパス取得
        /// </summary>
        public static string GetAtlasPath(TargetAtlasType atlasType)
        {
            _stringBuilder.Length = 0;
            var atlasName = atlasType.ToString();
            _stringBuilder.AppendFormat("{0}{1}/{1}", ATLAS_ROOT_PATH, atlasName);
            return _stringBuilder.ToString();
        }

        #endregion

        #region Story

        /// <summary>
        /// ストーリーのIDの桁数
        /// </summary>
        public const int STORY_ID_LENGTH = 9;

        /// <summary>
        /// ストーリーIDの分類分けを調べるときに使用する桁数
        /// </summary>
        public const int STORY_ID_HEAD_LENGTH = 2;

        /// <summary>
        /// パートボイスの分類ID
        /// </summary>
        public const int STORY_VOICE_ID_PARTVOICE = 5;

        /// <summary>StorySceneのプレハブ</summary>
        public const string STORY_PREFAB_PATH = "Prefabs/Story/";

        /// <summary>Storyで使用するUIPartsのパス</summary>
        public const string STORY_UI_PARTS_PATH = UIPartsPath + "/Story";

        public const string MENU_DIALOG_PREFAB_PATH = STORY_UI_PARTS_PATH + "/StoryMenuDialog";

        // パラメータ変化演出パーツ
        public const string TRAINING_PARAM_CHANGE_UI = STORY_UI_PARTS_PATH + "/TrainingParamChangeUI";
        
        /// <summary> 会話ウインドウ矢印 </summary>
        private const string STORY_MESSAGE_WINDOW_ARROW_PATH = STORY_ROOT + "MessageWindow/utx_ico_messagewindow_arrow_{0:D2}";
        
        /// <summary>
        /// 会話ウインドウ矢印画像パス
        /// </summary>
        public static string GetStoryMessageWindowArrowPath(int charaId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORY_MESSAGE_WINDOW_ARROW_PATH, charaId);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// 会話ウインドウ矢印デフォルト画像パス
        /// </summary>
        public static string GetStoryMessageWindowArrowDefaultPath() => GetStoryMessageWindowArrowPath(1);

        #region Timeline Asset Files

        /// <summary>
        /// ストーリースクリプトのベースディレクトリ
        /// </summary>
        public const string STORY_TIMELINE_BASE_PATH = "Story/Data/";

        /// <summary>
        /// パドックタイムラインのベースディレクトリ
        /// </summary>
        public const string PADDOCK_TIMELINE_BASE_PATH = "Paddock/Data/";

        /// <summary>
        /// パドックのカメラ配置データパス
        /// </summary>
        public const string PADDOCK_CAMERAPARAM_PATH = PADDOCK_TIMELINE_BASE_PATH + "ast_paddock_camera_param";

        /// <summary>
        /// パドックのキャラ配置データパス
        /// </summary>
        public const string PADDOCK_CHARA_PARAM_PATH = PADDOCK_TIMELINE_BASE_PATH + "ast_paddock_chara_param";

        /// <summary>
        /// ホームタイムラインのベースディレクトリ
        /// </summary>
        public const string HOME_TIMELINE_BASE_PATH = "Home/Data/";

        /// <summary>
        /// タイムラインのリソースリストアセットの接尾語
        /// </summary>
        public const string TIMELINE_RESOURCE_LIST_SUFFIX = "_resources";

        /// <summary>
        /// ストーリタイムラインのファイルパス
        /// </summary>
        private const string STORY_TIMELINE_PATH = STORY_TIMELINE_BASE_PATH + "{0:D2}/{1:D4}/storytimeline_{2:D9}";

        /// <summary>
        /// ストーリタイムラインのテキストルビのファイルパス
        /// </summary>
        private const string STORY_TIMELINE_RUBY_PATH = STORY_TIMELINE_BASE_PATH + "{0:D2}/{1:D4}/ast_ruby_{2:D9}";

        /// <summary>
        /// ストーリタイムラインのリソースリストファイルパス
        /// </summary>
        private const string STORY_TIMELINE_RESOURCE_LIST_PATH = STORY_TIMELINE_BASE_PATH + "{0:D2}/{1:D4}/ResourceList/storytimeline_{2:D9}" + TIMELINE_RESOURCE_LIST_SUFFIX;

        /// <summary>
        /// パドックタイムラインのファイルパス
        /// </summary>
        private const string PADDOCK_CHARA_TIMELINE_PATH = PADDOCK_TIMELINE_BASE_PATH + "{0}/{1:D4}/paddocktimeline_{1:D4}_{2:D2}";

        /// <summary>
        /// パドックタイムラインのリソースリストファイルパス
        /// </summary>
        private const string PADDOCK_CHARA_TIMELINE_RESOURCE_LIST_PATH =
            PADDOCK_TIMELINE_BASE_PATH + "{0}/{1:D4}/ResourceList/paddocktimeline_{1:D4}_{2:D2}" + TIMELINE_RESOURCE_LIST_SUFFIX;

        /// <summary>
        /// パドックタイムラインのファイルパス
        /// </summary>
        private const string PADDOCK_MOB_TIMELINE_PATH = PADDOCK_TIMELINE_BASE_PATH + "{0}/paddocktimeline_type{1:D2}_{2:D2}";

        /// <summary>
        /// パドックタイムラインのリソースリストファイルパス
        /// </summary>
        private const string PADDOCK_MOB_TIMELINE_RESOURCE_LIST_PATH = PADDOCK_TIMELINE_BASE_PATH + "{0}/ResourceList/paddocktimeline_type{1:D2}_{2:D2}" + TIMELINE_RESOURCE_LIST_SUFFIX;

        /// <summary>
        /// ホームタイムラインのファイルパス
        /// </summary>
        private const string HOME_TIMELINE_PATH = HOME_TIMELINE_BASE_PATH + "{0:D5}/{1:D2}/hometimeline_{0:D5}_{1:D2}_{2:D7}";

        /// <summary>
        /// ホームタイムラインのリソースリストファイルパス
        /// </summary>
        private const string HOME_TIMELINE_RESOURCE_LIST_PATH = HOME_TIMELINE_BASE_PATH + "{0:D5}/{1:D2}/ResourceList/hometimeline_{0:D5}_{1:D2}_{2:D7}" + TIMELINE_RESOURCE_LIST_SUFFIX;

        /// <summary>
        /// ホーム足元影リソースパス
        /// </summary>
        public const string HOME_PROJECTOR_SHADOW_PATH = "Home/Shadow/pfb_home_shadow";

        /// <summary>
        /// モブのフォルダ名
        /// </summary>
        private const string MobFolderName = "Mob";

        /// <summary>
        /// キャラのフォルダ名
        /// </summary>
        private const string CharacterFolderName = "Chara";

        /// <summary>StoryIdをintからStringにするときのフォーマット。9桁</summary>
        public const string STORY_ID_FORMAT = "D9";

        /// <summary>
        /// Timelineアセットのパス
        /// </summary>
        public static string GetStoryTimelinePath(int storyId)
        {
            return GetStoryTimelinePath(storyId, STORY_TIMELINE_PATH);
        }

        /// <summary>
        /// Timelineリソースリストアセットのパス
        /// </summary>
        public static string GetStoryTimelineResourceListPath(int storyId)
        {
            return GetStoryTimelinePath(storyId, STORY_TIMELINE_RESOURCE_LIST_PATH);
        }

        /// <summary>
        /// Timelineテキストのルビのパス
        /// </summary>
        public static string GetStoryTimelineRubyDataPath(int storyId)
        {
            return GetStoryTimelinePath(storyId, STORY_TIMELINE_RUBY_PATH);
        }

        /// <summary>
        /// Timelineアセットのパス
        /// </summary>
        private static string GetStoryTimelinePath(int storyId, string format)
        {
            // storyIdを文字列で扱う時は0埋めが必要
            string idStr = storyId.ToString(STORY_ID_FORMAT);
            string typeId = idStr.Substring(0, 2);
            string subId = idStr.Substring(2, 4);

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(format, typeId, subId, storyId).ToString();
        }

        /// <summary>
        /// Timelineアセットのパス
        /// </summary>
        /// <returns></returns>
        public static string GetPaddockTimelinePath(int charaId, int mobId,
            PaddockViewControllerBase.PaddockMotivation motivation)
        {
            return GetPaddockTimelinePath(PADDOCK_CHARA_TIMELINE_PATH, PADDOCK_MOB_TIMELINE_PATH,
                charaId, mobId, motivation);
        }

        /// <summary>
        /// Timelineリソースリストのパス
        /// </summary>
        /// <returns></returns>
        public static string GetPaddockTimelineResourceListPath(int charaId, int mobId,
            PaddockViewControllerBase.PaddockMotivation motivation)
        {
            return GetPaddockTimelinePath(
                PADDOCK_CHARA_TIMELINE_RESOURCE_LIST_PATH, PADDOCK_MOB_TIMELINE_RESOURCE_LIST_PATH,
                charaId, mobId, motivation);
        }

        private static string GetPaddockTimelinePath(string charaFormat, string mobFormat, int charaId, int mobId, PaddockViewControllerBase.PaddockMotivation motivation)
        {
            string typeFolderName;
            _stringBuilder.Length = 0;

            if (charaId == ModelLoader.MOB_CHARA_ID)
            {
                typeFolderName = MobFolderName;
                // モブのtypeで分岐する
                MasterMobData.MobData mobData = MasterDataManager.Instance.masterMobData.Get(mobId);
                int mobTypeId = mobData.DefaultPersonality;

                return _stringBuilder.AppendFormat(mobFormat, typeFolderName, mobTypeId, (int)motivation).ToString();
            }
            else
            {
                typeFolderName = CharacterFolderName;

                return _stringBuilder.AppendFormat(charaFormat, typeFolderName, charaId, (int)motivation).ToString();
            }
        }

        /// <summary>
        /// Timelineアセットのパス
        /// ファイルが存在しないキャラはモブのカットを再生する
        /// </summary>
        /// <returns></returns>
        public static string GetPaddockTimelinePathDummy(int charaId, int typeId, PaddockViewControllerBase.PaddockMotivation motivation)
        {
            string typeFolderName;
            _stringBuilder.Length = 0;

            typeFolderName = MobFolderName;

            return _stringBuilder.AppendFormat(PADDOCK_MOB_TIMELINE_PATH, typeFolderName, typeId, (int)motivation).ToString();
        }

        /// <summary>
        /// Timelineリソースリストのパス
        /// ファイルが存在しないキャラはモブのカットを再生する
        /// </summary>
        /// <returns></returns>
        public static string GetPaddockTimelineResourceListPathDummy(int charaId, int typeId,
            PaddockViewControllerBase.PaddockMotivation motivation)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder
                .AppendFormat(PADDOCK_MOB_TIMELINE_RESOURCE_LIST_PATH, MobFolderName, typeId, (int) motivation)
                .ToString();
        }

        /// <summary>
        /// Timelineアセットのパス（ホーム）
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="storyId"></param>
        /// <returns></returns>
        public static string GetHomeTimelinePath(int eventId, int num, int storyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HOME_TIMELINE_PATH, eventId, num, storyId).ToString();
        }

        /// <summary>
        /// Timelineリソースリストのパス（ホーム）
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="storyId"></param>
        /// <returns></returns>
        public static string GetHomeTimelineResourceListPath(int eventId, int num, int storyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HOME_TIMELINE_RESOURCE_LIST_PATH, eventId, num, storyId).ToString();
        }

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// Timelineエディタで使うリソース（アイコン画像など）のベースパス
        /// </summary>
        public const string StoryTimelineResourceBasePath = "Assets/_Gallop/Debug/StoryTimelineEditor/Resources/";
#endif

        #endregion Timeline Asset Files

        #region Voice

        public const string StoryVoiceNamePrefix = "snd_voi_story_";

        /// <summary>
        /// ストーリーボイスファイル名の取得
        /// </summary>
        public static string GetStoryVoiceFileName(string sheetId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.Append(StoryVoiceNamePrefix).Append(sheetId).ToString();
        }

        #endregion Voice

        #region Background and EnvParam

        // MainID 4桁, SubID 5桁
        private const string BACKGROUND_ID_FORMAT = "{0:D4}_{1:D5}";

        // 背景絵のディレクトリ
        public const string BACKGROUND_BASE_PATH = "Bg/";
        public const string BACKGROUND_FORMAT = "bg_" + BACKGROUND_ID_FORMAT;
        public const string BACKGROUND_VERTICAL_LIST_PATH = BACKGROUND_BASE_PATH + "vertical_bg_6001_00000";
        public const string VERTICAL_BACKGROUND_FORMAT = "vertical_bg_" + BACKGROUND_ID_FORMAT;
        private const string BACKGROUND_PATH = BACKGROUND_BASE_PATH + BACKGROUND_FORMAT;
        private const string VERTICAL_BACKGROUND_PATH = BACKGROUND_BASE_PATH + VERTICAL_BACKGROUND_FORMAT;

        // 背景マテリアルディレクトリ
        public const string BgMaterialRoot = BACKGROUND_BASE_PATH + "Material/";
        public const string OUTGAME_BG_SHADOW_ONLY_MATERIAL_PATH = BgMaterialRoot + "BgShadowOnlyMaterial";


        // 背景Prefab
        public const string GENERIC_BG_PATH = BACKGROUND_BASE_PATH + "GenericBg";
        public const string CHARACTER_BG_PATH = BACKGROUND_BASE_PATH + "CharacterBg";

        // Storyの環境設定パス
        public const string STORY_ENV_PARAM_ROOT = "Story/Env/";
        private const string ENV_PARAM_PATH = STORY_ENV_PARAM_ROOT + "story/ast_prm_story" + BACKGROUND_ID_FORMAT;

        // 横方向の流れ絵
        public const string STORY_LOOP_BG_BASE_PATH = "Story/LoopBg/";
        public const string STORY_LOOP_BG_ID_FORMAT = "lpbg_" + BACKGROUND_ID_FORMAT + "_{2}";
        public const string STORY_LOOP_BG_PATH_FORMAT = BACKGROUND_ID_FORMAT + "/" + STORY_LOOP_BG_ID_FORMAT;

        // 奥行き方向の流れ絵
        public const string FLOW_BG_ID_FORMAT = "{0:D3}_{1:D5}_{2:D4}_{3:D2}";
        public const string STORY_FLOW_BG_BASE_PATH = "Story/FlowBg/";
        public const string STORY_FLOW_BG_PATH_FORMAT = FLOW_BG_ID_FORMAT + "/flbg_" + FLOW_BG_ID_FORMAT + "_{4:D3}";
        private const string ENV_PARAM_FLOW_BG_PATH = STORY_ENV_PARAM_ROOT + "story/ast_prm_story" + FLOW_BG_ID_FORMAT;

        // 育成会話のスチル絵
        public const string SINGLE_MODE_STILL_ID_FORMAT = "{0:D9}_{1:D2}";
        public const string SINGLE_MODE_STILL_BASE_PATH = STORY_STILL_BASE_PATH + "{0:D2}/{1:D4}/";
        public const string SINGLE_MODE_STILL_PATH_FORMAT = "singlemode_still_" + SINGLE_MODE_STILL_ID_FORMAT;
        public const string SINGLE_MODE_STILL_PRATICLE_PATH_FORMAT = "pfb_uieff_single_still{0:D2}";

        // ストーリーのスチル絵
        public const string STORY_STILL_BG_ID_FORMAT = "{0:D9}_{1:D2}";
        public const string STORY_STILL_BG_BASE_PATH = STORY_STILL_BASE_PATH + "{0:D2}/{1:D4}/{2:D9}/";
        public const string STORY_STILL_BG_PATH_FORMAT = "story_still_" + STORY_STILL_BG_ID_FORMAT;

        // 加算合成テクスチャのディレクトリ
        public const string ADDITIVE_BASE_PATH = BACKGROUND_BASE_PATH + "Additive/";
        public const string ADDITIVE_FORMAT = "additive_{0:D5}";
        public const string ADDITIVE_PATH = ADDITIVE_BASE_PATH + ADDITIVE_FORMAT;

        /// <summary>
        /// 背景絵のパス
        /// </summary>
        /// <param name="bgId">背景ID</param>
        /// <param name="subId">季節・時間差分指定用ID</param>
        public static string GetBackgroundPath(int bgId, int subId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BACKGROUND_PATH, bgId, subId).ToString();
        }

        /// <summary>
        /// 縦画面用の背景絵のパス
        /// </summary>
        /// <param name="bgId">背景ID</param>
        /// <param name="subId">季節・時間差分指定用ID</param>
        public static string GetVerticalBackgroundPath(int bgId, int subId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(VERTICAL_BACKGROUND_PATH, bgId, subId).ToString();
        }

        /// <summary>
        /// 縦画面用の背景絵のパス
        /// </summary>
        /// <param name="bgId">背景ID</param>
        /// <param name="subId">季節・時間差分指定用ID</param>
        public static string GetVerticalBackgroundPath(int bgId, GameDefine.BgSeason season = GameDefine.BgSeason.None, RaceDefine.Weather weather = RaceDefine.Weather.None, RaceDefine.Time time = RaceDefine.Time.None)
        {
            //背景IDのルールに従って、subIdを作成する。ルールはコンフル参照(https://xxxxxxxxxx/pages/viewpage.action?pageId=28539941)
            int subId = (int)season * 1000 + (int)weather * 100 + (int)time * 10;
            return GetVerticalBackgroundPath(bgId, subId);
        }

        /// <summary>
        /// 横向き流れ絵のパス
        /// </summary>
        public static string GetLoopBackgroundPath(int bgId, int subId, int index)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(STORY_LOOP_BG_BASE_PATH).AppendFormat(STORY_LOOP_BG_PATH_FORMAT, bgId, subId, index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 奥行き方向の流れ絵のパス
        /// </summary>
        /// <returns></returns>
        public static string GetFlowBackgroundPath(int bgId, int subId, int transformId, int variantId, int index)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(STORY_FLOW_BG_BASE_PATH).AppendFormat(STORY_FLOW_BG_PATH_FORMAT, bgId, subId, transformId, variantId, index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成会話のスチル絵のパス
        /// </summary>
        public static string GetSingleModeStillPath(int bgId, int subId, int transformId, int variantId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_STILL_BASE_PATH, transformId, variantId)
                .AppendFormat(SINGLE_MODE_STILL_PATH_FORMAT, bgId, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリースチル背景のパス
        /// </summary>
        public static string GetStoryStillBackgroundPath(int bgId, int subId, int transformId, int variantId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORY_STILL_BG_BASE_PATH, transformId, variantId, bgId)
                .AppendFormat(STORY_STILL_BG_PATH_FORMAT, bgId, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成会話のスチル絵に載せるキラキラエフェクトのパス
        /// </summary>
        /// <returns></returns>
        public static string GetSingleModeStillParticlePath(int id = 0)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(SINGLE_MODE_UI_EFFECT_ROOT);
            _stringBuilder.AppendFormat(SINGLE_MODE_STILL_PRATICLE_PATH_FORMAT, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーの環境設定のパス
        /// </summary>
        /// <param name="bgId"></param>
        /// <param name="bgSubId"></param>
        /// <returns></returns>
        public static string GetStoryEnvParamPath(int bgId, int bgSubId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(ENV_PARAM_PATH, bgId, bgSubId).ToString();
        }

        /// <summary>
        /// 奥行きループ背景用の環境設定のパス
        /// </summary>
        public static string GetStoryEnvParamFlowBgPath(int bgId, int bgSubId, int transformId, int variationId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(ENV_PARAM_FLOW_BG_PATH, bgId, bgSubId, transformId, variationId).ToString();
        }

        /// <summary>
        /// 加算合成ファイル名
        /// </summary>
        public static string GetAdditiveFileName(int additiveId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(ADDITIVE_FORMAT, additiveId).ToString();
        }

        /// <summary>
        /// 加算合成ファイルのパス
        /// </summary>
        public static string GetAdditivePath(int additiveId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(ADDITIVE_PATH, additiveId).ToString();
        }

#if UNITY_EDITOR
        /// <summary>
        /// AssetsからのStoryの環境設定のパス
        /// </summary>
        public static string GetStoryEnvSavePath(int bgId, int bgSubId)
        {
            //AssetBundleRoot + GetStoryEnvParamPath + .assetとなる
            GetStoryEnvParamPath(bgId, bgSubId);    //_stringBuilderに設定されている
            _stringBuilder.Insert(0, AssetBundleRoot);
            _stringBuilder.Append(".asset");
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// AssetsからのStoryの環境設定のパス
        /// </summary>
        public static string GetStoryEnvFlowBgSavePath(int bgId, int bgSubId, int transformId, int variationId)
        {
            //AssetBundleRoot + GetStoryEnvParamPath + .assetとなる
            GetStoryEnvParamFlowBgPath(bgId, bgSubId, transformId, variationId);    //_stringBuilderに設定されている
            _stringBuilder.Insert(0, AssetBundleRoot);
            _stringBuilder.Append(".asset");
            return _stringBuilder.ToString();
        }
#endif

        #endregion

        #region Flash and Effect

        /// <summary>　Storyで使用するFlashパス　</summary>
        public const string STORY_FLASH_ANIME_ROOT = FLASH_ROOT + "Story/";

        /// <summary>　シングルモードUIエフェクトルート　</summary>
        public const string SINGLE_MODE_UI_EFFECT_ROOT = UI_EFFECT_ROOT + "SingleMode/";
        /// <summary>　シングルモードFlashActionルート　</summary>
        public const string SINGLE_MODE_FLASH_COMBINE_ROOT = FLASH_COMBINE_ROOT + "Action/SingleMode/";

        /// <summary>　イベントボーナス：キャラクター登場　</summary>
        public const string SINGLE_MODE_EVENTBONUS_APPTRAINING_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_apptraining00";
        /// <summary>　イベントボーナス：コンディション解消　</summary>
        public const string SINGLE_MODE_EVENTBONUS_RESOLVE_CONDITION_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_resolvecondition00";
        /// <summary>　イベントボーナス：コンディション獲得（ネガティブ）　</summary>
        public const string SINGLE_MODE_EVENTBONUS_DN_CONDITION_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_dncondition00";
        /// <summary>　イベントボーナス：コンディション獲得（ポジティブ）　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_CONDITION_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upcondition00";
        /// <summary>　イベントボーナス：スキルレベルアップ　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_SKILL_LEVEL_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upskilllv00";
        /// <summary>　イベントボーナス：スキル獲得（ネガティブ）　</summary>
        public const string SINGLE_MODE_EVENTBONUS_DN_SKILL_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_dnskill00";
        /// <summary>　イベントボーナス：スキル獲得（ポジティブ）　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_SKILL_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upskill00";
        /// <summary>　イベントボーナス：スキル解消 </summary>
        public const string SINGLE_MODE_EVENTBONUS_RESOLVE_SKILL_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_resolve_dnskill00";
        /// <summary>　イベントボーナス：ステータス上限上昇　</summary>
        public const string SINGLE_MODE_EVENTBONUS_MAX_UP_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_maxup00";
        /// <summary>　イベントボーナス：テキスト演出（ネガティブ）　</summary>
        public const string SINGLE_MODE_EVENTBONUS_DN_EVENT_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_dnevent00";
        /// <summary>　イベントボーナス：トレーニングLvアップ　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_EVENT_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upevent00";
        /// <summary>　イベントボーナス：ヒントLvアップ　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_HINT_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_uphint00";
        /// <summary>　イベントボーナス：やる気アップ　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_MOTIVATION_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upmotivation01";
        /// <summary>　イベントボーナス：やる気ダウン　</summary>
        public const string SINGLE_MODE_EVENTBONUS_DN_MOTIVATION_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_dnmotivation01";
        /// <summary>　イベントボーナス：数字が出る演出(ネガティブ)　</summary>
        public const string SINGLE_MODE_EVENTBONUS_DN_BONUS_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_dnbonus00";
        /// <summary>　イベントボーナス：数字が出る演出(ポジティブ)　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_BONUS_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upbonus00";
        /// <summary>　イベントボーナス：適性アップ　</summary>
        public const string SINGLE_MODE_EVENTBONUS_UP_STATUS_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_upstatus00";
        
        /// <summary>　イベントボーナス：スキルプレート画像ルート　</summary>
        public const string SINGLE_MODE_EVENTBONUS_SKILL_PLATE_ROOT = SINGLE_MODE_FLASH_ROOT + "Skill/";
        /// <summary>　イベントボーナス：スキルプレート画像　</summary>
        public const string SINGLE_MODE_EVENTBONUS_SKILL_PLATE_00_PATH = SINGLE_MODE_EVENTBONUS_SKILL_PLATE_ROOT + "utx_frm_skillplate_00";
        public const string SINGLE_MODE_EVENTBONUS_SKILL_PLATE_01_PATH = SINGLE_MODE_EVENTBONUS_SKILL_PLATE_ROOT + "utx_frm_skillplate_01";
        public const string SINGLE_MODE_EVENTBONUS_SKILL_PLATE_02_PATH = SINGLE_MODE_EVENTBONUS_SKILL_PLATE_ROOT + "utx_frm_skillplate_02";
        public const string SINGLE_MODE_EVENTBONUS_SKILL_PLATE_CHALLENGEMATCH_PATH = SINGLE_MODE_EVENTBONUS_SKILL_PLATE_ROOT + "utx_frm_skillplate_challengematch_00";
        public const string SINGLE_MODE_EVENTBONUS_SKILL_PLATE_DIFFICULTY_00_PATH = SINGLE_MODE_EVENTBONUS_SKILL_PLATE_ROOT + "utx_frm_skillplate_difficulty_00";
        /// <summary>
        /// イベントボーナス：スキルプレート画像
        /// </summary>
        public static string GetEventBonusSkillPlate(MasterSkillData.SkillData skillData)
        {
            if (ChallengeMatchUtil.IsMatchBonusSkill(skillData))
            {
                return SINGLE_MODE_EVENTBONUS_SKILL_PLATE_CHALLENGEMATCH_PATH;
            }
            else if(SingleModeUtils.IsDifficultyModeAdditionSkill(skillData.Id)) //育成ハードモード追加スキル
            {
                //今のところバッドスキルのみだがタイプが増えるならばここに分岐処理を追加する
                return SINGLE_MODE_EVENTBONUS_SKILL_PLATE_DIFFICULTY_00_PATH;
            }
            else
            {
                switch ((SkillDefine.SkillRarity)skillData.Rarity)
                {
                    case SkillDefine.SkillRarity.Rarity1: return SINGLE_MODE_EVENTBONUS_SKILL_PLATE_00_PATH;
                    case SkillDefine.SkillRarity.Rarity2: return SINGLE_MODE_EVENTBONUS_SKILL_PLATE_01_PATH;
                    default: return SINGLE_MODE_EVENTBONUS_SKILL_PLATE_02_PATH;
                }
            }
        }
        
        /// <summary>　オリジナルレース演出Flash　</summary>
        public const string SINGLE_MODE_EXTRARACE_RESULT_00_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_extrarace_result00";
        public const string SINGLE_MODE_EXTRARACE_RESULT_01_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_extrarace_result01";
        /// <summary>　オリジナルレース演出タイトル画像　</summary>
        public const string SINGLE_MODE_EXTRARACE_RESULT_TITLE_PATH = SINGLE_MODE_FLASH_ROOT + "RaceChampion/utx_RaceChampion_000";
        
        /// <summary>　継承開始！演出Flash　</summary>
        public const string SINGLE_MODE_EVENT_SUCCESSION_START_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_eventbonus_succession00";

        /// <summary>　育成用見出しFlashパス　</summary>
        public const string SINGLE_MODE_MAIN_VIEW_HEADER_TITLE_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_header_title00";
        /// <summary>　トレーニングヘッダのFlashパス　</summary>
        public const string SINGLE_MODE_MAIN_VIEW_TRAINING_HEADER_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_trainingmenu_base00";

        /// <summary>　タッグトレーニングFlashパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_tagtraining_cutin00";
        /// <summary>　タッグトレーニング開始TextFlashパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_START_TEXT_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_start_tagtraining00";
        /// <summary>　タッグトレーニング成功TextFlashパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_SUCCESS_TEXT_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_txt_tagtrainingresult00";
        /// <summary>　タッグトレーニングボタン押下Flashパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_BUTTON_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_tagtraining_btn_effect00";
        /// <summary>　タッグトレーニング流線エフェクトパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_LINE_EFFECT_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_tagtraining_line_effect_00";
        /// <summary>　タッグトレーニング開始エフェクトパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_START_EFFECT_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_start_tagtraining_particle_00";
        /// <summary>　タッグトレーニング成功エフェクトパス　</summary>
        public const string SINGLE_MODE_TAG_TRAINING_SUCCESS_EFFECT_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_tagtrainingresult_particle_00";

        /// <summary>　トレーニング　サボり　Flashパス　</summary>
        public const string SINGLE_MODE_TRAINING_SABORI_00_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_sabori00";
        /// <summary>　トレーニング　サボり　Flashパス　</summary>
        public const string SINGLE_MODE_TRAINING_SABORI_01_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_sabori01";

        /// <summary>　育成会話イベントで獲得したアイテム　Flashパス　</summary>
        public const string SINGLE_MODE_EVENT_ITEM_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_fukubiki00";

        /// <summary>　育成会話イベントの最後で目標変更　Flashパス　</summary>
        public const string SINGLE_MODE_TARGET_RACE_CHANGE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_target00";

        /// <summary>　育成会話イベントの途中で目標決定　Flashパス　</summary>
        public const string SINGLE_MODE_TARGET_RACE_DECIDE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_target_decision00";
        
        /// <summary>　育成会話イベントの最後で目標削除　Flashパス　</summary>
        public const string SINGLE_MODE_TARGET_RACE_DELETE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_target_cancel00";
        
        /// <summary>
        /// ワイプに使うFlashファイル名のフォーマット
        /// </summary>
        public const string STORY_FLASH_NAME_FORMAT = "wipe_{0:D2}_{1:D2}";

        // 会話で使うFlashの接頭辞
        private const string STORY_FLASH_NAME_PREFIX = "pf_fl_story_";
        private const string STORY_VERTICAL_FLASH_NAME_PREFIX = "pf_fl_vertical_story_";

        // 画面転換Flashアニメーションのパス
        private const string STORY_FLASH_PATH = STORY_FLASH_ANIME_ROOT + STORY_FLASH_NAME_PREFIX + STORY_FLASH_NAME_FORMAT;

        // 縦画面ワイプ用Flashアニメーションのパス
        private const string STORY_VERTICAL_FLASH_PATH = STORY_FLASH_ANIME_ROOT + STORY_VERTICAL_FLASH_NAME_PREFIX + STORY_FLASH_NAME_FORMAT;

        // イベント専用Flashアニメーションのパス
        private const string STORY_EVENT_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_spevent{0:D2}_{1:D2}";

        // イベント専用エフェクトのパス
        private const string STORY_EVENT_EFFECT_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_spevent{0:D2}_{1:D2}";

        // Miniモデルの感情表現用Flashアニメーションのパス
        public const string STORY_FLASH_MINI_MODEL_EMOTION_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_wipe_emotion00";

        /// <summary>
        /// 画面転換Flashアニメーションのパス (デフォルトで縦用を表示)
        /// </summary>
        public static string GetStoryFlashPath(int id, int subId, bool isVertical = true)
        {
            // Flashのファイル名はpf_fl_story_wipe_00_00など
            // 縦ワイプだとpf_fl_vertical_story_wipe_00_00

            _stringBuilder.Length = 0;
            if (isVertical)
            {
                _stringBuilder.AppendFormat(STORY_VERTICAL_FLASH_PATH, id, subId);
            }
            else
            {
                _stringBuilder.AppendFormat(STORY_FLASH_PATH, id, subId);
            }
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 特別な会話イベントで使うFlashアニメーションのパス (初出: 育成の安心沢イベント)
        /// </summary>
        /// <returns></returns>
        public static string GetStoryEventFlashPath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORY_EVENT_FLASH_PATH, id, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 特別な会話イベントで使うエフェクトのパス (初出: 育成の安心沢イベント)
        /// </summary>
        /// <returns></returns>
        public static string GetStoryEventEffectPath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORY_EVENT_EFFECT_PATH, id, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成会話イベントで獲得したアイテム画像のパスを取得
        /// </summary>
        public static string GetStoryEventItemIconPath(string itemDir, string itemName, int itemId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(SINGLE_MODE_FLASH_ROOT).Append(itemDir).Append("/");
            _stringBuilder.AppendFormat(itemName, itemId);
            return _stringBuilder.ToString();
        }
        #endregion Flash and Effect

        #region Movie

        private const string PROLOGUE_LOGO_MOVIE_ROOT_PATH = "SingleMode/mov_prologue_logo_{0:D2}_{1:D2}";
        private const string PROLOGUE_LOGO_LOOP_MOVIE_ROOT_PATH = "SingleMode/mov_prologue_logo_loop_{0:D2}_{1:D2}";

        /// <summary>
        /// 育成プロローグで流すムービーのパス
        /// </summary>
        public static string GetPrologueLogoMoviePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PROLOGUE_LOGO_MOVIE_ROOT_PATH, id, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成プロローグで流すムービーのパス (DL用)
        /// </summary>
        public static string GetPrologueLogoMovieAssetBundlePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(MovieFolderPath);
            _stringBuilder.AppendFormat(PROLOGUE_LOGO_MOVIE_ROOT_PATH, id, subId);
            _stringBuilder.Append(Cute.Cri.MovieManager.USM_EXTENSION);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成プロローグで流すループムービーのパス
        /// </summary>
        public static string GetPrologueLogoLoopMoviePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PROLOGUE_LOGO_LOOP_MOVIE_ROOT_PATH, id, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成プロローグで流すループムービーのパス (DL用)
        /// </summary>
        public static string GetPrologueLogoLoopMovieAssetBundlePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(MovieFolderPath);
            _stringBuilder.AppendFormat(PROLOGUE_LOGO_LOOP_MOVIE_ROOT_PATH, id, subId);
            _stringBuilder.Append(Cute.Cri.MovieManager.USM_EXTENSION);
            return _stringBuilder.ToString();
        }

        private const string STORYEVENT_LOGO_MOVIE_ROOT_PATH = "StoryEvent/mov_storyevent_logo_{0:D2}_{1:D2}";
        private const string STORYEVENT_LOGO_LOOP_MOVIE_ROOT_PATH = "StoryEvent/mov_storyevent_logo_loop_{0:D2}_{1:D2}";

        /// <summary>
        /// ストーリーイベントで流すムービーのパス
        /// </summary>
        public static string GetStoryEventLogoMoviePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORYEVENT_LOGO_MOVIE_ROOT_PATH, id, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベントで流すムービーのパス（DL用）
        /// </summary>
        public static string GetStoryEventLogoMovieAssetBundlePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(MovieFolderPath);
            _stringBuilder.AppendFormat(STORYEVENT_LOGO_MOVIE_ROOT_PATH, id, subId);
            _stringBuilder.Append(Cute.Cri.MovieManager.USM_EXTENSION);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベントで流すループムービーのパス
        /// </summary>
        public static string GetStoryEventLogoLoopMoviePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORYEVENT_LOGO_LOOP_MOVIE_ROOT_PATH, id, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベントで流すループムービーのパス（DL用）
        /// </summary>
        public static string GetStoryEventLogoLoopMovieAssetBundlePath(int id, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(MovieFolderPath);
            _stringBuilder.AppendFormat(STORYEVENT_LOGO_LOOP_MOVIE_ROOT_PATH, id, subId);
            _stringBuilder.Append(Cute.Cri.MovieManager.USM_EXTENSION);
            return _stringBuilder.ToString();
        }

        #endregion Movie

        #region Extra Icon
        private const string EXTRA_ICON_FORMAT = "Story/Extra/extra_icon_{0:D5}";

        /// <summary>
        /// ストーリーのエキストラキャラのアイコン (先代トレーナー等の黒いシルエット)
        /// </summary>
        /// <param name="extraId"></param>
        /// <returns></returns>
        public static string GetStoryExtraIconPath(int extraId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EXTRA_ICON_FORMAT, extraId);
            return _stringBuilder.ToString();
        }
        #endregion Extra Icon

        #region StoryStill

        public const string STORY_STILL_BASE_PATH = "Story/Still/";

#if CYG_DEBUG && UNITY_EDITOR

        public const string STORY_STILL_SOURCE_PATH = SourceResourcesAssetsPath + STORY_STILL_BASE_PATH;

        public static string GetStoryStillImportSettingsPath(string storyId, string storyTypeId, string storyChapterId, string storyPageId, string storyOrderId)
        {
            return TextUtil.Format(
                "{0}/{2}/{3}/{1}_{4}{5}/still_settings_{1}_{4}{5}.asset",
                STORY_STILL_SOURCE_PATH, storyId, storyTypeId, storyChapterId, storyPageId, storyOrderId);
        }

#endif // CYG_DEBUG && UNITY_EDITOR

        #endregion StoryStill

        #region Tutorial Story

        public const string TUTORIAL_STORY_BASE_PATH = "Story/Tutorial/";
        private const string TUTORIAL_STORY_PATH_FORMAT = "{0:D2}/{1:D4}/tutorial_{0:D2}_{1:D4}_{2:D3}";
        private const string TUTORIAL_STORY_FILE_FORMAT = TUTORIAL_STORY_BASE_PATH + TUTORIAL_STORY_PATH_FORMAT;

        /// <summary>
        /// チュートリアル会話のTimelineアセットのパス
        /// </summary>
        public static string GetTutorialStoryTimelinePath(int storyId)
        {
            // storyIdを文字列で扱う時は0埋めが必要
            string idStr = storyId.ToString(STORY_ID_FORMAT);
            string typeId = idStr.Substring(0, 2);
            string subId = idStr.Substring(2, 4);
            string serialId = idStr.Substring(6, 3);

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TUTORIAL_STORY_FILE_FORMAT, typeId, subId, serialId).ToString();
        }

        #endregion Tutorial Story

        #region Texture

        public const string STORY_TEXTURE_BASE_PATH = "Story/Texture/";
        private const string STORY_TEXTURE_PATH_FORMAT = "{0:D2}/{1:D4}/{0:D2}{1:D4}{2:D3}/tex_story_{0:D2}{1:D4}{2:D3}_{3:D3}";
        private const string STORY_TEXTURE_FILE_FORMAT = STORY_TEXTURE_BASE_PATH + STORY_TEXTURE_PATH_FORMAT;

        public static string GetStoryTextureTimelinePath(int storyId, int sequenceNumber)
        {
            string idStr = storyId.ToString(STORY_ID_FORMAT);
            string typeId = idStr.Substring(0, 2);
            string subId = idStr.Substring(2, 4);
            string serialId = idStr.Substring(6, 3);

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(STORY_TEXTURE_FILE_FORMAT, typeId, subId, serialId, sequenceNumber).ToString();
        }

        //ショートストーリー共通矢印アイコン
        public const string STORY_MESSAGE_WINDOW_ARROW_ICON = "Story/MessageWindow/utx_ico_messagewindow_arrow_01";

        //ストーリーテロップ画像関連
        public const string TELOP_BASE_PATH = "Story/Telop/";
        private const string TELOP_TEXTURE_FILE_FORMAT = TELOP_BASE_PATH + "tex_telop_{0:D9}_{1:D3}";
        public static string GetStoryTelopTextureTimelinePath(int telopId, int sequenceNumber)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TELOP_TEXTURE_FILE_FORMAT, telopId, sequenceNumber).ToString();
        }

        #endregion

        #region デバッグ用
#if CYG_DEBUG
        /// <summary>
        /// StoryDirectで表示するカテゴリ情報
        /// </summary>
        public const string STORY_CATEGORY_INFO_PATH = "Story/story_category_info";
        public const string STORY_CATEGORY_INFO_PATH_BY_ASSETS = "Assets/_GallopResources/Bundle/Debug/Resources/Story/story_category_info.asset";
#endif
        #endregion デバッグ用

        #region アンロックレースカットイン

        //アンロックレースカットイン
        public const string STORYRACE_UNLOCKRACE_CUT = "Cutt/CutIn/OutGame/outgame_unlockrace001/outgame_unlockrace001";

        #endregion 

        #endregion Story

        #region アニメーター
        //ダウンロードするAnimationClipはAnimator内を検索してファイル名を特定するため、
        //ログイン直後にAnimatorをダウンロードできるよう、パス定義を集約しておく。

        private const string CharacterAnimatorPrefix = "anm_";   //アニメーター接頭語

        // アニメーターの起点
        public const string CharacterAnimatorRoot = "3d/Animator/";

        public const string CutInAnimatorPath = CharacterAnimatorRoot + CharacterAnimatorPrefix + "cutin_animator";

        // 会話シーン用アニメーター
        public const string EventAnimatorPath = CharacterAnimatorRoot + CharacterAnimatorPrefix + "event_animator";

        //ホーム小物用アニメーター
        public const string HOME_PROP_ANIMATOR_PATH = CharacterAnimatorRoot + CharacterAnimatorPrefix + "home_prop_animator";

        public const string CharaDrivenKeyLoacatorPath = CharacterAnimatorRoot + "DrivenkeyLocator";

        // 上下分割用マスク
        public const string UpperBodyAnimationMaskPath = CharacterAnimatorRoot + "UpperBodyAnimationMask";
        public const string LowerBodyAnimationMaskPath = CharacterAnimatorRoot + "LowerBodyAnimationMask";

        // ミニ
        public const string MINI_ANIMATOR_PATH = CharacterAnimatorRoot + CharacterAnimatorPrefix + "mini_animator";
        public const string MINI_FACIAL_LOCATOR_PATH = CharacterAnimatorRoot + "pfb_mini_facial_locator";

        #endregion

        #region アニメーション名

        public const string MirrorSuffix = "_mirror";
        public const string CharacterHomeTypeMotionMame = "anm_hom_type";
        public const string CharacterEventMotionPrefixName = "anm_eve_";
        public const string CharacterEventMotionName = CharacterEventMotionPrefixName + "chr";
        public const string CharacterEventTypeMotionName = CharacterEventMotionPrefixName + "type";

        public const string MOTION_START_STATE_SUFFIX = "_S";
        public const string MOTION_LOOP_STATE_SUFFIX = "_loop";
        public const string MOTION_END_STATE_SUFFIX = "_E";
        public const string MOTION_POSE_STATE_SUFFIX = "_pose";
        public const string MOTION_MODIFIED_START_STATE_SUFFIX = "_SL";


        public const string EVENT_MOTION_PATH_ROOT = "3d/Motion/Event/";
        public const string EVENT_MOTION_BODY_PATH_FORMAT = EVENT_MOTION_PATH_ROOT + "Body/Chara/chr{0:D4}_00/{1}";

        public const string VS_MOTION_PATH_ROOT = "3d/Motion/OutGame/Rival/";
        public const string VS_MOTION_FACIAL_FORMAT = VS_MOTION_PATH_ROOT + "Facial/Type{0:D2}/{1}";
        public const string VS_MOTION_CAMERA_FORMAT = VS_MOTION_PATH_ROOT + "Camera/Type{0:D2}/{1}";

        public const string HOME_MOTION_CLIP_NAME = CharacterHomeTypeMotionMame + "{0:00}_{1:D3}_{2}{3}";

        #endregion

        #region リップシンク
        /// <summary>リップシンクのルート</summary>
        public const string CharacterLipSyncRoot = "LipSync/";

        /// <summary>システムテキスト用リップシンクのルート</summary>
        private const string CharacterSysytemLipsyncRoot = CharacterLipSyncRoot + "System/";

        /// <summary>システムテキスト用リップシンクのパスを取得する</summary>
        public static string GetCharacterSystemLipsyncPath(int charaId, string lipsyncPath)
        {
            const string lipSyncFileFormat = CharacterSysytemLipsyncRoot + "{0:D4}/{1}";

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(lipSyncFileFormat, charaId, lipsyncPath);
            return _stringBuilder.ToString();
        }

        /// <summary>ストーリー用リップシンクのルート</summary>
        private const string StoryLipsyncRoot = CharacterLipSyncRoot + "Story/";

        /// <summary>ストーリー用リップシンクのパスを取得する</summary>
        public static string GetStoryLipSyncPath(string sheetId)
        {
            const string lipSyncFileFormat = StoryLipsyncRoot + "{0}/{1}/story_{2}";

            string typeId = sheetId.Substring(0, 2);
            string charaId = sheetId.Substring(2, 4);

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(lipSyncFileFormat, typeId, charaId, sheetId);
            return _stringBuilder.ToString();
        }

        /// <summary>ホーム会話用リップシンクのフォーマット</summary>
        private const string HOME_LIPSYNC_FILE_FORMAT = CharacterLipSyncRoot + "Home/{0}/{1}/home_{2}";

        /// <summary>ホーム会話用リップシンクのパスを取得する</summary>
        public static string GetHomeLipSyncPath(string sheetId)
        {
#if UNITY_EDITOR
            if (sheetId.Length < 8)
            {
                // エディタで編集中は手入力されるのでここに入る可能性あり
                return string.Empty;
            }
#endif

            string mainId = sheetId.Substring(0, 5);
            string subId = sheetId.Substring(6, 2);

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(HOME_LIPSYNC_FILE_FORMAT, mainId, subId, sheetId);
            return _stringBuilder.ToString();
        }

        /// <summary>チュートリアル会話用リップシンクのフォーマット</summary>
        private const string TUTORIAL_LIPSYNC_FILE_FORMAT = CharacterLipSyncRoot + "Tutorial/" + TUTORIAL_STORY_PATH_FORMAT;

        /// <summary>
        /// チュートリアル会話のリップシンクのパスを取得する
        /// </summary>
        public static string GetTutorialLipSyncPath(string sheetId)
        {
#if UNITY_EDITOR
            if (sheetId.Length < 9)
            {
                // エディタで編集中は手入力されるのでここに入る可能性あり
                return string.Empty;
            }
#endif

            string typeId = sheetId.Substring(0, 2);
            string subId = sheetId.Substring(2, 4);
            string serialId = sheetId.Substring(6, 3);

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TUTORIAL_LIPSYNC_FILE_FORMAT, typeId, subId, serialId).ToString();
        }

        #endregion リップシンク

        #region Sound

        public const string MOTION_SE_PATH_ROOT = "3d/MotionSe/";

        /// <summary>
        /// モーションに付随するSEの再生情報のパスを取得する
        /// </summary>
        public static string GetMotionSePathStoryTimeline(string motionPath)
        {
            return TextUtil.Replace(TextUtil.Replace(motionPath, "Motion", "MotionSe"), ".anim", ".asset");
        }

        #endregion

        #region Character(3D)

        public const string Character3DRoot = "3d/Chara/";
        //キャラ頭部のルートディレクトリ
        public const string CharacterHeadRoot = Root3d + "Chara/Head/";
        public const string CharacterBodyRoot = Root3d + "Chara/Body/";
        public const string CHARACTER_TAIL_ROOT = Character3DRoot + "Tail/";

        // モーションルートパス
        public const string MOTION_ROOT = Root3d + "Motion/";
        public const string EventMotionRoot = MOTION_ROOT + "Event/";

        // 会話シーン用のモーションルートパス
        public const string CharacterEventMotionRoot = MOTION_ROOT + "Event/Body/";
        public const string CharacterRaceMotionRoot = MOTION_ROOT + "RaceMain/Body/";
        public const string CharacterEventFacialMotionRoot = MOTION_ROOT + "Event/Facial/";
        public const string CharacterCompositeMaterial = Character3DRoot + "Body/bdy0001_00/Materials/TextureComposite";

        // 会話シーン用のキャラ別モーション接頭辞フォーマット
        public const string CharacterEventMotionPrefixFormat = "{0}" + CharacterEventMotionName + "{1:D4}_{2:D2}_";

        // 会話シーン用のキャラ別モーションフォーマット
        public const string CHARACTER_EVENT_MOTION_FORMAT = "{0}" + CharacterEventMotionName + "{1:D4}_{2:D2}_{3}";

        // 会話シーン用の性格別モーション接頭辞フォーマット
        public const string CHARACTER_TYPE_MOTION_FORMAT = "{0}" + CharacterEventTypeMotionName + "{1:D2}_{2}";

        // 会話シーン用のレースモーション接頭辞フォーマット
        public const string CharacterRaceMotionPrefixFormat = "{0}anm_rac_type{1:D2}_{2}";

        // 会話シーン用の接触モーションフォーマット
        public const string EVENT_PAIR_MOTION_FORMAT_S = EVENT_COMMON_MOTION_PATH + "pair/" + CharacterEventTypeMotionName + "00_{0}_{1}_S";
        public const string EVENT_PAIR_MOTION_FORMAT_L = EVENT_COMMON_MOTION_PATH + "pair/" + CharacterEventTypeMotionName + "00_{0}_{1}_L";

        public const string TrainingMotionRoot = MOTION_ROOT + "Training/";

        // 会話シーンキャラ別必須モーション接尾辞
        public static readonly ReadOnlyCollection<string> eventRequiredAnimStateSuffixes =
            Array.AsReadOnly(new string[] {
                "idle01_S",
                "idle01_S_mirror",
                "idle01_loop",
                "idle01_loop_mirror",
                "idle01_pose",
                "idle01_pose_mirror",
                "idle01_E",
                "idle01_E_mirror",
            });

        /// <summary>
        /// 目パチ設定が入ったアセット
        /// </summary>
        public const string EYEBLINK_SETTING = CharacterAnimatorRoot + "ast_eyeblink_set";

        // キャラクタ用小物ルートパス
        public const string CharacterPropRoot = Root3d + "Chara/Prop/";
        public const string CharacterToonPropRoot = Root3d + "Chara/ToonProp/";

        public const string CharaCommonTearTexutre = Character3DRoot + "common/textures/tex_chr_tear00";
        public const string MobHairRootPath = CharacterHeadRoot + "chr0001_00/pfb_chr0001_00_hair{0:000}";
        public const string AUDIENCE_HAIR_ROOT_PATH = CharacterHeadRoot + "chr0900_00/pfb_chr0900_00_hair{0:000}";

        private const string EventFacialMotionRoot = CharacterEventFacialMotionRoot + "Chara/chr{0:0000}_{1:00}/" + CharacterEventMotionName + "{0:0000}_{1:00}_{2}";

        //ランダム再生する時の耳アセットパス
        public const string RandomEarAssetPath = CharacterEventFacialMotionRoot + "Type{0:00}/anm_eve_type{0:00}_{1}_driven";

        // 涙
        public const string CHARACTER_TEARDROP_ROOT = Character3DRoot + "Common/Tear/";
        private const string CharacterTeardropAnimModelPath = CHARACTER_TEARDROP_ROOT + "tear{0:000}/pfb_chr_tear{0:000}";
        private const string CHARACTER_TEARDROP_ANIM_PATH = CharacterHeadRoot + "chr{0:0000}_{1:00}/Facial/anm_chr{0:0000}_{1:00}_tear{2:000}_00";
        private const string MOB_CHARACTER_TEARDROP_ANIM_PATH = CharacterHeadRoot + "chr0001_00/Facial/anm_chr0001_00_face{0:000}_tear{1:000}_00";
        private const string CHARACTER_EYE_HIGHLIGHT_ANIM_PATH = CharacterHeadRoot + "chr{0:0000}_{1:00}/Facial/anm_chr{0:0000}_{1:00}_facial_eye{2:00}";
        private const string MOB_CHARACTER_EYE_HIGHLIGHT_ANIM_PATH = CharacterHeadRoot + "chr0001_00/Facial/anm_chr0001_00_face{0:000}_facial_eye{1:00}";

        // FKモーション判定アセット
        public const string EVENTCHARACTER_FKMOTION_GROUP = CharacterAnimatorRoot + "ast_event_fk_group";

        // モーション入れ替えアセット
        public const string EVENTCHARACTER_MOTIONSWAP = CharacterAnimatorRoot + "ast_event_swap_motion";

        // 疑似風を設定したいモーションの情報
        public const string EVENTCHARACTER_WIND_POWER_RATE = CharacterAnimatorRoot + "ast_event_wind_power_rate";
        public const string EVENTCHARACTER_WIND_POWER_RATE_BY_ASSETS = "Assets/_GallopResources/Bundle/Resources/" + CharacterAnimatorRoot + "ast_event_wind_power_rate.asset";

        // モーションSEで共通シートを利用する衣装IDのHashSet
        public const string MOTION_SE_COMMON_DRESS_ID = CharacterAnimatorRoot + "ast_motion_se_common_dress_id";

        /// <summary>
        /// 涙モデルデータパス取得。
        /// </summary>
        public static string GetTeardropAnimModelPath(int id = 0)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CharacterTeardropAnimModelPath, id);
            return _stringBuilder.ToString();
        }
        /// <summary>
        /// キャラ別涙アニメーションデータパス取得。
        /// </summary>
        public static string GetTeardropAnimPath(CharacterBuildInfo buildInfo, int id = 0)
        {
            _stringBuilder.Length = 0;
            if (buildInfo.IsMob())
            {
                _stringBuilder.AppendFormat(MOB_CHARACTER_TEARDROP_ANIM_PATH, buildInfo.MobInfo.faceKindId, id);
                return _stringBuilder.ToString();
            }
            else
            {
                _stringBuilder.AppendFormat(CHARACTER_TEARDROP_ANIM_PATH, buildInfo.CharaId, buildInfo.HeadModelSubId, id);
                return _stringBuilder.ToString();
            }
        }
        /// <summary>
        /// ハイライトアニメーションデータパス取得
        /// </summary>
        public static string GetEyeHighlightAnimPath(CharacterBuildInfo buildInfo, int id = 0)
        {
            _stringBuilder.Length = 0;
            if (buildInfo.IsMob())
            {
                _stringBuilder.AppendFormat(MOB_CHARACTER_EYE_HIGHLIGHT_ANIM_PATH, buildInfo.MobInfo.faceKindId, id);
                return _stringBuilder.ToString();
            }
            else
            {
                _stringBuilder.AppendFormat(CHARACTER_EYE_HIGHLIGHT_ANIM_PATH, buildInfo.CharaId, buildInfo.HeadModelSubId, id);
                return _stringBuilder.ToString();
            }
        }

        /// <summary>
        /// 会話シーン用のキャラ別モーションパスの取得
        /// </summary>
        public static string GetCharacterEventOriginalMotionPath(int charaId, int sub = 0)
        {
            const string EventOriginalMotionPath = CharacterEventMotionRoot + "Chara/chr{0:0000}_{1:00}/";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EventOriginalMotionPath, charaId, sub);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 会話シーン用で使うレースモーションパスの取得
        /// </summary>
        public static string GetCharacterRaceOriginalMotionPath(int type)
        {
            const string RaceOriginalMotionPath = CharacterRaceMotionRoot + "Type{0:D2}/";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RaceOriginalMotionPath, type);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// Eventフェイシャルモーションパスを取得(性格別)
        /// </summary>
        public static string GetEventFacialTypeMotionPath(int personality, string name)
        {
            // 会話シーン用のパス
            const string CharacterEventFacialMotionTypePath = CharacterEventFacialMotionRoot + "Type{0:D2}/anm_eve_type{0:D2}_{1}_driven";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CharacterEventFacialMotionTypePath, personality, name);
            return _stringBuilder.ToString();
        }
        /// <summary>
        /// 耳のモーションパスを取得(性格別)
        /// </summary>
        public static string GetCharacterStoryEventEarTypeMotionPath(int personality, string earName)
        {
            // 会話シーン用の耳パス
            const string CharacterEventEarMotionTypePath = CharacterEventFacialMotionRoot + "Type{0:D2}/anm_eve_type{0:D2}_ear{1}_driven";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CharacterEventEarMotionTypePath, personality, earName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 会話シーン用の共通モーションのパス（会話では性格別モーションの概念が無くなった）
        /// </summary>
        public const string EVENT_COMMON_MOTION_PATH = CharacterEventMotionRoot + "Type00/";

        /// <summary>
        /// SimpleModelController用の共通モーションのパスを取得（会話では性格別モーションの概念が無くなった）
        /// </summary>
        /// <param name="motionName">モーションの名前</param>
        /// <param name="suffix">Start, Loop, Endを示す接尾辞</param>
        /// <param name="isMirror">Mirrorモーションか</param>
        public static string GetEventCommonMotionPath(string motionName, string suffix, bool isMirror)
        {
            // anm_eve_type00_で固定
            const string EVENT_COMMON_MOTION_PATH_FORMAT = EVENT_COMMON_MOTION_PATH + CharacterEventTypeMotionName + "00_{0}{1}";

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EVENT_COMMON_MOTION_PATH_FORMAT, motionName, suffix);
            if (isMirror)
            {
                // ミラーモーションなら"_mirror"を足す
                _stringBuilder.Append(MirrorSuffix);
            }

            return _stringBuilder.ToString();
        }

        /// <summary>
        /// キャラ固有モーションパス
        /// </summary>
        public const string CHARA_MOTION_PATH = CharacterEventMotionRoot + "Chara/chr{0:D4}_00/anm_eve_chr{0:D4}_00_{1}{2}";

        /// <summary>
        /// SimpleModelController用のキャラ固有モーションを取得
        /// </summary>
        public static string GetEventCharaMotionPath(int charaId, string motionName, string suffix, bool isMirror)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CHARA_MOTION_PATH, charaId, motionName, suffix);
            if (isMirror)
            {
                // ミラーモーションなら"_mirror"を足す
                _stringBuilder.Append(MirrorSuffix);
            }

            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 加算のモーションパスを取得
        /// </summary>
        /// <remarks>
        /// mirrorモーションを使う場合はmotionNameに含んだ状態で指定される
        /// </remarks>
        public static string GetEventPlusMotionPath(string motionName)
        {
            // anm_eve_type00_で固定
            const string EVENT_PLUS_MOTION_PATH_FORMAT = EVENT_COMMON_MOTION_PATH + "Plus/" + CharacterEventTypeMotionName + "00_{0}";

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EVENT_PLUS_MOTION_PATH_FORMAT, motionName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 尻尾のモーションパスを取得
        /// </summary>
        /// <remarks>
        /// mirrorモーションを使う場合はmotionNameに含んだ状態で指定される
        /// </remarks>
        public static string GetEventTailMotionPath(string motionName)
        {
            // anm_eve_type00_で固定
            const string EVENT_TAIL_MOTION_PATH_FORMAT = EVENT_COMMON_MOTION_PATH + "Tail/" + CharacterEventTypeMotionName + "00_{0}";

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EVENT_TAIL_MOTION_PATH_FORMAT, motionName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// オーバーライドモーションのパスを取得
        /// </summary>
        /// <remarks>
        /// mirrorモーションを使う場合はmotionNameに含んだ状態で指定される
        /// </remarks>
        public static string GetOverrideMotionPath(string motionName)
        {
            // anm_eve_type00_で固定
            const string EVENT_OVERRIDE_MOTION_PATH_FORMAT = EVENT_COMMON_MOTION_PATH + CharacterEventTypeMotionName + "00_{0}";

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EVENT_OVERRIDE_MOTION_PATH_FORMAT, motionName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// Homeシーン用の性格別モーションパスの取得
        /// </summary>
        /// <param name="personalityType"></param>
        /// <returns></returns>
        public static string GetCharacterHomePersonalityMotionDirectory(int personalityType)
        {
            const string MOTION_DIRECTORY_PATH = MOTION_ROOT + "Home/Body/Type{0:00}/";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MOTION_DIRECTORY_PATH, personalityType);
            return _stringBuilder.ToString();
        }

#if UNITY_EDITOR

        /// <summary>
        /// HomePropのフォルダを取得する
        /// </summary>
        /// <param name="personalityType"></param>
        /// <returns></returns>
        public static string GetPropHomePersonalityMotionDirectory(int personalityType)
        {
            const string MOTION_DIRECTORY_PATH = HomePropAnimationRoot + "Type{1:00}/";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MOTION_DIRECTORY_PATH, "Home", personalityType);
            return _stringBuilder.ToString();

        }

        /// <summary>
        /// Editor専用
        /// FbxのパスからFacialTargetデータ出力パスへ変換
        /// </summary>
        /// <param name="fbxPath"></param>
        /// <returns></returns>
        public static string GetFacialTargetPath(string fbxPath)
        {
            var lowerPath = fbxPath.ToLower();
            var filename = System.IO.Path.GetFileName(lowerPath);
            if (filename.StartsWith("mdl_") && lowerPath.EndsWith(".fbx"))
            {
                return lowerPath.Replace("mdl_", "ast_").Replace(".fbx", ".asset");
            }
            Debug.LogError("ファイル名の開始は「mdl_」、拡張子は「.fbx」でお願いします！　path=" + fbxPath);
            return "";
        }

#endif

#if UNITY_EDITOR && CYG_DEBUG
        public const string CHARACTER_IKCOLLISION_PATH = BundleResourcesAssetsPath + Character3DRoot;
        public const string CHARACTER_IKCOLLISION_HEAD_PATH = BundleResourcesAssetsPath + Character3DRoot + "Head/chr{0:D4}_{1:D2}/Ikcols/ast_chr{0:D4}_{1:D2}_ikcol00.asset";
        public const string CHARACTER_IKCOLLISION_SPECIAL_BODY_PATH = BundleResourcesAssetsPath + Character3DRoot + "Body/bdy{0:D4}_{1:D2}/Ikcols/ast_bdy{0:D4}_{1:D2}_ikcol00.asset";
        public const string CHARACTER_IKCOLLISION_COMMON_BODY_PATH = BundleResourcesAssetsPath + Character3DRoot + "Body/bdy{0:D4}_{1:D2}/Ikcols/ast_bdy{0:D4}_{1:D2}{2}_{3:D1}_ikcol00.asset";
        public const string CHARACTER_IKCOLLISION_GENDER_PATH = "_{0:D2}";

        public static string GetIKCollisionHeadAssetPath(CharacterBuildInfo info)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(CHARACTER_IKCOLLISION_HEAD_PATH, info.CharaId, info.HeadModelSubId).ToString();
        }

        public static string GetIKCollisionBodyAssetPath(CharacterBuildInfo info)
        {
            _stringBuilder.Length = 0;
            if (info.IsSpecialDress())
            {
                _stringBuilder.AppendFormat(CHARACTER_IKCOLLISION_SPECIAL_BODY_PATH, info.CharaId, info.BodyModelSubId);
            }
            else
            {
                string genderPath = info.GetBodyPartsIDStringForAdditional(info.DressElement.IsChangeByGender);
                _stringBuilder.AppendFormat(CHARACTER_IKCOLLISION_COMMON_BODY_PATH, (int)info.DressElement.BodyType, info.BodyModelSubId, genderPath, (int)info.BustType);
            }
            return _stringBuilder.ToString();
        }

#endif


        // ゼッケンのカラーアセット名
        public const string ZekkenColorPath = Character3DRoot + "Common/Zekken/ast_zekken_color";

#if UNITY_EDITOR
        public const string ZekkenColorAssetPath = BundleResourcesAssetsPath + ZekkenColorPath + ".asset";
#endif

        // ゼッケン名前描画用のフォント
        public const string ZekkenNameFont = "Fonts/Dynamic01";

        #region 体に関するパス

        public const string BodyPathBase = "bdy{0:0000}_{1:00}";
        public const string BodyPathPersonalBase = "bdy{0:0000}_{1:00}_{2:0000}";

        public const string BodyMaterialName = "mtl_bdy{0:0000}_{1:00}";

        //体汚れテクスチャパス
        private const string CharaBodyWetDirtCommonRoot = CharacterBodyRoot + "bdy{0:0000}_{1:00}/Textures/tex_bdy{0:0000}_{1:00}_";
        private const string CharaBodyPersonalWetDirtCommonRoot = CharacterBodyRoot + BodyPathPersonalBase + "/Textures/tex_" + BodyPathPersonalBase;

        public const string CharaBodyDirtRoot = CharaBodyWetDirtCommonRoot + "dirt";

        //体濡れテクスチャパス(SSR)
        public const string CharaBodyDiffuseWetRoot = CharaBodyWetDirtCommonRoot + "diff_wet";
        public const string CharaBodyMultiBaseWetRoot = CharaBodyWetDirtCommonRoot + "base_wet";
        public const string CharaBodyMultiOptionWetRoot = CharaBodyWetDirtCommonRoot + "ctrl_wet";
        public const string CharaBodyToonWetRoot = CharaBodyWetDirtCommonRoot + "shad_c_wet";
        public const string CharaBodyEmissiveWetRoot = CharaBodyWetDirtCommonRoot + "emi_wet";

        //体濡れテクスチャパス(SSR、体操服以外)
        public const string CharaGeneralBodyDiffuseWetRoot = CharaBodyWetDirtCommonRoot + "{2:00}_{3:0}_{4:0}_diff_wet";
        public const string CharaGeneralBodyMultiBaseWetRoot = CharaBodyWetDirtCommonRoot + "{2:00}_{3:0}_{4:0}_base_wet";
        public const string CharaGeneralBodyMultiOptionWetRoot = CharaBodyWetDirtCommonRoot + "{2:00}_{3:0}_{4:0}_ctrl_wet";
        public const string CharaGeneralBodyToonWetRoot = CharaBodyWetDirtCommonRoot + "{2:00}_{3:0}_{4:0}_shad_c_wet";
        public const string CharaGeneralBodyEmissiveWetRoot = CharaBodyWetDirtCommonRoot + "{2:00}_{3:0}_{4:0}_emi_wet";

        //個人用、体濡れテクスチャパス(汎用服)
        public const string CharaGeneralBodyPersonalDiffuseWetRoot = CharaBodyPersonalWetDirtCommonRoot + "{3:00}_{4:0}_{5:0}_diff_wet";
        public const string CharaGeneralBodyPersonalMultiBaseWetRoot = CharaBodyPersonalWetDirtCommonRoot + "{3:00}_{4:0}_{5:0}_base_wet";
        public const string CharaGeneralBodyPersonalMultiOptionWetRoot = CharaBodyPersonalWetDirtCommonRoot + "{3:00}_{4:0}_{5:0}_ctrl_wet";
        public const string CharaGeneralBodyPersonalToonWetRoot = CharaBodyPersonalWetDirtCommonRoot + "{3:00}_{4:0}_{5:0}_shad_c_wet";
        public const string CharaGeneralBodyPersonalEmissiveWetRoot = CharaBodyPersonalWetDirtCommonRoot + "{3:00}_{4:0}_{5:0}_emi_wet";

        //汚れテクスチャパス(SSR、体操服以外)
        public const string CharaGeneralBodyDirtRoot = CharaBodyWetDirtCommonRoot + "{2:D2}_0_0_dirt";

        //個人、汚れテクスチャパス(汎用)
        public const string CharaGeneralBodyPersonalDirtRoot = CharaBodyPersonalWetDirtCommonRoot + "00_0_0_dirt";

        //体操服テクスチャパス
        public const string TrackSuitZekkenTexturePath = CharacterBodyRoot + BodyPathBase + "/Textures/zekken/tex_" + BodyPathBase + "_zekken{2:0}_{3:0}{4}";
        public const string TrackSuitZekkenNumberTexturePath = CharacterBodyRoot + "bdy{0:0000}_00/Textures/num/tex_bdy{0:0000}_00_num{1:00}";
        public const string TrackSuitFrameTexturePath = CharacterBodyRoot + BodyPathBase + "/Textures/offline/tex_" + BodyPathBase + "_00_waku{2:0}{3}";
        public const string TrackSuitSkinTexturePath = CharacterBodyRoot + BodyPathBase + "/Textures/offline/tex_" + BodyPathBase + "_00_{2:0}_{3:0}_{4:00}{5}";
        public const string TrackSuitBodyMaterialName = BodyMaterialName + "_{2}";

        //個人用、体操服テクスチャパス
        public const string TrackSuitSkinPersonalTexturePath = CharacterBodyRoot + BodyPathPersonalBase + "/Textures/offline/tex_" + BodyPathPersonalBase + "_00_{3:0}_{4:0}_{5:00}{6}";

        //バックダンサーテクスチャパス
        public const string BackDancerTexturePath = CharacterBodyRoot + BodyPathBase + "/Textures/tex_" + BodyPathBase + "_00_{2:0}_{3:0}_{4:00}{5}";

        #endregion

        #region 頭に関するパス

        public const string HEADMATERIAL_NAME = "mtl_chr{0:0000}_{1:00}";

        //汚れテクスチャのパス
        private const string CharaHeadWetDirtCommonFileName = "tex_chr{0:0000}_{1:00}_";
        public const string CharaHeadWetDirtCommonRootPath = CharacterHeadRoot + "chr{0:0000}_{1:00}/Textures/";
        public const string CharaHeadWetDirtCommonRoot = CharaHeadWetDirtCommonRootPath + CharaHeadWetDirtCommonFileName;

        public const string CharaHeadDirtRoot = CharaHeadWetDirtCommonRoot + "face_dirt";

        public const string CharaMobHeadDirtRoot = CharaHeadWetDirtCommonRootPath + "Face/" + CharaHeadWetDirtCommonFileName + "face{2:000}_0_dirt";

        public const string CharaFaceToonWetRoot = CharaHeadWetDirtCommonRoot + "face_" + "shad_c_wet";
        public const string CharaHairToonWetRoot = CharaHeadWetDirtCommonRoot + "hair_" + "shad_c_wet";
        public const string CharaMobFaceToonWetRoot = CharaHeadWetDirtCommonRootPath + "Face/" + CharaHeadWetDirtCommonFileName + "face{2:000}_{3:0}_shad_c_wet";
        public const string CharaMobHairToonWetRoot = CharaHeadWetDirtCommonRootPath + "Hair/" + CharaHeadWetDirtCommonFileName + "hair{2:000}_0_shad_c_wet";


        //濡れテクスチャのパス
        public const string CharaHeadWetRoot = CharaHeadWetDirtCommonRoot + "face_diff_wet";
        public const string CharaHairWetRoot = CharaHeadWetDirtCommonRoot + "hair_diff_wet";
        public const string CharaHeadWetEmissiveRoot = CharaHeadWetDirtCommonRoot + "face_emi_wet";
        public const string CharaHairWetEmissiveRoot = CharaHeadWetDirtCommonRoot + "hair_emi_wet";

        public const string CharaMobHeadWetRoot = CharaHeadWetDirtCommonRootPath + "Face/" + CharaHeadWetDirtCommonFileName + "face{2:000}_{3:0}_diff_wet";
        public const string CharaMobHairWetRoot = CharaHeadWetDirtCommonRootPath + "Hair/" + CharaHeadWetDirtCommonFileName + "hair{2:000}_0_diff_wet";
        public const string MOB_AREA_FACE_WET_TEX_PATH = CharaHeadWetDirtCommonRootPath + "Face/" + CharaHeadWetDirtCommonFileName + "face{2:000}_0_area_wet";
        public const string MOB_AREA_HAIR_WET_TEX_PATH = CharaHeadWetDirtCommonRootPath + "Hair/" + CharaHeadWetDirtCommonFileName + "hair{2:000}_0_area_wet";

        public const string MobFaceRootPath = CharacterHeadRoot + "chr0001_00/pfb_chr0001_00_face{0:000}";
        public const string AUDIENCE_FACE_ROOT_PATH = CharacterHeadRoot + "chr0900_00/pfb_chr0900_00_face{0:000}";

        public const string CharaRoot = "Chara/";
        public const string CharacterHeadModelFilePath = CharacterHeadRoot + "chr{0:0000}_{1:00}/pfb_chr{0:0000}_{1:00}";
        public const string CharacterHeadModelDirPath = CharacterHeadRoot + "chr{0:0000}_{1:00}/";

        #endregion

        #region 尻尾に関するパス

        public const string CHARACTER_TAIL_MODEL_DIR_PATH = CHARACTER_TAIL_ROOT + "tail{0:0000}_{1:00}/";
        public const string CHARACTER_TAIL_TEXTURE_DIR_PATH = CHARACTER_TAIL_MODEL_DIR_PATH + "Textures/";
        public const string CHARACTER_TAIL_MODEL_FILE_PATH = CHARACTER_TAIL_MODEL_DIR_PATH + "pfb_tail{0:0000}_{1:00}";
        public const string CHARACTER_TAIL_CLOTH_ASSET_FILE_PATH = CHARACTER_TAIL_MODEL_DIR_PATH + "Clothes/pfb_tail{0:0000}_{1:00}_cloth{2:00}";
        public const string CHARACTER_TAIL_TEXTURE_DIFF_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_{2:0000}_diff";
        public const string CHARACTER_TAIL_TEXTURE_BASE_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_0000_base"; // _baseは共通で0000を使用。
        public const string CHARACTER_TAIL_TEXTURE_CTRL_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_0000_ctrl"; // _ctrlは共通で0000を使用。
        public const string CHARACTER_TAIL_TEXTURE_SHAD_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_{2:0000}_shad_c";
        public const string MOB_TAIL_TEXTURE_DIFF_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_{2:0000}_diff";
        public const string MOB_TAIL_TEXTURE_SHAD_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_{2:0000}_shad_c";
        public const string MOB_TAIL_TEXTURE_AREA_PATH = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_{2:0000}_area";

        //濡れテクスチャのパス
        public const string CharaTailWetRoot = CHARACTER_TAIL_TEXTURE_DIFF_PATH + "_wet";
        public const string CharaTailWetToonRoot = CHARACTER_TAIL_TEXTURE_SHAD_PATH + "_wet";
        public const string SpecialDressTailWetRoot = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_diff_wet";
        public const string SpecialDressTailWetToonRoot = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_shad_c_wet";
        public const string CharaTailWetEmissiveRoot = CHARACTER_TAIL_TEXTURE_DIR_PATH + "tex_tail{0:0000}_{1:00}_emi_wet";
        public const string CharaMobTailWetRoot = MOB_TAIL_TEXTURE_DIFF_PATH + "_wet";
        public const string CharaMobTailWetToonRoot = MOB_TAIL_TEXTURE_SHAD_PATH + "_wet";
        public const string MOB_TAIL_WET_TEXTURE_AREA_PATH = MOB_TAIL_TEXTURE_AREA_PATH + "_wet";

        public const string FLARE_COLLISION_TAIL_PATH = CHARACTER_TAIL_MODEL_DIR_PATH + "Flares/ast_tail{0:0000}_{1:00}_flare";

        #endregion 尻尾に関するパス

        #region アタッチメントに関するパス
        public const string CHARACTER_ATTACHMENT_MODEL_ROOT = Character3DRoot + "Attach/attach{0:D4}_{1:D2}/";
        public const string CHARACTER_ATTACHMENT_TEXTURE_ROOT = CHARACTER_ATTACHMENT_MODEL_ROOT + "Textures/";

        public const string CHARACTER_ATTACHMENT_MODEL_FILE_PATH = CHARACTER_ATTACHMENT_MODEL_ROOT + "pfb_attach{0:D4}_{1:D2}_{2:D4}_{3:D2}";
        public const string CHARACTER_ATTACHMENT_DIRT_TEXTURE_FILE_PATH = CHARACTER_ATTACHMENT_TEXTURE_ROOT + "tex_attach{0:D4}_{1:D2}_dirt";
        public const string CHARACTER_ATTACHMENT_DIFF_WET_TEXTURE_FILE_PATH = CHARACTER_ATTACHMENT_TEXTURE_ROOT + "tex_attach{0:D4}_{1:D2}_diff_wet";
        public const string CHARACTER_ATTACHMENT_BASE_WET_TEXTURE_FILE_PATH = CHARACTER_ATTACHMENT_TEXTURE_ROOT + "tex_attach{0:D4}_{1:D2}_base_wet";
        public const string CHARACTER_ATTACHMENT_CTRL_WET_TEXTURE_FILE_PATH = CHARACTER_ATTACHMENT_TEXTURE_ROOT + "tex_attach{0:D4}_{1:D2}_ctrl_wet";
        public const string CHARACTER_ATTACHMENT_SHAD_C_WET_TEXTURE_FILE_PATH = CHARACTER_ATTACHMENT_TEXTURE_ROOT + "tex_attach{0:D4}_{1:D2}_shad_c_wet";
        #endregion アタッチメントに関するパス


        /// <summary>
        /// レアスキルカットインファイル名取得。
        /// </summary>
        /// <param name="charaId">キャラId。</param>
        /// <param name="subId">バリエーション予約。</param>
        public static string GetRareSkillCutInName(int charaId, int subId = 1)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append($"chr{charaId:D4}_{subId:D3}/chr{charaId:D4}_{subId:D3}");
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// SSレアスキル発動カットインファイル名取得。
        /// </summary>
        public static string GetSSRareSkillActivateCutInName()
        {
            return "cmn_001/cmn_001";
        }

        /// <summary>
        /// SSレアスキルカットインファイル名取得。
        /// </summary>
        /// <param name="cardId">カードId。</param>
        /// <param name="subId">バリエーション予約。</param>
        public static string GetSSRareSkillCutInName(int cardId, int subId = 1)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append($"crd{cardId:D6}_{subId:D3}/crd{cardId:D6}_{subId:D3}");
            return _stringBuilder.ToString();
        }

        public const string RaceCutInRoot = "Cutt/CutIn/Skill/";
        private const string RaceCutInPath = RaceCutInRoot + "{0}";

        public static string GetSkillCutInTimelineControlPath(string cutinId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RaceCutInPath, cutinId);
            return _stringBuilder.ToString();
        }

        #region キャラアイコン

        public const string MOB_ROOT = "Mob/";

        /// <summary>
        /// キャラIDに対応するキャラアイコン（一覧表示用）のリソースパスを取得する
        /// </summary>
        /// <param name="charaId"></param>
        /// <returns></returns>
        public static string GetCharaThumbnailIconPath(int charaId)
        {
            charaId = Mathf.Max(0, charaId);
            _stringBuilder.Length = 0;
            _stringBuilder.Append(CharaRoot);
            return _stringBuilder.AppendFormat("Chr{0:0000}/chr_icon_{0:0000}", charaId).ToString();
        }

        /// <summary>
        /// キャラIDに対応する併せウマキャラアイコンのリソースパスを取得する
        /// </summary>
        public static string GetCharaTrainingIconPath(int charaId)
        {
            charaId = Mathf.Max(0, charaId);
            _stringBuilder.Length = 0;
            _stringBuilder.Append(CharaRoot);
            return _stringBuilder.AppendFormat("Chr{0:0000}/chr_icon_training_{0:0000}", charaId).ToString();
        }
        
        public const string PUSH_ICON_PATH_FORMAT = CharaRoot + "Chr{0:0000}/push_icon_{0:0000}_{1:000000}";

        /// <summary>
        /// キャラIDとドレスIDに対応するキャラアイコンを取得する.
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="dressId"></param> 
        /// <returns></returns>
        public static string GetCharaPushNoticeIconPath(int charaId, int dressId)
        {
            charaId = Mathf.Max(0, charaId);
            _stringBuilder.Length = 0;
            _stringBuilder.Append(CharaRoot);

            string assetpath = _stringBuilder.AppendFormat("Chr{0:0000}/push_icon_{0:0000}_{1:000000}", charaId, dressId).ToString();

            // アイコンが存在しなかった場合に備えて今までの物をセットしておく.
            if(!ResourceManager.IsExistAsset(assetpath))
            {
                _stringBuilder.Clear();
                _stringBuilder.Append(CharaRoot);
                assetpath = _stringBuilder.AppendFormat("Chr{0:0000}/chr_icon_{0:0000}", charaId).ToString();
            }
            return assetpath;
        }

        #endregion

        public static int GetPropHighId(int propId)
        {
            return propId / 100;
        }

        public static int GetPropLowId(int propId)
        {
            return propId % 100;
        }

        public static int MakePropId(int propId, int propSubId)
        {
            return (propId * 100) + propSubId;
        }

        /// <summary>
        /// モデルパス取得。
        /// </summary>
        public static string GetPropPath(int propId, int propSubId)
        {
            int finalPropId = MakePropId(propId, propSubId);
            return GetCharaPropPath(finalPropId);
        }

        /// <summary>
        /// キャラ用小物のパス
        /// </summary>
        /// <param name="propId"></param>
        /// <returns></returns>
        public static string GetCharaPropPath(int propId)
        {
            const string PropPath = CharacterPropRoot + "prop{0:0000}_{1:00}/pfb_chr_prop{0:0000}_{1:00}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PropPath, GetPropHighId(propId), GetPropLowId(propId));
            return _stringBuilder.ToString();
        }
        public static string GetCharaPropClothPath(int propId, int clothCategory)
        {
            const string PropPath = CharacterPropRoot + "prop{0:0000}_{1:00}/Clothes/pfb_chr_prop{0:0000}_{1:00}_cloth{2:00}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PropPath, GetPropHighId(propId), GetPropLowId(propId), clothCategory);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// // #79837 トゥーンプロップ対応
        /// </summary>
        public static string GetCharaToonPropPath(int propId)
        {
            const string PropPath = CharacterToonPropRoot + "toon_prop{0:0000}_{1:00}/pfb_chr_toon_prop{0:0000}_{1:00}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PropPath, GetPropHighId(propId), GetPropLowId(propId));
            return _stringBuilder.ToString();
        }

        private const string HomePropAnimationRoot = MOTION_ROOT + "{0}/Prop/";

        /// <summary>
        /// ホーム用のキャラ小物の性格別アニメーションパス。
        /// </summary>
        /// <param name="propId"></param>
        /// <param name="personalityType"></param>
        /// <param name="suffix"></param>
        /// <returns></returns>
        public static string GetHomeCharaPropAnimationByPersonalityTypePath(int personalityType, HomeDefine.StandPos pos, string stateName, string valiation = "000")
        {
            const string CharaPropAnimationPath = HomePropAnimationRoot + "Type{1:00}/{2}";
            var motionName = GetHomeCharaPropAnimationByPersonalityTypeName(personalityType,pos,stateName, valiation);
            return TextUtil.Format(CharaPropAnimationPath, "Home", personalityType, motionName);
        }

        /// <summary>
        /// ホーム用のキャラ小物の性格別アニメーション名
        /// </summary>
        /// <param name="propId"></param>
        /// <param name="personalityType"></param>
        /// <param name="suffix"></param>
        /// <returns></returns>
        public static string GetHomeCharaPropAnimationByPersonalityTypeName(int personalityType, HomeDefine.StandPos pos, string stateName, string valiation = "000")
        {
            const string CharaPropAnimationPath = CharacterHomeTypeMotionMame + "{0:00}_{1}_{2}_{3}_prop";
            var posStr = HomeStandPosToMotionStr(pos);
            return TextUtil.Format(CharaPropAnimationPath,personalityType, valiation, posStr, stateName);
        }

        /// <summary>
        /// 会話用のキャラ小物のアニメーションパス。
        /// </summary>
        public static string GetEventCharaPropAnimation(string motionName
            , StoryTimelineCharaPropClipData.MotionStateType stateType
            , bool isMirror = false
            , string suffix = "")
        {
            const int PERSONALITY_TYPE = 0; // 会話では性格別モーションの概念が無くなった
            const string EVENT_CHARA_PROP_ANIMATION_PATH = HomePropAnimationRoot + "Type{1:00}/" + CharacterEventMotionPrefixName + "type{1:00}_{2}{3}_prop{4}{5}";

            var motionSliceSuffix = string.Empty;
            switch (stateType)
            {
                case StoryTimelineCharaPropClipData.MotionStateType.Start:
                    motionSliceSuffix = ResourcePath.MOTION_START_STATE_SUFFIX;
                    break;
                case StoryTimelineCharaPropClipData.MotionStateType.Loop:
                case StoryTimelineCharaPropClipData.MotionStateType.Action:
                    motionSliceSuffix = ResourcePath.MOTION_LOOP_STATE_SUFFIX;
                    break;
                case StoryTimelineCharaPropClipData.MotionStateType.End:
                    motionSliceSuffix = ResourcePath.MOTION_END_STATE_SUFFIX;
                    break;
            }

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EVENT_CHARA_PROP_ANIMATION_PATH
                , "Event"
                , PERSONALITY_TYPE
                , motionName
                , motionSliceSuffix
                , (isMirror ? MirrorSuffix : string.Empty)
                , suffix);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 会話用のキャラ小物のキャラ固有アニメーションパス。
        /// </summary>
        public static string GetEventCharaPropUniqueAnimation(int charaId, int subId
            , string motionName
            , StoryTimelineCharaPropClipData.MotionStateType stateType
            , bool isMirror = false
            , string suffix = "")
        {
            const string EVENT_CHARA_PROP_UNIQUE_ANIMATION_PATH = HomePropAnimationRoot + "Chara/chr{1:0000}_{2:00}/" + CharacterEventMotionPrefixName + "chr{1:0000}_{2:00}_{3}{4}_prop{5}{6}";

            var motionSliceSuffix = string.Empty;
            switch (stateType)
            {
                case StoryTimelineCharaPropClipData.MotionStateType.Start:
                    motionSliceSuffix = ResourcePath.MOTION_START_STATE_SUFFIX;
                    break;
                case StoryTimelineCharaPropClipData.MotionStateType.Loop:
                case StoryTimelineCharaPropClipData.MotionStateType.Action:
                    motionSliceSuffix = ResourcePath.MOTION_LOOP_STATE_SUFFIX;
                    break;
                case StoryTimelineCharaPropClipData.MotionStateType.End:
                    motionSliceSuffix = ResourcePath.MOTION_END_STATE_SUFFIX;
                    break;
            }

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EVENT_CHARA_PROP_UNIQUE_ANIMATION_PATH
                , "Event"
                , charaId
                , subId
                , motionName
                , motionSliceSuffix
                , (isMirror ? MirrorSuffix : string.Empty)
                , suffix);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 会話キャラ小物共通アニメーションパス。
        /// </summary>
        public static string GetEventCharaPropCommonAnimationPath(string clipName, string suffix, bool isMirror)
        {
            const string EventCharaPropAnimationPath = EventMotionRoot + "Prop/Type00/" + CharacterEventMotionPrefixName + "type00_{0}{1}_prop{2}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EventCharaPropAnimationPath, clipName, suffix, (isMirror) ? MirrorSuffix : "");
            return _stringBuilder.ToString();
        }
        /// <summary>
        /// 会話キャラ小物キャラ別アニメーションパス。
        /// </summary>
        public static string GetEventCharaPropCharaAnimationPath(int charaId, int subId, string clipName, string suffix, bool isMirror)
        {
            const string EventCharaPropAnimationPath = EventMotionRoot + "Prop/Chara/chr{0}_{1:D2}/" + CharacterEventMotionPrefixName + "chr{0}_{1:D2}_{2}{3}_prop{4}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EventCharaPropAnimationPath, charaId, subId, clipName, suffix, (isMirror) ? MirrorSuffix : "");
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 尻尾モーションパスを取得
        /// </summary>
        /// <param name="motionName"></param>
        /// <returns></returns>
        public static string GetTailMotionPath(string motionName)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("3d/Motion/Event/Body/Type00/Tail/anm_eve_type00_{0}", motionName);
            return _stringBuilder.ToString();
        }

        #endregion

        #region Character(Mini)

        #region ミニモデル
        public const string MINI_3D_ROOT = Root3d + "Chara/Mini/";
        public const string MINI_EMOTICON_LOCATOR_PATH = MINI_3D_ROOT + "pfb_mini_chara_emoticon_locator";

        #region Head
        public const string MINI_HEAD_ROOT = MINI_3D_ROOT + "Head/mchr{0:D4}_{1:D2}/";

        // cloth
        public const string MINI_HEAD_CLOTH_ASSET_FILE_PATH = MINI_HEAD_ROOT + "Clothes/pfb_mchr{0:D4}_{1:D2}_cloth{2:D2}";

        // model
        public const string MINI_HAIR_MODEL_PATH = MINI_HEAD_ROOT + "pfb_mchr{0:D4}_{1:D2}_hair";

        // texture
        public const string MINI_HEAD_TEXTURE_ROOT = MINI_HEAD_ROOT + "Textures/";
        public const string MINI_FACE_TEXTURE_PATH = MINI_HEAD_TEXTURE_ROOT + "tex_mchr{0:D4}_{1:D2}_face{2:D1}_{3:D1}_diff";
        public const string MINI_CHEEK_TEXTURE_PATH = MINI_HEAD_TEXTURE_ROOT + "tex_mchr{0:D4}_{1:D2}_face{2:D1}_{3:D1}_cheek{4:D1}";
        public const string MINI_MAYU_TEXTURE_PATH = MINI_HEAD_TEXTURE_ROOT + "tex_mchr{0:D4}_{1:D2}_mayu_diff";
        public const string MINI_EYE_TEXTURE_PATH = MINI_HEAD_TEXTURE_ROOT + "tex_mchr{0:D4}_{1:D2}_eye_diff";
        public const string MINI_MOUTH_TEXTURE_PATH = MINI_HEAD_TEXTURE_ROOT + "tex_mchr{0:D4}_{1:D2}_mouth_diff";

        #endregion Head

        #region Body
        public const string MINI_BODY_ROOT = MINI_3D_ROOT + "Body/mbdy{0:D4}_{1:D2}/";

        // cloth
        public const string MINI_BODY_CLOTH_ASSET_FILE_PATH = MINI_BODY_ROOT + "Clothes/pfb_mbdy{0:D4}_{1:D2}{2}_cloth{3:D2}";
        public const string MINI_SKIRT_ASSET_FILE_PATH = MINI_BODY_ROOT + "Clothes/ast_mbdy{0:D4}_{1:D2}{2}_skirt{3:D2}";
        public static string GetMiniDressSkirtAssetFilePath(CharacterBuildInfo charBuildInfo, int category)
        {
            var dress = charBuildInfo.DressElement;
            string genderSubId = charBuildInfo.GetBodyPartsIDStringForAdditional(dress.IsChangeByGender);

            if (charBuildInfo.IsMiniMob)
            {
                return string.Format(MINI_SKIRT_ASSET_FILE_PATH, charBuildInfo.CharaId, charBuildInfo.BodyModelSubId, genderSubId, category);
            }

            int modelId = (charBuildInfo.IsSpecialDress()) ? charBuildInfo.CharaId : dress.BodyType;
            int modelSubId = dress.BodyTypeSub;
            return string.Format(MINI_SKIRT_ASSET_FILE_PATH, modelId, modelSubId, genderSubId, category);
        }

        // model
        public const string MINI_BODY_MODEL_PATH = MINI_BODY_ROOT + "pfb_mbdy{0:D4}_{1:D2}_{2:D2}_{3:D1}";
        public const string MINI_BODY_SPECIAL_DRESS_MODEL_PATH = MINI_BODY_ROOT + "pfb_mbdy{0:D4}_{1:D2}";
        public const string MINI_MOB_BODY_PATH = MINI_BODY_ROOT + "pfb_mbdy{0:D4}_{1:D2}";

        // texture
        public const string MINI_BODY_TEXTURE_ROOT = MINI_BODY_ROOT + "Textures/";
        public const string MINI_BODY_TEXTURE_PATH = MINI_BODY_TEXTURE_ROOT + "tex_mbdy{0:D4}_{1:D2}_{2:D2}_{3:D1}_{4:D1}_diff";

        #endregion Body

        #region Tail
        public const string MINI_TAIL_ROOT = MINI_3D_ROOT + "Tail/mtail{0:D4}_{1:D2}/";
        public const string MINI_TAIL_MODEL_PATH = MINI_TAIL_ROOT + "pfb_mtail{0:D4}_{1:D2}";

        // cloth
        public const string MINI_TAIL_CLOTH_ASSET_FILE_PATH = MINI_TAIL_ROOT + "Clothes/pfb_mtail{0:D4}_{1:D2}_cloth{2:D2}";

        // texture
        public const string MINI_TAIL_TEXTURE_ROOT = MINI_TAIL_ROOT + "Textures/";
        public const string MINI_TAIL_TEXTURE_PATH = MINI_TAIL_TEXTURE_ROOT + "tex_mtail{0:D4}_{1:D2}_{2:D4}_diff";
        #endregion Tail

        #region Attachment
        public const string MINI_ATTACH_ROOT = MINI_3D_ROOT + "Attach/mattach{0:D4}_{1:D2}/";
        public const string MINI_ATTACH_MODEL_PATH = MINI_ATTACH_ROOT + "pfb_mattach{0:D4}_{1:D2}_{2:D4}_{3:D2}";

        #endregion

        #endregion // ミニモデル

        #region ミニモーション
        public const string MINI_MOTION_ROOT = Root3d + "Motion/Mini/";
        private const string MINI_TAIL_MOTION_PATH = Root3d + "Motion/Event/Body/Type00/Tail/anm_eve_type00_{0}";

        #region 体
        public static string GetMiniBodyMotionPath(int charaId, int charaType, CharacterBuildInfo buildInfo, MasterMiniMotionSet.MiniMotionSet motionSetMaster, string suffix)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_MOTION_ROOT + "{4}/Body/{0}/anm_min_{5}_{6}_{1}{2}{3}",
                motionSetMaster.GetBodyMotionTypeDirName(charaId, charaType),
                motionSetMaster.BodyMotion,
                suffix,
                motionSetMaster.GetMirrorSuffix(),
                motionSetMaster.GetBodyMotionSceneDirName(),
                motionSetMaster.GetBodyMotionSceneFileNamePrefix(),
                motionSetMaster.GetBodyMotionTypeFilePrefix(charaId, charaType, buildInfo)
            );
            return _stringBuilder.ToString();
        }
        #endregion // 体

        #region 加算
        public static string GetMiniPlusMotionPath(int charaId, int charaType, CharacterBuildInfo buildInfo, MasterMiniMotionSet.MiniMotionSet motionSetMaster)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_MOTION_ROOT + "{4}/Body/{0}/anm_min_{5}_{6}_{1}_plus{2}{3}",
                motionSetMaster.GetBodyMotionTypeDirName(charaId, charaType),
                motionSetMaster.BodyMotion,
                "", //加算にはStart-Loop-Endはない
                motionSetMaster.GetMirrorSuffix(),
                motionSetMaster.GetBodyMotionSceneDirName(),
                motionSetMaster.GetBodyMotionSceneFileNamePrefix(),
                motionSetMaster.GetBodyMotionTypeFilePrefix(charaId, charaType, buildInfo)
            );

            return _stringBuilder.ToString();
        }
        #endregion // 加算

        #region 耳
        public static string GetMiniEarMotionPath(int charaId, int charaType, CharacterBuildInfo buildInfo, MasterMiniMotionSet.MiniMotionSet motionSetMaster, string suffix)
        {
            if (!string.IsNullOrEmpty(motionSetMaster.FacialMotion))
            {
                // フェイシャルの指定があればそれと同等の耳を再生する。
                var facialPath = GetMiniFacialAnimationPathImpl(charaId, charaType, buildInfo, motionSetMaster, suffix, "ear");
                _stringBuilder.Length = 0;
                _stringBuilder.Append(facialPath);
                _stringBuilder.Append("_driven");
                return _stringBuilder.ToString();
            }
            else
            {
                if (string.IsNullOrEmpty(motionSetMaster.EarMotion))
                {
                    return "";
                }
                return GetMiniEarMotionPath(motionSetMaster.EarMotion);
            }
        }
        public static string GetMiniEarMotionPath(string motionName)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("3d/Motion/Event/Facial/Type00/anm_eve_type00_{0}_driven", motionName);
            return _stringBuilder.ToString();
        }
        #endregion // 耳

        #region 尻尾
        public static string GetMiniTailMotionPath(MasterMiniMotionSet.MiniMotionSet motionSetMaster)
        {
            if (string.IsNullOrEmpty(motionSetMaster.TailMotion))
            {
                return "";
            }
            return GetMiniTailMotionPath(motionSetMaster.TailMotion + motionSetMaster.GetMirrorSuffix());
        }
        // mirrorモーションを使う場合はmotionNameに含んだ状態で指定される
        public static string GetMiniTailMotionPath(string motionName)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_TAIL_MOTION_PATH, motionName);
            return _stringBuilder.ToString();
        }

        #endregion // 尻尾

        #region 小物
        private const string MINI_EVENT_PROP_COMMON_ANIMATION_PATH = MOTION_ROOT + "Mini/Event/Prop/Type00/anm_min_eve_type00_{0}{1}_prop{2}";
        private const string MINI_EVENT_PROP_CHARA_ANIMATION_PATH = MOTION_ROOT + "Mini/Event/Prop/chr{0}_{1}/anm_min_eve_chr{0}_{1}_{2}{3}_prop{4}";
        private const string MINI_CIRCLE_PROP_COMMON_ANIMATION_PATH = MOTION_ROOT + "Mini/Circle/Prop/Type00/anm_min_cir_type00_{0}{1}_prop{2}";
        private const string MINI_CIRCLE_PROP_CHARA_ANIMATION_PATH = MOTION_ROOT + "Mini/Circle/Prop/chr{0}_{1}/anm_min_cir_chr{0}_{1}_{2}{3}_prop{4}";

        /// <summary>
        /// ミニキャラ小物共通アニメーションパス。
        /// </summary>
        public static string GetMiniPropAnimationPath(int charaId, int charaType, CharacterBuildInfo buildInfo, MasterMiniMotionSet.MiniMotionSet motionSetMaster, string suffix)
        {
            string clipName = motionSetMaster.PropMotion;
            bool isMirror = motionSetMaster.IsMirrorMotion();
            _stringBuilder.Length = 0;

            //ex. Mini/Event/Prop/Type00/anm_min_eve_type00_book_E_prop

            _stringBuilder.AppendFormat(MOTION_ROOT +
                "Mini/{3}/Prop/{4}/anm_min_{5}_{6}_{0}{1}_prop{2}",
                 clipName,
                 suffix,
                 (isMirror) ? MirrorSuffix : "",
                 motionSetMaster.GetPropMotionSceneDirName(),
                 motionSetMaster.GetBodyMotionTypeDirName(charaId, charaType),
                 motionSetMaster.GetPropMotionSceneFilePrefix(),
                 motionSetMaster.GetBodyMotionTypeFilePrefix(charaId, charaType, buildInfo)
            );
            return _stringBuilder.ToString();
        }

        #endregion // 小物

        #region フェイシャル

        /// <summary>
        /// ミニキャラフェイシャル共通アニメーションパス。
        /// </summary>
        public static string GetMiniFacialAnimationPath(int charaId, int charaType, CharacterBuildInfo buildInfo, MasterMiniMotionSet.MiniMotionSet motionSetMaster, string suffix)
        {
            return GetMiniFacialAnimationPathImpl(charaId, charaType, buildInfo, motionSetMaster, suffix, "face");
        }
        private static string GetMiniFacialAnimationPathImpl(int charaId, int charaType, CharacterBuildInfo buildInfo, MasterMiniMotionSet.MiniMotionSet motionSetMaster, string suffix, string faseOrEar)
        {
            string clipName = motionSetMaster.FacialMotion;
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(
                MOTION_ROOT + "Mini/{2}/Facial/{3}/anm_min_{4}_{5}_{0}{1}_{6}",
                clipName,
                suffix,
                motionSetMaster.GetBodyMotionSceneDirName(),
                motionSetMaster.GetBodyMotionTypeDirName(charaId, charaType),
                motionSetMaster.GetBodyMotionSceneFileNamePrefix(),
                motionSetMaster.GetBodyMotionTypeFilePrefix(charaId, charaType, buildInfo),
                faseOrEar
            );
            return _stringBuilder.ToString();
        }

        #endregion // フェイシャル

        #endregion // ミニモーション

        public const string MINI_CHARA_VIEWER_PATH = "Prefabs/MiniCharaViewer/MiniCharaViewer";

        #endregion Character(Mini)

        #region Character(UI)

        public const string CHARACTER_UIPARTS_PATH_ROOT = "UI/Parts/Character";

        /// <summary>
        /// ウマ娘詳細ダイアログ：殿堂入りウマ娘
        /// </summary>
        public const string DIALOG_TRAINED_CHARACTER_DETAIL_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogTrainedCharacterDetail";
        
        /// <summary>
        /// ウマ娘詳細ダイアログ：モブウマ娘
        /// </summary>
        public const string DIALOG_MOB_CHARACTER_DETAIL_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogMobCharacterDetail";

        /// <summary>
        /// ウマ娘詳細ダイアログ：カード
        /// </summary>
        public const string DIALOG_CHARACTER_CARD_DETAIL_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogCharacterCardDetail";

        public const string DIALOG_CHARACTER_LIST_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogCharacterList";

        /// <summary>
        /// 殿堂入りウマ娘選択ダイアログ
        /// </summary>
        public const string DIALOG_TRAINED_CHARA_LIST_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogTrainedCharaList";

        /// <summary>
        /// プロフィールウマ娘選択TOPダイアログ
        /// </summary>
        public const string DIALOG_FAVORITE_CHARA_TOP_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogFavoriteCharacterTop";

        /// <summary>
        /// キャラクタ選択ダイアログ
        /// </summary>
        public const string DIALOG_CARD_SELECT_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogCharacterSelect";

        /// <summary>
        /// お気に入り（衣装）選択ダイアログ
        /// </summary>
        public const string DIALOG_FAVORITE_DRESS_SELECT_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogFavoriteDressSelect";

        /// <summary>
        /// 貸し出し育成サポート選択ダイアログ
        /// </summary>
        public const string DIALOG_RENTAL_SUPPORT_CARD_SELECT_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogRentalSupportCardSelect";

        public const string DIALOG_TRAINED_CHARACTER_FACTOR_LIST_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogTrainedCharacterFactorList";

        /// <summary>
        /// 衣装変更ダイアログ
        /// </summary>
        public const string DIALOG_DRESS_CHANGE = "UI/Parts/DialogDressChange";

        /// <summary>
        /// ピースでのウマ娘解放ダイアログ
        /// </summary>
        public const string DIALOG_RELEASE_CHARACTER_CARD_BY_PIECE_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogReleaseCharacterCardByPiece";
        /// <summary>
        /// ピースでのウマ娘解放確認ダイアログ
        /// </summary>
        public const string DIALOG_RELEASE_CHARACTER_CARD_BY_PIECE_CONFIRM_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogReleaseCharacterCardByPieceConfirm";
        // <summary>
        // ピースでのウマ娘解放成功通知ダイアログ
        // </summary>
        public const string DIALOG_RELEASE_CHARACTER_CARD_BY_PIECE_SUCCESS_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogReleaseCharacterCardByPieceSuccess";
        
        /// <summary> カードシナリオリンクハイライトエフェクト </summary>
        public const string CHARACTER_BUTTON_CARD_SCENARIO_LINK_EFFECT_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/PartsCharacterButtonCardScenarioLinkEffect";
        /// <summary> カードシナリオリンクハイライト </summary>
        public const string CHARACTER_BUTTON_CARD_SCENARIO_LINK_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/PartsCharacterButtonCardScenarioLink";
        
        /// <summary> チャレンジマッチボーナス因子ハイライトエフェクト </summary>
        public const string CHARACTER_BUTTON_CARD_CHALLENGE_MATCH_BONUS_FACTOR_EFFECT_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/PartsCharacterButtonCardChallengeMatchBonusFactorEffect";

        /// <summary>
        /// 2D背景+3Dキャラという構成で足元影を出したい時の板Prefab
        /// (Layerが3Dとなっている)
        /// </summary>
        public const string CHARACTER_SHADOW_RECEIVER_PREFAB_PATH = "OutGame/Shadow/Character/pfb_3d_chara_shadow_receiver";

        /// <summary>
        /// お気に入りアイコン選択ダイアログ
        /// </summary>
        public const string DIALOG_FAVORITE_ICON_SELECT_PATH = CHARACTER_UIPARTS_PATH_ROOT + "/DialogFavoriteIconSelect";

        /// <summary>
        /// 殿堂入りウマ娘のメモ変更ダイアログ
        /// </summary>
        public const string DIALOG_CHANGE_TRAINED_CHARACTER_MEMO = CHARACTER_UIPARTS_PATH_ROOT + "/DialogChangeTrainedCharacterMemo";
        
        #endregion

        #region ギャラリー

        public const string GALLERY_UIPARTS_PATH_ROOT = "UI/Parts/Outgame/Gallery";
        
        /// <summary>
        /// ウマ娘詳細ダイアログ：ギャラリー
        /// </summary>
        public const string DIALOG_GALLERY_CHARACTER_DETAIL_PATH = GALLERY_UIPARTS_PATH_ROOT + "/GalleryDialogCharacterDetail";
        
        /// <summary>
        /// サポカ詳細ダイアログ：ギャラリー
        /// </summary>
        public const string DIALOG_GALLERY_SUPPORTCARD_DETAIL_PATH = GALLERY_UIPARTS_PATH_ROOT + "/GalleryDialogSupportCardDetail";

        /// <summary>
        /// シナリオダイアログ：ギャラリー
        /// </summary>
        public const string DIALOG_GALLERY_SCENARIO_DETAIL_PATH = GALLERY_UIPARTS_PATH_ROOT + "/GalleryDialogScenarioCardDetail";
        
        /// <summary>
        /// シナリオ再生確認ダイアログ：ギャラリー
        /// </summary>
        public const string DIALOG_GALLERY_PLAY_EVENT_CONFIRM_PATH = GALLERY_UIPARTS_PATH_ROOT + "/GalleryDialogPlayEventConfirm";

        #endregion

        #region トークギャラリー

        public const string OUTGAME_GALLERY_PATH_ROOT = OUTGAME_ASSET_ROOT + "Gallery/";
        public const string TALK_GALLERY_DIALOG_BG_PATH = OUTGAME_GALLERY_PATH_ROOT + "Bg/tex_talk_dialog_bg_01";
        public const string TALK_GALLERY_UIPARTS_PATH_ROOT = "UI/Parts/Outgame/TalkGallery/";
        public const string DIALOG_TALK_GALLERY_TRIGGER_LIST = TALK_GALLERY_UIPARTS_PATH_ROOT + "DialogTalkGalleryTriggerList";
        public const string DIALOG_TALK_GALLERY_PLAY_CONFIRM = TALK_GALLERY_UIPARTS_PATH_ROOT + "DialogTalkGalleryPlayConfirm";

        #endregion

        #region Item

        public const string ITEM_ICON_ROOT = "Item/";
        private const string ITEM_ICON_PATH_ROOT = ITEM_ICON_ROOT + "item_icon_{0:00000}";
        private const string ITEM_ICON_S_SIZE_PATH_ROOT = ITEM_ICON_ROOT + "item_icon_s_{0:00000}";
        public const string ITEM_RANDOM_ICON_PATH = ITEM_ICON_ROOT + "item_random";
        public const string PARTS_ITEM_ICON_PATH = UIPartsPath + "/Item/ItemIcon";
        public const string PARTS_ITEM_LONG_TAP_INFO_POP_PATH = UIPartsPath + "/Item/ItemLongTapInfoPop";
        public const string PARTS_SKILL_ICON_LONG_TAP_INFO_POP_PATH = UIPartsPath + "/SkillIconLongTapInfoPop";
        public const string PARTS_SKILL_ICON_PATH = UIPartsPath + "/SkillIcon";
        public const string DIALOG_USE_ITEM_AND_TICKET = UIPartsPath + "/Item/DialogUseItemAndTicket";

        //称号アイコンのアイテムID
        private const int ITEM_ICON_HONOR_ID = 55001;
        //楽曲アイコンのアイテムID
        private const int ITEM_ICON_SOUND_ID = 80001;
        //ジュークボックス楽曲アイコンのアイテムID
        private const int ITEM_ICON_JUKEBOX_ID = 170;

        //ピースのフレームなしの画像を取得するためのオプションの値
        public const int ITEM_ICON_PIECE_NO_FRAME_OPTION = 1;

        /// <summary>
        /// アイテムアイコンのパス取得
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        public static string GetItemIconPath(int itemId, bool isSmall = false)
        {
            _stringBuilder.Length = 0;
            if (isSmall)
            {
                _stringBuilder.AppendFormat(ITEM_ICON_S_SIZE_PATH_ROOT, itemId);
            }
            else
            {
                _stringBuilder.AppendFormat(ITEM_ICON_PATH_ROOT, itemId);
            }
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// アイテム用アイコンのパスを取得する
        /// </summary>
        /// <param name="itemTypeId"></param>
        /// <param name="itemNo"></param>
        /// <param name="opt"></param>
        /// <returns></returns>
        public static string GetItemIconPath(GameDefine.ItemCategory itemTypeId, int itemNo, int opt = 0)
        {
            string pathRootStr = ITEM_ICON_PATH_ROOT;
            _stringBuilder.Length = 0;
            switch (itemTypeId)
            {
                case GameDefine.ItemCategory.HONOR:
                    itemNo = ITEM_ICON_HONOR_ID;
                    break;
                case GameDefine.ItemCategory.CARD:
                    return GetCardDefaultDressThumbnailIconPath(itemNo);
                case GameDefine.ItemCategory.DRESS:
                    return GetDressIconPath(itemNo, opt);
                case GameDefine.ItemCategory.SOUND: // 楽曲
                    itemNo = ITEM_ICON_SOUND_ID;
                    break;
                case GameDefine.ItemCategory.JUKEBOX_MUSIC:
                    itemNo = ITEM_ICON_JUKEBOX_ID;
                    break;
                case GameDefine.ItemCategory.SUPPORT_CARD:
                case GameDefine.ItemCategory.SUPPORT_CARD_LIMIT_BREAK_4:
                    return GetSupportCardSTexturePath(itemNo);    // 正方形のサポートカードのテクスチャを取得
                case GameDefine.ItemCategory.CARD_PIECE:
                    if (opt == ITEM_ICON_PIECE_NO_FRAME_OPTION)
                    {
                        return GetCardPieceNoBaseIconPath(itemNo);
                    }
                    else
                    {
                        return GetCardPieceIconPath(itemNo);
                    }
                case GameDefine.ItemCategory.SINGLE_MODE_SCENARIO_FREE_SHOP_ITEM:
                    return GetSingleModeScenarioFreeItemIconPath(itemNo);
                case GameDefine.ItemCategory.TEAM_BUILDING_SCOUT_PT:
                    itemNo = ITEM_ICON_ID_TEAM_BUILDING_SCOUT_PT;
                    break;
                case GameDefine.ItemCategory.TEAM_BUILDING_TICKET:
                    itemNo = ITEM_ICON_ID_TEAM_BUILDING_TICKET;
                    break;
            }
            return _stringBuilder.AppendFormat(pathRootStr, itemNo).ToString();
        }

        /// <summary>
        /// ランダムアイコンのパス
        /// </summary>
        public static string GetRandomItemIconPath()
        {
            return ITEM_RANDOM_ICON_PATH;
        }
        
        /// <summary>
        /// ウマ娘専用ピースのルートディレクトリ
        /// </summary>
        public const string PIECE_ROOT = OUTGAME_ASSET_ROOT + "Piece/";

        /// <summary>
        /// ウマ娘カード専用ピースアイコンのパスを返す
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetCardPieceIconPath(int id)
        {
            const string PIECE_BASE_PATH = PIECE_ROOT + "piece_icon_{0:D6}";
            _stringBuilder.Length = 0;

            return _stringBuilder.AppendFormat(PIECE_BASE_PATH, id).ToString();
        }
        /// <summary>
        /// ウマ娘カード専用ピースアイコンのパスを返す。（下地が無い奴）
        /// </summary>
        public static string GetCardPieceNoBaseIconPath(int id)
        {
            const string PIECE_BASE_PATH = PIECE_ROOT + "piece_icon_{0:D6}_no_frame";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(PIECE_BASE_PATH, id).ToString();
        }

        #endregion

        #region カード
        public const string MobCardRoot = "Mob/";
        public const string CHARA_SET_UP_ICON_PATH = CharaRoot + "Chr0000/chr_icon_plus_0000";
        public const string CHARA_EMPTY_ICON_PATH = CharaRoot + "Chr0000/chr_icon_0000";
        public const string TRAINED_CHARA_EMPTY_ICON_PATH = CharaRoot + "Chr0000/trained_chr_icon_0000";

        // 汎用キャラ丸画像パス
        public const string CharCommonRoundIconPath = "Chara/Chr0000/chr_icon_round_0000";

        /// <summary>
        /// カードIDに対応するカードアイコン（デフォルトレアリティ衣装）のリソースパスを取得する
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetCardDefaultDressThumbnailIconPath(int cardId)
        {
            var masterCard = MasterDataManager.Instance.masterCardData.Get(cardId);
            if (masterCard == null)
            {
                Debug.LogWarning("masterCardDataがnullです" + cardId);
                return string.Empty;
            }
            return GetCardThumbnailIconPath(masterCard.DefaultRarityData, GameDefine.MIN_TALENT_LEVEL);
        }

        /// <summary>
        /// レアリティに対応するカードアイコン（一覧表示用）のリソースパスを取得する
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetCardThumbnailIconPath(MasterCardRarityData.CardRarityData rarityData, int talentLevel)
        {
            if (rarityData == null)
            {
                return "";
            }
            return GetCardThumbnailIconPath(rarityData.CharaId, rarityData.RaceDressId, talentLevel);
        }

        /// <summary>
        /// キャラIDとドレスIDを指定したアイコンのリソースパス取得
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="dressId"></param>
        /// <returns></returns>
        public static string GetCardThumbnailIconPath(int charaId, int dressId, int talentLevel)
        {
            charaId = Mathf.Max(0, charaId);
            dressId = Mathf.Max(0, dressId);

            //一定値以下なら初期値の1。特定値を超えると2のものを使う
            int talentLevelIndex = 1;
            if (talentLevel >= GameDefine.CHANGE_ICON_COLOR_TALENT_LEVEL)
            {
                talentLevelIndex = CARD_THUMNAIL_TALENT_LEVEL_INDEX_MAX;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Chr{1:D4}/chr_icon_{1:D4}_{2:D6}_{3:D2}", CharaRoot, charaId, dressId, talentLevelIndex);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// 殿堂入りウマ娘のレアリティに対応するカードアイコン（一覧表示用）のリソースパスを取得する
        /// </summary>
        public static string GetTrainedCardThumbnailIconPath(MasterCardRarityData.CardRarityData rarityData, int talentLevel)
        {
            if (rarityData == null)
            {
                return "";
            }
            return GetTrainedCardThumbnailIconPath(rarityData.CharaId, rarityData.RaceDressId, talentLevel);
        }

        /// <summary>
        /// 殿堂入りウマ娘のキャラIDとドレスIDを指定したアイコンのリソースパス取得
        /// </summary>
        public static string GetTrainedCardThumbnailIconPath(int charaId, int dressId, int talentLevel)
        {
            charaId = Mathf.Max(0, charaId);
            dressId = Mathf.Max(0, dressId);

            //一定値以下なら初期値の1。特定値を超えると2のものを使う
            int talentLevelIndex = 1;
            if (talentLevel >= GameDefine.CHANGE_ICON_COLOR_TALENT_LEVEL)
            {
                talentLevelIndex = CARD_THUMNAIL_TALENT_LEVEL_INDEX_MAX;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Chr{1:D4}/trained_chr_icon_{1:D4}_{2:D6}_{3:D2}", CharaRoot, charaId, dressId, talentLevelIndex);
            return _stringBuilder.ToString();
        }

        private const int CARD_THUMNAIL_TALENT_LEVEL_INDEX_MAX = 2;
        
        /// <summary>
        /// モブIDと覚醒レベルとキャラカラーを指定したアイコンのリソースパス取得
        /// </summary>
        public static string GetTrainedMobCharaColorThumbnailIconPath(int mobId, int talentLevel, RaceDefine.CharaColorType colorType)
        {
            Debug.Assert(colorType != RaceDefine.CharaColorType.Null, "CharaColorType.Nullのシルエットアイコンは存在しないはず。呼び出すパス取得関数を間違えている");
            
            mobId = Mathf.Max(0, mobId);

            //一定値以下なら初期値の1。特定値を超えると2のものを使う
            int talentLevelIndex = 1;
            if (talentLevel >= GameDefine.CHANGE_ICON_COLOR_TALENT_LEVEL)
            {
                talentLevelIndex = CARD_THUMNAIL_TALENT_LEVEL_INDEX_MAX;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(
                "{0}trained_mob_chr_icon_{1:D4}_col{2:D2}_{3:D2}", 
                MobCardRoot, 
                mobId, 
                talentLevelIndex, 
                (int)colorType);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// モブの顔アイコンのパス取得
        /// </summary>
        public static string GetMobCharaIconPath(int mobId)
        {
            const string mobIconFormat = MOB_ROOT + "mob_chr_icon_{0:0000}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(mobIconFormat, mobId).ToString();
        }
        
        /// <summary>
        /// キャラIDとドレスIDを指定したアイコンのリソースパス取得
        /// </summary>
        public static string GetMobCharaThumbnailIconPath(int mobId, int dressId, int talentLevel)
        {
            mobId = Mathf.Max(0, mobId);
            dressId = Mathf.Max(0, dressId);

            //一定値以下なら初期値の1。特定値を超えると2のものを使う
            int talentLevelIndex = 1;
            if (talentLevel >= GameDefine.CHANGE_ICON_COLOR_TALENT_LEVEL)
            {
                talentLevelIndex = CARD_THUMNAIL_TALENT_LEVEL_INDEX_MAX;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}mob_chr_icon_{1:D4}_{2:D6}_{3:D2}", MobCardRoot , mobId , dressId, talentLevelIndex);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// キャラIDとドレスIDを指定したアイコンのリソースパス取得
        /// </summary>
        public static string GetTrainedMobCharaThumbnailIconPath(int mobId, int dressId, int talentLevel)
        {
            mobId = Mathf.Max(0, mobId);
            dressId = Mathf.Max(0, dressId);

            //一定値以下なら初期値の1。特定値を超えると2のものを使う
            int talentLevelIndex = 1;
            if (talentLevel >= GameDefine.CHANGE_ICON_COLOR_TALENT_LEVEL)
            {
                talentLevelIndex = CARD_THUMNAIL_TALENT_LEVEL_INDEX_MAX;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}trained_mob_chr_icon_{1:D4}_{2:D6}_{3:D2}", MobCardRoot , mobId , dressId, talentLevelIndex);
            return _stringBuilder.ToString();
        }


        /// <summary>
        /// キャラIDと衣装IDに対応する立ち絵（通常サイズ）のリソースパスを取得する
        /// </summary>
        /// <param name="charaId">キャラID</param>
        /// <param name="standImageIndex">画像インデックス</param>
        /// <returns></returns>
        public static string GetCharaStandMediumImagePath(int charaId, int dressId)
        {
            charaId = Mathf.Max(0, charaId);
            dressId = Mathf.Max(0, dressId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Chr{1:D4}/chara_stand_{1:D4}_{2:D6}", CharaRoot, charaId, dressId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// キャラ立ち絵パス取得
        /// </summary>
        /// <param name="cardRarityData"></param>
        /// <returns></returns>
        public static string GetCharaStandMediumImagePath(MasterCardRarityData.CardRarityData cardRarityData)
        {
            if (cardRarityData == null)
            {
                Debug.LogError("MasterCardRarityDataがnullです");
                return string.Empty;
            }

            return GetCharaStandMediumImagePath(cardRarityData.CharaId, cardRarityData.RaceDressId);
        }

        /// <summary>
        /// デフォルトレアリティ衣装の立ち絵取得
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetCharaDefaultStandMediumImagePath(int cardId)
        {
            var masterCard = MasterDataManager.Instance.masterCardData.Get(cardId);
            if (masterCard == null)
            {
                Debug.LogError("MasterCardDataがnullです");
                return string.Empty;
            }

            return GetCharaStandMediumImagePath(masterCard.CharaId, masterCard.DefaultRarityData.RaceDressId);

        }

        #endregion

        #region ミニキャラアイコン

        public enum CharaPetitCatetory
        {
            SingleStartOff = 10,         // 育成開始_OFF
            SingleStartOn = 11,          // 育成開始_ON
            SingleTopTrainingOff = 20,   // 育成_トレーニング_OFF
            SingleTopTrainingOn = 21,    // 育成_トレーニング_ON
            SingleTopRaceOff = 30,       // 育成_レース_OFF
            SingleTopRaceOn = 31,        // 育成_レース_ON
            SingleTopUraOff = 32,        // 育成_URAレース_OFF
            SingleTopUraOn = 33,         // 育成_URAレース_ON
            SingleTopTscOff = 34,        // 育成_TSCレース_OFF
            SingleTopTscOn = 35,         // 育成_TSCレース_ON
            SingleTopTargetRaceOff = 40, // 育成_目標レース_OFF
            SingleTopTargetRaceOn = 41,  // 育成_目標レース_ON
            SingleTopTargetUraOff = 42,  // 育成_目標URA_OFF
            SingleTopTargetUraOn = 43,   // 育成_目標URA_ON
            SingleTopTargetTscOff = 44,  // 育成_目標TSC_OFF
            SingleTopTargetTscOn = 45,   // 育成_目標TSC_ON
            RaceResultNextOff = 50,      // レースリザルト_次へ
            RaceResultNextOn = 51,       // レースリザルト_次へ
            RaceResultLiveOff = 60,      // レースリザルト_ライブへ
            RaceResultLiveOn = 61,       // レースリザルト_ライブへ
            HomeSingleNormal = 70,       // ホーム_育成ボタン（通常）
            HomeSingleOnTap = 71,        // ホーム_育成ボタン（タップ時）
            SingleAlert = 80,            // 育成_警告ダイアログ
            SingleResultOff = 90,        // 育成_リザルト
            SingleResultOn = 91,         // 育成_リザルト
        }
        
        /// <summary>
        /// ミニキャラアイコン（衣装別）
        /// </summary>
        public static string GetCharaPetitIconPath(int charaId, int dressId, CharaPetitCatetory category)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(CharaRoot);
            _stringBuilder.AppendFormat("Chr{0:D4}/Petit/petit_chr_{0:D4}_{1:D6}_{2:D4}", charaId, dressId, (int)category);
            return _stringBuilder.ToString();
        }
        
        #endregion

        #region Cloth

        public static string ClothFolderName = "Clothes/";
        private static string SKIRT_PARAM_DATA_PATH = "{0}" + ClothFolderName + "ast_bdy{1:D4}_{2:D2}{3}_skirt{4:D2}";

        /// <summary>
        /// スカートエクスプレッションパラメータパスの取得。
        /// </summary>
        public static string GetSkirtParamDataPath(CharacterBuildInfo buildInfo, int category)
        {
            _stringBuilder.Length = 0;
            if (buildInfo.IsSpecialDress())
            {
                _stringBuilder.AppendFormat(SKIRT_PARAM_DATA_PATH, CharacterBuildPathInfo.GetSpecialDressBodyDirPath(buildInfo.CharaId, buildInfo.DressElement.BodyTypeSub),
                    buildInfo.CharaId, buildInfo.DressElement.BodyTypeSub, string.Empty, category);
                return _stringBuilder.ToString();
            }
            else
            {
                _stringBuilder.AppendFormat(SKIRT_PARAM_DATA_PATH, CharacterBuildPathInfo.GetCommonDressBodyDirPath(buildInfo.DressElement),
                    buildInfo.DressElement.BodyType, buildInfo.DressElement.BodyTypeSub, buildInfo.GetBodyPartsIDStringForAdditional(buildInfo.DressElement.IsChangeByGender), category);
                return _stringBuilder.ToString();
            }
        }

        #endregion

        #region Dress

        // 衣装アイコン（3Dキャラ用）
        public const string DressIconRoot = OUTGAME_ASSET_ROOT + "Dress/";
        private const string DressIconPathRoot = DressIconRoot + "dress_{0:D6}_{1:D1}_{2:D2}";

        // 衣装汎用アイコン
        public const string DressCommonIconPath = DressIconRoot + "dress_000000_0_00";

        /// <summary>
        /// 衣装アイコンのパスを取得する
        /// </summary>
        /// <param name="dressId"></param>
        /// <param name="genderType"></param>
        /// <param name="color">カラーバリエーション</param>
        /// <returns></returns>
        public static string GetDressIconPath(int dressId, int genderType, int color = 0)
        {
            var dressData = MasterDataManager.Instance.masterDressData.Get(dressId);
            if (dressData != null && dressData.ColorNum == 0)
            {
                color = 0;  //カラバリが無いなら引数を無視
            }

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(DressIconPathRoot, dressId, genderType, color).ToString();
        }

        #endregion
        
        #region キャンペーン

        //キャンペーンのパーツの親フォルダ
        public const string CAMPAIGN_UIPARTS_PATH_ROOT = "UI/Parts/Campaign/";
        //キャンペーン一覧ダイアログ
        public const string DIALOG_CAMPAIGN_LIST = CAMPAIGN_UIPARTS_PATH_ROOT + "DialogCampaignList";
        public const string CAMPAIGN_ROOT = OUTGAME_ASSET_ROOT + "Campaign/";
        private const string CAMPAIGN_ICON_PATH = CAMPAIGN_ROOT + "campaign_icon_{0}_{1:D4}";
        private const string CAMPAIGN_TITLE_LOGO_PATH = CAMPAIGN_ROOT + "Title/utx_obj_title_logo_{0:D4}";
        
        // バレンタインキャラ選択ダイアログ
        public const string DIALOG_VALENTINE_CHARACTER_SELECT = CAMPAIGN_UIPARTS_PATH_ROOT + "DialogValentineCharacterSelect";
        public const string DIALOG_VALENTINE_CHARACTER_SELECT_CONFIRM = CAMPAIGN_UIPARTS_PATH_ROOT + "DialogValentineCharacterSelectConfirm";

        // ミッション系キャンペーンの簡易版ロゴ
        public const string MISSION_CAMPAIGN_SIMPLE_LOGO_PATH = CAMPAIGN_ROOT + "Mission/tex_campaign_mission_logo_00000";
        // ミッション系キャンペーンの簡易版サムネイル
        public const string MISSION_CAMPAIGN_SIMPLE_THUMBNAIL_PATH = CAMPAIGN_ROOT + "Mission/Thumb/tex_campaign_mission_list_00000";

        /// <summary>
        /// キャンペーン用のタイトルロゴを取得する
        /// </summary>
        public static string GetCampaginTitleLogo(int campaignIconId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CAMPAIGN_TITLE_LOGO_PATH , campaignIconId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// キャンペーン用のアイコンを取得する
        /// </summary>
        public static string GetCampaginIcon(int campaignIconId, bool isSmall = false)
        {
            _stringBuilder.Length = 0;
            const string strS = "s";
            const string strL = "l";
            var sizeStr = isSmall ? strS : strL;

            _stringBuilder.AppendFormat(CAMPAIGN_ICON_PATH , sizeStr , campaignIconId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミッション系キャンペーンのロゴを取得
        /// </summary>
        public static string GetMissionCampaignLogoPath(int campaignIconId)
        {
            const string PATH_FORMAT = CAMPAIGN_ROOT + "Mission/tex_campaign_mission_logo_{0:D5}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PATH_FORMAT, campaignIconId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミッション系キャンペーンのサムネイルを取得
        /// </summary>
        public static string GetMissionCampaignThumbnailPath(int campaignId)
        {
            const string PATH_FORMAT = CAMPAIGN_ROOT + "Mission/Thumb/tex_campaign_mission_list_{0:D5}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PATH_FORMAT, campaignId);
            return _stringBuilder.ToString();
        }

        #endregion

        #region 称号

        public const string HONOR_ROOT = OUTGAME_ASSET_ROOT + "Honor/";
        private const string HONOR_IMAGE_PATH = HONOR_ROOT + "honor_{0:D6}";

        /// <summary>
        /// 称号画像パス
        /// </summary>
        public static string GetHonorImagePath(int honorId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(HONOR_IMAGE_PATH, honorId);
            return _stringBuilder.ToString();
        }

        #endregion

        #region Episode

        private const int MAIN_STORY_CHAPTER = 1; // メインストーリーの部（運用で部を追加することになったら変更できるように）
        private const string STORY_ROOT = "Story/";
        private const string MAIN_STORY_MOVIE_PATH = "MainStory/mainstory_header_{0:D2}{1:D2}";
        private const string MAIN_STORY_FULL_SCREEN_MOVIE_PATH = "MainStory/Contents/mainstory_contents_{0:D4}";
        private const string MAIN_STORY_LIST_THUMB_PATH = STORY_ROOT + "MainStoryThumb/{0:D2}{1:D2}/main_story_list_thumb_{2:D2}{3:D2}_{4:D3}"; // メインストーリー：話サムネイル画像パス
        private const string MAIN_STORY_LIST_TOP_THUMB_PATH = STORY_ROOT + "MainStoryThumb/main_story_list_thumb_{0:D2}{1:D2}"; // メインストーリー：章サムネイル画像パス
        private const string CHARA_STORY_CHARA_THUMB_PATH = STORY_ROOT + "CharaStoryThumb/chara_story_thumb_{0:D4}";              // キャラストーリー：キャラサムネイル画像パス
        private const string CHARA_STORY_LIST_THUMB_PATH = STORY_ROOT + "CharaStoryThumb/{0:D4}/chara_story_thumb_{1:D4}_{2:D3}"; // キャラストーリー：話サムネイル画像パス
        public const string EPISODE_UNLOCK_DIALOG_PATH = UIPartsPath + "/Episode/DialogEpisodeUnlockInfoBase";          // メインストーリー、キャラストーリーの解放ダイアログのパス
        /// <summary> ストーリー開始設定確認ダイアログ </summary>
        public const string DIALOG_EPISODE_BEGIN_SETTING_CONFIRM = UIPartsPath + "/Episode/DialogEpisodeStartSettingConfirm";

        /// <summary> キャラストーリー画面環境設定 </summary>
        public const string CHARA_STORY_ENV_PATH = EnvParamRoot + "Story/ast_prm_chara_story_view";

        /// <summary> エクストラストーリー画面環境設定 </summary>
        public const string EXTRA_STORY_ENV_PATH = EnvParamRoot + "Story/ast_prm_extra_story_view";

        #region メインストーリー

        /// <summary>
        /// エピソード：メインストーリー：話のサムネイル画像パス
        /// </summary>
        public static string GetMainStoryListThumbPath(int partId, int storyNumber)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MAIN_STORY_LIST_THUMB_PATH,
                MAIN_STORY_CHAPTER, partId,
                MAIN_STORY_CHAPTER, partId, storyNumber);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// エピソード：メインストーリー：章のサムネイル画像パス
        /// </summary>
        /// <param name="partId"></param>
        /// <returns></returns>
        public static string GetMainStoryListTopThumbPath(int partId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MAIN_STORY_LIST_TOP_THUMB_PATH, MAIN_STORY_CHAPTER, partId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// メインストーリーのサムネイル画像パスを取得する
        /// </summary>
        public static string GetMainStoryListThumbPath(int mainStoryId)
        {
            var masterMainStory = MasterDataManager.Instance.masterMainStoryData.Get(mainStoryId);
            return GetMainStoryListThumbPath(masterMainStory.PartId, masterMainStory.StoryNumber);
        }

        /// <summary>
        /// メインストーリーの章ムービーパスを取得する
        /// </summary>
        public static string GetMainStoryThumbnailMoviePath(int partId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MAIN_STORY_MOVIE_PATH, MAIN_STORY_CHAPTER, partId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// メインストーリーの章ムービーDLパスを取得する
        /// </summary>
        public static string GetMainStoryThumbnailMovieDownloadPath(int partId)
        {
            return TextUtil.Format(
                "{0}{1}{2}",
                MovieFolderPath,
                GetMainStoryThumbnailMoviePath(partId),
                Cute.Cri.MovieManager.USM_EXTENSION);
        }
        
        /// <summary>
        /// メインストーリーのフルスクリーンムービーパスを取得する
        /// </summary>
        public static string GetMainStoryFullScreenMoviePath(int movieNumber)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MAIN_STORY_FULL_SCREEN_MOVIE_PATH, movieNumber);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// メインストーリーのフルスクリーンムービーDLパスを取得する
        /// </summary>
        public static string GetMainStoryFullScreenMovieDownloadPath(int movieNumber)
        {
            return TextUtil.Format(
                "{0}{1}{2}",
                MovieFolderPath,
                GetMainStoryFullScreenMoviePath(movieNumber),
                Cute.Cri.MovieManager.USM_EXTENSION);
        }

        #endregion

        #region キャラストーリー

        /// <summary>
        /// エピソード：キャラストーリー：キャラサムネイル画像パス
        /// </summary>
        public static string GetEpisodeCharaPartThumbPath(int charaId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CHARA_STORY_CHARA_THUMB_PATH, charaId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// エピソード：キャラストーリー：話サムネイル画像パス
        /// </summary>
        public static string GetEpisodeCharaStoryThumbPath(int charaId, int episodeIndex)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CHARA_STORY_LIST_THUMB_PATH, charaId, charaId, episodeIndex);
            return _stringBuilder.ToString();
        }

        #endregion

        #endregion

        #region スキル
        public const string SKILL_ICON_DIR = OUTGAME_ASSET_ROOT + "SkillIcon/";

        /// <summary>
        /// スキルのアイコンパス
        /// </summary>
        /// <param name="skillIconId"></param>
        /// <returns></returns>
        public static string GetSkillIconPath(int skillIconId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SKILL_ICON_DIR + "utx_ico_skill_{0}", skillIconId);
            return _stringBuilder.ToString();
        }

        #endregion

        #region Effect

        public const string EffectRoot = "3d/Effect/";
        public const string EffectCommonRoot = EffectRoot + "Common/";
        public const string EffectEnvRoot = EffectRoot + "Env/";
        public const string EFFECT_SINGLE_MODE_ROOT = EffectRoot + "SingleMode/";
        public const string EFFECT_HOME_JUKEBOX_ROOT = EffectRoot + "Jukebox/";

        public const string EffectShadowPath = EffectRoot + "Chara/pfb_eff_chr_shadow_01";
        private const string EffectFootPath = EffectCommonRoot + "pfb_eff_com_{0}_{1:00}_{2:00}";
        private const string RichEffectFootPath = EffectCommonRoot + "rich/" + "pfb_eff_com_{0}_{1:00}_{2:00}_3d";
        private const string RichEffectBreathPath = EffectCommonRoot + "rich/" + "pfb_eff_com_breath{0:00}_{1:00}_{2:00}_3d";
        
        private const string EffectEnvPath = EffectEnvRoot + "pfb_eff_env_";
        private const string RichEffectEnvPath = EffectEnvPath + "{0}_3d";

        public const string TrainingEffectRootPath = EffectRoot + "Training/";
        private const string TrainingEffectFootPath = TrainingEffectRootPath + "pfb_eff_tra_{0}_{1:00}_{2:00}";
        
        private const string EFFECT_TYPE_STR_TURF = "turf";
        private const string EFFECT_TYPE_STR_DIRT = "dirt";

        /// <summary>
        /// キャラクタ足元エフェクト用の時間と天気の丸めこみ処理。
        /// </summary>
        /// <param name="time"></param>
        /// <param name="weather"></param>
        /// <returns></returns>
        public static (int, int) ConvertTimeAndWeatherForFootEffect(RaceDefine.Time time, RaceDefine.Weather weather)
        {
            const int TIME_MORNING_NO = 1;    // 朝・昼
            const int TIME_EVENING_NO = 2;    // 夕
            const int TIME_NIGHT_NO = 3;      // 夜

            int timeNo;
            if (time == RaceDefine.Time.Morning || time == RaceDefine.Time.Daytime)
            {
                timeNo = TIME_MORNING_NO;
            }
            else if (time == RaceDefine.Time.Night)
            {
                timeNo = TIME_NIGHT_NO;
            }
            else
            {
                timeNo = TIME_EVENING_NO;
            }

            const int WeatherSunnyNo = 1;   //晴れ
            const int WeatherRainNo = 2;    //雨・雪
            const int WeatherCloudNo = 3;   //曇り

            int weatherNo;
            if (weather == RaceDefine.Weather.Sunny)
            {
                weatherNo = WeatherSunnyNo;
            }
            else if (weather == RaceDefine.Weather.Cloudy)
            {
                weatherNo = WeatherCloudNo;
            }
            else
            {
                weatherNo = WeatherRainNo;
            }

            return (timeNo, weatherNo);
        }

        /// <summary>
        /// キャラクター足元のエフェクトパス取得
        /// </summary>
        /// <param name="type"></param>
        /// <param name="time"></param>
        /// <param name="weather"></param>
        /// <returns></returns>
        public static string GetFootEffectName(RaceDefine.GroundType type, RaceDefine.Time time, RaceDefine.Weather weather)
        {
            (int timeNo, int weatherNo) = ConvertTimeAndWeatherForFootEffect(time, weather);
            var raceType = (type == RaceDefine.GroundType.Turf) ? EFFECT_TYPE_STR_TURF : EFFECT_TYPE_STR_DIRT;
            
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(EffectFootPath, raceType, timeNo, weatherNo);
            return _stringBuilder.ToString();
        }

        public static string GetRichFootEffectName(RaceDefine.GroundType type, RaceDefine.Time time, RaceDefine.Weather weather)
        {
            (int timeNo, int weatherNo) = ConvertTimeAndWeatherForFootEffect(time, weather);
            var raceType = (type == RaceDefine.GroundType.Turf) ? EFFECT_TYPE_STR_TURF : EFFECT_TYPE_STR_DIRT;
            
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RichEffectFootPath, raceType, timeNo, weatherNo);
            return _stringBuilder.ToString();
        }
            
        public static string GetRichBreathEffectName(BreathSmokeController.EffectType effectType, RaceDefine.Time time, RaceDefine.Weather weather)
        {
            // 吐息は冬の雪のみ出す
            const int WeatherSnowNo = 2;    //雪
            (int timeNo, int weatherNo) = ConvertTimeAndWeatherForFootEffect(time, weather);

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RichEffectBreathPath, (int)effectType + 1, timeNo, WeatherSnowNo);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 通常のエフェクトパスの取得(雨、雪、さくら等)
        /// レースの環境設定ファイルに『Rain_01』等と文字が設定されていて、その値が渡される。
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string GetEnvEffectPath(string name)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append(EffectEnvPath);
            _stringBuilder.Append(name);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// リッチ用のエフェクトパスの取得(確認当時の実装だと雨雪のみ)
        /// 基本的には通常のエフェクト+ファイル名に『_3d』をつけるだけ。
        /// 
        /// リッチ時パドックのみ特殊処理で読むエフェクトを変える必要があるため、
        /// 予め『Snow_01』を『Snow_02』に変換しておく。
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string GetEnvRichEffectPath(string name)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RichEffectEnvPath, name);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// キャラクター足元のエフェクトパス取得(育成)
        /// </summary>
        /// <param name="type"></param>
        /// <param name="time"></param>
        /// <param name="weather"></param>
        /// <returns></returns>
        public static string GetTrainingFootEffectName(CutInTimelineData.FootSmokeType type, RaceDefine.Weather weather)
        {
            const int CommonId = 1; //足元エフェクトの共通No
            const int WeatherSunnyNo = 1;   //晴れ・曇り
            const int WeatherRainNo = 2;   //雨・雪

            int weatherNo;
            if (weather == RaceDefine.Weather.Sunny || weather == RaceDefine.Weather.Cloudy)
            {
                weatherNo = WeatherSunnyNo;
            }
            else
            {
                weatherNo = WeatherRainNo;
            }

            _stringBuilder.Length = 0;
            switch (type)
            {
                case CutInTimelineData.FootSmokeType.Turf:
                    _stringBuilder.AppendFormat(TrainingEffectFootPath, "turf", CommonId, weatherNo);
                    break;
                case CutInTimelineData.FootSmokeType.Dirt:
                    _stringBuilder.AppendFormat(TrainingEffectFootPath, "dirt", CommonId, weatherNo);
                    break;
                case CutInTimelineData.FootSmokeType.Slope:
                    _stringBuilder.AppendFormat(TrainingEffectFootPath, "slope", CommonId, weatherNo);
                    break;
                case CutInTimelineData.FootSmokeType.Beach:
                    //ビーチは天候の影響を受けない
                    _stringBuilder.AppendFormat(TrainingEffectFootPath, "beach", CommonId, weatherNo);
                    break;
            }

            return _stringBuilder.ToString();
        }

        public static string GetSmokeEffectPrefabPath(CutInTimelineData.FootSmokeType type, RaceDefine.Weather weather)
        {
            string footSmokePath = "";
            switch (type)
            {
                case CutInTimelineData.FootSmokeType.Dirt:
                    footSmokePath = GetTrainingFootEffectName(CutInTimelineData.FootSmokeType.Dirt, weather);
                    break;
                case CutInTimelineData.FootSmokeType.Turf:
                    footSmokePath = GetTrainingFootEffectName(CutInTimelineData.FootSmokeType.Turf, weather);
                    break;
                case CutInTimelineData.FootSmokeType.Slope:
                    footSmokePath = GetTrainingFootEffectName(CutInTimelineData.FootSmokeType.Slope, weather);
                    break;
                case CutInTimelineData.FootSmokeType.Beach:
                    footSmokePath = GetTrainingFootEffectName(CutInTimelineData.FootSmokeType.Beach, weather);
                    break;
            }

            return footSmokePath;
        }

        /// <summary>
        /// 漫画目
        /// </summary>
        public const string CHARA_EMOTION_EFFECT_ROOT = EffectRoot + "CharaEmotion/";
        public const string MANGAME_EFFECT_FILE_PREFIX = "pfb_eff_chr_emo_eye_";
        private const string MANGAME_EFFECT_PATH = CHARA_EMOTION_EFFECT_ROOT + MANGAME_EFFECT_FILE_PREFIX + "{0:000}";
        public const string NOSEBLEED_EFFECT_FILE_FORMAT = "pfb_eff_chr_emo_nosebleed_{0:000}";
        private const string NOSEBLEED_EFFECT_PATH = CHARA_EMOTION_EFFECT_ROOT + NOSEBLEED_EFFECT_FILE_FORMAT;

        /// <summary>
        /// 漫画目用エフェクト取得。
        /// </summary>
        public static string GetMangameEffectPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MANGAME_EFFECT_PATH, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 鼻血エフェクト取得
        /// </summary>
        public static string GetNosebleedEffectPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(NOSEBLEED_EFFECT_PATH, id);
            return _stringBuilder.ToString();
        }

        #region ミニキャラ
        public const string MINI_CHARA_EFFECT_ROOT = EffectRoot + "Mini/";
        private const string MINI_CHARA_EFFECT_PATH = MINI_CHARA_EFFECT_ROOT + "pfb_eff_mini_{0:D2}";

        /// <summary>
        /// ミニキャラ用エフェクトパスの取得。
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetMiniCharaEffectPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_CHARA_EFFECT_PATH, id);
            return _stringBuilder.ToString();
        }
        #endregion // ミニキャラ

#if UNITY_EDITOR

        /// <summary>
        /// **Editor用
        /// エフェクトのリソースフォルダ
        /// </summary>
        /// <returns></returns>
        public static string GetEffectResourcePath()
        {
            return AssetBundleRoot + EffectCommonRoot;
        }

        /// <summary>
        /// **Editor用
        /// エフェクトのワーキングフォルダ
        /// </summary>
        /// <returns></returns>
        public static string GetEffectWorkPath()
        {
            return SourceResourcesAssetsPath + "3d/Effect/";
        }

#endif
        #endregion

        #region Home

        public const string HOME_MOTION_ROOT = MOTION_ROOT + "Home/";
        public const string HOME_ENV_ROOT = "3d/env/Home/";
        public const string HOME_ENV_POSTER_ROOT = HOME_ENV_ROOT + "Poster/";        
        public const string HOME_BANNER_PATH = OUTGAME_ASSET_ROOT + "Banner/";
        public const string HOME_DIRECTOR_PATH = "Prefabs/Home/HomeDirector";
        public const string HOME_BG_CONTROLLER_PATH = "Prefabs/Home/HomeBgController";
        public const string HOME_CAMERA_SWITCHER_PATH = "Prefabs/Home/HomeCameraSwitcher";

        public const string HOME_HEADER_FLASH = FLASH_ROOT + "Home/pf_fl_home_header00";
        
        public const string HOME_UI_PARTS_ROOT = UIPartsPath + "/Home/";

        // ホーム専用戻るボタン
        public const string HOME_BACK_BUTTON = HOME_UI_PARTS_ROOT + "HomeBackButton";
        
        // ホーム専用大見出し
        public const string PARTS_HOME_HEADER_TITLE = HOME_UI_PARTS_ROOT + "PartsHomeHeaderTitle";

        // ウマ娘変更
        public const string DIALOG_HOME_CHANGE_FAVORITE = HOME_UI_PARTS_ROOT + "DialogHomeChangeFavorite";

        // ホーム：キャラ変更ダイアログ
        public const string DIALOG_HOME_CHARA_SELECT = HOME_UI_PARTS_ROOT + "DialogHomeCharaSelect";

        // プレゼント
        public const string DIALOG_PRESENT_PATH = HOME_UI_PARTS_ROOT + "Present/DialogPresent";
        public const string DIALOG_REWARD_RECIEVE_SINGLE_PATH = HOME_UI_PARTS_ROOT + "Present/DialogRewardRecieveSingle";
        public const string DIALOG_REWARD_RECIEVE_PATH = HOME_UI_PARTS_ROOT + "Present/DialogRewardRecieve";
        public const string DIALOG_SERIAL_REWARD_RECIEVE_PATH = HOME_UI_PARTS_ROOT + "Present/DialogSerialRewardRecieve";

        // ジュエルアイコンのパス
        public const string JEWEL_ICON_ROOT = OUTGAME_ASSET_ROOT + "Shop/Jewel/";
        public const string JEWEL_ICON_NAME = "img_shopitem_jewel_{0:0000}";

        // 限定セールス
        public const string LIMITED_SALES_ROOT = OUTGAME_ASSET_ROOT + "Shop/LimitedSales/";
        public const string LIMITED_SALES_PREP_IMAGE = LIMITED_SALES_ROOT + "img_shopitem_limitedsales_0001";
        public const string LIMITED_SALES_OPEN_IMAGE = LIMITED_SALES_ROOT + "img_shopitem_limitedsales_0002";

        public const string SHOP_PARTS_ROOT = UIPartsPath + "/Shop/";

        /// <summary>
        /// ジュエル購入ダイアログ
        /// </summary>
        public const string DIALOG_BUY_JEWEL = SHOP_PARTS_ROOT + "DialogBuyJewel";

        /// <summary>
        /// ジュエル購入確認ダイアログ
        /// </summary>
        public const string DIALOG_CONFIRM_BUY_JEWEL = SHOP_PARTS_ROOT + "DialogConfirmBuyJewel";

        /// <summary>
        /// ジュエル購入完了確認ダイアログ
        /// </summary>
        public const string DIALOG_RESULT_BUY_JEWEL = SHOP_PARTS_ROOT + "DialogResultBuyJewel";

        /// <summary>
        /// グーグルプレイポイント交換完了ダイアログ
        /// </summary>
        public const string DIALOG_GOOGLE_REWARD_SUCCEED = SHOP_PARTS_ROOT + "DialogGoogleRewardSucceed";

        /// <summary>
        /// ユーザーの所持ジュエル情報
        /// </summary>
        public const string DIALOG_USER_JEWEL_INFO = SHOP_PARTS_ROOT + "DialogUserJewelInfo";

        /// <summary>
        /// ユーザーの所持ジュエル詳細情報
        /// </summary>
        public const string DIALOG_USER_JEWEL_PRECISE_INFO = SHOP_PARTS_ROOT + "DialogUserJewelPreciseInfo";

        /// <summary>
        /// 最大ジュエル超過警告ダイアログ
        /// </summary>
        public const string DIALOG_EXCESS_MAX_JEWEL_ALERT = SHOP_PARTS_ROOT + "DialogExcessMaxJewelAlert";
        
        /// <summary>
        /// デイリージュエルパック通知ダイアログ
        /// </summary>
        public const string DIALOG_NOTIFY_DAILY_JEWEL = HOME_UI_PARTS_ROOT + "DialogNotifyDailyJewel";
#if BUMA_T
        /// <summary>
        /// 每日体力礼包通知弹窗界面
        /// </summary>
        public const string DIALOG_NOTIFY_DAILY_PACK_TP = HOME_UI_PARTS_ROOT + "DialogNotifyDailyPackTp";
#endif

        /// <summary>
        /// ガチャストック通知ダイアログ
        /// </summary>
        public const string DIALOG_NOTIFY_GACHA_STOCK = HOME_UI_PARTS_ROOT + "DialogNotifyGachaStock";

        /// <summary>
        /// 立ち位置IDをモーション名に使用されている文字列に変換して返す
        /// </summary>
        /// <returns></returns>
        public static string HomeStandPosToMotionStr(HomeDefine.StandPos pos)
        {
            switch (pos)
            {
                case HomeDefine.StandPos.Mypage:
                case HomeDefine.StandPos.Character:
                case HomeDefine.StandPos.Story:
                case HomeDefine.StandPos.Race:
                case HomeDefine.StandPos.Race_Support:
                    return ((int)pos).ToString("D3");
                default:
                    Debug.LogWarning("立ち位置IDに対応するモーションが用意されていません");
                    return ((int)HomeDefine.StandPos.Character).ToString("D3");
            }
        }

        /// <summary>
        /// ホーム用フェイシャルアニメーションのパス
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="temper"></param>
        /// <param name="suffix"></param>
        /// <returns></returns>
        public static string GetHomeMainCharaFacialPath(HomeDefine.StandPos pos, int temper, string stateSymbol, int valiation = 0)
        {
            const string FacialPath = HOME_MOTION_ROOT + "Facial/Type{0:00}/" + CharacterHomeTypeMotionMame + "{0:00}_{1:D3}_{2}_{3}_face_driven";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(FacialPath, temper, valiation, HomeStandPosToMotionStr(pos), stateSymbol).ToString();
        }

        /// <summary>
        /// ホーム用の耳アニメーションパス
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="temper"></param>
        /// <param name="stateSymbol"></param>
        /// <returns></returns>
        public static string GetHomeMainCharaEarPath(HomeDefine.StandPos pos, int temper, string stateSymbol, int valiation = 0)
        {
            const string FacialPath = HOME_MOTION_ROOT + "Facial/Type{0:00}/" + CharacterHomeTypeMotionMame + "{0:00}_{1:D3}_{2}_{3}_ear_driven";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(FacialPath, temper, valiation, HomeStandPosToMotionStr(pos), stateSymbol).ToString();
        }

        /// <summary>
        /// 立ち位置別モーションのファイルパス取得
        /// </summary>
        /// <param name="buildInfo"></param>
        /// <param name="pos"></param>
        /// <param name="suffix"></param>
        /// <returns></returns>
        public static string GetStandPosMotionPath(CharacterBuildInfo buildInfo, HomeDefine.StandPos pos, string suffix, int valiation = 0)
        {
            int personality = (int)MasterHomeCharacterType.GetChangePersonalityType(pos, (ModelLoader.DefaultPersonalityType)(int)buildInfo.DefaultPersonalityType);
            string homeMotionDirectory = GetCharacterHomePersonalityMotionDirectory(personality);
            var assetName = GetStandPosMotionName(buildInfo, pos, suffix, valiation);
            return TextUtil.Format("{0}{1}",homeMotionDirectory,assetName);
        }

        /// <summary>
        /// 事前に求めたクリップ名からファイルパスを求める
        /// </summary>
        /// <param name="buildInfo"></param>
        /// <param name="pos"></param>
        /// <param name="motionName"></param>
        /// <returns></returns>
        public static string GetStandPosMotionPath(CharacterBuildInfo buildInfo, HomeDefine.StandPos pos, string motionName)
        {
            int personality = (int)MasterHomeCharacterType.GetChangePersonalityType(pos, (ModelLoader.DefaultPersonalityType)(int)buildInfo.DefaultPersonalityType);
            string homeMotionDirectory = GetCharacterHomePersonalityMotionDirectory(personality);
            return TextUtil.Format("{0}{1}", homeMotionDirectory, motionName);
        }

        /// <summary>
        /// 立ち位置別モーションのファイル名取得
        /// </summary>
        /// <param name="buildInfo"></param>
        /// <param name="pos"></param>
        /// <param name="suffix"></param>
        /// <returns></returns>
        public static string GetStandPosMotionName(CharacterBuildInfo buildInfo, HomeDefine.StandPos pos, string suffix, int valiation = 0)
        {
            int personality = (int)MasterHomeCharacterType.GetChangePersonalityType(pos, (ModelLoader.DefaultPersonalityType)(int)buildInfo.DefaultPersonalityType);
            string posString = HomeStandPosToMotionStr(pos);
            return string.Format(HOME_MOTION_CLIP_NAME, personality, valiation, posString, suffix);
        }

        /// <summary>
        /// バナーテクスチャ取得
        /// </summary>
        /// <param name="bannerId"></param>
        /// <returns></returns>
        public static string GetBannerPath(int bannerId)
        {
            return string.Format(HOME_BANNER_PATH + "img_bnr_home_{0:D6}", bannerId);
        }

        /// <summary>
        /// キャロットストーンアイコンのパスの取得
        /// </summary>
        public static string GetCarrotStoneIconPath(int index)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(JEWEL_ICON_ROOT + JEWEL_ICON_NAME, index);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// イベントレースボタンのテクスチャを取得
        /// </summary>
        public const string HOME_EVENT_RACE_BUTTON_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_00";

        /// <summary>
        /// イベントレース開催中のアニメーション
        /// </summary>
        public const string HOME_EVENT_RACE_BUTTON_ANIM_PATH = FLASH_COMIBINE_TIMELINE_ROOT + "Home/" + "tat_home_btn_race_eventopen00";

        #region Home 3D

        public const string HOME_ROOT = "Home/";
        public const string HOME_CAMERA_ROOT = HOME_ROOT + "Camera/";
        public const string HOME_CHARACTER_ROOT = HOME_ROOT + "Character/";
        public const string HOME_WALK_ROOT = HOME_ROOT + "WalkPath/";
        public const string HOME_ENV_PARAM_ROOT = HOME_ROOT + "Env/";
        public const string HOME_IMAGE_EFFECT_ROOT = HOME_ROOT + "ImageEffect/";

        public const string HOME_CAMERA_LAYOUT_PATH_FORMAT = HOME_CAMERA_ROOT + "home_camera_layout{0:D5}";
        public const string HOME_CHARACTER_LAYOUT_PATH_FORMAT = HOME_CHARACTER_ROOT + "home_character_layout{0:D5}";
        public const string HOME_WALK_PATH_CURVE_DATA_PATH = HOME_WALK_ROOT + "ast_walk_path_home_curve_data{0:D5}_{1:D2}";
        public const string HOME_WALK_PATH_DATA_PATH_FORMAT = HOME_WALK_ROOT + "ast_walk_path_home_data{0:D5}";

        /// <summary>
        /// キャラ毎の、カメラに対する補正値
        /// </summary>
        public const string HOME_CAMERA_CHARACTER_CORRECTION_EXTENSION_PATH = HOME_CAMERA_ROOT + "CameraCharacterCorrectionExtension";

        /// <summary>
        /// ホームのカメラ配置プレハブのパスを取得
        /// </summary>
        /// <param name="modelId"></param>
        /// <returns></returns>
        public static string GetHomeCameraLayoutPath(int modelId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(HOME_CAMERA_LAYOUT_PATH_FORMAT, modelId);
            return _stringBuilder.ToString();
        }
        public static string GetHomeCameraLayoutAssetPath(int modelId)
        {
            return BundleResourcesAssetsPath + GetHomeCameraLayoutPath(modelId) + ".prefab";
        }

        /// <summary>
        /// ホームのキャラ位置プレハブのパスを取得
        /// </summary>
        /// <param name="modelId"></param>
        /// <returns></returns>
        public static string GetHomeCharacterLayoutPath(int modelId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(HOME_CHARACTER_LAYOUT_PATH_FORMAT, modelId);
            return _stringBuilder.ToString();
        }
        public static string GetHomeCharacterLayoutAssetPath(int modelId)
        {
            return BundleResourcesAssetsPath + GetHomeCharacterLayoutPath(modelId) + ".prefab";
        }

        /// <summary>
        /// ホームの歩行パス用の曲線データのパス
        /// </summary>
        private static string GetHomeWalkPathCurveDataPath(int modelId, int pathId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(HOME_WALK_PATH_CURVE_DATA_PATH, modelId, pathId);
            return _stringBuilder.ToString();
        }
        public static string GetHomeWalkCurveDataAssetPath(int modelId, int pathId)
        {
            return BundleResourcesAssetsPath + GetHomeWalkPathCurveDataPath(modelId, pathId) + ".asset";
        }

        /// <summary>
        /// ホームの歩行パスアセットのパス
        /// </summary>
        /// <param name="modelId"></param>
        /// <returns></returns>
        public static string GetHomeWalkPathDataPath(int modelId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(HOME_WALK_PATH_DATA_PATH_FORMAT, modelId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ホーム背景オブジェクト
        /// </summary>
        /// <returns></returns>
        public static string GetHomeBgPath(int modelId, int eventId, int envId)
        {
            const string HomeBgPath = HOME_ENV_ROOT + "home{0}/Main/pfb_env_home{0}_main{1:000}_{2:000}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeBgPath, modelId, eventId, envId).ToString();
        }

        /// <summary>
        /// ホーム追加オブジェクト
        /// </summary>
        /// <returns></returns>
        public static string GetHomeAdditionPath(int modelId, int eventId, int envId)
        {
            const string HomeAdditionPath = HOME_ENV_ROOT + "home{0}/Addition/pfb_env_home{0}_add{1:000}_{2:000}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeAdditionPath, modelId, eventId, envId).ToString();
        }

        /// <summary>
        /// ホーム中景オブジェクト
        /// </summary>
        /// <returns></returns>
        public static string GetHomeMiddleBgPath(int modelId, int eventId, int envId)
        {
            const string HomeMiddleBgPath = HOME_ENV_ROOT + "home{0}/Middle/pfb_env_home{0}_mid{1:000}_{2:000}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeMiddleBgPath, modelId, eventId, envId).ToString();
        }

        /// <summary>
        /// 天球
        /// </summary>
        /// <returns></returns>
        public static string GetHomeSkyPath(int modelId, int eventId, int envId)
        {
            const string HomeSkyPath = HOME_ENV_ROOT + "home{0}/Sky/pfb_env_home{0}_sky{1:000}_{2:000}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeSkyPath, modelId, eventId, envId).ToString();
        }

        /// <summary>
        /// ホームテクスチャ(main)
        /// </summary>
        public static string GetHomeTexMainBasePath(int modelId)
        {
            const string HomeTexBasePath = HOME_ENV_ROOT + "home{0}/Main/Textures/";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeTexBasePath, modelId).ToString();
        }

        /// <summary>
        /// ホームテクスチャ(addition)
        /// </summary>
        public static string GetHomeTexAdditionBasePath(int modelId)
        {
            const string HomeTexAddPath = HOME_ENV_ROOT + "home{0}/Addition/Textures/";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeTexAddPath, modelId).ToString();
        }

        /// <summary>
        /// ホームテクスチャ(middle)
        /// </summary>
        public static string GetHomeTexMiddleBasePath(int modelId)
        {
            const string HomeTexMidPath = HOME_ENV_ROOT + "home{0}/Middle/Textures/";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeTexMidPath, modelId).ToString();
        }

        /// <summary>
        /// ホームテクスチャ(sky)
        /// </summary>
        public static string GetHomeTexSkyBasePath(int modelId)
        {
            const string HomeTexSkyPath = HOME_ENV_ROOT + "home{0}/Sky/Textures/";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeTexSkyPath, modelId).ToString();
        }

        /// <summary>
        /// ポスターテクスチャ
        /// </summary>
        /// <param name="posterId"></param>
        /// <param name="isLarge"></param>
        /// <returns></returns>
        public static string GetHomePosterTexturePath(int posterId, bool isLarge)
        {
            const string POSTER_PATH_FORMAT = HOME_ENV_ROOT + "Poster/tex_env_home_poster_{0:D4}";
            const string LARGE_POSTER_PATH_FORMAT = HOME_ENV_ROOT + "Poster/tex_env_home_poster_large_{0:D4}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(isLarge ? LARGE_POSTER_PATH_FORMAT : POSTER_PATH_FORMAT, posterId).ToString();
        }

        /// <summary>
        /// ジュークボックステクスチャ(Name)
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetHomeJukeboxNameTexturePath(int musicId)
        {
            const string JUKEBOX_NAME_PATH_FORMAT = HOME_ENV_ROOT + "Jukebox/Name/home_musicname_{0:D4}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(JUKEBOX_NAME_PATH_FORMAT, musicId).ToString();
        }

        /// <summary>
        /// ジュークボックステクスチャ(Jacket)
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetHomeJukeboxJacketTexturePath(int musicId)
        {
            const string JUKEBOX_JACKET_PATH_FORMAT = HOME_ENV_ROOT + "Jukebox/Jacket/home_jacket_icon_l_{0:D4}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(JUKEBOX_JACKET_PATH_FORMAT, musicId).ToString();
        }

        /// <summary>
        /// ジュークボックステクスチャ(SideLamp)
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetHomeJukeboxSideLampTexturePath(int lampType)
        {
            const string JUKEBOX_LAMPTEXTURE_PATH_FORMAT = HOME_ENV_ROOT + "Jukebox/Lamp/home_musiclamp_{0:D4}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(JUKEBOX_LAMPTEXTURE_PATH_FORMAT, lampType).ToString();
        }

        /// <summary>
        /// ジュークボックスアニメーション(SideLamp)
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetHomeJukeboxSideLampAnimationPath(int animationId)
        {
            const string JUKEBOX_LAMPANIME_PATH_FORMAT = HOME_ENV_ROOT + "Jukebox/Lamp/home_musiclamp_anime_{0:D4}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(JUKEBOX_LAMPANIME_PATH_FORMAT, animationId).ToString();
        }

        /// <summary>
        /// ジュークボックスミニ表示用
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetHomeJukeboxMiniViewerPath()
        {
            const string JUKEBOX_MINIVIEW_PATH_FORMAT = HOME_ENV_ROOT + "Jukebox/Mini/MiniViewer";
            return JUKEBOX_MINIVIEW_PATH_FORMAT;
        }

        /// <summary>
        /// ジュークボックスミニ表示用
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetHomeJukeboxMiniEffectPath(int effectType, int subType = 1)
        {
            const string JUKEBOX_MINI_EFFECTLPATH_FORMAT = EFFECT_HOME_JUKEBOX_ROOT + "pfb_eff_jukebox_{0:D2}/pfb_eff_jukebox_{0:D2}_{1:D2}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(JUKEBOX_MINI_EFFECTLPATH_FORMAT, subType, effectType).ToString();
        }

        /// <summary>
        /// ホームの環境設定のパス
        /// </summary>
        /// <param name="modelId"></param>
        /// <param name="eventId"></param>
        /// <param name="envId"></param>
        /// <returns></returns>
        public static string GetHomeEnvParamPath(int modelId, int eventId, int envId)
        {
            const string HomeEnvParamPath = HOME_ENV_PARAM_ROOT + "home{0}/ast_prm_home{0}_{1:00000}_{2:000}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeEnvParamPath, modelId, eventId, envId).ToString();
        }

        /// <summary>
        /// ホームのイメージエフェクト設定のパス
        /// </summary>
        /// <param name="modelId"></param>
        /// <param name="eventId"></param>
        /// <param name="envId"></param>
        /// <returns></returns>
        public static string GetHomeImageEffectParamPath(int modelId, int eventId, int envId)
        {
            const string HomeEnvParamPath = HOME_IMAGE_EFFECT_ROOT + "home{0}/image_effect_home{0}_{1:00000}_{2:000}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(HomeEnvParamPath, modelId, eventId, envId).ToString();
        }

        #endregion old bg

        #region Home UIテクスチャ

        /// <summary>
        /// ホームUIテクスチャフォルダ
        /// </summary>
        private const string HOME_UI_TEXTURE_ROOT = HOME_ROOT + "UI/Texture/";

        /// <summary>
        /// 育成ウマ娘ボタンのテクスチャパス
        /// </summary>
        public const string HOME_CARD_ROOT_TEXTURE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_umamusume_00";
        
        /// <summary>
        /// サポートカードボタンのテクスチャパス
        /// </summary>
        public const string HOME_SUPPORT_CARD_ROOT_TEXTURE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_umamusume_01";
        
        /// <summary>
        /// 殿堂入りウマ娘ボタンのテクスチャパス
        /// </summary>
        public const string HOME_TRAINED_CHARA_ROOT_TEXTURE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_umamusume_02";

        /// <summary>
        /// メインストーリーボタンのテクスチャパス
        /// </summary>
        public const string HOME_MAIN_STORY_BUTTON_TEXTURE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_story_00";
        
        /// <summary>
        /// ウマ娘ストーリーボタンのテクスチャパス
        /// </summary>
        public const string HOME_CHARACTER_STORY_BUTTON_TEXTURE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_story_01";
        
        /// <summary>
        /// エクストラストーリーボタンのテクスチャパス
        /// </summary>
        public const string HOME_EXTRA_STORY_BUTTON_TEXTURE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_story_02";

        /// <summary>
        /// チーム競技場ボタンのテクスチャパス：降級
        /// </summary>
        public const string HOME_TEAM_STADIUM_BUTTON_TEXTURE_DOWN = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_down_01";

        /// <summary>
        /// チーム競技場ボタンのテクスチャパス：維持
        /// </summary>
        public const string HOME_TEAM_STADIUM_BUTTON_TEXTURE_KEEP = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_keep_01";

        /// <summary>
        /// チーム競技場ボタンのテクスチャパス：昇格
        /// </summary>
        public const string HOME_TEAM_STADIUM_BUTTON_TEXTURE_UP = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_up_01";

        /// <summary>
        /// チーム競技場ボタンのテクスチャパス：集計中
        /// </summary>
        public const string HOME_TEAM_STADIUM_BUTTON_TEXTURE_AGGREGATE = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_aggregate_01";

        /// <summary>
        /// レース画面 第1階層の旧「デイリーレース」ボタンのテクスチャ（10004500のリリース後なら削除OK）
        /// </summary>
        public const string HOME_DAILY_BUTTON_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_01";

        /// <summary>
        /// レース画面 第1階層の「デイリープログラム」ボタンのテクスチャ
        /// </summary>
        public const string HOME_DAILY_PROGRAM_BUTTON_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_08";

        /// <summary>
        /// レース画面 第1.5階層の「デイリーレース」ボタンのテクスチャ
        /// </summary>
        public const string HOME_DAILY_RACE_BUTTON_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_06";

        /// <summary>
        /// レース画面 第1.5階層の「デイリーレジェンドレース」ボタンのテクスチャ
        /// </summary>
        public const string HOME_DAILY_LEGEND_RACE_BUTTON_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_07";

        /// <summary>
        /// 旧ルームマッチボタンのテクスチャを取得(練習公開後削除)
        /// </summary>
        public const string HOME_ROOM_MATCH_TEXTURE_PATH_OLD = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_03";

        /// <summary>
        /// エキシビションボタンのテクスチャを取得
        /// </summary>
        public const string HOME_EXHIBITION_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_02";

        /// <summary>
        /// 練習ボタンのテクスチャを取得
        /// </summary>
        public const string HOME_PRACTICE_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_04";

        /// <summary>
        /// ルームマッチボタンのテクスチャを取得
        /// </summary>
        public const string HOME_ROOM_MATCH_TEXTURE_PATH = HOME_UI_TEXTURE_ROOT + "utx_btn_home_race_05";

        /// <summary>
        /// チーム競技場ボタンのテクスチャを取得
        /// </summary>
        /// <param name="canEntry"></param>
        /// <param name="promotionType"></param>
        /// <returns></returns>
        public static string GetHomeTeamStadiumButtonTexturePath(bool canEntry, TeamStadiumDefine.PromotionType promotionType)
        {
            if (canEntry)
            {
                switch (promotionType)
                {
                    case TeamStadiumDefine.PromotionType.Down:
                        return HOME_TEAM_STADIUM_BUTTON_TEXTURE_DOWN;
                    case TeamStadiumDefine.PromotionType.Keep:
                        return HOME_TEAM_STADIUM_BUTTON_TEXTURE_KEEP;
                    case TeamStadiumDefine.PromotionType.Up:
                        return HOME_TEAM_STADIUM_BUTTON_TEXTURE_UP;
                }
            }

            return HOME_TEAM_STADIUM_BUTTON_TEXTURE_AGGREGATE;
        }

        #endregion

        #endregion

        #region サポートカード
        public const string SUPPORT_ROOT = "SupportCard/";

        // LvUpダイアログ
        public const string DIALOG_SUPPORT_STRENGTHEN_CONFIRM = UIPartsPath + "/SupportCard/DialogSupportCardStrengthenConfirm";
        // 上限解放確認ダイアログ
        public const string DIALOG_SUPPORT_LIMIT_BREAK_CONFIRM = UIPartsPath + "/SupportCard/DialogSupportCardLimitBreakConfirm";
        // 上限解放確認ダイアログ(アイテム使用)
        public const string DIALOG_SUPPORT_LIMIT_BREAK_ITEM_CONFIRM = UIPartsPath + "/SupportCard/DialogSupportCardLimitBreakItemConfirm";

        /// <summary>
        /// デッキ名変更ダイアログ
        /// </summary>
        public const string DIALOG_CHANGE_SUPPORT_DECK_NAME_PATH = UIPartsPath + "/SupportCard/DialogChangeSupportDeckName";
        /// <summary>
        /// デッキリセット確認ダイアログ
        /// </summary>
        public const string DIALOG_RESET_CONFIRM_SUPPORT_DECK_PATH = UIPartsPath + "/SupportCard/DialogResetConfirmSupportDeck";
        /// <summary>
        /// デッキ編成サポカダイアログ
        /// </summary>
        public const string DIALOG_SUPPORT_DECK_CARD_SELECT = UIPartsPath + "/SupportCard/DialogSupportDeckCardSelect";
        /// <summary>
        /// デッキおまかせ編成確認
        /// </summary>
        public const string DIALOG_SUPPORT_DECK_AUTO_SELECT_CONFIRM = UIPartsPath + "/SupportCard/DialogSupportCardDeckAutoSelectConfirm";
        /// <summary>
        /// デッキコピーダイアログ
        /// </summary>
        public const string DIALOG_SUPPORT_DECK_COPY = UIPartsPath + "/SupportCard/DialogSupportDeckCopy";
        /// <summary>
        /// デッキ上書きダイアログ
        /// </summary>
        public const string DIALOG_OVERWRITE_CONFIRM_SUPPORT_DECK_PATH = UIPartsPath + "/SupportCard/DialogOverwriteConfirmSupportDeck";

        /// <summary>
        /// サポートカードを角丸でマスクする用のテクスチャ
        /// </summary>
        public const string SUPPORT_CARD_MASK_TEXTURE_PATH = SUPPORT_ROOT + "Support00000/tex_support_card_00000_mask";

        /// <summary>
        /// サムネイル用サポートカードを角丸でマスクする用のテクスチャ
        /// </summary>
        public const string SUPPORT_CARD_THUMBNAIL_MASK_TEXTURE_PATH = SUPPORT_ROOT + "Support00000/tex_support_thumb_00000_mask";

        /// <summary>
        /// サポートカードIDに対応する亜空シェーダー無し用カードテクスチャのリソースパスを取得する
        /// </summary>
        public static string GetSupportCardTexTexturePath(int supportCardId)
        {
            supportCardId = Mathf.Max(0, supportCardId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Support{1:00000}/tex_support_card_{1:00000}", SUPPORT_ROOT, supportCardId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// サポートカードIDに対応する亜空シェーダー無し用サポートスチルテクスチャのリソースパスを取得する
        /// </summary>
        public static string GetSupportStillTexTexturePath(int supportCardId)
        {
            supportCardId = Mathf.Max(0, supportCardId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Support{1:00000}/tex_support_stl_{1:00000}", SUPPORT_ROOT, supportCardId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// サポートカードアイコンのリソースパスを取得する
        /// </summary>
        public static string GetSupportCardThumbnailIconPath(int supportCardId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Support{1:00000}/support_thumb_{1:00000}", SUPPORT_ROOT, supportCardId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// サポートカードの正方形アイテムアイコン用テクスチャのリソースパスを取得する
        /// </summary>
        /// <param name="supportCardId"></param>
        /// <returns></returns>
        public static string GetSupportCardSTexturePath(int supportCardId)
        {
            supportCardId = Mathf.Max(0, supportCardId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Support{1:00000}/support_card_s_{1:00000}", SUPPORT_ROOT, supportCardId);
            return _stringBuilder.ToString();
        }

        /// <summary> サポートカード控室画面環境設定 </summary>
        public const string SUPPORT_CARD_WAITING_ROOM_VIEW_ENV_PATH = EnvParamRoot + "PhotoCardWaitingRoom/ast_prm_photo_card_waiting_room_view";

        /// <summary> サポートカードシナリオリンクハイライトエフェクト </summary>
        public const string SUPPORT_CARD_SCENARIO_LINK_EFFECT_PATH = UIPartsPath + "/SupportCard/PartsSupportCardScenarioLinkEffect";
        /// <summary> サポートカードシナリオリンクハイライト </summary>
        public const string SUPPORT_CARD_SCENARIO_LINK_PATH = UIPartsPath + "/SupportCard/PartsSupportCardScenarioLink";

        #endregion

        #region メインストーリー

        /// <summary> NPCレース詳細ダイアログ </summary>
        public const string DIALOG_NPC_RACE_DETAIL_PATH = "UI/Parts/Episode/DialogMainStoryNpcRaceDetail";

        /// <summary> NPCレース登録確認ダイアログ </summary>
        public const string DIALOG_NPC_RACE_ENTRY_CONFIRM_PATH = "UI/Parts/Episode/DialogMainStoryNpcRaceEntryConfirm";

        // ストーリー画面　持ち方変更ダイアログ
        public const string DialogStoryChangeOrientationPath = "UI/Parts/Story/DialogStoryChangeOrientation";

        // ストーリー画面　連続再生ダイアログ
        public const string DialogStoryNextPath = "UI/Parts/Story/DialogStoryNext";

        // ホームのストーリーの解放通知ダイアログ
        public const string DIALOG_STORY_RELEASE_NOTICE = "UI/Parts/Story/DialogStoryReleaseNotice";

        //メインストーリーアンロックレース継承成功テキストA2U「想い継承!」
        public const string FLASH_STORY_UNLOCK_RACE_START_SUCCESSION = FLASH_COMBINE_ROOT + "Action/Story/fa_fl_story_race_succession00";
        #endregion

        #region エクストラ

        private const string EXTRA_STORY_BG_ROOT = "Story/ExtraStoryBg/";
        private const string EXTRA_STORY_STORY_EVENT_STILL_BG_PATH_FORMAT = EXTRA_STORY_BG_ROOT + "StoryEvent/bg_extra_01_{0:D4}";
        private const string EXTRA_STORY_STORY_EXTRA_STILL_BG_PATH_FORMAT = EXTRA_STORY_BG_ROOT + "StoryExtra/bg_extra_01_{0:D4}";

        /// <summary>
        /// エクストラ：イベントストーリーのスチル背景のパス
        /// </summary>
        /// <param name="storyEventId"></param>
        /// <returns></returns>
        public static string GetStoryEventExtraStillBgPath(int storyEventId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(EXTRA_STORY_STORY_EVENT_STILL_BG_PATH_FORMAT, storyEventId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// エクストラ: エクストラストーリーのスチル背景のパス
        /// </summary>
        public static string GetStoryExtraExtraStillBgPath(int storyId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(EXTRA_STORY_STORY_EXTRA_STILL_BG_PATH_FORMAT, storyId);
            return _stringBuilder.ToString();
        }

        #endregion

        #region 共通UI
        public const string UIPartsPath = "UI/Parts";
        public const string UIViewsPath = "UI/Views";
        public const string UI_BASE_PARTS_PATH = UIPartsPath + "/Base";
        public const string BUTTON_PUSH_EFFECT_PATH = UI_BASE_PARTS_PATH + "/ButtonPushEffect";
        public const string MenuDialogCommonPath = UIPartsPath + "/MenuDialogCommon";
        public const string TOGGLE_MENU_DIALOG_PATH = UIPartsPath + "/ToggleMenuDialog";
        public const string CHARACTER_LIST_UI_PATH = UIPartsPath + "/CharacterListUI";
        public const string CharacterUIPath = UIPartsPath + "/Character";
        public const string DIALOG_SIMPLE_CHECK_PATH = UIPartsPath + "/DialogSimpleCheck";
        public const string DIALOG_SIMPLE_CHECK_NO_WARNING_PATH = UIPartsPath + "/DialogSimpleCheckNoWarning";
        public const string DIALOG_SIMPLE_CHECK_WITH_IMAGE_PATH = UIPartsPath + "/DialogSimpleCheckWithImage";

        public const string FOOTER_PATH = UIPartsPath + "/Footer/Footer";
        public const string FOOTER_FLASH_ROOT = FLASH_ROOT + "Footer/";
        public const string HEADER_PATH = UIPartsPath + "/Header/Header";
        public const string HEADER_TITLE_PATH = UIPartsPath + "/PartsCommonHeaderTitle";

        public const string CommonDialogResourcePath = UIPartsPath + "/DialogCommon";
        public const string CommonDialogBgResourcePath = UIPartsPath + "/DialogBgCommon";

        public const string CommonDialogHorizontalResourcePath = UIPartsPath + "/DialogCommonHorizontal";

        public const string NEW_ICON_PATH = UIPartsPath + "/NewIcon";
        public const string NEW_ICON_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_icon_new00";
        public const string LOCK_ICON_M_PATH = UIPartsPath + "/LockIconM";
        public const string BADGE_ICON_FLASH_PATH = COMMON_FLASH_ROOT + "pf_fl_cmn_badge00";
        public const string BADGE_ICON_TAT_PATH = UIPartsPath + "/BadgeCommon";
        public const string HORSE_SHOE_BADGE_ICON_PREFAB_PATH = UIPartsPath + "/HorseShoeBadgeIcon";
        public const string PARTS_PIECE_BADGE_PATH = UIPartsPath + "/PartsPieceBadge";
        public const string PARTS_EVENT_BONUS_LABEL = UIPartsPath + "/EventBonusLabel";

        //UnityAnimationを使用した汎用フレーム
        public const string COMMON_UNITYANIMATION_FRAME_00 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame00";
        public const string COMMON_UNITYANIMATION_FRAME_01 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame01";
        public const string COMMON_UNITYANIMATION_FRAME_02 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame02";
        public const string COMMON_UNITYANIMATION_FRAME_03 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame03";
        public const string COMMON_UNITYANIMATION_FRAME_05 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame05";
        public const string COMMON_UNITYANIMATION_FRAME_06 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame06";
#if BUMA_T
        public const string COMMON_UNITYANIMATION_FRAME_CROSS = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame_cross00";
#endif
        public const string ComicTextureRoot = OUTGAME_ASSET_ROOT + "Comic/tex_comic_";
        public const string ComicThumbnailTextureRoot = OUTGAME_ASSET_ROOT + "Comic/tex_comic_thumb_";
        
        /// <summary> スタンプ選択ダイアログ </summary>
        public const string DIALOG_STAMP_SELECT = UIPartsPath + "/DialogStampSelect";

        /// <summary> スタンプ選択エフェクト </summary>
        public const string PARTS_STAMP_SELECT_EFFECT = UIPartsPath + "/PartsStampSelectEffect";

        public const string SETTING_MARK_PATH = UIPartsPath + "/SettingMark";

        public const string UI_DEFAULT_MATERIAL_PATH = "UI/Materials/UI-Default";

        public static string GetComicTexturePath(int charaID, int subIndex)
        {
            const string ComicTexturePath = ComicTextureRoot + "{0:0000}_{1:00}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(ComicTexturePath, charaID, subIndex);
            return _stringBuilder.ToString();
        }
        public static string GetComicThumbnailTexturePath(int charaID, int subIndex)
        {
            const string ComicThumbnailTexturePath = ComicThumbnailTextureRoot + "{0:0000}_{1:00}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(ComicThumbnailTexturePath, charaID, subIndex);
            return _stringBuilder.ToString();
        }

        public const string CharacterIconButtonPath = UIPartsPath + "/CharacterButton";
        public const string BUTTON_ACCESSORY_PATH = UIPartsPath + "/PartsButtonAccessory";
        public const string BUTTON_SELECT_NUM_PATH = UIPartsPath + "/PartsSelectNum";

        //マテリアル
        public const string UI_MATERIAL_ROOT = "UI/Materials/";
        public const string UI_ADD_COLOR_MATERIAL_PATH = UI_MATERIAL_ROOT + "UIAddColor";
        public const string UI_ADD_MATERIAL_PATH = UI_MATERIAL_ROOT + "UIAdditive";
        public const string UI_FILL_ADD_COLOR_MATERIAL_PATH = UI_MATERIAL_ROOT + "UIFillAddColor";
        public const string UI_ADD_SUB_COLOR_MATERIAL_PATH = UI_MATERIAL_ROOT + "UIAddSubColor";

        // 遷移表現
        public const string CaptureDisplayPath = UIPartsPath + "/CaptureDisplay";

        // タヅナさんメッセージプレハブ
        public const string TazunaMessageBalloonPath = UIPartsPath + "/TazunaMessageBalloon";

        #if BUMA_JP
        /*
         * 移除日方菜单按钮：平台成就(Google Play Games Achievements, iOS Game Center Achievements)、连携、账号绑定
         * 以下页面均为连携需要
         * by xuxinwei 2021-12-15
         */
        //データ引継ぎ
        public const string DataTransitionRoot = UIPartsPath + "/DataTransition/";
        public const string DialogDataTransitionNotice = DataTransitionRoot + "DialogDataTransitionNotice";
        public const string DialogDataTransitionSelect = DataTransitionRoot + "DialogDataTransitionSelect";
        public const string DialogDataTransitionInputIdPassword = DataTransitionRoot + "DialogDataTransitionInputIdPassword";
        public const string DialogDataTransitionConfirmOverwrite = DataTransitionRoot + "DialogDataTransitionConfirmOverwrite";
        public const string DialogDataTransitionSetPasswordNotice = DataTransitionRoot + "DialogDataTransitionSetPasswordNotice";
        public const string DialogDataTransitionSetPassword = DataTransitionRoot + "DialogDataTransitionSetPassword";
        public const string DialogDataTransitionGetUser = DataTransitionRoot + "DialogDataTransitionGetUser";
        public const string DialogDataTransitionTitleNotice = DataTransitionRoot + "DialogDataTransitionTitleNotice";
        public const string DialogDataTransitionSelectTransitionType = DataTransitionRoot + "DialogDataTransitionSelectTransitionType";
        public const string DialogDataTransitionInputIdPasswordEnd = DataTransitionRoot + "DialogDataTransitionSetPasswordEnd";
        #endif
        
        // 利用規約
        public const string DialogTermsOfServiceConfirmPath = UIPartsPath + "/Title/DialogTermsOfServiceConfirm";
        public const string DialogTermsOfServiceConfirmWinPath = UIPartsPath + "/Title/DialogTermsOfServiceConfirmWin";
        
        // サービス開始前事前DL
        public const string DialogNotifyPreRelease = UIPartsPath + "/Title/DialogNotifyPreRelease";

        // チュートリアルダウンロード確認ダイアログ
        public const string DialogTutorialDownload = UIPartsPath + "/Title/DialogTutorialDownload";
        public const string DialogBackgroundDownload = UIPartsPath + "/Title/DialogBackgroundDownload";
        
        // チュートリアルウマ娘選択ダイアログ
        public const string DialogTutorialConfirmUmamusumeSelect = UIPartsPath + "/Tutorial/DialogConfirmSelectUmamusume";
        
        // チュートリアルマスクUI
        public const string TUTORIAL_MASK_UI = UIPartsPath + "/Tutorial/TutorialMaskUI";

        //一括DL
        public const string DialogDownloadAllConfirmPath = UIPartsPath + "/Title/DialogTitleDownloadAll";
        
        // キャッシュクリアのマスク
        public const string DIALOG_TITLE_CACHE_CLEAR_MASK = UIPartsPath + "/Title/DialogTitleCacheClearMask";

        //お問い合わせ
        public const string DialogOtoiawase = UIPartsPath + "/Title/DialogOtoiawase";
        
        //TP,RP回復アイテム使用ダイアログ
        public const string DIALOG_TP_RP_ITEM_USE = UIPartsPath + "/Item/DialogTpRpItemUse";

        //TP,RP回復アイテム使用しましたメッセージダイアログ
        public const string DIALOG_TP_RP_ITEM_USED_MESSAGE = UIPartsPath + "/Item/DialogTpRpItemUsedMessage";

        //アイテム使用確認
        public const string DIALOG_ITEM_USE_CONFIRM = UIPartsPath + "/Item/DialogItemUseConfirm";
        //アイテム詳細
        public const string DIALOG_ITEM_INFORMATION = UIPartsPath + "/Item/DialogItemInformation";
        //パドックの出走ウマ娘一覧ダイアログ
        public const string DIALOG_PADDOCK_RACE_CHARA_LIST = UIPartsPath + "/Paddock/DialogPaddockRaceCharaList";

        //機能別チュートリアルのダイアログ
        public const string DIALOG_TUTORIAL_GUIDE = UIPartsPath + "/DialogTutorialGuide";

        // 交換所アイテム入手箇所ダイアログ
        public const string DIALOG_ITEM_AVAILABLE_PLACE = UIPartsPath + "/Item/DialogItemAvailablePlace";

        //共通アトラスパス
#if UNITY_EDITOR
        public const string ATLAS_IMAGES_ASSETS_PATH = SourceResourcesAssetsPath + "AtlasImages/";  //相対パス基準
#endif
        public const string ATLAS_ROOT_PATH = "Atlas/";

        /// <summary>
        /// 汎用テキスト
        /// </summary>
        public const string PARTS_UI_TEXT = UIPartsPath + "/Base/Text";

        public const string DIALOG_SELECT_LIST_VERTICAL = UIPartsPath + "/SelectList/DialogSelectListVertical";

        /// <summary>
        /// 汎用バッジ
        /// </summary>
        public const string PARTS_TEXT_BADGE = UIPartsPath + "/PartsTextBadge";

        /// <summary>
        /// アイテム獲得スタンプアニメ(スタンプ画像差し替え可能)
        /// </summary>
        public const string FLASH_COMMON_GET_STAMP_REPLACABLE = COMMON_FLASH_ROOT + "pf_fl_cmn_get_stamp01";

        /// <summary>
        /// アイテム獲得スタンプ（共通）
        /// </summary>
        /// <returns></returns>
        public static string COMMON_GET_STAMP_TEXTURE => GetSpecialLoginBonusStampPath(0);

        #endregion

        #region シングルモード

        public const string SINGLE_MODE_UI_ROOT = UIPartsPath + "/SingleMode/";

        //育成開始確認ダイアログ
        public const string DIALOG_SINGLE_MODE_START_CONFIRM_ENTRY = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeStartConfirmEntry";
        /// <summary> 育成開始確認ダイアログ内パーツ：TPブーストUI </summary>
        public const string PARTS_SINGLE_MODE_START_TP_BOOST = SINGLE_MODE_UI_ROOT + "SingleModeStart/PartsSingleModeStartTpBoost";
        /// <summary> 育成開始確認ダイアログ内パーツ：合計イベントボーナス表示パーツのパス </summary>
        public const string PARTS_SINGLE_MODE_START_EVENT_TOTAL_BONUS_PATH = SINGLE_MODE_UI_ROOT + "SingleModeStart/PartsSingleModeStartEventTotalBonus";
        /// <summary> TPブースト情報ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_START_TP_BOOST_INFO = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeStartTpBoostInfo";

        //育成開始：育成情報ダイアログ
        public const string DIALOG_SINGLE_MODE_START_INFO_ENTRY = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeStartInfo";
        //育成開始：新シナリオ通知ダイアログ
        public const string DIALOG_SINGLE_MODE_START_NOTIFY_NEW_SCENARIO = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeStartNotifyNewScenario";
        // 育成開始：シナリオ詳細ダイアログ
        public const string DIALOG_SINGLE_MODE_START_SCENARIO_TIPS = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeStartScenarioTips";
        // 育成開始：シナリオ詳細 育成目標Tipsダイアログ
        public const string DIALOG_SINGLE_MODE_START_SCENARIO_ROUTE_RACE_TIPS = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeStartScenarioRouteRaceTips";


        //継承演出カット
        public const string SINGLE_MODE_SUCCESSION_CUT = "Cutt/CutIn/OutGame/outgame_succession001/outgame_succession001";
        // 育成開始継承Flash
        public const string FLASH_SINGLE_MODE_START_SUCCESSION = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_start_succession00";
        // 育成中因子継承完了Flash
        public const string FLASH_SINGLE_MODE_SUCCESSION_FACTOR_COMPLETE = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_txt_factor_succession00";
        // 育成中因子継承失敗Flash
        public const string FLASH_SINGLE_MODE_SUCCESSION_FACTOR_FAILED = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_txt_factor_fail00";
        // 育成因子発動Flash
        public const string FLASH_SINGLE_MODE_SUCCESSION_FACTOR = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventbonus_factor00";
        // 育成中因子継承ボタンFlash
        public const string FLASH_SINGLE_MODE_SUCCESSION_FACTOR_BUTTON = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_btn_factor_succession00";

        /// <summary> 育成中共通ヘッダー </summary>
        public const string PARTS_SINGLE_MODE_COMMON_HEADER_PATH = SINGLE_MODE_UI_ROOT + "SingleModeMain/PartsSingleModeCommonHeader";

        /// <summary> 育成中共通フッター </summary>
        public const string PARTS_SINGLE_MODE_COMMON_FOOTER_PATH = SINGLE_MODE_UI_ROOT + "SingleModeMain/PartsSingleModeCommonFooter";

        /// <summary> 育成TOP コマンドボタン </summary>
        public const string PARTS_SINGLE_MODE_MAIN_STABLES_PANEL_BUTTON_PATH = SINGLE_MODE_UI_ROOT + "SingleModeMain/PartsSingleModeSingleModeMainStablesPanelButton";

        // たずなさんダイアログ
        public const string DIALOG_SINGLE_MODE_TAZUNA_MESSAGE = SINGLE_MODE_UI_ROOT + "DialogSingleModeTazunaMessage";

        //スキル習得確率確認用ダイアログ
        public const string DIALOG_SINGLE_MODE_SKILL_LEARNING_CONDITION = SINGLE_MODE_UI_ROOT + "DialogSingleModeSkillLearningCondition";

        //育成編成時のフレンド選択のダイアログ
        public const string DIALOG_SINGLE_MODE_SELECT_FRIEND_CARD = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeSelectFriendCard";
        
        //育成シナリオレコードダイアログ
        public const string DIALOG_SINGLE_MODE_SCENARIO_RECORD = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeScenarioRecord";
        //育成シナリオリンクダイアログ
        public const string DIALOG_SINGLE_MODE_SCENARIO_LINK = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeScenarioLink";
        
        //フレンド選択時の確認ダイアログ
        public const string DIALOG_SINGLE_MODE_RENTAL_FRIEND_CONFIRM = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeRentalFriendConfirm";
        
        //育成編成時の3D環境設定
        public const string SINGLE_MODE_START_ENV_PATH = EnvParamRoot + "SingleStart/ast_prm_single_start";

        //目標達成ダイアログ
        public const string DIALOG_SINGLE_MODE_ROUTE_CLEAR = SINGLE_MODE_UI_ROOT + "RaceResultRouteDialog/DialogSingleModeRouteClear";

        //紙吹雪つきライトエフェクト
        public const string UIEFFECT_COMMON_CONFETTI_GLITTER_PARTICLE_00 = UI_EFFECT_ROOT + "Common/pfb_uieff_confetti_glitter_particle_00";
        public const string UIEFFECT_COMMON_CONFETTI_GLITTER_PARTICLE_01 = UI_EFFECT_ROOT + "Common/pfb_uieff_confetti_glitter_particle_01";

        //次の目標
        public const string FLASH_COMMON_NEXT_TARGET = COMMON_FLASH_ROOT + "pf_fl_cmn_icon_next00";

        //目標達成演出Flashのパス
        public const string FLASH_SINGLE_MODE_TARGET_ACHIEVE = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_single_target_achieve00";
        //育成目標達成演出Flashのパス
        public const string FLASH_SINGLE_MODE_ALL_TARGET_ACHIEVE = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_all_target_achieve00";
        //育成目標未達成演出Flashのパス
        public const string FLASH_SINGLE_MODE_TARGET_FAILED = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_target_failed00";
        //目標失敗ダイアログ
        public const string DIALOG_SINGLE_MODE_ROUTE_FAILED = SINGLE_MODE_UI_ROOT + "RaceResultRouteDialog/DialogSingleModeRouteFailed";
        // ルート目標コンティニュー
        public const string DIALOG_SINGLE_MODE_ROUTE_CONTINUE = SINGLE_MODE_UI_ROOT + "RaceResultRouteDialog/DialogSingleModeRouteContinue";
        // ルート目標1日1回無料コンティニュー
        public const string DIALOG_SINGLE_MODE_ROUTE_FREE_CONTINUE = SINGLE_MODE_UI_ROOT + "RaceResultRouteDialog/DialogSingleModeRouteFreeContinue";

        public const string DIALOG_SINGLE_MODE_ROUTE_ALARM_CLOCK_BUY_CONFIRM = SINGLE_MODE_UI_ROOT + "RaceResultRouteDialog/DialogSingleModeRouteAlarmClockBuyConfirm";
        //シングルモードのレース予約ダイアログ
        public const string DIALOG_SINGLE_MODE_RESERVE_CONFIRM = SINGLE_MODE_UI_ROOT + "DialogSingleModeReserveConfirm";
        //おすすめレース選択ダイアログ
        public const string DIALOG_SINGLE_MODE_RECOMMEND_RACE = SINGLE_MODE_UI_ROOT + "DialogSingleModeRecommendRace";
        //シングルモードの予約レースが存在するダイアログ
        public const string DIALOG_SINGLE_MODE_RESERVE_ALERT = SINGLE_MODE_UI_ROOT + "DialogSingleModeReserveAlert";
        //シングルモードの予約レースが存在する時にコマンド選択したら開くダイアログ
        public const string DIALOG_SINGLE_MODE_RESERVE_ALERT_COMMAND = SINGLE_MODE_UI_ROOT + "DialogSingleModeReserveAlertCommand";
        //シングルモードの予約レース強制キャンセルダイアログ
        public const string DIALOG_SINGLE_MODE_RESERVE_CANCEL = SINGLE_MODE_UI_ROOT + "DialogSingleModeReserveCancel";
        //レース登録時の連戦警告ダイアログ
        public const string DIALOG_SINGLE_MODE_RACE_ENTRY_WARNING = SINGLE_MODE_UI_ROOT + "DialogSingleModeRaceEntryWarning";
        //シングルモードの再開確認用ダイアログ
        public const string DIALOG_HOME_SINGLE_MODE_RESTART_CONFIRM = UIPartsPath + "/Home/DialogHomeSingleModeRestartConfirm";
        //ホームからのシングルモードの削除確認用ダイアログ
        public const string DIALOG_HOME_SINGLE_MODE_DELETE_CONFIRM = UIPartsPath + "/Home/DialogHomeSingleModeDeleteConfirm";
        //シングルモードの削除確認用ダイアログ
        public const string DIALOG_SINGLE_MODE_DELETE_CONFIRM = SINGLE_MODE_UI_ROOT + "DialogSingleModeDeleteConfirm";
        // 因子一覧
        public const string DIALOG_SINGLE_MODE_FACTOR_LIST = SINGLEMODE_UI_PARTS_PATH + "DialogSingleModeFactorList";
        // 因子詳細
        public const string DIALOG_SINGLE_MODE_FACTOR_DETAIL = SINGLEMODE_UI_PARTS_PATH + "DialogSingleModeFactorDetail";
        // 継承スキル
        public const string DIALOG_SINGLE_MODE_SUCCESSION_SKILL_List = SINGLEMODE_UI_PARTS_PATH + "DialogSingleModeSuccessionSkillList";

        //シングルモード 評価メッセージ表示用のダイアログ
        public const string DIALOG_SINGLE_MODE_EVALUATION_MESSAGE = SINGLE_MODE_UI_ROOT + "DialogSingleModeEvaluationMessage";
        //短縮レースロック情報
        public const string DIALOG_SINGLE_MODE_SHORT_RACE_LOCK_INFO = SINGLE_MODE_UI_ROOT + "DialogSingleModeShortRaceLockInfo";

        /// <summary>
        /// シングルモードお出かけ選択
        /// </summary>
        public const string DIALOG_SINGLE_MODE_OUTING_PARTNER_SELECT = SINGLE_MODE_UI_ROOT + "DialogSingleModeOutingPartnerSelect";

        /// <summary>
        /// シングルモード開始直前、殿堂入りのキャラ上限達している場合ウマ娘除外のダイアログ
        /// </summary>
        public const string DIALOG_SINGLE_MODE_REMOVE_TRAINED_HORSE_WARNING = SINGLE_MODE_UI_ROOT + "DialogSingleModeRemoveTrainedHorseWarning";

        //トレーニング予測数値表示
        public const string PARTS_SINGLE_MODE_IMAGE_NUMBER = SINGLE_MODE_UI_ROOT + "SingleModeMain/PartsSingleModeImageNumber";

        /// <summary>
        /// コツアイコン用バッジFlashパス
        /// </summary>
        public const string SINGLE_MODE_BADGE_KNACK_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_badge_knack00";

        /// <summary>
        /// 併せ馬表示用Flashのパス
        /// </summary>
        public const string SINGLE_MODE_PARTNER_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_icon_supporter00";
        public const string SINGLE_MODE_PARTNER_FLASH_PATH_01 = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_icon_supporter01";

        /// <summary> 育成TOP画面　タズナヒントFlash </summary>
        public const string SINGLE_MODE_HINT_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_hint00";
        
        /// <summary> 育成TOP画面　レースボタンエフェクト </summary>
        public const string SINGLE_TOP_BUTTON_RACE_EFFECT_PATH = FLASH_COMBINE_ROOT + "Timeline/SingleMode/tat_single_top_btn_race00";
        /// <summary> 育成TOP画面　レースボタンパーティクルエフェクト　</summary>
        public const string SINGLE_TOP_BUTTON_RACE_EFFECT_PARTICLE_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_top_racebtn_glitter_00";
        /// <summary> 育成TOP画面　トレーニングボタンエフェクト </summary>
        public const string SINGLE_TOP_BUTTON_TRAINING_EFFECT_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eff_btn_trainingtop00";

        /// <summary> シングルモード　シナリオバナールート </summary>
        public const string SINGLE_MODE_SCENARIO_BANNER_ROOT = "Single/Banner/";
        /// <summary> シングルモード　シナリオバナー(タイトル) </summary>
        public const string SINGLE_MODE_SCENARIO_BANNER_TITLE_PATH = SINGLE_MODE_SCENARIO_BANNER_ROOT + "bnr_{0:000}";
        /// <summary> シングルモード　シナリオバナー(タイトル) 文字のみ </summary>
        public const string SINGLE_MODE_SCENARIO_BANNER_TITLE_TEXT_PATH = SINGLE_MODE_SCENARIO_BANNER_ROOT + "bnr_{0:000}_text";
        /// <summary> シングルモード　シナリオバナー(キャラ) </summary>
        public const string SINGLE_MODE_SCENARIO_BANNER_CHARA_PATH = SINGLE_MODE_SCENARIO_BANNER_ROOT + "bnr_chara_{0:000}";
        /// <summary> シングルモード　シナリオバナー(キャラ大)  </summary>
        public const string SINGLE_MODE_SCENARIO_BANNER_CHARA_BIG_PATH = SINGLE_MODE_SCENARIO_BANNER_ROOT + "bnr_chara_big_{0:000}";
        /// <summary> シングルモード　シナリオアイコン  </summary>
        public const string SINGLE_MODE_SCENARIO_ICON_PATH = SINGLE_MODE_SCENARIO_BANNER_ROOT + "bnr_ico_{0:000}";
        /// <summary> シングルモード　Thumbアイコン </summary>
        public const string SINGLE_MODE_THUMB_ICON = "Single/Thumb/single_scenario_thumb_{0:D3}";

        /// <summary> シングルモード　目標警告ルート </summary>
        public const string SINGLE_MODE_TARGET_ALERT_ROOT = "Single/TargetAlert/";
        /// <summary> シングルモード　目標警告(未勝利ゲームオーバー) </summary>
        public const string SINGLE_MODE_TARGET_ALERT_NO_WIN_GAMEOVER_PATH = SINGLE_MODE_TARGET_ALERT_ROOT + "single_targetalert_001";
        /// <summary> シングルモード　目標警告(未勝利) </summary>
        public const string SINGLE_MODE_TARGET_ALERT_NO_WIN_PATH = SINGLE_MODE_TARGET_ALERT_ROOT + "single_targetalert_002";
        /// <summary> シングルモード　目標警告(ファン数) </summary>
        public const string SINGLE_MODE_TARGET_ALERT_FAN_PATH = SINGLE_MODE_TARGET_ALERT_ROOT + "single_targetalert_003";
        /// <summary> シングルモード　目標警告(出走条件) </summary>
        public const string SINGLE_MODE_TARGET_ALERT_CONDITION_PATH = SINGLE_MODE_TARGET_ALERT_ROOT + "single_targetalert_004";
        /// <summary> シングルモード　目標警告(グレード勝利数) </summary>
        public const string SINGLE_MODE_TARGET_ALERT_GRADE_WIN_PATH = SINGLE_MODE_TARGET_ALERT_ROOT + "single_targetalert_005";

        /// <summary> シングルモード 継承選択設定 </summary>
        public const string SINGLE_MODE_START_CHARAVIEWR_SETTING_DATA_PATH = "UI/Camera/ast_SingleModeStartCharaViewerSetting";

        //おすすめレースアイコンパス
        public const string SINGLE_MODE_RACE_RECOMMEND_PATH = "Single/RaceRecommend/single_racerecommend_{0:D3}";


        /// <summary> 育成シナリオ詳細 </summary>
        public const string SINGLE_MODE_DETAIL_ROOT = "Single/Detail/";
        public const string SINGLE_MODE_DETAIL_00 = SINGLE_MODE_DETAIL_ROOT + "img_singlemode_detail_00";
        public const string SINGLE_MODE_DETAIL_01 = SINGLE_MODE_DETAIL_ROOT + "img_singlemode_detail_01";


        /// <summary> シングルモード　得意練習アイコン </summary>
        public const string SINGLE_MODE_OBTAIN_ICON_ROOT = "Single/ObtainIcon/";
        public const string SINGLE_MODE_OBTAIN_ICON_SPEED = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_00";
        public const string SINGLE_MODE_OBTAIN_ICON_STAMINA = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_01";
        public const string SINGLE_MODE_OBTAIN_ICON_POWER = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_02";
        public const string SINGLE_MODE_OBTAIN_ICON_GUTS = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_03";
        public const string SINGLE_MODE_OBTAIN_ICON_WIZ = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_04";
        public const string SINGLE_MODE_OBTAIN_ICON_FRIEND = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_05";
        public const string SINGLE_MODE_OBTAIN_ICON_GROUP = SINGLE_MODE_OBTAIN_ICON_ROOT + "utx_ico_obtain_06";

        /// <summary> シングルモード　育成シナリオ別カメラ配置データ </summary>
        public const string SINGLE_MODE_CAMERA_CHARACTER_CORRECTION_PRESET_PATH = "UI/Camera/SingleModeScenarioCameraCharacterCorrectionPreset";
        /// <summary> シングルモード　背景ルート </summary>
        public const string SINGLE_MODE_BG_ROOT = "Single/Bg/";
        /// <summary> シングルモード　ピラミッド型のエフェクトオブジェクトのパス </summary>
        public const string SINGLE_MODE_PYRAMID_EFFECT_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_pyramid_particle_00";
        /// <summary>
        /// ピラミッドテクスチャのパス
        /// </summary>
        public const string SINGLE_MODE_PYRAMID_TEXTURE_00_PATH = "Single/Class/utx_frm_class_pyramid_00";
        public const string SINGLE_MODE_PYRAMID_TEXTURE_01_PATH = "Single/Class/utx_frm_class_pyramid_01";

        /// <summary> シングルモードスキル獲得画面環境設定 </summary>
        public const string SINGLE_MODE_SKILL_LEARNING_VIEW_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_skill_learning_view";
        public const string DIALOG_SINGLE_MODE_SKILL_LEARNING_COMPLETE = SINGLE_MODE_UI_ROOT + "DialogSingleModeSkillLearningComplete";

        // <summary> シングルモード育成感力確認画面環境設定 </summary>
        public const string SINGLE_MODE_CONFIRM_COMPLETE_VIEW_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_confirm_complete_view";

        /// <summary>
        /// シングルモードレース登録画面環境設定
        /// (ルームマッチのたづなさんでも使用)
        /// </summary>
        public const string SINGLE_MODE_RACE_ENTRY_VIEW_SUNNY_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_race_entry_view_sunny";
        public const string SINGLE_MODE_RACE_ENTRY_VIEW_CLOUDY_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_race_entry_view_cloudy";
        public const string SINGLE_MODE_RACE_ENTRY_VIEW_RAINY_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_race_entry_view_rainy";
        public const string SINGLE_MODE_RACE_ENTRY_VIEW_SNOWY_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_race_entry_view_snowy";
        
        public const string SINGLE_MODE_RACE_LIST_ITEM_REWARD_CONTENT = SINGLE_MODE_UI_ROOT + "PartsSingleModeRaceListItemRewardContent";
        
        public const string DIALOG_SINGLE_MODE_CONFIRM_COMPLETE = SINGLE_MODE_UI_ROOT + "SingleModeConfirmComplete/DialogSingleModeConfirmComplete";
        public const string DIALOG_SINGLE_MODE_RESULT = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResult";
        public const string DIALOG_SINGLE_MODE_RESULT_FACTOR_DECIDE_CONFIRM = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultFactorDecideConfirm";
        public const string DIALOG_SINGLE_MODE_RESULT_FACTOR_TREE = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultFactorTree";
        public const string DIALOG_SINGLE_MODE_RESULT_FACTOR_LOTTERY_CONFIRM = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultFactorLotteryConfirm";
        public const string DIALOG_SINGLE_MODE_RESULT_SELECT_NICKNAME = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultSelectNickName";
        public const string DIALOG_SINGLE_MODE_RESULT_FOLLOW_TRAINER = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultFollowTrainer";
        public const string DIALOG_SINGLE_MODE_RESULT_UPDATE_RANKSCORE_RANKING = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultUpdateRankScoreRanking";
        public const string DIALOG_SINGLE_MODE_RESULT_UPDATE_SCENARIO_RECORD = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultUpdateScenarioRecord";
        public const string SINGLE_MODE_RESULT_BREAK_RESULTPOINT = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_break_resultpoint_record00";
        public const string SINGLE_MODE_RESULT_GLITTER_00 = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_title_chararesult_glitter_00";
        public const string SINGLE_MODE_CHARA_RESULT_GLITTER_00 = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_chararesult_glitter_00";
        public const string SINGLE_MODE_CHARA_RESULT_GLITTER_01 = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_chararesult_glitter_01";
        public const string SINGLE_MODE_RESULT_GAUGE_EVALUATION_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_gauge_evaluation00";
        public const string SINGLE_MODE_RESULT_CHARARANK_TITLE_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_title_chararank00";
        public const string SINGLE_MODE_RESULT_CHARARANK_RESULT_00 = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_result_chararank00";
        public const string SINGLE_MODE_RESULT_TITLE_GETREWARD_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_title_getreward00";
        public const string SINGLE_MODE_RESULT_TITLE_GETFACTOR_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_title_getfactor00";
        public const string SINGLE_MODE_RESULT_TITLE_REGETFACTOR_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_title_regetfactor00";
        public const string SINGLE_MODE_RESULT_TITLE_FACTOR_SELECTION_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_title_factorselection00";
        public const string SINGLE_MODE_RESULT_CARDEXP_EXCHANGESP_00 = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_eff_cardexp_exchangesp00";
        public const string SINGLE_MODE_RESULT_SPCOUNT_00 = FLASH_ROOT + "SingleMode/pf_fl_singlemode_eff_result_spcount00";
        public const string SINGLE_MODE_RESULT_SCENARIO_RECORD_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_result_scenariorecord00";
        
#if BUMA_T
        public const string DIALOG_SINGLE_MODE_RESULT_RANKSCORE_AND_FACTORSELECT = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultRankScoreAndFactorSelect";
        public const string DIALOG_SINGLE_MODE_RESULT_FACTOR_LOTTERY_CAMPAIGN_CONFIRM = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultFactorLotteryCampaignConfirm";// add by thl
#endif
        
        public const string COMMON_LOVEPOINT_HEART_00 = FLASH_ROOT + "Common/pf_fl_cmn_lovepoint_heart00";
        public const string COMMON_ICON_MAX = FLASH_ROOT + "Common/pf_fl_cmn_icon_max00";
        public const string COMMON_ICON_MVP = FLASH_ROOT + "Common/pf_fl_cmn_icon_mvp00";

        /// <summary>
        /// ストーリーイベント、レイドイベント：カンストダイアログ
        /// </summary>
        public const string DIALOG_SINGLE_MODE_RESULT_COMPLETE_EVENT_REWARD = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultCompleteEventReward";

        /// <summary>
        /// レイドイベント：育成リザルトからTopへ遷移するダイアログ
        /// </summary>
        public const string DIALOG_SINGLE_MODE_RESULT_GOTO_FAN_RAID = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultGotoFanRaid";

        /// <summary>
        /// たづなさんメッセージダイアログ(育成でよく利用されるが他の個所でも使う)
        /// </summary>
        public const string DIALOG_TAZUNA_MESSEAGE_SELECT_NEXTVIEW = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogTazunaMessageNextView";
        
        /// <summary>
        /// 因子継承カットインのパス
        /// </summary>
        public const string SINGLE_MODE_FACTOR_CUTIN_PATH = "Cutt/CutIn/OutGame/outgame_factor001/outgame_factor001";

        /// <summary>
        /// 因子継承環境設定
        /// </summary>
        public const string SINGLE_MODE_FACTOR_RESULT_ENVPARAM_PATH = "3d/EnvParam/FactorResult/ast_prm_factor_result";

        /// <summary>
        /// 因子継承カットフェイシャルモーションパス
        /// </summary>
        public const string FACTOR_CUTINB_FACIAL_MOTION_PATH = "3d/Motion/OutGame/Factor/Facial/Type{0:D2}/anm_fac_type{0:D2}_factor001_A_03_face_driven";

        /// <summary>
        /// 因子継承カット耳モーションパス
        /// </summary>
        public const string FACTOR_CUTINB_EAR_MOTION_PATH = "3d/Motion/OutGame/Factor/Facial/Type{0:D2}/anm_fac_type{0:D2}_factor001_A_03_ear_driven";
        /// <summary>
        /// トレーニングレポート背景
        /// </summary>
        public const string SINGLEMODE_STATUS_RANK_ICON_FILE = "ui_statusrank_";
#if BUMA_T
        public const string TRAINING_REPORT_BG_PATH = BACKGROUND_BASE_PATH + "vertical_bg_trainingreport_00";
          /// <summary>
        /// トレーニングレポート報酬獲得演出用flash actionパス
        /// </summary>
        public const string TRAINING_REPORT_RECEIVE_REWARD_FLASH_ACTION_PATH = FLASH_COMBINE_ACTION_ROOT + "Outgame/fa_trainingreport_reward00";
        /// <summary>
        /// トレーニングレポート報酬獲得演出用flashパス
        /// </summary>
        public const string TRAINING_REPORT_RECEIVE_REWARD_FLASH_PATH = FLASH_ROOT + "Outgame/pf_fl_trainingreport_reward00";

        
        /// <summary>
        /// トレーニングレポート報酬獲得ダイアログ
        /// </summary>
        public const string DIALOG_TRAINING_REPORT_REWARD = OUTGAME_UI_PATH + "/TrainingReport/DialogTrainingReportReward";
        /// <summary>
        /// トレーニングレポート詳細ダイアログ
        /// </summary>
        public const string DIALOG_TRAINING_REPORT_INFORMATION = OUTGAME_UI_PATH + "/TrainingReport/DialogTrainingReportInformation";
        // <summary>
        /// トレーニングレポートの期限通知ダイアログ
        /// </summary>
        public const string DIALOG_NOTIFY_TRAINING_REPORT_PERIOD = OUTGAME_UI_PATH + "/TrainingReport/DialogNotifyTrainingReportPeriod";
        /// <summary>
        /// トレーニングレポートピックアップ報酬ダイアログ
        /// </summary>
        public const string DIALOG_TRAINING_REPORT_PICKUP_REWARD = OUTGAME_UI_PATH + "/TrainingReport/DialogTrainingReportPickupReward";
        /// <summary>
        /// トレーニングレポート画面獲得ポイントTAT
        /// </summary>
        public const string TRAINING_REPORT_POINT_TAT_PATH = FLASH_COMIBINE_TIMELINE_ROOT + "OutGame/tat_training_report_point00";
        /// <summary>
        /// トレーニングレポートノート背景格納先
        /// </summary>
        public const string TRAINING_REPORT_NOTE_BG_ROOT_PATH = OUTGAME_ASSET_ROOT + "TrainingReport/";
#endif
        /// <summary>
        /// シングルモード最終育成ランクアイコンパス取得
        /// </summary>
        public static string GetSingleModeFinalTrainingRankIconPath(GameDefine.FinalTrainingRank rank)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Rank/utx_txt_rank_{1:D2}", SINGLE_MODE_FLASH_ROOT, (int)rank - 1);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// 二つ名取得(フラッシュ用)
        /// </summary>
        public static string GetNickNameFramePath(MasterNickname.Nickname nickName)
        {
            int nickNameRank = 0;
            switch((MasterNickname.NichNameRank)nickName.Rank)
            {
                case MasterNickname.NichNameRank.Copper://銅
                    nickNameRank = 2;
                    break;
                case MasterNickname.NichNameRank.Silver://銀
                    nickNameRank = 1;
                    break;
                case MasterNickname.NichNameRank.Gold://金
                    nickNameRank = 0;
                    break;
            }
            
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}NickName/utx_frm_nickname_{1:D2}", SINGLE_MODE_FLASH_ROOT, nickNameRank);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモードステータスランクアイコンパス取得
        /// </summary>
        public static string GetSingleModeStatusRankIconPath(GameDefine.ParameterRank rank)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}StatusRank/{1}{2:D2}", SINGLE_MODE_FLASH_ROOT, SINGLEMODE_STATUS_RANK_ICON_FILE, (int)rank-1);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモードイベントタイトルサポートカードレアリティパス取得
        /// </summary>
        public static string GetSingleModeSupportCardRarityIconPath(GameDefine.SupportCardRarity rarity)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}SupportRarity/supportcard_rarity_{1:D2}", SINGLE_MODE_FLASH_ROOT, (int)rarity - 1);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ギャラリー用のシナリオのサムネイルパス
        /// </summary>
        /// <param name="scenarioImageId"></param>
        /// <returns></returns>
        public static string GetGallerySingleModeScenarioThumbPath(int scenarioImageId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_THUMB_ICON, scenarioImageId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモードトレーニングカットステータスアイコンパス取得
        /// </summary>
        private const string SINGLE_MODE_TRAINING_CUT_STATUS_ICON_PATH = SINGLE_MODE_FLASH_ROOT + "Trainingcut/";
        public const string SINGLE_MODE_TRAINING_CUT_STATUS_ICON_SPEED = SINGLE_MODE_TRAINING_CUT_STATUS_ICON_PATH + "utx_ico_trainingcut_status_00";
        public const string SINGLE_MODE_TRAINING_CUT_STATUS_ICON_STAMINA = SINGLE_MODE_TRAINING_CUT_STATUS_ICON_PATH + "utx_ico_trainingcut_status_01";
        public const string SINGLE_MODE_TRAINING_CUT_STATUS_ICON_POWER = SINGLE_MODE_TRAINING_CUT_STATUS_ICON_PATH + "utx_ico_trainingcut_status_02";
        public const string SINGLE_MODE_TRAINING_CUT_STATUS_ICON_GUTS = SINGLE_MODE_TRAINING_CUT_STATUS_ICON_PATH + "utx_ico_trainingcut_status_03";
        public const string SINGLE_MODE_TRAINING_CUT_STATUS_ICON_WIZ = SINGLE_MODE_TRAINING_CUT_STATUS_ICON_PATH + "utx_ico_trainingcut_status_04";

        /// <summary>
        /// シングルモードステータス変化アイコンパス
        /// </summary>
        private const string SINGLE_MODE_STATUS_ICON_PATH = SINGLE_MODE_FLASH_ROOT + "StatusIcon/";
        public const string SINGLE_MODE_STATUS_ICON_SPEED = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_charastatus_l_00";
        public const string SINGLE_MODE_STATUS_ICON_STAMINA = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_charastatus_l_01";
        public const string SINGLE_MODE_STATUS_ICON_POWER = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_charastatus_l_02";
        public const string SINGLE_MODE_STATUS_ICON_GUTS = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_charastatus_l_03";
        public const string SINGLE_MODE_STATUS_ICON_WIZ = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_charastatus_l_04";
        public const string SINGLE_MODE_STATUS_ICON_SKILL_POINT = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_skillpt_l_00";
        public const string SINGLE_MODE_STATUS_ICON_FAN = SINGLE_MODE_STATUS_ICON_PATH + "utx_ico_fun_l_00";

        // シングルモードの適性ランクアイコン
        public const string SINGLE_MODE_PROPER_RANK_ICON = SINGLE_MODE_FLASH_ROOT + "Proper/ui_proper_{0:D2}"; 
        
        /// <summary>
        /// シングルモードステータス変化やる気アイコン大パス取得
        /// </summary>
        public static string GetSingleModeStatusIconMotivationLPath(RaceDefine.Motivation motivation)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}utx_ico_motivation_l_{1:D2}", SINGLE_MODE_STATUS_ICON_PATH, (int)motivation - 1);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ターン下地のキャラ別素材
        /// </summary>
        public static string GetSingleModeTurnBgChara(int charaId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Turn/chr_training_turn_bg_{1:0000}", SINGLE_MODE_FLASH_ROOT, charaId);
            return _stringBuilder.ToString();
        }


        /// <summary>
        /// シングルモード勲章アイコンパス取得
        /// </summary>
        public static string GetSingleModeHonorIconPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Honor/utx_ico_honor_{1:D2}", SINGLE_MODE_FLASH_ROOT, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモードトレーニングアイコンのパス取得
        /// </summary>
        public static string GetSingleModeTrainingIconPath(TrainingDefine.TrainingCommandId commandId, bool isOn)
        {
            var masterTraining = MasterDataManager.Instance.masterSingleModeTraining.GetWithCommandId((int) commandId);
            int baseCommandId = masterTraining != null ? masterTraining.BaseCommandId : (int)commandId;
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Training/singlemode_training_{2}_{1:00000}", SINGLE_MODE_FLASH_ROOT, baseCommandId, isOn ? "on" : "off");
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成トレーニングアイコンLv別下地パス取得
        /// </summary>
        public static string GetSingleModeTrainingIconBasePath(int level)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("{0}Training/singlemode_base_training_{1:00}", SINGLE_MODE_FLASH_ROOT, level);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモード　シナリオバナー画像のパス(タイトル)
        /// </summary>
        public static string GetSingleModeScenarioTitleImagePath(int scenarioId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_BANNER_TITLE_PATH, scenarioId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモード　シナリオバナー文字画像のパス(タイトル)
        /// </summary>
        public static string GetSingleModeScenarioTitleTextImagePath(int scenarioId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_BANNER_TITLE_TEXT_PATH, scenarioId);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// シングルモード　シナリオバナー画像のパス(キャラ、大きい版)
        /// </summary>
        public static string GetSingleModeScenarioCharaImageBigPath(int scenarioId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_BANNER_CHARA_BIG_PATH, scenarioId);
            return _stringBuilder.ToString();
        }
        /// <summary>
        /// シングルモード　シナリオアイコンのパス
        /// </summary>
        public static string GetSingleModeScenarioIconPath(int scenarioId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_ICON_PATH, scenarioId);
            return _stringBuilder.ToString();
        }
        /// <summary>
        /// シングルモード　シナリオ背景フェード素材のパス
        /// </summary>
        public static string GetSingleModeScenarioBgFadePath(int scenarioId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_FADE_BG_PATH, scenarioId-1);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// シングルモード　シナリオ別背景エフェクトのパス
        /// </summary>
        public static string GetSingleModeScenarioBgEffectPath(int scenarioId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_BG_EFFECT_PATH, scenarioId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 年度に併せたStartViewのメイン画像パスを返す
        /// </summary>
        /// <param name="degree"></param>
        /// <returns></returns>
        public static string GetSingleModeStartMainImagePath(SingleModeDefine.DegreeType degree)
        {
            switch (degree)
            {
                case SingleModeDefine.DegreeType.Junior:
                    return "UIAnimation/Flash/SingleMode/Start/fl_section_00_junior";
                case SingleModeDefine.DegreeType.Classic:
                    return "UIAnimation/Flash/SingleMode/Start/fl_section_01_classic";
                case SingleModeDefine.DegreeType.Senior:
                    return "UIAnimation/Flash/SingleMode/Start/fl_section_02_senior";
                case SingleModeDefine.DegreeType.Final:
                    return "UIAnimation/Flash/SingleMode/Start/fl_section_03_ura";
            }

            return string.Empty;
        }

        public static string GetSingleModeStartLogoImagePath(SingleModeDefine.DegreeType degree)
        {
            switch (degree)
            {
                case SingleModeDefine.DegreeType.Junior:
                    return "UIAnimation/Flash/SingleMode/SectionStart/utx_sectionstart_00";
                case SingleModeDefine.DegreeType.Classic:
                    return "UIAnimation/Flash/SingleMode/SectionStart/utx_sectionstart_01";
                case SingleModeDefine.DegreeType.Senior:
                    return "UIAnimation/Flash/SingleMode/SectionStart/utx_sectionstart_02";
                case SingleModeDefine.DegreeType.Final:
                    return "UIAnimation/Flash/SingleMode/SectionStart/utx_sectionstart_10";
            }

            return string.Empty;
        }

        /// <summary> 育成リザルトの環境設定 </summary>
        public const string SINGLE_MODE_RESULT_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_result";

        /// <summary> シングルモード　背景フェード画像のパス </summary>
        public const string SINGLE_MODE_FADE_BG_PATH = SINGLE_MODE_BG_ROOT + "singlemode_bg_fade_{0:D2}";
        /// <summary> シングルモード　シナリオ別背景エフェクトのパス </summary>
        public const string SINGLE_MODE_SCENARIO_BG_EFFECT_PATH = EFFECT_SINGLE_MODE_ROOT + "pfb_eff_single_scenario_{0:D2}";
        /// <summary> Flashルートパス </summary>
        public const string SINGLE_MODE_FLASH_ROOT = FLASH_ROOT + "SingleMode/";
        /// <summary> イベントタイトルFlash </summary>
        public const string SINGLE_MODE_EVENT_TITLE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_eventtitle00";
        /// <summary> 育成キャラ目標ロゴタイムライン</summary>
        public const string SINGLE_MODE_START_LOGO_TARGET = FLASH_COMBINE_ROOT + "Timeline/SingleMode/tat_single_logo_target{0:D2}";
        /// <summary> 育成キャラ目標ロゴタイムライン</summary>
        public const string SINGLE_MODE_START_LOGO_CHARA = FLASH_COMBINE_ROOT + "Timeline/SingleMode/tat_single_logo_chara{0:D2}";
        /// <summary> 継承キャラ相性表示</summary>
        public const string SINGLE_MODE_START_MATCHING = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_start_matching00";
        /// <summary> トレーニング選択ボタンFlashパス </summary>
        public const string SINGLE_MODE_BUTTON_TRAINING_MENU_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_btn_trainingmenu00";
        /// <summary> やる気ボタンFlashパス </summary>
        public const string SINGLE_MODE_BUTTON_MOTIVATION_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_icon_motivation00";
        /// <summary> トレーニングカット中ステータスパネルFlashパス </summary>
        public const string SINGLE_MODE_TRAINING_CUT_STATUS_FRAME_FLASH_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_training_statusbox00";
        /// <summary> トレーニングス結果Flashパス </summary>
        public const string SINGLE_MODE_TRAINING_RESULT_FLASH_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_txt_trainingresult00";
        /// <summary> 親愛度ランクアップFlashパス </summary>
        public const string SINGLE_MODE_LOVE_RANKUP_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_rankup_lovepoint00";
        /// <summary> トレーナーレベルアップFlashパス </summary>
        public const string SINGLE_MODE_TRAINER_LEVEL_UP_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_result_lvup_tlv00";
        /// <summary> 育成リザルトランクFlashパス </summary>
        public const string SINGLE_MODE_RESULT_RANK_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_result_chararank00";
        /// <summary> 終了時のタイトルアニメーション </summary>
        public const string SINGLE_MODE_RESULT_TITLE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_title_chararesult00";

        /// <summary> 因子再獲得アニメーション </summary>
        public const string SINGLE_MODE_RESULT_REGET_FACTOR_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_regetfactor00";

        /// <summary> 育成リザルト勲章Flashパス </summary>
        public const string SINGLE_MODE_RESULT_HONOR_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_result_record00";
        /// <summary> ターン切り替えFlashパス </summary>
        public const string SINGLE_MODE_REMAIN_TURN_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_header_turncounter00";
        /// <summary> 桜吹雪エフェクトのパス </summary>
        public const string SINGLE_MODE_FALLING_CHERRY_BLOSSOMS_EFFECT_PATH = "3d/Effect/Training/pfb_eff_tra_cherry_01";
        /// <summary> TAT友情トレーニングエフェクト </summary>
        public const string TAT_SINGLE_MODE_EFF_TAG_TRAINING = FLASH_COMBINE_ROOT + "Timeline/SingleMode/tat_single_eff_tag_trainingmenu00";

        /// <summary> シングルモードUIパーツパス </summary>
        public const string SINGLE_MODE_UI_PARTS_PATH = UIPartsPath + "/SingleMode/";
        /// <summary> ウマ娘詳細ダイアログ：シングルモード厩舎TOP </summary>
        public const string DIALOG_SINGLE_MODE_MAIN_CHARACTER_DETAIL_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeMainCharacterDetail";

        /// <summary>
        /// ウマ娘詳細ダイアログ：キャラ効果アイコン ロングタップ
        /// </summary>
        public const string PARTS_CHARA_EFFECT_LONG_TAP_INFO_POP_PATH = UIPartsPath + "/CharaEffect/CharaEffectLongTapInfoPop";
        /// <summary> 戦績ダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_RACE_HISTORY_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeRaceHistory";
        /// <summary> 目標詳細ダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_ROUTE_INFO_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeRouteInfo";
        /// <summary> やる気情報ダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_MOTIVATION_INFO_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeMotivationInfo";
        /// <summary> やる気情報ダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_SHORTCUT_SETTING_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeShortCutSetting";
        /// <summary> キャラクラス情報ダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_CHARA_GRADE_INFO_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeCharaGradeInfo";
        /// <summary> 獲得スキルダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_ACQUISITION_SKILL_LIST_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeAcquisitionSkillList";
        /// <summary> HPゲージ：シングルモード </summary>
        public const string SINGLE_MODE_HP_GAUGE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_header_hpgauge00";
        /// <summary> 月初画面演出用Flash </summary>
        public const string SINGLE_MODE_SECTION_START_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "pf_fl_singlemode_sectionstart00";
        /// <summary> 月初画面演出用Particle </summary>
        public const string SINGLE_MODE_START_PARTICLE_PATH = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_sectionstart_particle_00";
        /// <summary> ルート目標ターン表示用のFlashAction </summary>
        public const string SINGLE_MODE_ROUTE_TARGET_FLASH_ACTION_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_next_target00";

        /// <summary> 夏合宿お休み＆お出かけ確認ダイアログ：シングルモード </summary>
        public const string DIALOG_SINGLE_MODE_SUMMER_OUTING_CONFIRM_PATH = SINGLE_MODE_UI_PARTS_PATH + "DialogSingleModeSummerOutingConfirm";

        /// <summary> レース詳細ダイアログ通常報酬表示：シングルモード </summary>
        public const string PARTS_SINGLE_MODE_CONFIRM_REWARD = SINGLE_MODE_UI_PARTS_PATH + "PartsSingleModeRaceConfirmReward";


        #region ハードモードキャンペーン

        /// <summary> シングルモード　キャンペーンルート </summary>
        public const string SINGLE_MODE_CAMPAIGN_ROOT = "Single/Campaign/";
        /// <summary> シングルモード　キャンペーンヘッダールート </summary>
        public const string SINGLE_MODE_CAMPAIGN_HEADER_ROOT = "Single/CampaignHeader/";

        /// <summary> シングルモード　ハードモードキャンペーン 報酬条件達成 背景画像のパス </summary>
        public const string SINGLE_MODE_CAMPAIGN_REWARD_COMPLETE_BG_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_itemlimited";
        /// <summary> シングルモード　ハードモードキャンペーン 挑戦画像のパス </summary>
        public const string SINGLE_MODE_CAMPAIGN_CHALLENGE_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_challenge_{0:D4}";
        /// <summary> シングルモード　ハードモードキャンペーン BOXガチャアイコン </summary>
        public const string SINGLE_MODE_CAMPAIGN_BOX_ICON_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_difficulty_box_{0:D4}";
        /// <summary> シングルモード　ハードモードキャンペーン マニー報酬アイコン </summary>
        public const string SINGLE_MODE_CAMPAIGN_MODE_MONEY_ICON_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_difficulty_money_{0:D4}";
        /// <summary> シングルモード　ハードモードキャンペーン 挑戦画像のパス 複数難易度用 </summary>
        public const string SINGLE_MODE_CAMPAIGN_CHALLENGE_MULTI_CLOSED_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_challenge_closed_{0:D4}_{1:D4}";
        /// <summary> シングルモード　ハードモードキャンペーン モード別BOXガチャアイコン </summary>
        public const string SINGLE_MODE_CAMPAIGN_MODE_BOX_ICON_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_challenge_{0:D4}_{1:D4}";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度Lv別アイコン </summary>
        public const string SINGLE_MODE_CAMPAIGN_CHALLENGE_LV_ICON_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_singlemode_challenge_lv_ico_{0:D4}_{1:D3}";
        /// <summary> シングルモード　ハードモードキャンペーン ヘッダー画像パス </summary>
        public const string SINGLE_MODE_CAMPAIGN_HEADER_PATH = SINGLE_MODE_CAMPAIGN_HEADER_ROOT + "tex_singlemode_difficulty_header_{0:D4}_sl";
        /// <summary> シングルモード　ハードモードキャンペーン複数難易度対応 ヘッダー画像パス </summary>
        public const string SINGLE_MODE_CAMPAIGN_HEADER_WITH_DIFFICULTY_INDEX_PATH = SINGLE_MODE_CAMPAIGN_HEADER_ROOT + "tex_singlemode_difficulty_header_{0:D4}_{1:D4}_sl";
        /// <summary> シングルモード　ハードモードキャンペーン ブーストモードアイコン </summary>
        public const string SINGLE_MODE_CAMPAIGN_BOOST_ICON_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "utx_ico_singlemode_challenge_bonusup_{0:D4}_00";

        /// <summary> シングルモード　ハードモードキャンペーン 難易度選択ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_START_SELECT_DIFFICULTY = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeSelectDifficulty";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度選択ダイアログ(BOX報酬タイプ) </summary>
        public const string DIALOG_SINGLE_MODE_START_SELECT_DIFFICULTY_BOX = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeSelectDifficultyBox";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度選択ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_START_SELECT_DIFFICULTY_MULTI = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeSelectDifficultyMulti";
        /// <summary> シングルモード　ハードモードキャンペーン 報酬条件達成ダイアログ </summary>
        public const string DIALOG_DIFFICULTY_CAMPAIGN_REWARD_COMPLETE = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogDifficultyCampaignRewardComplete";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度と効果ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_DIFFICULTY_EFFECT_INFO = SINGLE_MODE_UI_ROOT + "DialogSingleModeDifficultyEffectInfo";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度と効果ダイアログパーツ </summary>
        public const string PARTS_SINGLE_MODE_EFFECT_INFO = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyEffectInfo";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度と効果ダイアログパーツ 難易度レベル表示あり</summary>
        public const string PARTS_SINGLE_MODE_EFFECT_INFO_WITH_LEVEL = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyEffectInfoWithLevel";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度と効果ダイアログパーツ 追加スキルあり </summary>
        public const string PARTS_SINGLE_MODE_EFFECT_INFO_WITH_SKILL = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyEffectInfoWithSkill";

        /// <summary> シングルモード　ハードモードキャンペーン BOX報酬一覧ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_DIFFICULTY_BOX_LIST = SINGLE_MODE_UI_ROOT + "DialogSingleModeDifficultyBoxList";
        /// <summary> シングルモード　ハードモードキャンペーン ランダム報酬一覧ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_DIFFICULTY_RANDOM_REWARD_LIST = SINGLE_MODE_UI_ROOT + "DialogSingleModeDifficultyRandomRewardList";
        /// <summary> シングルモード　ハードモードキャンペーン タイトル </summary>
        public const string PARTS_SINGLE_MODE_DIFFICULTY_TITLE = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyTitle";
        /// <summary> シングルモード　ハードモードキャンペーン 難易度効果パーツ </summary>
        public const string PARTS_SINGLE_MODE_DIFFICULTY_EFFECT_INFO = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyEffectInfo";
         /// <summary> シングルモード　ハードモードキャンペーン 難易度効果パーツ小 (育成最終確認などで使用</summary>
        public const string PARTS_SINGLE_MODE_DIFFICULTY_EFFECT_INFO_SMALL = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyEffectInfoSmall";
         /// <summary> シングルモード　ハードモードキャンペーン 難易度効果パーツ小 難易度レベルあり </summary>
        public const string PARTS_SINGLE_MODE_DIFFICULTY_EFFECT_INFO_WITH_LEVEL_SMALL = SINGLE_MODE_UI_ROOT + "PartsSingleModeDifficultyEffectInfoWithLevelSmall";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX報酬ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_RESULT_DIFFICULTY_BOX = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultDifficultyBox";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン モード結果タイトルFlash </summary>
        public const string SINGLE_MODE_HARDMODE_TITLE_MODE_REWARD_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "HardMode/pf_fl_singlemode_hardmode_title_modereward00";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX開封演出タイトルFlash </summary>
        public const string SINGLE_MODE_HARDMODE_TXT_TITLE_FLASH_PATH = SINGLE_MODE_FLASH_ROOT + "HardMode/pf_fl_singlemode_hardmode_txt_title{0:D4}";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX開封演出FlashAction(大ガチャ） </summary>
        public const string SINGLE_MODE_HARDMODE_REWARD_GAUGE00_FLASH_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "HardMode/fa_singlemode_hardmode_reward_gauge{0:D4}";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX開封演出FlashAction（小ガチャ） </summary>
        public const string SINGLE_MODE_HARDMODE_REWARD_GAUGE01_FLASH_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "HardMode/fa_singlemode_hardmode_reward_gauge{0:D4}_01";
        /// <summary> シングルモードリザルト　ハードモード ランダム報酬演出 </summary>
        public const string SINGLE_MODE_HARDMODE_RANDOM_REWARD_ITEM_PATH = SINGLE_MODE_FLASH_ROOT + "HardMode/pf_fl_singlemode_hardmode_reward_item{0:D4}";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX報酬コンプリートダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_RESULT_DIFFICULTY_BOX_COMPLETE = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultDifficultyBoxComplete";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX報酬コンプリートタイトル画像のパス </summary>
        public const string SINGLE_MODE_CAMPAIGN_BOX_REWARD_COMPLETE_TITLE_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_txt_singlemode_allget_00";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン BOX報酬コンプリートモード別画像のパス </summary>
        public const string SINGLE_MODE_CAMPAIGN_BOX_REWARD_COMPLETE_MODE_PATH = SINGLE_MODE_CAMPAIGN_ROOT + "tex_ico_singlemode_allget_{0:D4}";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン 新難易度解放ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_RESULT_DIFFICULTY_OPEN = SINGLE_MODE_UI_ROOT + "SingleModeResult/DialogSingleModeResultDifficultyOpen";
        /// <summary> シングルモードリザルト　ハードモードキャンペーン ランダム報酬エフェクト </summary>
        public const string SINGLE_MODE_HARDMODE_RANDOM_REWARD_EFFECT = "UIAnimation/UIEffect/SingleMode/HardMode/pfb_uieff_single_hm_result_pa{0:D4}_{1:D2}";

        public const string SINGLE_MODE_HARDMODE_RESULT_SMOG_EFFECT = "UIAnimation/UIEffect/SingleMode/HardMode/pfb_uieff_single_hm_result_smog00"; 

        /// <summary>
        /// ハードモードのモード別ヘッダー素材パス
        /// </summary>
        public static string GetDifficultyTitleBasePath(MasterSingleModeDifficultyData.SingleModeDifficultyData difficultyData)
        {
            _stringBuilder.Length = 0;
            var difficultyMode = MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(difficultyData.DifficultyId);
            if(difficultyMode != null && difficultyMode.IsContinueBonusRewardType) //難易度別にタイトル下地を読み替える必要がある
            {
                return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_HEADER_WITH_DIFFICULTY_INDEX_PATH, difficultyData.DifficultyId, difficultyData.DifficultyIndex).ToString();
            }
            else
            {
                return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_HEADER_PATH, difficultyData.DifficultyId).ToString();
            }
        }

        /// <summary>
        /// ハードモード : ブーストモード用アイコンパス
        /// </summary>
        public static string GetDifficultyBoostIconPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_BOOST_ICON_PATH, difficultyId).ToString();
        }

        /// <summary>
        /// ハードモードBOX開封演出タイトルFlashパス
        /// </summary>
        public static string GetDifficultyModeTextTitleFlashPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_HARDMODE_TXT_TITLE_FLASH_PATH, difficultyId).ToString();
        }
        
        /// <summary>
        /// ハードモードBOXガチャ大の開封演出Flashパス
        /// </summary>
        public static string GetDifficultyModeGauge00FlashPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_HARDMODE_REWARD_GAUGE00_FLASH_PATH, difficultyId).ToString();
        }
        /// <summary>
        /// ハードモードBOXガチャ小の開封演出Flashパス
        /// </summary>
        public static string GetDifficultyModeGauge01FlashPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_HARDMODE_REWARD_GAUGE01_FLASH_PATH, difficultyId).ToString();
        }
        
        /// <summary>
        /// ハードモードBOX報酬コンプリートモード別画像パス
        /// </summary>
        public static string GetDifficultyBoxRewardCompleteModePath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_BOX_REWARD_COMPLETE_MODE_PATH, difficultyId).ToString();
        }
        
        /// <summary>
        /// ハードモードのモード別アイコンパス
        /// </summary>
        public static string GetDifficultyModeIconPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_CHALLENGE_PATH, difficultyId).ToString();
        }

        /// <summary>
        /// ハードモードのモード別未解放アイコンパス
        /// </summary>
        /// <param name="difficultyId"></param>
        /// <returns></returns>
        public static string GetDifficultyModeLockedIconPath(int difficultyId, int difficultyIndex)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_CHALLENGE_MULTI_CLOSED_PATH, difficultyId, difficultyIndex).ToString();
        }
        
        /// <summary>
        /// ハードモードのBOXガチャアイコンパス
        /// </summary>
        public static string GetDifficultyBoxIconPath(int boxId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_BOX_ICON_PATH, boxId).ToString();
        }

        /// <summary>
        /// ハードモードのモード画像BOXガチャアイコンパス
        /// </summary>
        public static string GetDifficultyModeBoxIconPath(int difficultyId, int boxId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_MODE_BOX_ICON_PATH, difficultyId, boxId).ToString();
        }

        /// <summary>
        /// ハードモードのマニー報酬アイコンパス
        /// </summary>
        /// <returns></returns>
        public static string GetDifficultyModeMoneyIconPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_MODE_MONEY_ICON_PATH, difficultyId).ToString();
        }

        /// <summary>
        /// ハードモードの難易度レベル別アイコンパス
        /// </summary>
        public static string GetDifficultyLevelIconPath(MasterSingleModeDifficultyData.SingleModeDifficultyData masterDifficultyData)
        {
            var difficultyMode = MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(masterDifficultyData.DifficultyId);
            _stringBuilder.Length = 0;
            if(difficultyMode != null && difficultyMode.IsContinueBonusRewardType )
            {
                //ゴルシモード2022で使用する難易度別画像
                return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_MODE_BOX_ICON_PATH, masterDifficultyData.DifficultyId, masterDifficultyData.DifficultyIndex).ToString();
            }
            else
            {
                //通常のLv表示画像
                return _stringBuilder.AppendFormat(SINGLE_MODE_CAMPAIGN_CHALLENGE_LV_ICON_PATH, masterDifficultyData.DifficultyId, masterDifficultyData.Difficulty).ToString();
            }
        }
        /// <summary>
        /// ハードモードのランダム報酬獲得演出パス
        /// </summary>
        public static string GetResultRandomRewardFlashPath(int difficultyId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_HARDMODE_RANDOM_REWARD_ITEM_PATH, difficultyId).ToString();
        }

        /// <summary>
        /// 難易度情報パーツPrefabパス
        /// </summary>
        public static string GetDifficultyEffectInfoPath(MasterSingleModeDifficultyData.SingleModeDifficultyData difficultyData)
        {
            var mode = MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(difficultyData.DifficultyId);
            if(mode != null && mode.IsContinueBonusRewardType) //ゴルシモード2022では難易度レベル表示しない
            {
                if(difficultyData.ExistAdditionSkill) //鬼ゴルシモードで追加スキルあり
                {
                    return PARTS_SINGLE_MODE_EFFECT_INFO_WITH_SKILL;
                }
                else //追加スキル無し
                {
                    return PARTS_SINGLE_MODE_EFFECT_INFO;
                }
            }
            else //ショータイムモード レベル表示あり
            {
                return PARTS_SINGLE_MODE_EFFECT_INFO_WITH_LEVEL;
            }
        }

        /// <summary>
        /// 難易度情報表示パーツ(小)のパスを取得
        /// </summary>
        public static string GetDifficultyEffectInfoSmallPath(MasterSingleModeDifficultyData.SingleModeDifficultyData singleModeDifficultyData)
        {
            var mode = MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(singleModeDifficultyData.DifficultyId);
            if(mode != null && mode.IsContinueBonusRewardType) //ゴルシモード2022では難易度レベル表示しない
            {
                return PARTS_SINGLE_MODE_DIFFICULTY_EFFECT_INFO_SMALL;
            }
            else
            {
                return PARTS_SINGLE_MODE_DIFFICULTY_EFFECT_INFO_WITH_LEVEL_SMALL;
            }
        }

        /// <summary>
        /// ランダム報酬獲得時エフェクトのパスを取得
        /// </summary>
        /// <returns></returns>
        public static string GetDifficultyRandomRewaradEffectPath(MasterSingleModeDifficultyData.SingleModeDifficultyData singleModeDifficultyData, int prize_type)
        {
            return TextUtil.Format(SINGLE_MODE_HARDMODE_RANDOM_REWARD_EFFECT,singleModeDifficultyData.DifficultyId,prize_type);
        }

        /// <summary> シングルモード　育成チャレンジ選択ダイアログ </summary>
        public const string DIALOG_SINGLE_MODE_START_SELECT_TRAINING_CHALLENGE = SINGLE_MODE_UI_ROOT + "SingleModeStart/DialogSingleModeSelectTrainingChallenge";

        #endregion

        #region シングルモード用エフェクト
        // 親ディレクトリ
        private const string SINGLE_MODE_EFFECT_ROOT = EffectRoot + "Training/";
        private const string SINGLE_MODE_PROLOGUE_EFFECT_ROOT = SINGLE_MODE_EFFECT_ROOT + "00_prologue/";

        // フォーマット
        public const string SINGLE_MODE_PROLOGUE_EFFECT_FILE_FORMAT = "pfb_eff_tra_prologue_{0:D2}";
        private const string SINGLE_MODE_PROLOGUE_EFFECT_PATH_FORMAT = SINGLE_MODE_PROLOGUE_EFFECT_ROOT + SINGLE_MODE_PROLOGUE_EFFECT_FILE_FORMAT;

        // ストーリー用エフェクトの読み込みベースパス
        private const string STORY_EFFECT_ROOT = EffectRoot + "Story/";

        // イベントストーリー用の読み込みベースパスとファイル用フォーマット
        private const string EVENT_STORY_EFFECT_ROOT_FORMAT = STORY_EFFECT_ROOT + "StoryEvent/{0:D2}/";
        public const string EVENT_STORY_EFFECT_FILE_FORMAT = "pfb_eff_storyevent_{0:D2}_{1:D3}";

        /// <summary>
        /// シングルモードリザルトのエフェクト
        /// </summary>
        public const string SINGLE_MODE_RESULT_EFFECT_PATH = SINGLE_MODE_EFFECT_ROOT + "pfb_eff_tra_confetti_01";

        /// <summary>
        /// 育成プロローグのエフェクトのパス
        /// </summary>
        public static string GetSingleModePrologueEffectPath(int effectId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_PROLOGUE_EFFECT_PATH_FORMAT, effectId).ToString();
        }

        /// <summary>
        /// イベントストーリー用エフェクトリソースのパスを取得する
        /// ストーリーイベントとは異なるので注意
        /// </summary>
        /// <param name="eventId">イベント番号</param>
        /// <param name="effectId">エフェクトID</param>
        /// <returns></returns>
        public static string GetEventStoryEffectPath(int eventId, int effectId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder
                .AppendFormat(EVENT_STORY_EFFECT_ROOT_FORMAT, eventId)
                .AppendFormat(EVENT_STORY_EFFECT_FILE_FORMAT, eventId, effectId)
                .ToString();
        }
        #endregion シングルモード用エフェクト

        #endregion

        #region アウトゲーム

        public const string OUTGAME_ASSET_ROOT = "OutGame/";

        /// <summary>
        /// アウトゲームUIパーツパス
        /// </summary>
        public const string OUTGAME_UI_PATH = UIPartsPath + "/Outgame";
        
        /// <summary>
        /// サポートカード詳細ダイアログ
        /// </summary>
        public const string SUPPORTCARD_DETAIL_ASSET_ROOT = OUTGAME_ASSET_ROOT + "SupportCardDetails/";

        /// <summary>
        /// サポートカード詳細ダイアログ
        /// </summary>
        public const string DIALOG_SUPPORTCARD_DETAIL_PATH = OUTGAME_UI_PATH + "/DialogSupportCardDetail";
        
        /// <summary>
        /// サポートカード詳細ダイアログのレアリティ別背景画像
        /// </summary>
        public static string GetSupportCardDetailRarityBgPath(int rarity)
        {
            switch ((GameDefine.SupportCardRarity)rarity)
            {
                case GameDefine.SupportCardRarity.RareSSR:
                    return SUPPORTCARD_DETAIL_ASSET_ROOT + "tex_supportcard_bg_ssr";
                case GameDefine.SupportCardRarity.RareSR:
                    return SUPPORTCARD_DETAIL_ASSET_ROOT + "tex_supportcard_bg_sr";
                case GameDefine.SupportCardRarity.RareR:
                default:
                    return String.Empty;
            }
        }

        //サポートカード詳細ダイアログのヘッダー背景の表面画像パス
        public const string SUPPORTCARD_DETAIL_FRONT_BG_PATH = SUPPORTCARD_DETAIL_ASSET_ROOT + "tex_supportcard_diagonalline";

        /// <summary>
        /// サポートカードデッキ効果一覧ダイアログ
        /// </summary>
        public const string DIALOG_SUPPORTCARD_DECK_EFFECT_LIST_PATH = UIPartsPath + "/SupportCard/DialogSupportCardDeckEffectList";

        /// <summary>
        /// サポートカード練習効果詳細ダイアログ
        /// </summary>
        public const string DIALOG_SUPPORTCARD_TRAINING_EFFECT_DETAIL_PATH = UIPartsPath + "/SupportCard/DialogSupportCardTrainingEffectDetail";

        /// <summary>
        /// サポートカードグループ情報ダイアログ
        /// </summary>
        public const string DIALOG_SUPPORTCARD_GROUP_INFO_PATH = UIPartsPath + "/SupportCard/DialogSupportCardGroupInfo";
        
        /// <summary>
        /// サポートカード編成ルールダイアログ
        /// </summary>
        public const string DIALOG_SUPPORTCARD_RESTRICT_SCENARIO_PATH = UIPartsPath + "/SupportCard/DialogSupportCardRestrictScenario";

        /// <summary>
        /// 移籍確認ダイアログ
        /// </summary>
        public const string DIALOG_DECIDE_RETIRE_PATH = OUTGAME_UI_PATH + "/TrainedCharaList/DialogDecideRetire";

        /// <summary>
        /// 移籍完了メッセージダイアログ
        /// </summary>
        public const string DIALOG_TRAINED_CHARA_RETIRED_MESSAGE_PATH = CharacterUIPath + "/DialogTrainedCharaRetiredMessage";
        
        /// <summary>
        /// 才能開花カット
        /// </summary>
        public const string PARTS_LIMITBREAK_CUT_PATH = UIPartsPath + "/Character/PartsLimitBreakCut";

        /// <summary>
        /// 才能開花ダイアログ
        /// </summary>
        public const string DIALOG_CHARA_LIMITBREAK_PATH = OUTGAME_UI_PATH + "/DialogCharacterLimitBreak";

        /// <summary>
        /// 才能開花スキル進化内容表示
        /// </summary>
        public const string DIALOG_CHARA_LIMITBREAK_SKILL_DIFF_PATH = OUTGAME_UI_PATH + "/DialogCharacterLimitBreakSkillDiff";

        /// <summary>
        /// 才能開花スキル表示
        /// </summary>
        public const string DIALOG_CHARA_LIMITBREAK_SKILL_SIMPLE_PATH = OUTGAME_UI_PATH + "/DialogCharacterLimitBreakSkillSimple";

        /// <summary>
        /// 才能開花時コメントテクスチャ
        /// </summary>
        public const string LIMITBREAK_COMMENT_TEXTURE_BASE_PATH = ResourcePath.FLASH_ROOT + "Chara/Comment/chara_rarity_upgrade_comment_{0:D6}{1:D1}";
        public static string GetLimitBreakCommentTexturePath(int cardId)
        {
            var path = TextUtil.Format(LIMITBREAK_COMMENT_TEXTURE_BASE_PATH, cardId, WorkDataManager.Instance.UserData.Gender == GameDefine.Gender.Female ? 1 : 0);
            if (ResourceManager.IsExistAsset(path))
            {
                // 性別差分が存在すれば性別用のリソースで返却
                return path;
            }
            // なければ共通
            return TextUtil.Format(LIMITBREAK_COMMENT_TEXTURE_BASE_PATH, cardId, 0);
        }

        /// <summary>
        /// 才能開花フラッシュパス
        /// </summary>
        public const string LIMITBREAK_FLASH_COMMENT_PATH = ResourcePath.FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_rarity_upgrade_comment00";
        public const string LIMITBREAK_FLASH_WHITE_PATH = ResourcePath.FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_rarity_upgrade_flash00";
        public const string LIMITBREAK_FLASH_STAR_PATH = ResourcePath.FLASH_COMBINE_ROOT + "Action/Chara/fa_chara_rarity_upgrade_stars00";
        public const string SCREEN_EFFECT_PATH = ResourcePath.FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_eff_rarity_upgrade_stars_complete00";
        
        //覚醒レベル系演出Path
        public const string LIMITBREAK_FLASH_TALENT_PATH = ResourcePath.FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_talent_levelup00";
        public const string TALENT_LV_UP_SCREEN_EFFECT = FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_eff_talent_levelup_complete00";
        

        public const string LIMITBREAK_COMMENT_TEX_1_PATH = "OBJ_mc_dum_comment01_add/MOT_mc_dum_comment00/PLN_dum_comment00";
        public const string LIMITBREAK_COMMENT_TEX_2_PATH = "OBJ_mc_comment_set/MOT_mc_comment_set/OBJ_mc_dum_comment00/MOT_mc_dum_comment00/PLN_dum_comment00";


        /// <summary>
        /// 覚醒レベル強化ダイアログ
        /// </summary>
        public const string DIALOG_CHARA_TALENT_UPGRADE_PATH = OUTGAME_UI_PATH + "/DialogCharacterTalentUpgrade";
        
        /// <summary>
        /// ヒントLvアップダイアログ
        /// </summary>
        public const string DIALOG_CHARA_HINT_LV_UP_PATH = OUTGAME_UI_PATH + "/DialogCharacterHintLvUp";

        /// <summary>
        /// キャラのスキル詳細を一つだけ出すダイアログ
        /// </summary>
        public const string DIALOG_CHARA_SIMPLE_SKILL_DETAIL_PATH = OUTGAME_UI_PATH + "/DialogCharacterSimpleSkillDetail";
        
        /// <summary>
        /// ヒントLvアップのリザルトダイアログ
        /// </summary>
        public const string DIALOG_CHARA_RESULT_HINT_LV_UP = OUTGAME_UI_PATH + "/DialogCharacterResultHintLvUp";

        /// <summary>
        /// キャラのスキル詳細を一つだけ出すダイアログ
        /// </summary>
        public const string DIALOG_UNIQUE_EFFECT_DETAIL_SIMPLE_PATH = OUTGAME_UI_PATH + "/DialogUniqueEffectDetailSimple";
        
        /// <summary>
        /// 絞り込み条件設定ダイアログ
        /// </summary>
        public const string DIALOG_CARD_FILTER_CONDITION_PATH = OUTGAME_UI_PATH + "/DialogCardFilterCondition";

        /// <summary>
        /// サポカ絞り込み条件設定ダイアログ
        /// </summary>
        public const string DIALOG_SUPPORT_CARD_FILTER_CONDITION_PATH = OUTGAME_UI_PATH + "/DialogSupportCardFilterCondition";
        
        /// <summary>
        /// サポカイベント詳細ダイアログ
        /// </summary>
        public const string DIALOG_SUPPORT_CARD_SKILLHINT_PATH = OUTGAME_UI_PATH + "/DialogSkillHint";

        /// <summary>
        /// アイテム入手導線ダイアログ
        /// </summary>
        public const string DIALOG_ITEM_PLACE_PATH = OUTGAME_UI_PATH + "/DialogItemPlace";

        /// <summary>
        /// アイテム変換確認ダイアログ
        /// </summary>
        public const string DIALOG_ITEM_TRADE_CONFIRM = OUTGAME_UI_PATH + "/DialogItemTradeConfirm";

        /// <summary>
        /// アイテム変換完了ダイアログ
        /// </summary>
        public const string DIALOG_ITEM_TRADE_COMPLETE = OUTGAME_UI_PATH + "/DialogItemTradeComplete";

        /// <summary>
        /// ウマ娘（カード）一覧画面の3D環境設定
        /// </summary>
        public const string CARD_HAVE_LIST_ENV_PATH = EnvParamRoot + "CardHaveList/ast_prm_card_have_list";

        /// <summary>
        /// ウマ娘選択画面の3D環境設定
        /// </summary>
        public const string CHARACTER_SELECT_ENV_PATH = EnvParamRoot + "CharacterSelect/ast_prm_character_select_view";

        /// <summary>
        /// プロフィールの3D表示
        /// </summary>
        public const string PROFILE_CHARACTER_3D_VIEWER_PATH = "Prefabs/Outgame/ProfileCharacter3DViewer";

        /// <summary>
        /// プロフィールの3D環境設定
        /// </summary>
        public const string CARD_PROFILE_ENV_PATH = EnvParamRoot + "Profile/ast_prm_profile";

        /// <summary>
        /// ウマ娘ノートの3D表示
        /// </summary>
        public const string CHARACTER_NOTE_3D_VIEWER_PATH = "Prefabs/Outgame/CharacterNote3DViewer";

        /// <summary>
        /// ウマ娘ノートの3D環境設定
        /// </summary>
        public const string CHARACTER_NOTE_ENV_PATH = EnvParamRoot + "CharacterDirectory/ast_prm_character_directory";

        /// <summary>
        /// 限定ミッションダイアログ
        /// </summary>
        public const string DIALOG_LIMITED_MISSION_PATH = UIPartsPath + "/DialogLimitedMission";

        /// <summary>
        /// イベント限定ミッションダイアログ（ミッションのあるイベントの一覧ダイアログ）
        /// </summary>
        public const string DIALOG_MISSION_EVENT_LIST = UIPartsPath + "/Outgame/Mission/DialogMissionEventList";

        /// <summary>
        /// ミッションUI
        /// </summary>
        public const string OUTGAME_MISSION_UI_PATH = OUTGAME_UI_PATH + "/Mission/";
        
        /// <summary>
        /// ミッション：レース詳細
        /// </summary>
        public const string DIALOG_MISSION_RACE_DETAIL = OUTGAME_MISSION_UI_PATH + "DialogMissionRaceDetail";

        /// <summary>
        /// ミッション：レースのおすすめウマ娘一覧
        /// </summary>
        public const string DIALOG_MISSION_RACE_RECOMMEND_CHARA_LIST = OUTGAME_MISSION_UI_PATH + "DialogMissionRaceRecommendCharaList";

        /// <summary>
        /// ノート演出キャラガチャパス
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetNoteActGachaCharacterCardTexturePath(int cardId)
        {
            cardId = Mathf.Max(0, cardId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("Outgame/Note/gacha_thumb/chara_card/gacha_thumb_{0:000000}", cardId);

            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ノート演出サポカガチャパス
        /// </summary>
        /// <param name="supportCardId"></param>
        /// <returns></returns>
        public static string GetNoteActGachaSupportCardTexturePath(int supportCardId)
        {
            supportCardId = Mathf.Max(0, supportCardId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("Outgame/Note/gacha_thumb/support_card/gacha_thumb_{0:00000}", supportCardId);

            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ノート演出才能開花パス
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetNoteActLimitBreakCharacterCardTexturePath(int cardId)
        {
            cardId = Mathf.Max(0, cardId);
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("Outgame/Note/limitbreak_thumb/limitbreak_thumb_{0:000000}", cardId);

            return _stringBuilder.ToString();
        }

        /// <summary>
        /// トレーナー名変更ダイアログ
        /// </summary>
        public const string DIALOG_CHANGE_USER_NAME_PATH = OUTGAME_UI_PATH + "/DialogChangeUserName";
        
        /// <summary>
        /// トレーナー名・性別一括変更ダイアログ
        /// </summary>
        public const string DIALOG_CHANGE_USER_NAME_AND_GENDER_PATH = OUTGAME_UI_PATH + "/DialogChangeUserNameAndGender";

        /// <summary>
        /// トレーナー名・性別変更確認ダイアログ
        /// </summary>
        public const string DIALOG_CONFIRM_USER_NAME_AND_GENDER_PATH = OUTGAME_UI_PATH + "/DialogConfirmUserNameAndGender";

        /// <summary>
        /// コメント変更ダイアログ
        /// </summary>
        public const string DIALOG_CHANGE_USER_COMMENT_PATH = OUTGAME_UI_PATH + "/DialogChangeUserComment";

        /// <summary>
        /// トレーナー誕生日設定ダイアログ
        /// </summary>
        public const string DIALOG_BIRTH_DAY_SETTING_PATH = OUTGAME_UI_PATH + "/DialogTrainerBirthDaySetting";

        /// <summary>
        /// トレーナー誕生日設定完了ダイアログ
        /// </summary>
        public const string DIALOG_BIRTH_DAY_SETTING_COMPLETE_PATH = OUTGAME_UI_PATH + "/DialogTrainerBirthDaySettingComplete";

        /// <summary>
        /// チームランク詳細ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_RANK_DETAIL = OUTGAME_UI_PATH + "/DialogTeamRankDetail";

        /// <summary>
        /// 進行状況ダイアログ
        /// </summary>
        public const string DIALOG_PROGRESS = OUTGAME_UI_PATH + "/DialogProgress";

        /// <summary>
        /// TP回復ダイアログ
        /// </summary>
        public const string DIALOG_RECOVER_TP_ITEM_LIST_PATH = OUTGAME_UI_PATH + "/DialogRecoverTpItemList";

        /// <summary>
        /// RP回復ダイアログ
        /// </summary>
        public const string DIALOG_RECOVER_RP_ITEM_LIST_PATH = OUTGAME_UI_PATH + "/DialogRecoverRpItemList";

        /// <summary>
        /// RP回復完了ダイアログ
        /// </summary>
        public const string DIALOG_RECOVER_RP_COMPLETE_PATH = OUTGAME_UI_PATH + "/DialogRecoverRpComplete";

        /// <summary> ミッション画面環境設定 </summary>
        public const string MISSION_ENV_PATH = EnvParamRoot + "Mission/ast_prm_mission_view";

        /// <summary>
        /// 称号絞り込みダイアログ
        /// </summary>
        public const string DIALOG_FILTER_HONOR_PATH = OUTGAME_UI_PATH + "/DialogFilterHonor";

        /// <summary>
        /// ホームのオプションダイアログ
        /// </summary>
        public const string DIALOG_OPTION_HOME = OUTGAME_UI_PATH + "/Option/DialogOptionHome";
        
        #if BUMA_T // 删除用户账号数据功能（刷初始）by jiahan. 2021-12-13
        public const string DIALOG_OPTION_PartsOptionItemTitle_AccountSetting_BUMA = OUTGAME_UI_PATH + "/Option/PartsOptionPageBasicSetting_BUMA/PartsOptionItemTitle_AccountSetting_BUMA";
        public const string DIALOG_OPTION_PartsOptionItemInfo_DeleteUserData_BUMA = OUTGAME_UI_PATH + "/Option/PartsOptionPageBasicSetting_BUMA/PartsOptionItemInfo_DeleteUserData_BUMA";
        public const string DIALOG_OPTION_PartsOptionItemButton_DeleteUserData_BUMA = OUTGAME_UI_PATH + "/Option/PartsOptionPageBasicSetting_BUMA/PartsOptionItemButton_DeleteUserData_BUMA";
        #endif
        
        /// <summary>
        /// 育成のオプションダイアログ
        /// </summary>
        public const string DIALOG_OPTION_SINGLE_MODE = OUTGAME_UI_PATH + "/Option/DialogOptionSingleMode";
        /// <summary>
        /// レースのオプションダイアログ
        /// </summary>
        public const string DIALOG_OPTION_RACE = OUTGAME_UI_PATH + "/Option/DialogOptionRace";
        /// <summary>
        /// ライブシアターのオプションダイアログ
        /// </summary>
        public const string DIALOG_OPTION_LIVE_THEATER = OUTGAME_UI_PATH + "/Option/DialogOptionLiveTheater";
        
        /// <summary>
        /// ライブ楽曲獲得表示用のダイアログ
        /// </summary>
        public const string DIALOG_GET_LIVE_MUSIC = UIPartsPath + "/DialogGetLiveMusic";

        /// <summary>
        /// ライブ楽曲を複数獲得した時のダイアログ
        /// </summary>
        public const string DIALOG_GET_LIVE_MUSIC_MULTI = UIPartsPath + "/DialogGetLiveMusicMulti";

        /// <summary>
        /// 衣装を1つ獲得した時のダイアログ
        /// </summary>
        public const string DIALOG_GET_DRESS_SINGLE = UIPartsPath + "/DialogGetDressSingle";

        /// <summary>
        /// 衣装を複数獲得した時のダイアログ
        /// </summary>
        public const string DIALOG_GET_DRESS_MULTI = UIPartsPath + "/DialogGetDressMulti";

        /// <summary>
        /// ソートダイアログ
        /// </summary>
        public const string DIALOG_SORT = UIPartsPath + "/DialogSort";
        
        /// <summary>
        /// 絞り込みダイアログ
        /// </summary>
        public const string DIALOG_FILTER = UIPartsPath + "/DialogFilter";
        
        /// <summary>
        /// ソート&絞り込みダイアログ
        /// </summary>
        public const string DIALOG_SORT_AND_FILTER = UIPartsPath + "/DialogSortAndFilter";

        /// <summary>
        /// トロフィー詳細
        /// </summary>
        public const string DIALOG_TROPHY_INFO = UIPartsPath + "/TrophyRoom/DialogTrophyInfo";
        
        /// <summary>
        /// トロフィールームのキャラ勝利数表示用ダイアログ
        /// </summary>
        public const string DIALOG_TROPHY_ROOM_CHARA_WIN_NUM = UIPartsPath + "/TrophyRoom/DialogCharaTrophyWinNum";

        /// <summary>
        /// 演出一覧ダイアログ
        /// </summary>
        public const string DIALOG_CHARACTER_NOTE_ACT_MOVIEW_LIST_PATH = OUTGAME_UI_PATH + "/DialogCharacterNoteActMovieList";

        /// <summary>
        /// トロフィーのテクスチャを入れているフォルダのパス
        /// </summary>
        public const string TROPHY_TEXTURE_DIRECTORY_PATH = OUTGAME_ASSET_ROOT + "Trophy";

        /// <summary>
        //トロフィー獲得ダイアログのアニメーション（レース結果で使用）
        /// </summary>
        public const string TROPHY_ADD_DIALOG_ANIMATION_PATH = FLASH_COMBINE_ROOT + "Timeline/Common/tat_cmn_eff_get_trophy00";

        /// <summary>
        //トロフィーボタンのアニメーション（レース結果で使用）
        /// </summary>
        public const string TROPHY_BUTTON_ANIMATION_PATH = FLASH_COMBINE_ROOT + "Timeline/Common/tat_cmn_raceresult_trophy00";

        /// <summary>
        /// 各トロフィーのテクスチャパス
        /// </summary>
        public const string TROPHY_COURCE_ICON_PATH_BASE = TROPHY_TEXTURE_DIRECTORY_PATH + "/trophy_course_race_{0}";
        public const string TROPHY_RACE_ICON_PATH_BASE = TROPHY_TEXTURE_DIRECTORY_PATH + "/trophy_race_{0}";
        /// <summary>
        /// トロフィーのテクスチャ取得
        /// </summary>
        /// <param name="trophyCsvId">引数はRaceTrophy.Id （※RaceTrophy.TrophyIdではないので注意）</param>
        /// <returns></returns>
        public static string GetTrophyIconPath(int trophyCsvId)
        {
            var trophy = MasterDataManager.Instance.masterRaceTrophy.Get(trophyCsvId);
            if (trophy == null)
            {
                Debug.LogError("trophyがnullです");
                return string.Empty;
            }

            if (trophy.OriginalFlag == 1)
            {
                return TextUtil.Format(TROPHY_RACE_ICON_PATH_BASE, trophy.TrophyId);
            }
            else
            {
                var raceInstance = MasterDataManager.Instance.masterRaceInstance.Get(trophy.RaceInstanceId);
                if (trophy == null)
                {
                    Debug.LogError("raceInstanceがnullです");
                    return string.Empty;
                }
                var course = raceInstance.GetRaceCourseSetMaster();
                if (course == null)
                {
                    Debug.LogError("courceがnullです");
                    return string.Empty;
                }
                return TextUtil.Format(TROPHY_COURCE_ICON_PATH_BASE, course.RaceTrackId);
            }

        }

        public const string TROPHY_ROOM_FLOOR_TEXTURE_PATH = TROPHY_TEXTURE_DIRECTORY_PATH + "/Bg/trophy_bg_00";

        //保管室
        public const string DIALOG_WAITING_ROOM_CHANGE_COMPLETE = OUTGAME_UI_PATH + "/WaitingRoom/DialogWaitingRoomChangeComplete";
        public const string DIALOG_WAITING_ROOM_CHANGE_CONFIRM = OUTGAME_UI_PATH + "/WaitingRoom/DialogWaitingRoomChangeConfirm";
        //ピース保管室
        public const string DIALOG_CHARACTER_PIECE_EXCHANGE_COMPLETE = OUTGAME_UI_PATH + "/CharacterPieceExchange/DialogCharacterPieceExchangeComplete";
        public const string DIALOG_CHARACTER_PIECE_EXCHANGE_CONFIRM = OUTGAME_UI_PATH + "/CharacterPieceExchange/DialogCharacterPieceExchangeConfirm";

        /// <summary>
        /// キャラクター検索用汎用ダイアログ
        /// </summary>
        public const string DIALOG_CHARA_SEARCH = "UI/Parts/DialogCharaSearch";

        /// <summary>
        /// ウマ娘ノート：プロフィールダイアログ
        /// </summary>
        public const string DIALOG_CHARACTER_NOTE_PROFILE = OUTGAME_UI_PATH + "/DialogCharacterNoteProfile";

        /// <summary>
        /// ウマ娘ノート：アルバムダイアログ
        /// </summary>
        public const string DIALOG_CHARACTER_NOTE_ALBUM = OUTGAME_UI_PATH + "/DialogCharacterNoteAlbum";

        /// <summary>
        /// ウマ娘ノート : ボイスダイアログカード選択
        /// </summary>
        public const string DIALOG_CHARACTER_NOTE_VOICE_CARD = OUTGAME_UI_PATH + "/DialogCharacterNoteVoiceCard";

        /// <summary>
        /// ウマ娘ノート : ボイスダイアログ
        /// </summary>
        public const string DIALOG_CHARACTER_NOTE_VOICE = OUTGAME_UI_PATH + "/DialogCharacterNoteVoice";

        /// <summary>
        /// 一コマのサムネイル表示パーツ
        /// </summary>
        public const string PARTS_COMIC_THUMBNAIL = OUTGAME_UI_PATH + "/Topics/PartsComicThumbnail";
        /// <summary>
        /// 一コマ検索結果表示ダイアログ
        /// </summary>
        public const string DIALOG_COMIC_SEARCH_RESULT = OUTGAME_UI_PATH + "/Topics/DialogComicSearchResult";
        /// <summary>
        /// 一コマ一覧の3D環境設定ファイル
        /// </summary>
        public const string COMIC_LIST_ENV_PARAM = EnvParamRoot + "ComicList/ast_prm_comic_list_top";

        /// <summary>
        /// ウワサの表示パーツ
        /// </summary>
        public const string PARTS_GOSSIP_LIST_ITEM = OUTGAME_UI_PATH + "/Topics/PartsGossipListItem";
        /// <summary>
        /// ウワサ検索結果表示ダイアログ
        /// </summary>
        public const string DIALOG_GOSSIP_SEARCH_RESULT = OUTGAME_UI_PATH + "/Topics/DialogGossipSearchResult";
        /// <summary>
        /// ウワサ一覧の3D環境設定ファイル
        /// </summary>
        public const string GOSSIP_LIST_ENV_PARAM = EnvParamRoot + "GossipList/ast_prm_gossip_list_top";

        /// <summary>
        /// ウマ娘名鑑のトップの環境設定
        /// </summary>
        public const string DIRECTORY_TOP_ENV_PARAM = EnvParamRoot + "Directory/ast_prm_directory_top";

        /// <summary>
        /// ウマ娘名鑑の環境設定
        /// </summary>
        public const string DIRECTORY_ENV_PARAM = EnvParamRoot + "Directory/ast_prm_directory";

        /// <summary>
        /// ウマ娘名鑑報酬確認ダイアログ
        /// </summary>
        public const string DIALOG_CONFIRM_DIRECTORY_REWARD = OUTGAME_UI_PATH + "/Directory/DialogConfirmDirectoryReward";

        /// <summary>
        /// ウマ娘名鑑レベルアップ演出
        /// </summary>
        public const string CHARACTER_NOTE_LEVEL_UP = FLASH_COMBINE_ROOT + "Action/OutGame/fa_characternote_levelup00";
        public const string CHARACTER_NOTE_LEVEL_UP_A2U_PREFAB = FLASH_ROOT + "OutGame/prefab/pf_fl_characternote_levelup00";

        /// <summary>ウマ娘名鑑レベルアップ演出のフレーム</summary>        
        public const string CHARACTER_NOTE_LEVEL_UP_UNITYANIMATION_FRAME_04 = UI_UNITY_ANIMATION_ROOT + "AnimationFrame/pfb_cmn_animation_frame04";

        /// <summary>
        /// ウマ娘名鑑報酬一覧ダイアログ
        /// </summary>
        public const string DIALOG_DIRECTORY_REWARD_LIST = OUTGAME_UI_PATH + "/Directory/DialogDirectoryRewardList";

        /// <summary>
        /// ウマ娘名鑑歴代カードランキングダイアログ
        /// </summary>
        public const string DIALOG_DIRECTORY_CARD_RANKING = OUTGAME_UI_PATH + "/Directory/DialogDirectoryCardRanking";

        /// <summary>
        /// 才能開花カットインのパス
        /// </summary>
        public const string LIMITBREAK_CUTIN_PATH = "Cutt/CutIn/OutGame/outgame_limitbreak001/outgame_limitbreak001";

        /// <summary>
        /// ショップの環境設定
        /// </summary>
        public const string SHOP_ENV_PARAM = EnvParamRoot + "Shop/ast_prm_shop";

        /// <summary>
        /// ショップ交換確認ダイアログ(アイテム)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONFIRM_ITEM = SHOP_PARTS_ROOT + "DialogItemExchangeConfirmItem";

        /// <summary>
        /// ショップ交換完了確認ダイアログ(アイテム)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_COMPLETE_CONFIRM_ITEM = SHOP_PARTS_ROOT + "DialogItemExchangeCompleteConfirmItem";

        /// <summary>
        /// ショップ交換確認ダイアログ(カード)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONFIRM_CARD = SHOP_PARTS_ROOT + "DialogItemExchangeConfirmCard";

        /// <summary>
        /// ショップ交換確認ダイアログ(カード、おまけつき)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONFIRM_CARD_WITH_PIECE = SHOP_PARTS_ROOT + "DialogItemExchangeConfirmCardWithPiece";

        /// <summary>
        /// ショップ交換完了確認ダイアログ(カード)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_COMPLETE_CONFIRM_CARD = SHOP_PARTS_ROOT + "DialogItemExchangeCompleteConfirmCard";

        /// <summary>
        /// ショップ交換完了確認ダイアログ(カード、おまけつき)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_COMPLETE_CONFIRM_CARD_WITH_PIECE = SHOP_PARTS_ROOT + "DialogItemExchangeCompleteConfirmCardWithPiece";

        /// <summary>
        /// ショップ交換確認ダイアログ(フォロー枠)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONFIRM_FOLLOW = SHOP_PARTS_ROOT + "DialogItemExchangeConfirmFollow";

        /// <summary>
        /// ショップ交換完了確認ダイアログ(フォロー枠)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_COMPLETE_CONFIRM_FOLLOW = SHOP_PARTS_ROOT + "DialogItemExchangeCompleteConfirmFollow";

        /// <summary>
        /// ショップ交換確認ダイアログ(所持済みカード変換)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONVERSION_CONFIRM_CARD = SHOP_PARTS_ROOT + "DialogItemExchangeConversionConfirmCard";

        /// <summary>
        /// ショップ交換確認ダイアログ(所持済みカード変換、おまけつき)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONVERSION_WITH_PIECE_CONFIRM_CARD = SHOP_PARTS_ROOT + "DialogItemExchangeConversionWithPieceConfirmCard";

        /// <summary>
        /// ショップ交換完了確認ダイアログ(所持済みカード変換)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONVERSION_COMPLETE_CONFIRM_CARD = SHOP_PARTS_ROOT + "DialogItemExchangeConversionCompleteConfirmCard";

        /// <summary>
        /// ショップ交換完了確認ダイアログ(所持済みカード変換、おまけつき)
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_CONVERSION_WITH_PIECE_COMPLETE_CONFIRM_CARD = SHOP_PARTS_ROOT + "DialogItemExchangeConversionWithPieceCompleteConfirmCard";

        /// <summary>
        /// ショップ交換価格ダイアログ
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_PRICE_LIST = SHOP_PARTS_ROOT + "DialogItemExchangePriceList";

        /// <summary>
        /// ショップ交換ラインナップ解放リストダイアログ
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_SATISFY_CONDITION_LIST = SHOP_PARTS_ROOT + "DialogItemExchangeSatisfyConditionList";

        /// <summary>
        /// ショップ交換限定セールスオープンダイアログ
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_LIMITED_SALES_OPEN = SHOP_PARTS_ROOT + "DialogItemExchangeLimitedSalesOpen";

        /// <summary>
        /// ショップ交換限定セールス準備中ダイアログ
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_LIMITED_SALES_PREP = SHOP_PARTS_ROOT + "DialogItemExchangeLimitedSalesPrep";

        /// <summary>
        /// ショップ交換限定セールス閉じるダイアログ
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_LIMITED_SALES_CLOSE = SHOP_PARTS_ROOT + "DialogItemExchangeLimitedSalesClose";

        /// <summary>
        /// ショップ交換価格変更確認ダイアログ
        /// </summary>
        public const string DIALOG_ITEM_EXCHANGE_PRICE_CHANGE_CONFIRM = SHOP_PARTS_ROOT + "DialogItemExchangePriceChangeConfirm";

        public const string LIMITBREAK_MOTION_ROOT = "3d/Motion/OutGame/RankUp/";

        public const string LIMITBREAK_BODY_MOTION_TYPE_ROOT = LIMITBREAK_MOTION_ROOT + "Body/Type{0:00}/";
        public const string LIMITBREAK_FACIAL_MOTION_TYPE_ROOT = LIMITBREAK_MOTION_ROOT + "Facial/Type{0:00}/";
        public const string LIMITBREAK_POSITION_MOTION_TYPE_ROOT = LIMITBREAK_MOTION_ROOT + "Position/Type{0:00}/";
        public const string LIMITBREAK_CAMERA_MOTION_TYPE_ROOT = LIMITBREAK_MOTION_ROOT + "Camera/Type{0:00}/";
        public const string LIMITBREAK_EAR_MOTION_TYPE_ROOT = LIMITBREAK_MOTION_ROOT + "Ear/Type{0:00}/";

        private const string LIMITBREAK_BODY_MOTION_TYPE_FORMAT = LIMITBREAK_BODY_MOTION_TYPE_ROOT + "{1}";
        private const string LIMITBREAK_FACIAL_MOTION_TYPE_FORMAT = LIMITBREAK_FACIAL_MOTION_TYPE_ROOT + "{1}";
        private const string LIMITBREAK_POSITION_MOTION_TYPE_FORMAT = LIMITBREAK_POSITION_MOTION_TYPE_ROOT + "{1}";
        private const string LIMITBREAK_CAMERA_MOTION_TYPE_FORMAT = LIMITBREAK_CAMERA_MOTION_TYPE_ROOT + "{1}";
        private const string LIMITBREAK_EAR_MOTION_TYPE_FORMAT = LIMITBREAK_EAR_MOTION_TYPE_ROOT + "{1}";

        /// <summary>
        /// アウトゲーム : ランキングスコアフォント
        /// </summary>
        public const string OUTGAME_RANKING_SCORE_FONT_PATH = OUTGAME_ASSET_ROOT + "Font/RankingScoreFont";

        /// <summary>
        /// アウトゲーム ：複数タブ限定ミッションダイアログのプレハブ
        /// </summary>
        public const string DIALOG_LIMITED_MISSION_MULTI_TAB_PREFAB_PATH = OUTGAME_UI_PATH + "/DialogLimitedMissionMultiTab";


        /// <summary>
        /// ルールに基づいた名前の置き換えをしたモーションパスの取得
        /// </summary>
        /// <param name="motionKey"></param>
        /// <param name="motionType"></param>
        /// <param name="personalityId"></param>
        /// <param name="replaceIndex"></param>
        /// <param name="bodyClipIndex"></param>
        /// <returns></returns>
        public static string GetLimitBreakMotionPath(TimelineKeyCharacterMotionData motionKey, CutInHelper.MotionType motionType, int personalityId)
        {
            // 変更するモーションの元の名前を取得
            var originalClipName = GetCharaMotionName(motionKey, motionType, -1, 0);

            // 置き換えて返す
            return GetLimitBreakReplacedMotionPath(originalClipName, motionType, personalityId);
        }

        /// <summary>
        /// 元のモーション名からルールに基づいて置き換える
        /// </summary>
        /// <param name="clipName"></param>
        /// <param name="motionType"></param>
        /// <param name="personalityId"></param>
        /// <returns></returns>
        private static string GetLimitBreakReplacedMotionPath(string clipName, CutInHelper.MotionType motionType,
            int personalityId)
        {
            var replacedClipPath = ReplaceLimitBreakMotionPath(motionType, clipName, personalityId);

            // リソースが見つからなければ共通IDで探す
            return ResourceManager.IsExistAsset(replacedClipPath)
                ? replacedClipPath
                : ReplaceLimitBreakMotionPath(motionType, clipName, CutInHelper.COMMON_PERSONALITY_ID);
        }

        /// <summary>
        /// IDを差し替えた形でパスを返す
        /// </summary>
        /// <param name="motionType"></param>
        /// <param name="motionName"></param>
        /// <param name="personalityId"></param>
        /// <returns></returns>
        private static string ReplaceLimitBreakMotionPath(CutInHelper.MotionType motionType, string motionName, int personalityId)
        {
            var clipName = System.Text.RegularExpressions.Regex
                .Replace(motionName, "_type[0-9]{2}", TextUtil.Format("_type{0:D2}", personalityId));
            var format = GetLimitBreakMotionPathFormat(motionType);
            return TextUtil.Format(format, personalityId, clipName);
        }

        /// <summary>
        /// モーションパスのルートのフォーマットをモーションの種類ごとに返す
        /// </summary>
        /// <param name="motionType"></param>
        /// <returns></returns>
        private static string GetLimitBreakMotionPathFormat(CutInHelper.MotionType motionType)
        {
            switch (motionType)
            {
                case CutInHelper.MotionType.Body:
                    return LIMITBREAK_BODY_MOTION_TYPE_FORMAT;
                case CutInHelper.MotionType.Facial:
                    return LIMITBREAK_FACIAL_MOTION_TYPE_FORMAT;
                case CutInHelper.MotionType.Position:
                    return LIMITBREAK_POSITION_MOTION_TYPE_FORMAT;
                case CutInHelper.MotionType.Camera:
                    return LIMITBREAK_CAMERA_MOTION_TYPE_FORMAT;
                case CutInHelper.MotionType.Ear:
                    return LIMITBREAK_EAR_MOTION_TYPE_FORMAT;
                default:
                    throw new ArgumentOutOfRangeException(nameof(motionType), motionType, null);
            }
        }

        #endregion

        #region フレンド

        /// <summary>
        /// ID検索ダイアログパス
        /// </summary>
        public const string DIALOG_IDSEARCH_PATH = UIPartsPath + "/Friend/DialogIDSearch";

        /// <summary>
        /// フレンド
        /// </summary>
        public const string DIALOG_TRAINER_INFO_PATH = UIPartsPath + "/Friend/DialogTrainerInfo";

        /// <summary>
        /// トレーナー：ウマ娘名鑑ダイアログ
        /// </summary>
        public const string DIALOG_TRAINER_DIRECTORY_PATH = UIPartsPath + "/Friend/DialogTrainerDirectory";

        #endregion フレンド

        #region サークル、ミニ演出

        /// <summary>
        /// スタンプのパス取得
        /// </summary>
        public static string GetStampPath(int stampId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("OutGame/Stamp/{0}", stampId);
            return _stringBuilder.ToString();
        }

        private const string CIRCLE_ROOT = UIPartsPath + "/Circle/";
        public const string DIALOG_CIRCLE_JOIN_REQUEST_CONFIRM = CIRCLE_ROOT + "Unjoin/DialogCircleJoinRequestConfirm";
        public const string DIALOG_CIRCLE_APPROVE_SCOUT_CONFIRM = CIRCLE_ROOT + "DialogClubApproveScoutConfirm";
        public const string DIALOG_CIRCLE_MEMBER_LIST_PATH = CIRCLE_ROOT + "DialogCircleMemberList";
        public const string DIALOG_CIRCLE_DETAIL_PATH = CIRCLE_ROOT + "DialogCircleDetail";
        public const string DIALOG_CIRCLE_SETTING_PATH = CIRCLE_ROOT + "Joined/DialogCircleSetting";
        public const string DIALOG_CIRCLE_SEARCH_REFINE_PATH = CIRCLE_ROOT + "Unjoin/DialogCircleSearchRefine";
        public const string DIALOG_CIRCLE_ESTABLISH_PATH = CIRCLE_ROOT + "Unjoin/DialogCircleEstablish";
        public const string DIALOG_CIRCLE_ITEM_REQUEST_PATH = CIRCLE_ROOT + "DialogCircleItemRequest";
        public const string DIALOG_CIRCLE_ITEM_REQUEST_CONFIRM_PATH = CIRCLE_ROOT + "DialogCircleItemRequestConfirm";
        public const string DIALOG_CIRCLE_ITEM_REQUEST_DETAIL_PATH = CIRCLE_ROOT + "DialogCircleItemRequestDetail";
        public const string DIALOG_CIRCLE_ITEM_REQUEST_MULTI_CONFIRM_PATH = CIRCLE_ROOT + "DialogCircleItemRequestMultiConfirm";
        public const string DIALOG_CIRCLE_SHARE_ITEM_LIST = CIRCLE_ROOT + "DialogCircleShareItemList";
        public const string DIALOG_CIRCLE_DONATE_PATH = CIRCLE_ROOT + "DialogCircleItemDonate";
        public const string DIALOG_CIRCLE_DONATE_COMPLETE_PATH = CIRCLE_ROOT + "DialogCircleItemDonateComplete";
        public const string DIALOG_CIRCLE_PROFILE = CIRCLE_ROOT + "DialogCircleProfile";
        public const string CIRCLE_PROFILE_FULL_SCREEN = CIRCLE_ROOT + "CircleProfileImageFullScreen";
        public const string DIALOG_CIRCLE_PROFILE_EDIT = CIRCLE_ROOT + "DialogCircleProfileEdit";
        public const string DIALOG_CIRCLE_RANKING = CIRCLE_ROOT + "DialogCircleRanking";
        public const string DIALOG_CIRCLE_RANKING_RESULT = CIRCLE_ROOT + "DialogCircleRankingResult";
        public const string DIALOG_CIRCLE_MENU = CIRCLE_ROOT + "DialogCircleMenu";
        public const string DIALOG_CIRCLE_RANKING_REWARD_LIST = CIRCLE_ROOT + "DialogCircleRankingRewardList";
        public const string DIALOG_CIRCLE_RANKING_INFO = CIRCLE_ROOT + "DialogCircleRankingInfo";
        public const string DIALOG_CIRCLE_PROFILE_CHARA_SELECT = CIRCLE_ROOT + "DialogCircleProfileSelectChara";
        public const string DIALOG_CIRCLE_SHARE_PRACTICE_CHARACTER_CONFIRM = CIRCLE_ROOT + "DialogCircleSharePracticeCharacterConfirm";
        
        //ランキングアニメ
        public const string DIALOG_CIRCLE_RANKING_EMBLEM = UIANIMATION_ROOT+ "Flash/Circle/pf_fl_circle_rank_emblem00";
        public const string DIALOG_CIRCLE_RANKING_ICON = FLASH_COMBINE_ROOT + "Action/Circle/fa_circle_rank_icon00";
        public const string DIALOG_CIRCLE_RANKING_NUMBER = UIANIMATION_ROOT + "Flash/Circle/pf_fl_circle_rank_number00";
        public const string DIALOG_CIRCLE_RANKING_TEXT = UIANIMATION_ROOT + "Flash/Circle/pf_fl_circle_rank_text00";

        // ミニキャラ切り替えのwipe(1.16.0～)
        public const string CIRCLE_MINI_WIPE = FLASH_ROOT + "Circle/pf_fl_circle_bg_wipe00";

        //メンバー管理
        public const string DIALOG_CIRCLE_MEMBER_MANAGE = CIRCLE_ROOT + "Joined/DialogCircleMemberManage";

        //Grid
        public const string GRID_PATH = "MiniDirector/Grid/";
        public const string GRID_MESH_PATH = GRID_PATH + "Mesh/Quad";

        //位置ロケータのアニメータ
        public const string MINI_DIRECTOR_POSITION_ANIMATOR = "MiniDirector/Position/MiniPositionAnimator";

        //AlphaMask
        public const string CIRCLE_PROFILE_MASK_TEXTURE = ImageEffectRootPath + "AlphaMask/Texture/tex_circleprof_alphamask";

        public const string MINI_DIRECTOR_PATH = "MiniDirector/MiniDirector";

        /// <summary>
        /// 背景サムネイル取得
        /// </summary>
        /// <param name="bgId"></param>
        /// <param name="subId"></param>
        /// <returns></returns>
        public static string GetBgThumPath(int bgId, int subId)
        {
            const string PATH = "Bg/bg_thumb/bg_thumb_{0:D4}_{1:D5}";
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, bgId, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 桁数指定でランキング表示エフェクトのパス取得
        /// </summary>
        /// <param name="digit"></param>
        /// <returns></returns>
        public static string GetCircleRankingEffectPath(int digit)
        {
            const string PATH = UI_EFFECT_ROOT + "Circle/pfb_uieff_circle_rank_glitter_{0:D2}";
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, digit);
            return _stringBuilder.ToString();
        }

        #region 背景

        public const int MINI_DEFAULT_BG_ID = 10001;

        public const string MINI_BG_BASE_PATH = Root3d + "Env/Mini/";
        public const string MINI_ENV_PARAM_PATH = Root3d + "EnvParam/Mini/";
        public const string MINI_SKY_PATH = MINI_BG_BASE_PATH + "mini00000/Sky/pfb_env_mini00000_sky000_000";

        //ポジションデータ、0 = ファイル名(ディレクトリ含む)
        public const string MINI_BG_POSITION_PATH = Root3d + "Motion/Mini/{0}";

        //ミニタイムラインルート
        public const string MINI_TIMELINE_ROOT = Root3d + "Motion/Mini/{0}/Timeline/";

        //タイムラインデータ、0 =  シーン種、1= ファイル名
        public const string MINI_TIMELINE_PATH = MINI_TIMELINE_ROOT + "{1}";

        //設定が無い場合のデフォルト値
        public const string MINI_ENV_PARAM_DEFAULT = "MiniDirector/Params/DefaultEnvParam";
        public const string MINI_IMAGE_EFFECT_PARAM_DEFAULT = "MiniDirector/Params/DefaultImageEffectParam";

        /// <summary>
        /// ミニ環境設定ファイルのパス取得
        /// </summary>
        /// <param name="baseModelId"></param>
        /// <param name="eventId"></param>
        /// <param name="envId"></param>
        /// <returns></returns>
        public static string GetMiniEnvParamPath(int baseModelId, int eventId, int envId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_ENV_PARAM_PATH + "ast_prm_mini_{0:D4}_{1:D5}_{2:D3}", baseModelId, eventId, envId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミニポストエフェクト設定の取得
        /// </summary>
        /// <param name="baseModelId"></param>
        /// <param name="eventId"></param>
        /// <param name="envId"></param>
        /// <returns></returns>
        public static string GetMiniImageEffectParamPath(int baseModelId, int eventId, int envId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_ENV_PARAM_PATH + "ast_prm_mini_{0:D4}_{1:D5}_{2:D3}_imageeffect", baseModelId, eventId, envId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 背景モデル(メイン)パス取得
        /// </summary>
        /// <param name="baseModelId"></param>
        /// <returns></returns>
        public static string GetMiniBgMainPath(int baseModelId)
        {
            //イベント差分とか用の予備ID
            var subId = 0;
            var subId2 = 0;

            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_BG_BASE_PATH + "mini{0:D5}/Main/pfb_env_mini{0:D5}_main{1:D3}_{2:D3}", baseModelId, subId, subId2);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミニ背景の読み替え用テクスチャパス
        /// </summary>
        /// <returns></returns>
        public static string GetMiniBgTexturePath(int baseModelId, string textureName)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_BG_BASE_PATH + "mini{0:D5}/Main/Textures/{1}", baseModelId, textureName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミニ用天球の読み替え用テクスチャパス
        /// </summary>
        /// <returns></returns>
        public static string GetMiniSkyTexturePath(string textureName)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_BG_BASE_PATH + "mini00000/Sky/Textures/{0}", textureName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミニ背景のポジションロケータパス
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static string GetMiniBgPositionPath(string fileName)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_BG_POSITION_PATH, fileName);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ミニのタイムラインパス取得
        /// </summary>
        /// <param name="animInfo"></param>
        /// <returns></returns>
        public static string GetMiniTimelinePath(MiniBgObjectAnimInfo animInfo)
        {
            if (animInfo == null || animInfo.MasterBg == null)
                return null;

            return GetMiniTimelinePath(animInfo.Timeline, animInfo.MasterBg.GetSceneType());
        }

        public static string GetMiniTimelinePath(string fileName, MiniDirectorDefines.SceneType sceneType)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(MINI_TIMELINE_PATH, MiniDirectorDefines.GetSceneTypeStr(sceneType), fileName);
            return _stringBuilder.ToString();
        }

        #endregion

        #region キャラ影

        public const string MINI_CHARA_SHADOW = EffectRoot + "Chara/pfb_eff_chr_shadow_10";

        #endregion

        #endregion

        #region バレンタイン

        public const string FLASH_ACTION_VALENTINE_ROOT = FLASH_COMBINE_ROOT + "Action/Campaign/Valentine/";
        public const string FLASH_ACTION_VALENTINE_TITLE = FLASH_ACTION_VALENTINE_ROOT + "fa_cnp_valentine_title00";
        public const string FLASH_ACTION_VALENTINE_GETINFO = FLASH_ACTION_VALENTINE_ROOT + "fa_cnp_valentine_getitem_info00";
        public const string FLASH_EFFECT_VALENTINE_PARTICLE = UI_EFFECT_ROOT + "Campaign/Valentine/pfb_uieff_cnp_valentine_particle";

        #endregion

        #region ログインボーナス

        public const string FLASH_LOGIN_BONUS_ROOT = FLASH_ROOT + "LoginBonus/";
        public const string FLASH_ACTION_LOGIN_BONUS_ROOT = FLASH_COMBINE_ROOT + "Action/LoginBonus/";
        public const string FLASH_ACTION_NORMAL_LOGIN_BONUS_TITLE = FLASH_ACTION_LOGIN_BONUS_ROOT + "fa_login_normal_title00";
        public const string FLASH_ACTION_NORMAL_LOGIN_BONUS_GETINFO = FLASH_ACTION_LOGIN_BONUS_ROOT + "fa_login_getitem_info00";
        public const string FLASH_ACTION_NORMAL_LOGIN_BONUS_GETINFO_01 = FLASH_ACTION_LOGIN_BONUS_ROOT + "fa_login_getitem_info01";
        public const string FLASH_ACTION_LOGIN_BONUS_TRANSITION = FLASH_ACTION_LOGIN_BONUS_ROOT + "fa_login_transition00";
        private const string FLASH_LOGIN_BONUS_ITEM_LIST_FORMAT = FLASH_LOGIN_BONUS_ROOT + "pf_fl_login_item_list{0:D2}";
        private const string FLASH_SPECIAL_LOGIN_BONUS_LOGO_FORMAT = FLASH_ACTION_LOGIN_BONUS_ROOT + "Logo/fa_login_logo_{0:D5}";
        public const string LOGIN_BONUS_ROOT = "LoginBonus/";
        private const string LOGIN_BONUS_BG_FORMAT = LOGIN_BONUS_ROOT + "Bg/loginbonus_vertical_bg_{0:D5}";
        private const string LOGIN_BONUS_STAMP_FORMAT = LOGIN_BONUS_ROOT + "Stamp/utx_ico_stamp_get_{0:D4}";

        /// <summary>
        /// ログインボーナス：アイテム一覧用のFlashActionPlayerのパスを取得
        /// </summary>
        /// <param name="itemNum"></param>
        /// <returns></returns>
        public static string GetLoginBonusItemListFlashActionPath(int itemNum)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FLASH_LOGIN_BONUS_ITEM_LIST_FORMAT, itemNum);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 特別ログインボーナス：ロゴ用のFlashActionPlayerのパスを取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetSpecialLoginBonusLogoFlashActionPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FLASH_SPECIAL_LOGIN_BONUS_LOGO_FORMAT, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 特別ログインボーナス：背景画像パスを取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetSpecialLoginBonusBgPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(LOGIN_BONUS_BG_FORMAT, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 特別ログインボーナス：スタンプ画像パスを取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetSpecialLoginBonusStampPath(int id)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(LOGIN_BONUS_STAMP_FORMAT, id); 
            return _stringBuilder.ToString();
        }

        #endregion

        #region ImageEffect

        /// <summary>
        /// イメージエフェクト用ノイズテクスチャ
        /// </summary>
        public const string IMAGEEFFECT_TEXTURE_NOISE = "ImageEffect/Fluctuation/noise";

        #endregion ImageEffect

        #region Live

        public const string LiveSettingsAssetBundlePath = "liveSettings";
        public const string LiveSettingsAssetPath = "Live/Settings/{0:D4}";

        public static string GetLiveSettingsPath(int musicId)
        {
            return string.Format(LiveSettingsAssetPath, musicId);
        }

        #endregion

        #region LiveTheater

        public const string VOICE_ICON_PATH = UIPartsPath + "/LiveTheater/VoiceIcon";

        /// <summary>
        /// ムービーのロード用パスをダウンロード用のパスに変換する
        /// （そもそもパスを2個持たないといけないのも無駄な気がするが）
        /// </summary>
        /// <returns></returns>
        public static string MovieLoadPathToDownloadPath(string loadPath)
        {
            return MovieFolderPath + loadPath + Cute.Cri.MovieManager.USM_EXTENSION;
        }

        /// <summary>
        /// ライブ、サンプル動画。
        /// </summary>
        private const string LIVE_SAMPLE_PATH = "Live/{0}/mov_live_{0}";
        public static string GetLiveSampleMoviePath(int liveId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(LIVE_SAMPLE_PATH, liveId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 動画サムネパス
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetLiveThumbnailPath(int musicId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("Live/Theater/{0:D4}/tex_live_thumb_{0:D4}", musicId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 動画背景パス
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetLiveMusicBgPath(int musicId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("Live/Theater/{0:D4}/tex_live_music_bg_{0:D4}", musicId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// フォーメーションプレハブのパス
        /// </summary>
        /// <param name="musicId"></param>
        /// <param name="formationType"></param>
        /// <returns></returns>
        public static string GetLiveFormationPath(int musicId, LiveTheaterInfo.FormationType formationType)
        {
            _stringBuilder.Length = 0;
            var suffix = "";
            switch (formationType)
            {
                case LiveTheaterInfo.FormationType.All: suffix = "all"; break;
                case LiveTheaterInfo.FormationType.Main: suffix = "main"; break;
            }
            _stringBuilder.AppendFormat("Live/Theater/{0:D4}/pfb_live_formation_{0:D4}_{1}", musicId, suffix);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ライブシアター：キャラ選択
        /// </summary>
        public const string DIALOG_LIVE_THEATER_CHARA_SELECT = UIPartsPath + "/LiveTheater/Dialog/DialogLiveTheaterCharaSelect";

        /// <summary>
        /// ライブシアター：衣装選択
        /// </summary>
        public const string DIALOG_LIVE_THEATER_DRESS_SELECT = UIPartsPath + "/LiveTheater/Dialog/DialogLiveTheaterDressSelect";

        /// <summary>
        /// ライブシアター：環境設定
        /// </summary>
        public const string DIALOG_LIVE_THEATER_ENV_SETTING = UIPartsPath + "/LiveTheater/Dialog/DialogLiveEnvironmentSetting";

        /// <summary>
        /// ライブシアター：楽曲詳細
        /// </summary>
        public const string DIALOG_LIVE_THEATER_MUSIC_DETAIL = UIPartsPath + "/LiveTheater/Dialog/DialogLiveTheaterMusicDetail";

        /// <summary>
        /// ライブシアター：歌唱一覧
        /// </summary>
        public const string DIALOG_LIVE_THEATER_SING_CHARA_LIST = UIPartsPath + "/LiveTheater/Dialog/DialogLiveTheaterSingCharaList";

        /// <summary>
        /// ライブシアター：お任せ配置
        /// </summary>
        public const string DIALOG_LIVE_THEATER_AUTO_SETTING = UIPartsPath + "/LiveTheater/Dialog/DialogLiveTheaterAutoSetting";

        /// <summary>
        /// ライブシアター：インデックス表示
        /// </summary>
        public const string LIVE_THEATER_INDEX_UI = UIPartsPath + "/LiveTheater/Index";

        #endregion

        #region Guide

        public const string GUIDE_ROOT = "Guide/";
        public const string GUIDE_SCREEN_ORIENTATION_ROOT = GUIDE_ROOT + "ScreenOrientation/";
        public const string GUIDE_TUTORIAL_ROOT = GUIDE_ROOT + "Tutorial/";
        public const string GUIDE_ORIENTATION_ICON_PORTRAIT = GUIDE_SCREEN_ORIENTATION_ROOT + "utx_ico_loading_rotation_00";
        public const string GUIDE_ORIENTATION_ICON_LANDSCAPE = GUIDE_SCREEN_ORIENTATION_ROOT + "utx_ico_loading_rotation_01";

        /// <summary>
        /// シングルモードイベントタイトルサポートカードレアリティパス取得
        /// </summary>
        public static string GetTutorialGuideImagePath(int tutorialId, int index)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(GUIDE_TUTORIAL_ROOT + "tutorial_guide_l_{0:D5}_{1:D2}", tutorialId, index).ToString();
        }
        #endregion
        
        #region クレーンゲーム

        public const string CRANE_BG_PATH = Root3d + "Env/Set/set20006/Main/pfb_env_set20006_main000_000";
        public const string CRANE_BG_DALL_PATH = Root3d + "Env/Set/set20006/Prop/pfb_env_set20006_prop{0:D3}_{1:D3}";
        public const string CRANE_ARM_PATH = Root3d + "Env/Set/set20006/Prop/pfb_env_set20006_prop000_000";
        public const string CRANE_GAME_DEFINE_ASSET = "MiniGame/mng_0001/Param/ast_param_crane_defines_00";
        private const string CRANE_GAME_PRIZE_POSITION = "MiniGame/mng_0001/Param/ast_param_crane_prize_position_{0}_{1:D2}";
        public const string CRANE_GAME_CRANE_ANIMATOR = "MiniGame/mng_0001/Animator/CraneAnimator";
        public const string CRANE_GAME_CAMERA_ANIMATOR = "MiniGame/mng_0001/Animator/CameraAnimator";
        private const string CRANE_GAME_PROP_COLLISION_BASE_PATH = "MiniGame/mng_0001/Prefabs/PropCollision{0:D3}";
        private const string CRANE_GAME_MOTION_ROOT = Root3d + "Motion/Minigame/mng_0001/{0}/Type00/";
        private const string CRANE_GAME_CRANE_MOTION_ROOT = Root3d + "Motion/Minigame/mng_0001/BG/Type00/";
        private const string CRANE_GAME_CAMERA_MOTION_ROOT = Root3d + "Motion/Minigame/mng_0001/Camera/Type00/";
        public const string CRANE_GAME_ARM_COLLISION_L_PATH = "MiniGame/mng_0001/Prefabs/ArmCollisionL";
        public const string CRANE_GAME_ARM_COLLISION_R_PATH = "MiniGame/mng_0001/Prefabs/ArmCollisionR";
        public const string CRANE_GAME_JOINT_OBJECT_001 = "MiniGame/mng_0001/Prefabs/JointObject001";
        public const string CRANE_GAME_JOINT_OBJECT_002 = "MiniGame/mng_0001/Prefabs/JointObject002";
        public const string CRANE_GAME_JOINT_ROOT = "MiniGame/mng_0001/Prefabs/JointRoot";
        public const string CRANE_GAME_MODEL_COLLISION = "MiniGame/mng_0001/Prefabs/ModelCollision";
        public const string CRANE_GAME_WALL_COLLISION = "MiniGame/mng_0001/Prefabs/Wall";

        //imageeffect
        public const string CRANE_GAME_IMAGE_EFFECT_PATH = "MiniGame/mng_0001/Param/ast_param_mng0001_imageeffect";

        //クレーンのモーションパス
        private const string CRANE_GAME_CRANE_MOTION_CATCH = CRANE_GAME_CRANE_MOTION_ROOT + "anm_mng_0001_type00_catch{0:D2}_suc01_bg";
        private const string CRANE_GAME_CRANE_MOTION_FALL = CRANE_GAME_CRANE_MOTION_ROOT + "anm_mng_0001_type00_fall{0:D2}_suc01_plus_bg";
        private const string CRANE_GAME_CRANE_MOTION_BAD = CRANE_GAME_CRANE_MOTION_ROOT + "anm_mng_0001_type00_catch{0:D2}_bad01_plus_bg";
        public const string CRANE_GAME_CRANE_MOTION_DEFAULT = CRANE_GAME_CRANE_MOTION_ROOT + "anm_mng_0001_type00_catch00_suc00_bg";
        private const string CRANE_GAME_CRANE_MOTION_SLIP = CRANE_GAME_CRANE_MOTION_ROOT + "anm_mng_0001_type00_slip{0:D2}_plus_bg";
        public const string CRANE_GAME_CRANE_MOTION_SHAKE01 = CRANE_GAME_CRANE_MOTION_ROOT + "anm_mng_0001_type00_shake01_plus_bg";

        //カメラのパス
        public const string CRANE_GAME_CAMERA_CATCH_MOTION = CRANE_GAME_CAMERA_MOTION_ROOT + "anm_mng_0001_type00_catch01_suc01_cam";
        public const string CRANE_GAME_CAMERA_FALL_MOTION = CRANE_GAME_CAMERA_MOTION_ROOT + "anm_mng_0001_type00_fall01_suc01_cam";

        //フラッシュ
        private const string MINI_GAME_FLASH_ROOT = FLASH_ROOT + "MiniGame/";
        private const string MINI_GAME_FLASH_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/MiniGame/";
        private const string CRANE_GAME_FLASH_ROOT = MINI_GAME_FLASH_ROOT + "mng_0001/";
        private const string CRANE_GAME_FLASH_ACTION_ROOT = MINI_GAME_FLASH_ACTION_ROOT + "mng_0001/";
        public const string CRANE_GAME_UIANIM_CHALLENGE = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_challenge00";
        public const string CRANE_GAME_UIANIM_FINISH = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_finish00";
        public const string CRANE_GAME_RESULT_00 = CRANE_GAME_FLASH_ACTION_ROOT + "fa_mng_txt_result00";    //ストーリー用
        public const string CRANE_GAME_RESULT_01 = CRANE_GAME_FLASH_ACTION_ROOT + "fa_mng_txt_result01";    //クレーンゲーム用
        public const string CRANE_GAME_UIANIM_GET = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_get00";
        public const string CRANE_GAME_UIANIM_START = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_start00";
        public const string CRANE_GAME_UIANIM_BUTTON = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_playbtn00";
        public const string CRANE_GAME_UIANIM_CREDIT = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_credit00";
        public const string CRANE_GAME_UIANIM_DIALOG_GET = CRANE_GAME_FLASH_ACTION_ROOT + "fa_mng_dialog_get01";
        public const string CRANE_GAME_UIANIM_SPEEDUP = CRANE_GAME_FLASH_ROOT + "pf_fl_mng_speed_up00";

        //アイコンベイク用
        public const string CRANE_GAME_ICON_BAKE_MAT = "MiniGame/mng_0001/Material/BakeIcon";
        public const string CRANE_GAME_ICON_BASE_TEX_00 = "MiniGame/mng_0001/Icon/bg_item_icon_00000";
        public const string CRANE_GAME_ICON_FRAME_TEX_00 = "MiniGame/mng_0001/Icon/frm_item_icon_00000";
        public const string CRANE_GAME_ICON_FRAME_TEX_01 = "MiniGame/mng_0001/Icon/frm_item_icon_00001";

        //エフェクト
        public const string CRANE_GAME_EFFECT_PRIZE = EffectRoot + "MiniGame/pfb_eff_Minigame_02_001";
        public const string CRANE_GAME_EFFECT_PRIZE_RARE = EffectRoot + "MiniGame/pfb_eff_Minigame_02_002";

        //小物アニメータ
        public const string CRANE_GAME_RESULT_PROP_ANIMATOR = "MiniGame/mng_0001/Animator/ResultPropAnimator";

        /// <summary>
        /// 配置パターンアセット取得
        /// </summary>
        /// <param name="posType"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetCraneGamePosPatternPath(CraneGameDefines.PrizePosType posType, int id)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_GAME_PRIZE_POSITION, posType.PrizePosTypePath(), (int)id).ToString();
        }

        /// <summary>
        /// アームが滑るモーションのパス
        /// </summary>
        /// <param name="step"></param>
        /// <returns></returns>
        public static string GetCraneGameCraneSlipMotionPath(int step)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_GAME_CRANE_MOTION_SLIP, step).ToString();
        }

        /// <summary>
        /// 獲得
        /// </summary>
        /// <param name="animtionId"></param>
        /// <returns></returns>
        public static string GetCraneGameCatchMotionPath(int animtionId)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_GAME_CRANE_MOTION_CATCH, animtionId).ToString();
        }

        /// <summary>
        /// 開く
        /// </summary>
        /// <param name="animtionId"></param>
        /// <returns></returns>
        public static string GetCraneGameFallMotionPath(int animtionId)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_GAME_CRANE_MOTION_FALL, animtionId).ToString();
        }

        /// <summary>
        /// 落ちた
        /// </summary>
        /// <param name="animtionId"></param>
        /// <returns></returns>
        public static string GetCraneGameBadMotionPath(int animtionId)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_GAME_CRANE_MOTION_BAD, animtionId).ToString();
        }

        /// <summary>
        /// 小物のコリジョンパス
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string GetCraneGamePropCollisionPath(int type)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_GAME_PROP_COLLISION_BASE_PATH, type).ToString();
        }

        /// <summary>
        /// 背景小物パス
        /// </summary>
        /// <param name="type"></param>
        /// <param name="typeSub"></param>
        /// <returns></returns>
        public static string GetCraneGameBgPropPath(int type, int typeSub)
        {
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(CRANE_BG_DALL_PATH, type, typeSub).ToString();
        }

        /// <summary>
        /// クレーンゲーム：カメラ位置取得
        /// </summary>
        /// <param name="result"></param>
        /// <returns></returns>
        public static string GetCraneGameCameraMotionPath(CraneGameDefines.CatchResult result)
        {
            const string PATH = CRANE_GAME_MOTION_ROOT + "anm_mng_0001_type00_catch01_{1}_cam";
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(PATH, "Camera", result.MotionSuffix()).ToString();
        }

        /// <summary>
        /// 景品配置パターンプレハブの取得
        /// </summary>
        /// <returns></returns>
        public static string GetCraneGamePositionPath(int animationId, int typeId)
        {
            const string PATH = Root3d + "Motion/Minigame/mng_0001/Position/pfb_pos_mng0001_{0:D2}_{1:D2}_pos";
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(PATH, animationId, typeId).ToString();
        }

        /// <summary>
        /// 獲得配置パターンプレハブの取得
        /// </summary>
        /// <param name="num">景品個数</param>
        /// <param name="pattern">パターン数、１～３</param>
        /// <param name="typeId">通常は１、巨大や逆さでバリエーションつけるなら２以降</param>
        /// <returns></returns>
        public static string GetCraneGameGetPositionPath(int num, int pattern, int typeId = 1)
        {
            const string PATH = Root3d + "Motion/Minigame/mng_0001/Position/pfb_pos_mng0001_{0:D2}_{1:D2}_{2:D2}_get";
            _stringBuilder.Clear();
            return _stringBuilder.AppendFormat(PATH, num, typeId, pattern).ToString();
        }

        /// <summary>
        /// リザルトの小物アニメ
        /// </summary>
        /// <param name="label">doll01~04</param>
        /// <returns></returns>
        public static string GetCraneGameResultPropMotion(string label)
        {
            _stringBuilder.Clear();
            return _stringBuilder.Append(EVENT_MOTION_PATH_ROOT + 
                $"Prop/Type00/anm_eve_type00_{label}_prop")
                .ToString();
        }

        #endregion

        #region チャンピオンズミーティング

        private const string CHAMPIONS_PATH_ROOT = "Race/Champions/";
        private const string CHAMPIONS_FLASH_ROOT = FLASH_ROOT + "Champions/";
        private const string CHAMPIONS_FLASH_COMBINE_ROOT = FLASH_COMBINE_ROOT + "Action/Champions/";
        public const string CHAMPIONS_VERSUS_ENV_PATH = EnvParamRoot + "Champions/ast_prm_champions_versus";
        public const string CHAMPIONS_MATCHING_PREFAB_PATH = FLASH_COMBINE_ROOT + "Timeline/Champions/tat_champions_txt_matching00";
        
        public const string CHAMPIONS_UI_PATH = UIPartsPath + "/Champions";

        /// <summary>
        /// Twinle増刊号ダイアログ
        /// </summary>
        public const string DIALOG_CHAMPIONS_NEWS_PATH = CHAMPIONS_UI_PATH + "/Dialog/DialogChampionsNews";
        // Twinle号外ダイアログ
        public const string DIALOG_CHAMPIONS_EXTRA_NEWS_PATH = CHAMPIONS_UI_PATH + "/Dialog/DialogChampionsExtraNews";
        
        /// <summary>
        /// ウマ娘詳細ダイアログ：チャンピオンズミーティングデータ入り
        /// </summary>
        public const string DIALOG_CHAMPION_TRAINED_CHARACTER_DETAIL_PATH = CHAMPIONS_UI_PATH + "/Dialog/DialogTrainedCharacterChampionsDetail";

        //勝利数表示
        public const string CHAMPIONS_FA_WIN_NUM_PATH = CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_txt_wincount00";
        //最終順位表示
        public const string CHAMPIONS_FA_FINAL_RANK_PATH = CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_final_rank00";
        //トップ紙吹雪
        public const string CHAMPIONS_TOP_PARTICLE_PATH = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_top_paper00";
        //報酬獲得ボタン、TAT
        public const string CHAMPIONS_LOBBY_REWARD_GET_TAT = FLASH_COMIBINE_TIMELINE_ROOT + "Champions/tat_champions_btn_get00";
        //報酬獲得ボタン、パーティクル
        public const string CHAMPIONS_LOBBY_REWARD_GET_EFFECT = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_btn_get_glitter_00";
        //報酬獲得ボックス、パーティクル
        public const string CHAMPIONS_LOBBY_REWARD_BOX_EFFECT = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_get_box_glitter_00";
        //GET文字
        public const string CHAMPIONS_LOBBY_GET_FLASH = COMMON_FLASH_ROOT + "pf_fl_cmn_txt_get00";

        //報酬アニメーションのパーティクル
        public const string CHAMPIONS_REWARD_PARTICLE1_PATH = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_presentbox_glitter_01";
        public const string CHAMPIONS_REWARD_PARTICLE2_PATH = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_presentbox_glitter_02";
        public const string CHAMPIONS_REWARD_PARTICLE3_PATH = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_presentbox_glitter_03";
        
        //報酬アニメーション
        public const string CHAMPIONS_FA_REWARD_PATH = CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_presentbox00";
        /// <summary> ラウンド報酬テキストFlashパス </summary>
        public const string CHAMPIONS_ROUND_REWARD_TEXT_ACTION_FLASH_PATH = CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_txt_round_reward00";

        //表示スタンプ、TAT
        public const string CHAMPIONS_SELECT_STAMP_TAT = FLASH_COMIBINE_TIMELINE_ROOT + "Champions/tat_champions_select_stamp00";
        
        /// <summary>
        /// チャンピオンズミーティング：キャラ表示パラメータ
        /// </summary>
        /// <param name="viewerType"></param>
        /// <returns></returns>
        public static string GetChampionsTeamViewerParamPath(ChampionsDefines.TeamViewerType viewerType)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_PATH_ROOT + "Params/ast_param_position_champions_{0}", viewerType.GetPathSuffix());
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：ポストエフェクトパラメータ
        /// </summary>
        /// <param name="viewerType"></param>
        /// <returns></returns>
        public static string GetChampionsImageEffectPath(ChampionsDefines.TeamViewerType viewerType)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_PATH_ROOT + "Params/ast_param_imageffect_champions_{0}", viewerType.GetPathSuffix());
            return _stringBuilder.ToString();
        }

        //ジュエル消費確認ダイアログ
        public const string DIALOG_CHAMPIONS_ENTRY_CONFIRM_JEWEL = UIPartsPath + "/Champions/Dialog/DialogChampionsEntryConfirmJewel";
        //チケット消費確認ダイアログ
        public const string DIALOG_CHAMPIONS_ENTRY_CONFIRM_TICKET = UIPartsPath + "/Champions/Dialog/DialogChampionsEntryConfirmTicket";
        //出走登録確認画面
        public const string DIALOG_CHAMPIONS_CONFIRM_CHARA_ENTRY = UIPartsPath + "/Champions/Dialog/DialogChampionsConfirmCharaEntry";
        //獲得報酬一覧
        public const string DIALOG_CHAMPIONS_REWARD_INFO = UIPartsPath + "/Champions/Dialog/DialogChampionsRewardInfo";
        //獲得報酬フラッシュ入りダイアログ
        public const string DIALOG_CHAMPIONS_REWARD_ITEM = UIPartsPath + "/Champions/Dialog/DialogChampionsRewardItem";
        //レース情報画面
        public const string DIALOG_CHAMPIONS_RACE_RESULT_INFO = UIPartsPath + "/Champions/Dialog/DialogChampionsRaceResultInfo";
        //レース結果詳細
        public const string DIALOG_CHAMPIONS_RACE_RESULT_DETAIL = UIPartsPath + "/Champions/Dialog/DialogChampionsRaceResultDetail";
        //ランキング
        public const string DIALOG_CHAMPIONS_RANKING = UIPartsPath + "/Champions/Dialog/DialogChampionsRanking";
        // チャンピオンズミーティング：レース情報ダイアログ
        public const string DIALOG_CHAMPIONS_RACE_INFO = UIPartsPath + "/Champions/Dialog/DialogChampionsRaceInfo";
        // チャンピオンズミーティング：ラウンド進出ダイアログ
        public const string DIALOG_CHAMPIONS_ROUND_ADVANCE = UIPartsPath + "/Champions/Dialog/DialogChampionsRoundAdvance";
        // チャンピオンズミーティング：決勝順位ダイアログ
        public const string DIALOG_CHAMPIONS_FINAL_RANKING = UIPartsPath + "/Champions/Dialog/DialogChampionsFinalRanking";
        //チャンピオンズミーティング：リーグ選択
        public const string DIALOG_CHAMPIONS_LEAGUE_SELECT = UIPartsPath + "/Champions/Dialog/DialogChampionsLeagueSelect";
        //チャンピオンズミーティング：リーグ選択確認
        public const string DIALOG_CHAMPIONS_LEAGUE_SELECT_CONFIRM = UIPartsPath + "/Champions/Dialog/DialogChampionsLeagueSelectConfirm";
        //チャンピオンズミーティング：ラウンド開始演出
        private const string CHAMPIONS_ROUND_START_FLASH = FLASH_COMBINE_ROOT + "Action/Champions/fa_champions_notice_{0}_00";
        public const string DIALOG_CHAMPIONS_ROUND_START = UIPartsPath + "/Champions/Dialog/DialogChampionsRoundStart";

        /// <summary>
        /// ラウンド開始演出
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        public static string GetChampionsRoundStartFlashPath(ChampionsDefines.Round round)
        {
            var roundStr = "";
            switch (round)
            {
                case ChampionsDefines.Round.Round1: roundStr = "round01"; break;
                case ChampionsDefines.Round.Round2: roundStr = "round02"; break;
                case ChampionsDefines.Round.Final: roundStr = "round_final"; break;
            }

            if (roundStr == "")
                return null;

            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_ROUND_START_FLASH, roundStr);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：VS画面（マッチング）のFlashファイルパス
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsVersusMatchingFlashPath(int index = 0)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_matching_comp{0:D2}", index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：VS画面（リザルト）のFlashファイルパス
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsVersusResultFlashPath(int index = 0)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_raceresult{0:D2}", index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：VS画面のトレーナー情報のFlashファイルパス
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsVersusTrainerFlashPath(int index = 0)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_FLASH_ROOT + "pf_fl_champions_matching_name{0:D2}", index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：VS画面のラウンド進出演出のFlashファイルパス
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsVersusRoundAdvanceFlashPath(int index = 0)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_result_roundadvance{0:D2}", index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：VS画面のランキング演出のFlashファイルパス
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsVersusFinalRankingFlashPath(int index = 0)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_result_finalranking{0:D2}", index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：順位表示のFlashファイルパス
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsResultRankFlashPath(int index = 0)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_FLASH_COMBINE_ROOT + "fa_champions_result_rank{0:D2}", index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：ホーム用ボタンのパス
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetChampionsHomeButtonPath(int id)
        {
            const string PATH = HOME_ROOT + "UI/Texture/Champions/utx_btn_home_race_champions_{0:D2}";
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：ラウンド表記
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetChampionsHomeButtonRoundPath(ChampionsDefines.Round round, ChampionsDefines.FinalState finalState)
        {
            const string PATH = HOME_ROOT + "UI/Texture/Champions/utx_txt_home_race_round_{0:D2}";
            var resourceId = 0;
            switch (round)
            {
                case ChampionsDefines.Round.LeagueSelect: resourceId = 5; break;
                case ChampionsDefines.Round.Round1: resourceId = 0; break;
                case ChampionsDefines.Round.Round2: resourceId = 1; break;
                case ChampionsDefines.Round.Final:
                    switch (finalState)
                    {
                        case ChampionsDefines.FinalState.Entry: resourceId = 2; break;
                        case ChampionsDefines.FinalState.Matching: resourceId = 3; break;
                        case ChampionsDefines.FinalState.Race: resourceId = 4; break;
                        default:
                            Debug.LogWarningFormat("未実装のFinalStateです。(FinalState={0})", finalState);
                            return string.Empty;
                    }
                    break;
                default:
                    Debug.LogWarningFormat("未実装のRoundです。(Round={0})", round);
                    return string.Empty;
            }
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, resourceId);
            return _stringBuilder.ToString();
        }

        private const string CHAMPIONS_TEXTURE_ROOT = "Race/Champions/Texture/";
        private const string CHAMPIONS_LOGO_BASE = CHAMPIONS_TEXTURE_ROOT + "Logo/utx_bg_champions_logo_{0:D2}";
        private const string CHAMPIONS_LOGO_BASE_S = CHAMPIONS_TEXTURE_ROOT + "Logo/utx_bg_champions_logo_{0:D2}_s";
        public const string CHAMPIONS_NEWS_BG = CHAMPIONS_TEXTURE_ROOT + "Twinkle/tex_twinkle_heading_00";
        public const string CHAMPIONS_NEWS_CHARACTER_BG = CHAMPIONS_TEXTURE_ROOT + "Twinkle/tex_twinkle_list_{0:D2}";
        public const string CHAMPIONS_EXTRA_NEWS_CHARACTER_BG = CHAMPIONS_TEXTURE_ROOT + "Twinkle/tex_twinkle_list_win_00";
        public const string CHAMPIONS_NEWS_TITLE = CHAMPIONS_TEXTURE_ROOT + "Twinkle/tex_twinkle_title_{0:D3}";
        public const string CHAMPIONS_NEWS_SUBTITLE = CHAMPIONS_TEXTURE_ROOT + "Twinkle/tex_twinkle_subtitle_{0:D3}";
        public const string CHAMPIONS_NEWS_BUTTON = UIPartsPath + "/Champions/Parts/NewsButton";
        public const string CHAMPIONS_NEWS_BUTTON_PARTICLE = UI_EFFECT_ROOT + "Champions/pfb_uieff_champions_btn_news00";
        public const string CHAMPIONS_RESULT_STAMP_UI = UIPartsPath + "/Champions/Parts/ChampionsResultStampUI";
        
        /// <summary>
        /// ロゴの下地画像
        /// </summary>
        /// <param name="resourceId"></param>
        /// <returns></returns>
        public static string GetChampionsLogoBasePath(int resourceId , bool isSmall = false)
        {
            _stringBuilder.Clear();
            var basePath = isSmall ? CHAMPIONS_LOGO_BASE_S : CHAMPIONS_LOGO_BASE;
            _stringBuilder.AppendFormat(basePath, resourceId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：ロゴ画像
        /// </summary>
        /// <param name="resourceId"></param>
        /// <param name=""></param>
        /// <returns></returns>
        public static string GetChampionsLogoPath(int resourceId, ChampionsDefines.LogoSize size)
        {
            const string PATH = CHAMPIONS_TEXTURE_ROOT + "Logo/tex_champions_logo_{0}_{1}";
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, resourceId, size.GetPathSuffix());
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チャンピオンズミーティング：ロゴ画像
        /// </summary>
        /// <param name="resourceId"></param>
        /// <param name=""></param>
        /// <returns></returns>
        public static string GetChampionsLogoAnimPath(int resourceId)
        {
            const string PATH = CHAMPIONS_FLASH_ROOT + "pf_fl_champions_top_logo{0:D2}_00";
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, resourceId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 報酬アイコン取得
        /// </summary>
        /// <param name="winNum"></param>
        /// <param name="open"></param>
        /// <returns></returns>
        public static string GetChampionsWinBoxPath(int winNum, bool open)
        {
            const string PATH_NO_OPEN = CHAMPIONS_TEXTURE_ROOT + "WinBox/utx_ico_winbox_m_{0:D2}";
            const string PATH_OPEN = CHAMPIONS_TEXTURE_ROOT + "WinBox/utx_ico_winbox_m_open_{0:D2}";
            _stringBuilder.Clear();
            if(open)
            {
                _stringBuilder.AppendFormat(PATH_OPEN, winNum);
            }
            else
            {
                _stringBuilder.AppendFormat(PATH_NO_OPEN, winNum);
            }
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 報酬アイコン（小）取得
        /// </summary>
        /// <param name="winNum"></param>
        /// <param name="open"></param>
        /// <returns></returns>
        public static string GetChampionsWinBoxSmallPath(int winNum)
        {
            const string PATH = CHAMPIONS_TEXTURE_ROOT + "WinBox/utx_ico_winbox_s_{0:D2}";
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(PATH, winNum);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// Twinle増刊号2面のキャラ背景
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetChampionsNewsCharacterBg(int index)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_NEWS_CHARACTER_BG, index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// Twinkle増刊号タイトルロゴ取得
        /// </summary>
        /// <param name="resourceID"></param>
        /// <returns></returns>
        public static string GetTextureChampionsTitle(int resourceID)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_NEWS_TITLE, resourceID);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// Twinkle増刊号サブタイトルロゴ取得
        /// </summary>
        /// <param name="resourceID"></param>
        /// <returns></returns>
        public static string GetTextureChampionsSubTitle(int resourceID)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(CHAMPIONS_NEWS_SUBTITLE, resourceID);
            return _stringBuilder.ToString();
        }

        #endregion

        #region パドック

        /// <summary>
        /// パドックのカメラアニメーションクリップの名前
        /// </summary>
        private const string PADDOCK_CAMERA_ANIMATION_CLIP_NAME = "anm_pdk_type{0:D2}_cam{1}{2:D2}_cam";

        /// <summary>
        /// パドックのカメラアニメーションクリップのパス
        /// </summary>
        private const string PADDOCK_CAMERA_ANIMATION_CLIP_PATH = MOTION_ROOT + "Paddock/Camera/" + PADDOCK_CAMERA_ANIMATION_CLIP_NAME;

        /// <summary>
        /// パドックのカメラアニメーションクリップのパスを取得する。
        /// </summary>
        /// <param name="type"></param>
        /// <param name="compositionToken"></param>
        /// <param name="pattern"></param>
        /// <returns></returns>
        public static string GetPaddockCameraAnimationClipPath(int type, string compositionToken, int pattern)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PADDOCK_CAMERA_ANIMATION_CLIP_PATH, type, compositionToken, pattern + 1);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パドックのカメラアニメーションクリップの名前を取得する。
        /// </summary>
        /// <param name="type"></param>
        /// <param name="compositionToken"></param>
        /// <param name="pattern"></param>
        /// <returns></returns>
        public static string GetPaddockCameraAnimationClipName(int type, string compositionToken, int pattern)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(PADDOCK_CAMERA_ANIMATION_CLIP_NAME, type, compositionToken, pattern + 1);
            return _stringBuilder.ToString();
        }

        #endregion

        #region Cutt共通

        private const string CUTT_ROOT = Root3d + "Cutt/";

        /// <summary>
        /// ボーン名のグループ
        /// 全キャラで単一のScriptableObjectを使う
        /// </summary>
        public const string CUTT_CHARA_BONE_GROUP_PATH = CUTT_ROOT + "CharaBoneGroup";

        /// <summary>
        /// 特定の衣装で差し替えるモーションの情報
        /// </summary>
        public const string CUTT_SWAP_MOTION_DATA_PATH = CUTT_ROOT + "SwapMotionData";

        #endregion
        
        #region チュートリアル
        
        //チュートリアルのカットパス
        public const string TUTORIAL_CUTIN_PATH = "Cutt/CutIn/OutGame/outgame_tutorial001/outgame_tutorial001";
        public const string TUTORIAL_CUTIN_TEXT_ROOT = "Outgame/Tutorial/";
        public const string TUTORIAL_CUTIN_TEXT_PATH = TUTORIAL_CUTIN_TEXT_ROOT + "tutorial_prologue_text_{0:D2}";

        public static string GetTutorialCutInTextPath(int index)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TUTORIAL_CUTIN_TEXT_PATH, index).ToString();
        }

        /// <summary>
        /// チュートリアルのムービーパス。
        /// </summary>
        public const string TUTORIAL_MOVIE_FORMAT = "Tutorial/tutorial_movie_{0:D2}";

        public static string GetTutorialMoviePath()
        {
            var format = MovieHelper.MovieFormat.H264;
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TUTORIAL_MOVIE_FORMAT, (int)format).ToString();
        }

        /// <summary>
        /// たづなさんメッセージが開いた際に再生されるボイスファイルパス
        /// </summary>
        public const string TUTORIAL_TADUNA_CUESHEET_FORMAT = "snd_voi_tutorial_09001{0:D2}";
        public static string GetTutorialTadunaVoiceCueSheetName(int textId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TUTORIAL_TADUNA_CUESHEET_FORMAT, (int)textId).ToString();
        }
        // チュートリアルガチャの確認ダイアログ
        public const string TUTORIAL_GACHA_EXPLANATION_DIALOG_PATH = UIPartsPath + "/Tutorial/DialogTutorialGachaExplanation";
        #endregion

        #region ストーリーイベント

        /// <summary>
        /// ストーリーイベント：ルート
        /// </summary>
        public const string STORY_EVENT_ROOT = "StoryEvent/";

        /// <summary>
        /// ストーリーイベント：イベントロゴのパス
        /// </summary>
        private const string STORY_EVENT_LOGO_PATH = STORY_EVENT_ROOT + "Logo/{0:D4}/tex_storyevent_logo_{0:D4}";

        /// <summary>
        /// ストーリーイベント：エクストラストーリー専用フォルダ
        /// </summary>
        private const string STORY_EVENT_EXTRA_ROOT = "Story/ExtraStoryThumb/StoryEvent/";

        /// <summary>
        /// ストーリーイベント：エクストラストーリーサムネイル（タイトル）のパス
        /// </summary>
        private const string STORY_EVENT_EXTRA_STORY_TITLE_THUMBNAIL_PATH = STORY_EVENT_EXTRA_ROOT + "{0:D4}/story_event_story_list_thumb_title_{0:D4}";
        
        /// <summary>
        /// ストーリーイベント：エクストラストーリーサムネイル（各話）のパス
        /// </summary>
        private const string STORY_EVENT_EXTRA_STORY_THUMBNAIL_PATH = STORY_EVENT_EXTRA_ROOT + "{0:D4}/story_event_story_list_thumb_{0:D4}_{1:D3}";

        /// <summary>
        /// ストーリーイベント：サポートカード上に表示する「特攻アイコン」パーツのパス
        /// </summary>
        public const string STORY_EVENT_EP_BONUS_ICON_PREFAB_PATH = UIPartsPath + "/SupportCard/PartsStoryEventEpBonusIcon";

        /// <summary>
        /// ストーリーイベント：トップ：ストーリー一覧ボタン
        /// </summary>
        private const string STORY_EVENT_TOP_STORY_BUTTON_TEXTURE_PATH = STORY_EVENT_ROOT + "Top/{0:D4}/utx_btn_storyevent_top_00_{0:D4}";

        /// <summary>
        /// ストーリーイベント：トップ：ストーリー一覧ボタン(～10000600)
        /// </summary>
        private const string STORY_EVENT_TOP_STORY_BUTTON_TEXTURE_PATH_OLD = STORY_EVENT_ROOT + "Top/1001/utx_btn_storyevent_top_00";

        /// <summary>
        /// ストーリーイベント：トップ：報酬一覧ボタン
        /// </summary>
        public const string STORY_EVENT_TOP_REWARD_BUTTON_TEXTURE_PATH = STORY_EVENT_ROOT + "Top/utx_btn_storyevent_top_01";

        /// <summary>
        /// ストーリーイベント：トップ：ルーレットダービーボタン
        /// </summary>
        public const string STORY_EVENT_TOP_ROULETTE_BUTTON_TEXTURE_PATH = STORY_EVENT_ROOT + "Top/utx_btn_storyevent_top_02";

        /// <summary>
        /// ストーリーイベント：トップ：ミッションボタン
        /// </summary>
        private const string STORY_EVENT_TOP_MISSION_BUTTON_TEXTURE_PATH = STORY_EVENT_ROOT + "Top/{0:D4}/utx_btn_storyevent_top_03_{0:D4}";

        /// <summary>
        /// ストーリーイベント：トップ：ミッションボタン(～10000600)
        /// </summary>
        private const string STORY_EVENT_TOP_MISSION_BUTTON_TEXTURE_PATH_OLD = STORY_EVENT_ROOT + "Top/utx_btn_storyevent_top_03";

        /// <summary>
        /// ストーリーイベント：イベントPtのフォント
        /// </summary>
        public const string STORY_EVENT_EVENT_PT_FONT_PATH = STORY_EVENT_ROOT + "Font/TexStoryEventPt";

        /// <summary>
        /// ストーリーイベント：ストーリー一覧ダイアログ
        /// </summary>
        public const string DIALOG_STORY_EVENT_EPISODE_PATH = UIPartsPath + "/StoryEvent/DialogStoryEventEpisode";

        /// <summary>
        /// ストーリーイベント：報酬一覧ダイアログ
        /// </summary>
        public const string DIALOG_STORY_EVENT_REWARD_PATH = UIPartsPath + "/StoryEvent/DialogStoryEventReward";

        /// <summary>
        /// ストーリーイベント：イベントPtボーナス詳細ダイアログ
        /// </summary>
        public const string DIALOG_STORY_EVENT_BONUS_PATH = UIPartsPath + "/StoryEvent/DialogStoryEventBonus";

        /// <summary>
        /// ストーリーイベント：ボーナスウマ娘詳細
        /// </summary>
        public const string DIALOG_STORY_EVENT_TOTAL_BONUS_DETAIL_PATH = UIPartsPath + "/StoryEvent/DialogStoryEventTotalBonusDetail";

        /// <summary>
        /// ストーリーイベント：ルーレットダービー：ルーレット報酬ダイアログ
        /// </summary>
        public const string DIALOG_ROULETTE_REWARD_PATH = UIPartsPath + "/StoryEvent/DialogRouletteReward";

        /// <summary>
        /// ストーリーイベント：ルーレットダービー：ビンゴ報酬ダイアログ
        /// </summary>
        public const string DIALOG_ROULETTE_BINGO_REWARD_PATH = UIPartsPath + "/StoryEvent/DialogRouletteBingoReward";

        /// <summary>
        /// ストーリーイベント：ルーレットダービー：オプションダイアログ
        /// </summary>
        public const string DIALOG_ROULETTE_OPTION_PATH = UIPartsPath + "/StoryEvent/DialogRouletteOption";

        /// <summary>
        /// ストーリーイベント：TOP画面の環境設定
        /// </summary>
        public const string STORY_EVENT_TOP_ENV_PARAM_PATH = EnvParamRoot + "StoryEventTop/story_event_top_" + BACKGROUND_ID_FORMAT + "/ast_prm_story_event_top_" + BACKGROUND_ID_FORMAT;
        public const string STORY_EVENT_TOP_ENV_PARAM_PATH_DUMMY = EnvParamRoot + "StoryEventTop/story_event_top_0002_00110/ast_prm_story_event_top_0002_00110"; // ダミー

        /// <summary>
        /// ストーリーイベント：ミッション画面の環境設定
        /// </summary>
        public const string STORY_EVENT_MISSION_ENV_PARAM_PATH = EnvParamRoot + "StoryEventMission/story_event_mission_" + BACKGROUND_ID_FORMAT + "/ast_prm_story_event_mission_" + BACKGROUND_ID_FORMAT;
        public const string STORY_EVENT_MISSION_ENV_PARAM_PATH_DUMMY = EnvParamRoot + "StoryEventMission/story_event_mission_0001_01110/ast_prm_story_event_mission_0001_01110"; // ダミー

        /// <summary>
        /// ストーリーイベント：ルーレット3D演出
        /// </summary>
        public const string STORY_EVENT_ROULETTE_DIRECTOR_3D = "Prefabs/StoryEvent/RouletteDirector3D";

        /// <summary>
        /// ストーリーイベント：ルーレットまとめて回す：ルーレット回転中に円盤上の扇１つを光らせる演出
        /// </summary>
        public const string STORY_EVENT_ROULETTE_CONTINUOUS_EFFECT_BOARD_LIGHT = UIPartsPath + "/StoryEvent/RouletteContinuousEffectBoardLight";

        /// <summary>
        /// ストーリーイベント：アイテムドロップ演出
        /// </summary>
        public const string DIALOG_STORY_EVENT_ITEM_DROP = UIPartsPath + "/StoryEvent/DialogStoryEventItemDrop";
        /// <summary>
        /// ストーリーイベント：アイテムドロップ演出のタイトルテキストFlashパス
        /// </summary>
        public const string STORY_EVENT_ITEM_DROP_TEXT_ACTION_FLASH_PATH = COMMON_FLASH_ACTION_ROOT + "fa_cmn_txt_event_reward00";

        /// <summary>
        /// ストーリーイベント：育成シナリオ選択からイベントトップへ遷移するボタンの画像のパス
        /// </summary>
        private const string STORY_EVENT_SINGLEMODE_ICON_PATH = STORY_EVENT_ROOT + "Icon/{0:D4}/tex_storyevent_ico_{0:D4}";

        /// <summary>
        /// ストーリーイベント：イベントPtカンスト画像のパス
        /// </summary>
        private const string STORY_EVENT_COMPLETE_EVENT_PT_IMAGE_PATH = STORY_EVENT_ROOT + "CompleteEventPt/{0:D4}/tex_storyevent_complete_{0:D4}";

        /// <summary>
        /// ストーリーイベント：UIアニメーションFlashのルート
        /// </summary>
        private const string UI_STORY_EVENT_ANIMATION_ROOT = FLASH_ROOT + "StoryEvent/";

        /// <summary>
        /// ストーリーイベント：イベントロゴFlash
        /// </summary>
        public const string STORY_EVENT_LOGO_FLASH_PATH = UI_STORY_EVENT_ANIMATION_ROOT + "pf_fl_storyevent_logo_top00";

        /// <summary>
        /// ストーリーイベント：ホームのストーリーイベントボタンのパス
        /// </summary>
        public const string STORY_EVENT_BUTTON_PREFAB_PATH = UIPartsPath + "/Home/PartsStoryEventButton";

        /// <summary>
        /// ストーリーイベント：イベントロゴのパスを取得
        /// </summary>
        public static string GetStoryEventLogoPath(int storyEventId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(STORY_EVENT_LOGO_PATH, storyEventId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：エクストラストーリーサムネイル（タイトル）のパス
        /// </summary>
        /// <param name="storyEventId"></param>
        /// <returns></returns>
        public static string GetStoryEventExtraStoryTitleThumbnailPath(int storyEventId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(STORY_EVENT_EXTRA_STORY_TITLE_THUMBNAIL_PATH, storyEventId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：エクストラストーリーサムネイル（各話）のパス
        /// </summary>
        /// <param name="storyEventId"></param>
        /// <param name="storyIndex"></param>
        /// <returns></returns>
        public static string GetStoryEventExtraStoryThumbnailPath(int storyEventId, int storyIndex)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(STORY_EVENT_EXTRA_STORY_THUMBNAIL_PATH, storyEventId, storyIndex);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：トップ：ストーリー一覧ボタン背景画像
        /// </summary>
        public static string GetStoryEventTopStoryButtonImagePath(int storyEventId)
        {
            _stringBuilder.Clear();
            // TODO:@kikuchi_nozomi: 命名の変更が入ったので旧リソースしか無い場合に備える。不要になったら消す
            if (ResourceManager.IsExistAsset(TextUtil.Format(STORY_EVENT_TOP_STORY_BUTTON_TEXTURE_PATH, storyEventId)))
            {
                _stringBuilder.AppendFormat(STORY_EVENT_TOP_STORY_BUTTON_TEXTURE_PATH, storyEventId);
            }
            else
            {
                _stringBuilder.AppendFormat(STORY_EVENT_TOP_STORY_BUTTON_TEXTURE_PATH_OLD);
            }
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：トップ：ミッションボタン背景画像
        /// </summary>
        public static string GetStoryEventTopMissionButtonImagePath(int storyEventId)
        {
            _stringBuilder.Clear();
            // TODO:@kikuchi_nozomi: 命名の変更が入ったので旧リソースしか無い場合に備える。不要になったら消す
            if (ResourceManager.IsExistAsset(TextUtil.Format(STORY_EVENT_TOP_MISSION_BUTTON_TEXTURE_PATH, storyEventId)))
            {
                _stringBuilder.AppendFormat(STORY_EVENT_TOP_MISSION_BUTTON_TEXTURE_PATH, storyEventId);
            }
            else
            {
                _stringBuilder.AppendFormat(STORY_EVENT_TOP_MISSION_BUTTON_TEXTURE_PATH_OLD);
            }
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：TOP画面の環境設定のパス
        /// </summary>
        public static string GetStoryEventTopEnvParamPath(int bgId, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORY_EVENT_TOP_ENV_PARAM_PATH, bgId, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：ミッション画面の環境設定のパス
        /// </summary>
        public static string GetStoryEventMissionEnvParamPath(int bgId, int subId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(STORY_EVENT_MISSION_ENV_PARAM_PATH, bgId, subId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：育成シナリオ選択内の遷移ボタンPathを取得
        /// </summary>
        public static string GetStoryEventSingleModeIconPath(int storyEventId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(STORY_EVENT_SINGLEMODE_ICON_PATH, storyEventId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// ストーリーイベント：イベントPtカンスト画像のパスを取得
        /// </summary>
        public static string GetStoryEventCompleteEventPtImagePath(int storyEventId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(STORY_EVENT_COMPLETE_EVENT_PT_IMAGE_PATH, storyEventId);
            return _stringBuilder.ToString();
        }

        #region ルーレットダービー

        /// <summary>
        /// ルーレット：Flashルート
        /// </summary>
        private const string ROULETTE_FLASH_ROOT = FLASH_ROOT + "Roulette/";
        
        /// <summary>
        /// ルーレット：FlashActionルート
        /// </summary>
        private const string ROULETTE_FLASH_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/Roulette/";
        
        /// <summary>
        /// ルーレット：ルーレット報酬GET専用Flash
        /// </summary>
        public const string ROULETTE_GET_FLASH_PATH = ROULETTE_FLASH_ROOT + "pf_fl_roulette_txt_get00";

        /// <summary>
        /// ルーレット：ビンゴシートFlash（画面トップ）
        /// </summary>
        public const string ROULETTE_SHEET_TOP_FLASH_PATH = ROULETTE_FLASH_ROOT + "pf_fl_roulette_sheet_bingo00";

        /// <summary>
        /// ルーレット：ビンゴシートFlash（画面トップ）
        /// </summary>
        public const string ROULETTE_SHEET_ZOOM_FLASH_PATH = ROULETTE_FLASH_ROOT + "pf_fl_roulette_sheet_bingo01";
        
        /// <summary>
        /// ルーレット：ビンゴシートダイアログ
        /// </summary>
        public const string DIALOG_ROULETTE_BINGO_SHEET_PATH = UIPartsPath + "/StoryEvent/DialogRouletteBingoSheet";
        
        /// <summary>
        /// ルーレット：ビンゴシート交換ダイアログ
        /// </summary>
        public const string DIALOG_ROULETTE_BINGO_SHEET_CHANGE_PATH = UIPartsPath + "/StoryEvent/DialogRouletteBingoSheetChange";

        /// <summary>
        /// ルーレット：ビンゴシート交換確認ダイアログ
        /// </summary>
        public const string DIALOG_ROULETTE_BINGO_SHEET_CHANGE_CONFIRM_PATH = UIPartsPath + "/StoryEvent/DialogRouletteBingoSheetChangeConfirm";
        
        /// <summary>
        /// ルーレット：ビンゴ文字演出
        /// </summary>
        public const string ROULETTE_BINGO_TXT_FLASH_ACTION_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_txt_bingo00";

        /// <summary>
        /// ルーレット：センターボタンエフェクト
        /// </summary>
        public const string ROULETTE_CENTER_BUTTON_EFFECT_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_eff_lig_btn00";

        /// <summary>
        /// ルーレット：アイコン上の光の反射エフェクト
        /// </summary>
        public const string ROULETTE_ICON_REFLECT_EFFECT_PATH = ROULETTE_FLASH_ROOT + "pf_fl_roulette_eff_ref_icon00";

        /// <summary>
        /// ルーレット：まとめて回す：ルーレット回転中に飛んでいくアイテムアイコンの演出
        /// </summary>
        public const string ROULETTE_CONTINUOUS_EFFECT_FLYING_ITEM_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_continuous_getitem00";

        /// <summary>
        /// ルーレット：ルーレット報酬アイテム獲得演出
        /// </summary>
        public const string ROULETTE_ROULETTE_ITEM_INFO_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_getitem_info00";

        /// <summary>
        /// ルーレット：ビンゴ報酬アイテム獲得演出
        /// </summary>
        public const string ROULETTE_BINGO_ITEM_INFO_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_getitem_info01";

        /// <summary>
        /// ルーレット：ゴール板演出
        /// </summary>
        public const string ROULETTE_GOAL_BOARD_FLASH_ACTION_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_goalboard00";

        /// <summary>
        /// ルーレット：○走目演出
        /// </summary>
        public const string ROULETTE_EXEC_NUM_FLASH_ACTION_PATH = ROULETTE_FLASH_ACTION_ROOT + "fa_roulette_run_count00";
        
        /// <summary>
        /// ルーレット：背景
        /// </summary>
        public const string ROULETTE_BG_PATH = STORY_EVENT_ROOT + "Bg/roulette_bingo_vertical_bg_0000";

        #endregion

        #endregion

        #region エクストラストーリー

        private const string EXTRA_STORY_CATEGORY_TOP_THUMBNAIL_ROOT = "Story/ExtraStoryThumb/CategoryTop/";

        /// <summary>
        /// エクストラストーリー：アニバーサリー系列のカテゴリトップサムネ
        /// </summary>
        public const string EXTRA_STORY_ANNIVERSARY_CATEGORY_TOP_THUMBNAIL_PATH = EXTRA_STORY_CATEGORY_TOP_THUMBNAIL_ROOT + "extra_story_anniversary_category_top_thumb";

        /// <summary>
        /// エクストラストーリー：ストーリーイベントのカテゴリトップサムネ
        /// </summary>
        public const string EXTRA_STORY_EVENT_CATEGORY_TOP_THUMBNAIL_PATH = EXTRA_STORY_CATEGORY_TOP_THUMBNAIL_ROOT + "extra_story_event_category_top_thumb";

        /// <summary>
        /// エクストラストーリー：アニバーサリー系列の背景
        /// </summary>
        public const string EXTRA_STORY_ANNIVERSARY_CATEGORY_TOP_BG_PATH = "Bg/bg_0136_00110";

        /// <summary>
        /// エクストラストーリー：ストイベの背景
        /// </summary>
        public const string EXTRA_STORY_EVENT_CATEGORY_TOP_BG_PATH = "Bg/bg_0068_00110";


        /// <summary>
        /// エクストラストーリー：ルート
        /// </summary>
        private const string STORY_EXTRA_ROOT = "Story/StoryExtra/";

        /// <summary>
        /// エクストラストーリー：イベントロゴのパス
        /// </summary>
        private const string STORY_EXTRA_LOGO_PATH = STORY_EXTRA_ROOT + "Logo/{0:D4}/tex_storyextra_logo_{0:D4}";

        /// <summary>
        /// エクストラストーリー：エクストラストーリー専用フォルダ
        /// </summary>
        private const string EXTRA_STORY_EXTRA_ROOT = "Story/ExtraStoryThumb/StoryExtra/";

        /// <summary>
        /// エクストラストーリー：エクストラストーリーサムネイル（タイトル）のパス
        /// </summary>
        private const string EXTRA_STORY_EXTRA_TITLE_THUMBNAIL_PATH = EXTRA_STORY_EXTRA_ROOT + "{0:D4}/story_extra_story_list_thumb_title_{0:D4}";

        /// <summary>
        /// エクストラストーリー：エクストラストーリーサムネイル（各話）のパス
        /// </summary>
        private const string EXTRA_STORY_EXTRA_THUMBNAIL_PATH = EXTRA_STORY_EXTRA_ROOT + "{0:D4}/story_extra_story_list_thumb_{0:D4}_{1:D3}";


        /// <summary>
        /// エクストラストーリー：イベントロゴのパスを取得
        /// </summary>
        public static string GetStoryExtraLogoPath(int storyId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(STORY_EXTRA_LOGO_PATH, storyId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// エクストラストーリー：エクストラストーリーサムネイル（タイトル）のパス
        /// </summary>
        public static string GetExtraStoryExtraTitleThumbnailPath(int storyId)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(EXTRA_STORY_EXTRA_TITLE_THUMBNAIL_PATH, storyId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// エクストラストーリー：エクストラストーリーサムネイル（各話）のパス
        /// </summary>
        public static string GetExtraStoryExtraThumbnailPath(int storyId, int storyIndex)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(EXTRA_STORY_EXTRA_THUMBNAIL_PATH, storyId, storyIndex);
            return _stringBuilder.ToString();
        }


        #endregion

        #region チャレンジマッチ

        /// <summary>
        /// チャレンジマッチ：ホームのチャレンジマッチボタンのパス
        /// </summary>
        public const string CHALLENGE_MATCH_BUTTON_PREFAB_PATH = UIPartsPath + "/ChallengeMatch/PartsChallengeMatchButton";

        /// <summary>
        /// チャレンジマッチ：イベントロゴのパス
        /// </summary>
        public const string CHALLENGE_MATCH_LOGO_PATH = "ChallengeMatch/utx_txt_challengematch_logo_00";

        /// <summary>
        /// チャレンジマッチ：イベントロゴ周りのキラキラエフェクト
        /// </summary>
        public const string CHALLENGE_MATCH_LOGO_PARTICLE_PATH = UI_EFFECT_ROOT + "ChallengeMatch/pfb_uieff_challengematch_eventlogo00";

        /// <summary>
        /// チャレンジマッチ：育成のシナリオ選択画面の右下のミニボタンのパス
        /// </summary>
        public const string CHALLENGE_MATCH_BUTTON_MINI_PREFAB_PATH = UIPartsPath + "/ChallengeMatch/PartsChallengeMatchButtonMini";

        /// <summary>
        /// チャレンジマッチ：育成リザルトで表示される、イベントTOPへ誘導させるダイアログ
        /// </summary>
        public const string DIALOG_CHALLENGE_MATCH_SINGLE_MODE_RESULT = UIPartsPath + "/ChallengeMatch/DialogChallengeMatchSingleModeResult";

        /// <summary>
        /// チャレンジマッチ : チャレンジゲージMAX時に再生する演出のFlash
        /// </summary>
        public const string CHALLENGE_MATCH_GAUGE_MAX_ANIMATION = FLASH_ROOT + "ChallengeMatch/pf_fl_challengematch_top_gaugemax00";

        /// <summary>
        /// チャレンジマッチ : チャレンジゲージMAX時に再生する演出のFlashに乗せるEffect
        /// </summary>
        public const string CHALLENGE_MATCH_GAUGE_MAX_EFFECT = "UIAnimation/UIEffect/ChallengeMatch/pfb_uieff_challengematch_top_gaugemax00";
        /// <summary>
        /// チャレンジマッチ：レーススタートスキルカットインに乗せるFlash
        /// </summary>
        public const string CHALLENGR_MATCH_RACE_START_ANIMATION = FLASH_ROOT + "ChallengeMatch/pf_fl_challengematch_racestart00";

        /// <summary>
        /// チャレンジマッチ：上記Flashに乗せるエフェクト
        /// </summary>
        public const string CHALLENGR_MATCH_RACE_START_EFFECT = "UIAnimation/UIEffect/ChallengeMatch/pfb_uieff_challengematch_racestart00";

        /// <summary>
        /// チャレンジマッチ：レース詳細ダイアログのパス
        /// </summary>
        public const string DIALOG_CHALLENGE_MATCH_RACE = UIPartsPath + "/ChallengeMatch/DialogChallengeMatchRace";

        /// <summary>
        /// チャレンジマッチ：ポイント獲得ダイアログのパス
        /// </summary>
        public const string DIALOG_CHALLENGE_MATCH_GET_PT = UIPartsPath + "/ChallengeMatch/DialogGetChallengeMatchPt";
        
        /// <summary>
        /// チャレンジマッチ : カーニバルボーナス因子獲得
        /// </summary>
        public const string DIALOG_RACE_BONUS_FACTOR_CHALLENGE_MATCH = UIPartsPath + "/ChallengeMatch/DialogRaceBonusFactorChallengeMatch";

        /// <summary>
        /// チャレンジマッチ：キャラアイコン上のボーナススキルアイコン
        /// </summary>
        public const string CHALLENGE_MATCH_PT_BONUS_ICON = UIPartsPath + "/ChallengeMatch/PartsChallengeMatchPtBonusIcon";

        /// <summary>
        /// チャレンジマッチ：キャラアイコン上のボーナス因子アイコン
        /// </summary>
        public const string CHALLENGE_MATCH_BONUS_FACTOR_ICON = UIPartsPath + "/ChallengeMatch/PartsChallengeMatchBonusFactorIcon";

        /// <summary>
        /// チャレンジマッチ : レース開始背景
        /// </summary>
        public const string CHALLENGE_MATCH_RACE_OPEN_BG_PATH = UIPartsPath + "/ChallengeMatch/ChallengeMatchRaceOpenBg";

        /// <summary>
        /// チャレンジマッチ : レース開始背景Texture
        /// </summary>
        public const string CHALLENGE_MATCH_RACE_OPEN_BG_TEXTURE_PATH = "Bg/bg_challengematch_occurrence_{0:D2}";

        /// <summary>
        /// チャレンジマッチ : レース開始背景パーティクル
        /// </summary>
        public const string CHALLENGE_MATCH_RACE_OPEN_BG_PARTICLE_PATH = UI_EFFECT_ROOT + "ChallengeMatch/pfb_uieff_challengematch_racestart_bg00";

        public const string CHALLENGE_MATCH_AUTO_SELECT_DIALOG_PATH = UIPartsPath + "/ChallengeMatch/DialogChallengeMatchAutoSelect";

        /// <summary>
        /// チャレンジマッチ : チャレンジゲージMAX時に再生するTAT
        /// </summary>
        public const string CHALLENGE_MATCH_GAUGE_MAX_TAT = "UIAnimation/FlashCombine/Timeline/ChallengeMatch/tat_button_effect";
        #endregion
        #region 特別移籍イベント

        /// <summary>
        /// 特別移籍イベント：ルート
        /// </summary>
        private const string TRANSFER_EVENT_ROOT = "TransferEvent/";

        /// <summary>
        /// 特別移籍イベント：ホームの特別移籍イベントボタンのパス
        /// </summary>
        public const string TRANSFER_EVENT_BUTTON_PREFAB_PATH = UIPartsPath + "/TransferEvent/PartsTransferEventButton";

        /// <summary>
        /// 特別移籍イベント：「育成開始TOP画面」「強化編成の殿堂入りウマ娘画面」の特別移籍イベントボタンのパス
        /// </summary>
        public const string TRANSFER_EVENT_BUTTON_MINI_PREFAB_PATH = UIPartsPath + "/TransferEvent/PartsTransferEventButtonMini";

        /// <summary>
        /// 特別移籍イベント：「殿堂入りウマ娘一覧画面」の特別移籍イベントボタンのパス
        /// </summary>
        public const string TRANSFER_EVENT_BUTTON_SQUARE_PREFAB_PATH = UIPartsPath + "/TransferEvent/PartsTransferEventButtonSquare";

        /// <summary>
        /// 特別移籍イベント：イベントロゴのパス
        /// </summary>
        public const string TRANSFER_EVENT_LOGO_PATH = TRANSFER_EVENT_ROOT + "{0:D5}/transfer_event_logo_{0:D5}";

        /// <summary>
        /// 特別移籍イベント：画面の環境設定のパス
        /// </summary>
        public const string TRANSFER_EVENT_ENV_PARAM_PATH = EnvParamRoot + "TransferEvent/ast_prm_transfer_event_{0:D4}_{1:D5}";

        /// <summary>
        /// 特別移籍イベント：トレーナーアイコンのパス
        /// </summary>
        private const string TRANSFER_EVENT_TRAINER_ICON_PATH = TRANSFER_EVENT_ROOT + "TrainerIcon/trainer_icon_{0:D3}";

        /// <summary>
        /// 特別移籍イベント：移籍確認ダイアログのプレハブのパス
        /// </summary>
        public const string DIALOG_TRANSFER_EVENT_CONFIRM_PATH = UIPartsPath + "/TransferEvent/DialogTransferEventConfirm";

        /// <summary>
        /// 特別移籍イベント：交渉成立フラッシュのプレハブのパス
        /// </summary>
        public const string TRANSFER_EVENT_NEGOTIATION_FLASH_PATH = FLASH_ROOT + "Outgame/pf_fl_transfer_txt_negotiation00";

        /// <summary>
        /// 特別移籍イベント：移籍完了ダイアログのプレハブのパス
        /// </summary>
        public const string DIALOG_TRANSFER_EVENT_RESULT_PATH = UIPartsPath + "/TransferEvent/DialogTransferEventResult";

        /// <summary>
        /// 特別移籍イベント：トレーナーアイコンのパスを取得
        /// </summary>
        public static string GetTransferEventTrainerIconPath(int trainerType)
        {
            _stringBuilder.Clear();
            _stringBuilder.AppendFormat(TRANSFER_EVENT_TRAINER_ICON_PATH, trainerType);
            return _stringBuilder.ToString();
        }

        #endregion

        #region 開催告知

        /// <summary>開催告知ルート</summary>
        public const string ANNOUNCE_ROOT = "Announce/";

        /// <summary>開催告知Flashルート</summary>
        public const string ANNOUNCE_FLASH_ROOT = FLASH_ROOT + "Announce/";
        
        /// <summary>開催告知FlashActionルート</summary>
        public const string ANNOUNCE_FLASH_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/Announce/";
        
        /// <summary>開催告知サポートカードFlashルート</summary>
        private const string ANNOUNCE_SUPPORT_CARD_FLASH_ROOT = FLASH_COMBINE_ROOT + "Action/Announce/SupportCard";
        
        /// <summary>開催告知繋ぎエフェクトパス</summary>
        public const string ANNOUNCE_BRIDGE_EFFECT_PATH = FLASH_ROOT + "Announce/Transition/pf_fl_announce_transition00";
        
        /// <summary>ムービールート</summary>
        public const string MOVIE_ROOT = "Movie/" + Cute.Cri.MovieManager.STR_SUBFOLDER_MOVIE;

        /// <summary>開催告知ムービールート</summary>
        public const string ANNOUNCE_MOVIE_ROOT = MOVIE_ROOT + "Announce/";
        
        /// <summary>
        /// 開催告知ムービーパスを取得
        /// </summary>
        /// <param name="movieId">ムービーID</param>
        /// <param name="isDownloadedPath">ダウンロード時のパスか (true: そう, false: 違う)</param>
        /// <returns>開催告知ムービーパス</returns>
        public static string[] GetAnnounceMoviePaths(int movieId, bool isDownloadedPath = false)
        {
            const int INTRO_TYPE = 1;
            const int LOOP_TYPE = 2;
            int format_id = (int)MovieHelper.GetMovieFormat();
            return new string[]
            {
                TextUtil.Format("{0}Announce/announce_{2:D7}_{3}_{4}{1}", 
                    (isDownloadedPath) ? (MOVIE_ROOT) : (""), 
                    (isDownloadedPath) ? (".usm") : (""), 
                    movieId, INTRO_TYPE, format_id),
                TextUtil.Format("{0}Announce/announce_{2:D7}_{3}_{4}{1}", 
                    (isDownloadedPath) ? (MOVIE_ROOT) : (""), 
                    (isDownloadedPath) ? (".usm") : (""), 
                    movieId, LOOP_TYPE, format_id),
            };
        }
        
        /// <summary>
        /// 開催告知サポートカードFlashパスを取得(リネーム前)
        /// TODO:@kikuchi_nozomi: 旧リソースを使わなくなったら削除
        /// </summary>
        /// <param name="supportCardPattern">サポートカード演出タイプ</param>
        /// <returns>開催告知サポートカードFlashパス (null: 無効)</returns>
        public static string GetAnnounceSupportCardFlashPath_Old(AnnounceDefine.SupportCardPattern supportCardPattern)
        {
            if ((int)AnnounceDefine.SupportCardPattern.None < (int)supportCardPattern && (int)supportCardPattern < (int)AnnounceDefine.SupportCardPattern.Max)
            {
                int supportCardPatternIdx = (int)supportCardPattern - 1;
                _stringBuilder.Length = 0;
                return _stringBuilder.AppendFormat("{0}/fa_announce_supportcard{1:00}", ANNOUNCE_SUPPORT_CARD_FLASH_ROOT, supportCardPatternIdx).ToString();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 開催告知サポートカードFlashパスを取得
        /// </summary>
        /// <param name="supportCardPattern">サポートカード演出タイプ</param>
        /// <returns>開催告知サポートカードFlashパス (null: 無効)</returns>
        public static string GetAnnounceSupportCardFlashPath(MasterAnnounceSupportCard.AnnounceType type, AnnounceDefine.SupportCardPattern supportCardPattern)
        {
            if ((int)AnnounceDefine.SupportCardPattern.None < (int)supportCardPattern && (int)supportCardPattern < (int)AnnounceDefine.SupportCardPattern.Max)
            {
                _stringBuilder.Length = 0;
                return _stringBuilder.AppendFormat("{0}/fa_announce_supportcard{1:00}_{2:00}", ANNOUNCE_SUPPORT_CARD_FLASH_ROOT, (int)type, (int)supportCardPattern).ToString();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 開催告知サポートカード短冊テクスチャパスを取得
        /// </summary>
        /// <param name="announceDataId">開催告知データID</param>
        /// <param name="supportCardId">サポートカードID</param>
        /// <returns>開催告知ムービーパス</returns>
        public static string GetAnnounceSupportCardRareSkillPath(int announceDataId, int supportCardId, bool isDownloadedPath = false)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat("Announce/SupportCardGacha/{0}/support_announce_skill_{1}", announceDataId, supportCardId).ToString();
        }

        /// <summary>
        /// 開催告知サポートカード短冊テクスチャパスを取得
        /// </summary>
        /// <param name="announceDataId">開催告知データID</param>
        /// <param name="supportCardId">サポートカードID</param>
        /// <returns>開催告知ムービーパス</returns>
        public static string GetAnnounceSupportCardObiPath(int announceDataId, int supportCardId, bool isDownloadedPath = false)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat("{0}SupportCardGacha/{1}/support_announce_{2}", ANNOUNCE_ROOT, announceDataId, supportCardId).ToString();
        }

        /// <summary>
        /// 開催告知サポートカードガチャ名
        /// </summary>
        /// <param name="announceDataId"></param>
        /// <returns></returns>
        public static string GetAnnounceSupportCardGachaName(int announceDataId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat("{0}SupportCardGacha/{1}/support_announce_index_{1}", ANNOUNCE_ROOT, announceDataId).ToString();
        }

        #endregion

        #region イベント予告
        
        /// <summary>
        /// イベント予告ダイアログ
        /// </summary>
        public const string DIALOG_ANNOUNCE_EVENT_PATH = UIPartsPath + "/Outgame/DialogAnnounceEvent";

        /// <summary>
        /// イベント予告ダイアログの倍計テクスチャを取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetAnnouceEventBgTexture(int id)
        {
            const string ANNOUNCE_EVENT_TEXTURE_PATH_FORMAT = OUTGAME_ASSET_ROOT + "AnnounceEvent/Bg/announce_event_{0:D5}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(ANNOUNCE_EVENT_TEXTURE_PATH_FORMAT, id);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// 育成シナリオログ風UI用のアセットを取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetAnnounceEventLogUIAsset(int id)
        {
            const string ANNOUNCE_EVENT_LOG_UI_ASSET_ROOT = OUTGAME_ASSET_ROOT + "AnnounceEvent/LogUIAsset/ast_announce_event_log_ui_asset_{0:D5}";
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(ANNOUNCE_EVENT_LOG_UI_ASSET_ROOT, id);
            return _stringBuilder.ToString();
        }

        #endregion

        #region ジュークボックス

        /// <summary>
        /// ジュークボックス：UIパーツのルート
        /// </summary>
        private const string JUKEBOX_UI_PARTS_ROOT = HOME_UI_PARTS_ROOT + "Jukebox/";

        /// <summary>
        /// ジュークボックス：楽曲リクエストダイアログ
        /// </summary>
        public const string DIALOG_JUKEBOX_REQUEST_SONG = JUKEBOX_UI_PARTS_ROOT + "DialogJukeboxRequestSong";

        /// <summary>
        /// ジュークボックス：履歴ダイアログ
        /// </summary>
        public const string DIALOG_JUKEBOX_HISTORY = JUKEBOX_UI_PARTS_ROOT + "DialogJukeboxHistory";

        /// <summary>
        /// ジュークボックス：いいね！ボタンと件数のアニメーションPrefab
        /// </summary>
        public const string PARTS_JUKEBOX_LIKE = JUKEBOX_UI_PARTS_ROOT + "PartsJukeboxLike";

        /// <summary>
        /// ジュークボックス：いいね！ボタンフラッシュ
        /// </summary>
        public const string JUKEBOX_FLASH_LIKE = FLASH_ROOT + "Home/pf_fl_home_jukebox_btn_nice00";

        /// <summary>
        /// ジュークボックス：いいね！ボタンFlashActionルート
        /// </summary>
        public const string JUKEBOX_FLASH_COMBINE_LIKE = FLASH_COMBINE_ROOT + "Action/Home/fa_home_jukebox_btn_nice00";

        /// <summary>
        /// ジュークボックス：いいね！件数TATルート
        /// </summary>
        public const string JUKEBOX_TAT_LIKE = FLASH_COMIBINE_TIMELINE_ROOT + "Home/tat_home_jukebox_nice00";

        /// <summary>
        /// ジュークボックス：楽曲名の代替テクスチャ
        /// </summary>
        public const string JUKEBOX_MUSIC_TITLE_ROOT = OUTGAME_ASSET_ROOT + "Jukebox/MusicTitle/";
        
        /// <summary>
        /// ジュークボックス：楽曲名の代替テクスチャ（フォントの都合で4つ点の「馬」を表示できない問題の対策）
        /// </summary>
        /// <param name="musicId"></param>
        /// <returns></returns>
        public static string GetJukeboxMusicNameTexture(int musicId)
        {
            _stringBuilder.Clear();
            const string FORMAT = JUKEBOX_MUSIC_TITLE_ROOT + "tex_jukebox_title_{0:D4}";
            _stringBuilder.AppendFormat(FORMAT, musicId);
            return _stringBuilder.ToString();
        }
        
        #endregion
    
        #region 名刺
        
        /// <summary>
        /// 名刺関係のリソースパス
        /// </summary>
        private const string PROFILECARD_TEXTURE_RESOURCE_PATH = "OutGame/ProfileCard/";

        /// <summary>
        /// 背景
        /// </summary>
        public const string PROFILECARD_TEXTURE_RESOURCE_BG_PATH = PROFILECARD_TEXTURE_RESOURCE_PATH + "bg/";
        
        /// <summary>
        /// 背景サムネイル
        /// </summary>
        public const string PROFILECARD_TEXTURE_RESOURCE_BG_THUMBNAIL_PATH = PROFILECARD_TEXTURE_RESOURCE_PATH + "thumb_bg/";
        
        /// <summary>
        /// フォトのサムネイルマスク
        /// </summary>
        public const string PROFILECARD_TEXTURE_MASK_PHOTO_TUMBNAIL = PROFILECARD_TEXTURE_RESOURCE_BG_THUMBNAIL_PATH + "utx_mask_thumb_photo";

        /// <summary>
        /// ラベル
        /// </summary>
        public const string PROFILECARD_TEXTURE_RESOURCE_TRAINER_PATH = PROFILECARD_TEXTURE_RESOURCE_PATH + "name_00/";

        /// <summary>
        /// ProfileCardダイアログのルート
        /// </summary>
        public const string PROFILECARD_UI_PARTS_ROOT = UIPartsPath + "/ProfileCard/";

        // キャラ背景変更
        public const string DIALOG_PROFILECARD_CHANGE_CHARACTER_BG = PROFILECARD_UI_PARTS_ROOT + "DialogProfileCardChangeCharacterBg";

        // キャラ背景変更
        public const string DIALOG_PROFILECARD_CHANGE_CHARACTER_BG_DETAIL = PROFILECARD_UI_PARTS_ROOT + "DialogProfileCardChangeCharacterBgDetail";

        // カード背景変更
        public const string DIALOG_PROFILECARD_CHANGE_CARD_BG = PROFILECARD_UI_PARTS_ROOT + "DialogProfileCardChangeCardBg";


        /// <summary>
        /// 名刺背景の取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetProfileCardBgPath(int id)
        {
            _stringBuilder.Clear();
            const string PROFILECARD_BG_PATH = PROFILECARD_TEXTURE_RESOURCE_BG_PATH + "profilecard_bg_{0:D2}";
            _stringBuilder.AppendFormat(PROFILECARD_BG_PATH, id);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// 名刺カード背景サムネイルの取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetProfileCardTumbBgPath(int id)
        {
            _stringBuilder.Clear();
            const string PROFILECARD_BG_PATH = PROFILECARD_TEXTURE_RESOURCE_BG_THUMBNAIL_PATH + "profilecard_thumb_bg_{0:D2}";
            _stringBuilder.AppendFormat(PROFILECARD_BG_PATH, id);
            return _stringBuilder.ToString();
        }
        
        /// <summary>
        /// 名刺マスクの取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetProfileCardMaskPath()
        {
            return PROFILECARD_TEXTURE_RESOURCE_BG_THUMBNAIL_PATH + "utx_mask_profilecard_00_sl";
        }
        
        /// <summary>
        /// トレーナーラベルの取得
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static string GetProfileCardTrainerLablePath(int id)
        {
            return GetProfileCardLabel(0, id);
        }

        /// <summary>
        /// プロフラベル取得
        /// type = 0 トレーナー、 type = 1 サークル
        /// </summary>
        /// <param name="type"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        private static string GetProfileCardLabel(int type,int id)
        {
            _stringBuilder.Clear();
            const string PROFILECARD_LABEL_PATH = PROFILECARD_TEXTURE_RESOURCE_TRAINER_PATH + "profilecard_name_{0:D2}_{1:D2}";
            _stringBuilder.AppendFormat(PROFILECARD_LABEL_PATH, type, id);
            return _stringBuilder.ToString();
        }

        #endregion

        #region フォト

        /// <summary>
        /// フォト確認画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_CHECK_PREFAB_PATH = "UI/Parts/PhotoCheck";

        /// <summary>
        /// フォト確認画面のPrefabリソースパス(横画面用)
        /// </summary>
        public const string PHOTO_CHECK_HORIZONTAL_PREFAB_PATH = "UI/Parts/PhotoCheckHorizontal";

        /// <summary>
        /// フォト確認画面のフィルターコンテンツPrefabリソースパス
        /// </summary>
        public const string PHOTO_CHECK_ITEM_PREFAB_PATH = "UI/Parts/PhotoCheckSelectScrollListItem";

        /// <summary>
        /// フォトライブラリのPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_PREFAB_PATH = "UI/Parts/Outgame/DialogPhotoLibraryVertical";

        /// <summary>
        /// フォトライブラリ内削除画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_DELETE_REFAB_PATH = "UI/Parts/DialogPhotoLibraryPhotoDelete";

        /// <summary>
        /// フォトライブラリ内削除画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_DELETE_HORIZONTAL_REFAB_PATH = "UI/Parts/Outgame/DialogPhotoLibraryPhotoDeleteHorizontal";
        /// <summary>
        /// フォトライブラリ横画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_HORIZONTAL_PREFAB_PATH = "UI/Parts/Outgame/DialogPhotoLibraryHorizontal";

        /// <summary>
        /// フォトライブラリ横画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_FILTER_PREFAB_PATH = "UI/Parts/DialogFilterPhoto";

        /// <summary>
        /// フォトライブラリ縦画面時詳細画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_DETAIL_PREFAB_PATH = "UI/Parts/DialogPhotoLibraryPhotoDetail";

        /// <summary>
        /// フォトライブラリ縦画面時詳細画面のPrefabリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_COMMMON_WARN_PREFAB_PATH = "UI/Parts/DialogPhotoCommonSmall";
        
        /// <summary>
        /// フォト保存時の確認用Prefabのリソースパス
        /// </summary>
        public const string PHOTO_LIBRARY_SAVE_CONFIRM_PREFAB_PATH = "UI/Parts/DialogPhotoSaveConfirm";
        
        /// <summary>
        /// 名刺画面の背景設定用フィルターPrefabのリソースパス
        /// </summary>
        public const string PROFILE_CARD_BG_FILTER_PREFAB_PATH = "UI/Parts/DialogFilterProfileBG";

        /// <summary>
        /// 名刺画面の背景設定用フィルターPrefabのリソースパス
        /// </summary>
        public const string PHOTO_CHECK_CAMERA_FLASH_TAT_NAME = FLASH_COMBINE_ROOT + "Timeline/OutGame/tat_contactcard_camflash00";
        
        /// <summary>
        /// フォト保存時のアイコンアニメーションのリソースパス
        /// </summary>
        public const string PHOTO_CHECK_SAVE_ICON_ANIM_PREFAB_PATH  = FLASH_ROOT + "OutGame/prefab/pf_fl_contactcard_photolibrary_icon00";
        
        #endregion

        #region レイドイベント

        private static string RAID_EVENT_TEXTURE_PATH = OUTGAME_ASSET_ROOT + "FanRaid/";

        /// <summary>
        /// レイドイベントのロゴパス
        /// </summary>
        public static string GetFanRaidLogoPath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_LOGO_PATH, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_LOGO_PATH = RAID_EVENT_TEXTURE_PATH + "{0:D4}/tex_fanraid_logo_{0:D4}";
        
        /// <summary>
        /// レイドイベントのリザルト背景画像パス
        /// </summary>
        public static string GetFanRaidResultBgPath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_RESULT_BG_PATH, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_RESULT_BG_PATH = RAID_EVENT_TEXTURE_PATH + "{0:D4}/tex_fanraid_bg_singleresult_{0:D4}";
        
        /// <summary>
        /// レイドイベント報酬全達成時のテクスチャパス
        /// </summary>
        public static string GetFanRaidCompleteTexturePath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_COMPLETE_PATH, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_COMPLETE_PATH = RAID_EVENT_TEXTURE_PATH + "{0:D4}/tex_fanraid_complete_{0:D4}";

        /// <summary>
        /// レイドイベントのアイコンパス
        /// </summary>
        public static string GetFanRaidIconPath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_ICON_PATH, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_ICON_PATH = RAID_EVENT_TEXTURE_PATH + "{0:D4}/tex_fanraid_ico_{0:D4}";

        /// <summary>
        /// レイドイベントTOPのボタン画像パス
        /// </summary>
        public static string GetFanRaidTopButtonPath(int eventId, int type)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_TOP_BUTTON_PATH, eventId, type);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_TOP_BUTTON_PATH = RAID_EVENT_TEXTURE_PATH + "{0:D4}/utx_btn_fanraid_top_{1:D2}_{0:D4}";

        /// <summary>
        /// レイドイベントエンディングの背景画像パス
        /// </summary>
        public static string GetFanRaidEndingBgPath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_ENDING_BG_PATH, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_ENDING_BG_PATH = RAID_EVENT_TEXTURE_PATH + "{0:D4}/fanraid_ending_00001";

        /// <summary>
        /// レイドイベント報酬獲得済み画像名
        /// </summary>
        public static string GetFanRaidItemGetStampPath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_ITEM_GET_STAMP_NAME, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_ITEM_GET_STAMP_NAME = "utx_ico_stamp_clear_event_{0:D4}";

        /// <summary>
        /// レイドイベントTOPの背景フレーム画像名
        /// </summary>
        public static string GetFanRaidTopFrameName(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_TOP_BG_NAME, eventId);

            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_TOP_BG_NAME = "utx_frm_underwindow_listbase_event_{0:D4}";

        /// <summary>
        /// ホームのレイドイベントボタンのパス
        /// </summary>
        public const string FAN_RAID_BUTTON_PREFAB_PATH = UIPartsPath + "/FanRaid/PartsFanRaidButton";

        /// <summary>
        /// レイドイベント：キャラアイコンなどのイベントボーナスのパス
        /// </summary>
        public const string FAN_RAID_BONUS_ICON_PREFAB_PATH = UIPartsPath + "/FanRaid/PartsFanRaidBonusIcon";

        /// <summary>
        /// レイドイベント：全体報酬一覧ダイアログ
        /// </summary>
        public const string FAN_RAID_COMMON_REWARD_DIALOG = UIPartsPath + "/FanRaid/DialogFanRaidCommonReward";

        /// <summary>
        /// レイドイベント：個人報酬一覧ダイアログ
        /// </summary>
        public const string FAN_RAID_INDIVIDUAL_REWARD_DIALOG = UIPartsPath + "/FanRaid/DialogFanRaidIndividualReward";

        /// <summary>
        /// レイドイベント：イベントボーナスダイアログ
        /// </summary>
        public const string FAN_RAID_BONUS_DIALOG = UIPartsPath + "/FanRaid/DialogFanRaidBonus";
        
        /// <summary>
        /// レイドイベント：全体報酬獲得条件達成ダイアログ
        /// </summary>
        public const string DIALOG_FAN_RAID_GET_ALL_FAN_REWARD = UIPartsPath + "/FanRaid/DialogFanRaidGetAllFanReward";
        
        public const string FAN_RAID_FLASH_COMBINE_ACTION_ROOT = FLASH_ROOT + "FanRaid/";
        private static string FAN_RAID_UI_EFFECT_ROOT = UI_EFFECT_ROOT + "FanRaid/{0:D4}/";

        /// <summary>
        /// レイドイベント：全体報酬獲得条件達成ダイアログ演出部分
        /// </summary>
        public const string FAN_RAID_GET_ALL_FAN_REWARD_FLASH = FAN_RAID_FLASH_COMBINE_ACTION_ROOT + "pf_fl_fanraid_reward_target00";

        /// <summary>
        /// レイドイベント：全体報酬獲得条件達成ダイアログ：タイトル
        /// </summary>
        public const string FAN_RAID_GET_ALL_FAN_REWARD_TITLE_FLASH = FAN_RAID_FLASH_COMBINE_ACTION_ROOT + "pf_fl_fanraid_title_reward_get00";

        /// <summary>
        /// レイドイベント：全体報酬獲得条件達成ダイアログ：達成人数エフェクト
        /// </summary>
        public static string GetFanRaidDialogGetAllFanRewardNumEffect(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_GET_ALL_FAN_REWARD_NUM_EFFECT_FLASH, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_GET_ALL_FAN_REWARD_NUM_EFFECT_FLASH =  UI_UNITY_ANIMATION_ROOT + "FanRaid/{0:D4}/pfb_fanraid_anim_reward_target_{0:D4}";

        /// <summary>
        /// レイドイベント：全体報酬獲得条件達成ダイアログ：コンプリート演出エフェクト
        /// </summary>
        public static string GetFanRaidDialogGetAllFanRewardCompEffect(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_GET_ALL_FAN_REWARD_COMP_EFFECT_FLASH, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_GET_ALL_FAN_REWARD_COMP_EFFECT_FLASH =  FAN_RAID_UI_EFFECT_ROOT + "pfb_uieff_fanraid_reward_comp_{0:D4}";

        /// <summary>
        /// レイドイベント：育成リザルト：背景用エフェクト
        /// </summary>
        public static string GetFanRaidSingleModeResultBgEffect(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_SINGLE_MODE_RESULT_BG_EFFECT, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_SINGLE_MODE_RESULT_BG_EFFECT =  FAN_RAID_UI_EFFECT_ROOT + "pfb_uieff_fanraid_singlemode_result_bg_{0:D4}";
        
        /// <summary>
        /// レイドイベント：育成リザルト：今回の獲得ファン数エフェクト
        /// </summary>
        public static string GetFanRaidSingleModeResultAddFanEffect(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_SINGLE_MODE_RESULT_ADD_FAN_EFFECT, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_SINGLE_MODE_RESULT_ADD_FAN_EFFECT =  FLASH_COMBINE_ROOT + "Timeline/FanRaid/{0:D4}/tat_fanraid_singlemode_result_addfun_{0:D4}";
        /// <summary>
        /// レイドイベント：育成リザルト：今回の獲得ファン数エフェクト(光の玉)
        /// </summary>
        public static string GetFanRaidSingleModeResultOrbEffect(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_SINGLE_MODE_RESULT_ORB_EFFECT, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_SINGLE_MODE_RESULT_ORB_EFFECT =  FLASH_COMBINE_ROOT + "Timeline/FanRaid/{0:D4}/tat_fanraid_singlemode_result_addscore_{0:D4}";
        /// <summary>
        /// レイドイベント：育成リザルト：総獲得ファン数エフェクト
        /// </summary>
        public static string FAN_RAID_SINGLE_MODE_RESULT_ALL_FAN_EFFECT =  FLASH_COMBINE_ROOT + "Timeline/FanRaid/tat_fanraid_singlemode_result_allfun00";


        /// <summary>
        /// レイドイベント：育成リザルト：ダイアログ全体エフェクト
        /// </summary>
        public static string GetFanRaidSingleModeResultDialogMainEffect(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_SINGLE_MODE_RESULT_DIALOG_MAIN_EFFECT, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_SINGLE_MODE_RESULT_DIALOG_MAIN_EFFECT =  FAN_RAID_UI_EFFECT_ROOT + "pfb_uieff_fanraid_singlemode_result_dialog_{0:D4}";

        /// <summary>
        /// レイドイベント：イベントTOP：全体ファン数背景エフェクト
        /// </summary>
        public static string GetFanRaidTopBgEffectPath(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(RAID_EVENT_TOP_BG_EFFECT_PATH, eventId);
            return _stringBuilder.ToString();
        }
        private static string RAID_EVENT_TOP_BG_EFFECT_PATH = FAN_RAID_UI_EFFECT_ROOT + "pfb_uieff_fanraid_eventtop_bg_{0:D4}";

        /// <summary>
        /// レイドイベント：エンディング：画面遷移アニメーション
        /// </summary>
        public static string GetFanRaidEventResultFadeAnimation(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_EVENT_RESULT_FADE_ANIMATION, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_EVENT_RESULT_FADE_ANIMATION = UI_UNITY_ANIMATION_ROOT + "FanRaid/{0:D4}/pfb_fanraid_anim_eventend_transition_{0:D4}";

        /// <summary>
        /// レイドイベント：エンディング：ファン数カウントアップアニメーション
        /// </summary>
        public static string GetFanRaidEventResultFanNumAnimation(int eventId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(FAN_RAID_EVENT_RESULT_FAN_NUM_ANIMATION, eventId);

            return _stringBuilder.ToString();
        }
        private static string FAN_RAID_EVENT_RESULT_FAN_NUM_ANIMATION = UI_UNITY_ANIMATION_ROOT + "FanRaid/{0:D4}/pfb_fanraid_anim_eventend_allfan_{0:D4}";

        /// <summary>
        /// レイドイベント：全体ファン数が報酬獲得条件を達成したときのエフェクト
        /// </summary>
        public const string FAN_RAID_EVENT_TOP_ACHEVE_EFFECT = UI_UNITY_ANIMATION_ROOT + "FanRaid/pfb_fanraid_anim_eventtop_target00";

        /// <summary>
        /// レイドイベント：イベント全体結果表示用パーツ
        /// </summary>
        public const string FAN_RAID_EVENT_RESULT_PARTS = UIPartsPath + "/FanRaid/PartsFanRaidEventResult";

        /// <summary>
        /// レイドイベント：イベント全体結果のダイアログ：イベント終了Flash
        /// </summary>
        public const string FAN_RAID_RESULT_EVENT_FINISH_FLASH = FAN_RAID_FLASH_COMBINE_ACTION_ROOT + "pf_fl_fanraid_txt_eventend00";

        /// <summary>
        /// レイドイベント：イベント全体結果のダイアログ：みんなの獲得ファン総数Flash
        /// </summary>
        public const string FAN_RAID_RESULT_TOTAL_FAN_FLASH = FLASH_COMBINE_ROOT + "Action/FanRaid/fa_fanraid_eventend_allfan00";

        /// <summary>
        /// レイドイベント：イベント全体結果のダイアログ：イベント終了Flash
        /// </summary>
        public const string FAN_RAID_RESULT_REWARD_ITEM_FLASH = FAN_RAID_FLASH_COMBINE_ACTION_ROOT + "pf_fl_fanraid_eventend_allreward00";

        /// <summary>
        /// レイドイベント：イベントエンディングの画面遷移再生用パーツ
        /// </summary>
        public const string FAN_RAID_EVENT_RESULT_FADE_PARTS = UIPartsPath + "/FanRaid/PartsFanRaidTransitionFade";

        /// <summary>
        /// レイドイベント：育成のシナリオ選択画面の右下のミニボタンのパス
        /// </summary>
        public const string FAN_RAID_BUTTON_MINI_PREFAB_PATH = UIPartsPath + "/FanRaid/PartsFanRaidEventButtonMini";

        #endregion

        #region チーム作りイベント

        /// <summary> チーム作りイベント：スカウトPtのアイコン取得用アイテムID </summary>
        private const int ITEM_ICON_ID_TEAM_BUILDING_SCOUT_PT = 1001;
        /// <summary> チーム作りイベント：チケットのアイコン取得用アイテムID </summary>
        private const int ITEM_ICON_ID_TEAM_BUILDING_TICKET = 2001;

        /// <summary> チーム作りイベント：テクスチャのパス </summary>
        private const string TEAM_BUILDING_TEXTURE_PATH = "TeamBuilding/";

        /// <summary> チーム作りイベント：イベントロゴ </summary>
        public const string TEAM_BUILDING_EVENT_LOGO_PATH = TEAM_BUILDING_TEXTURE_PATH + "utx_txt_teambuilding_logo_00";

        /// <summary> チーム作りイベント：「勝」の文字のテクスチャ </summary>
        public const string TEAM_BUILDING_WIN_NUM_SUFIX_PATH = TEAM_BUILDING_TEXTURE_PATH + "utx_txt_teamscore_win";

        /// <summary>チーム作りイベント：イベントTOPの3D環境設定</summary>
        public const string TEAM_BUILDING_TOP_ENV_PATH = EnvParamRoot + "TeamBuilding/ast_prm_team_building_top";

        /// <summary>チーム作りイベント：マイチーム画面の3D環境設定</summary>
        public const string TEAM_BUILDING_MYTEAM_ENV_PATH = EnvParamRoot + "TeamBuilding/ast_prm_team_building_myteam";

        /// <summary>チーム作りイベント：スカウト画面の3D環境設定</summary>
        public const string TEAM_BUILDING_SCOUT_ENV_PATH = EnvParamRoot + "TeamBuilding/ast_prm_team_building_scout";

        /// <summary> チーム作りイベント：アセバンカテゴリ「TeamBuilding」のパス </summary>
        private const string TEAM_BUILDING_BUNDLE_ROOT = "TeamBuilding/";

        /// <summary> チーム作りイベント：UIパーツのパス </summary>
        private const string TEAM_BUILDING_UI_PARTS_ROOT = ResourcePath.UIPartsPath + "/TeamBuilding/";

        /// <summary> チーム作りイベント：A2U(Flash)のパス </summary>
        private const string TEAM_BUILDING_FLASH_ROOT = FLASH_ROOT + "TeamBuilding/";
        private const string TEAM_BUILDING_FLASH_COMBINE_ACTION_ROOT = FLASH_COMBINE_ACTION_ROOT + "TeamBuilding/";
        private const string TEAM_BUILDING_UI_EFFECT_ROOT = UI_EFFECT_ROOT + "TeamBuilding/";

        /// <summary> チーム作りイベント：ホーム画面のイベントボタン</summary>
        public const string PARTS_TEAM_BUILDING_BUTTON = TEAM_BUILDING_UI_PARTS_ROOT + "PartsTeamBuildingButton";
        /// <summary>チーム作りイベント：レース画面1.5階層の「レースイベント」画面の右下に表示するミニボタン</summary>
        public const string PARTS_TEAM_BUILDING_BUTTON_MINI = TEAM_BUILDING_UI_PARTS_ROOT + "PartsTeamBuildingButtonMini";

        /// <summary>チーム作りイベント：「最高チームランク更新！」ダイアログ</summary>
        public const string DIALOG_TEAM_BUILDING_RANK_UP_CUTIN = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingRankUpCutin";
        /// <summary>チーム作りイベント：「最高チームランク更新！」のタイトルテキストA2U(Flash)</summary>
        public const string TEAM_BUILDING_RANK_UP_TITLE_TEXT_FLASH = TEAM_BUILDING_FLASH_ROOT + "pf_fl_teambuilding_evalution_text00";
        /// <summary>チーム作りイベント：「最高チームランク更新！」のランクアイコンA2U(Flash)</summary>
        public const string TEAM_BUILDING_RANK_UP_RANK_ICON_FLASH = TEAM_BUILDING_FLASH_COMBINE_ACTION_ROOT + "fa_teambuilding_evalution_rank00";
        /// <summary>チーム作りイベント：「最高チームランク更新！」の背景の灰色エンブレムのA2U(Flash)</summary>
        public const string TEAM_BUILDING_GRAY_EMBLEM_EMBLEM_FLASH = TEAM_BUILDING_FLASH_ROOT + "pf_fl_teambuilding_evalution_emblem00";

        /// <summary>チーム作りイベント：「開催レース更新！」カットイン</summary>
        public const string TEAM_BUILDING_RACE_REFLESH_CUTIN = TEAM_BUILDING_UI_PARTS_ROOT + "TeamBuildingRaceRefleshCutin";
        /// <summary>チーム作りイベント：「開催レース更新！」カットインのA2U(Flash)</summary>
        public const string TEAM_BUILDING_RACE_REFLESH_CUTIN_FLASH = TEAM_BUILDING_FLASH_COMBINE_ACTION_ROOT + "fa_teambuilding_txt_raceupdate00";

        /// <summary>チーム作りイベント：対戦相手選択画面の「出走確認」ダイアログ</summary>
        public const string DIALOG_TEAM_BUILD_USE_TICKET = UIPartsPath + "/TeamBuilding/DialogTeamBuildingUseTicket";

        /// <summary>チーム作りイベント：対戦相手選択画面の「キャプテン適性確認」ダイアログ</summary>
        public const string DIALOG_TEAM_BUILDING_CAPTAIGN_PROPER = UIPartsPath + "/TeamBuilding/DialogTeamBuildingCaptaignProper";

        /// <summary>チーム作りイベント：対戦相手選択画面の「次走出走レース」ダイアログ</summary>
        public const string DIALOG_TEAM_BUILDING_NEXT_RACE = UIPartsPath + "/TeamBuilding/DialogTeamBuildingNextRace";

        /// <summary>チーム作りイベント：VSカットインの背景A2U(Flash)</summary>
        public const string TEAM_BUILDING_VS_CUTIN_BG_FLASH = TEAM_BUILDING_FLASH_COMBINE_ACTION_ROOT + "fa_teambuilding_ready_teambg00";
        /// <summary>チーム作りイベント：VSカットインのマイチームネームプレートA2U(Flash)</summary>
        public const string TEAM_BUILDING_VS_CUTIN_MY_TEAM_NAME_PLATE_FLASH = TEAM_BUILDING_FLASH_ROOT + "pf_fl_teambuilding_ready_name00";
        /// <summary>チーム作りイベント：VSカットインの相手チームネームプレートA2U(Flash)</summary>
        public const string TEAM_BUILDING_VS_CUTIN_OPPONENT_TEAM_NAME_PLATE_FLASH = TEAM_BUILDING_FLASH_ROOT + "pf_fl_teambuilding_ready_name01";
        /// <summary>チーム作りイベント：VSカットインの「カメラ位置向き画角」「3Dキャラ向き」のパラメータ</summary>
        public const string TEAM_BUILDING_VS_CUTIN_CHARA_PARAM_AST = TEAM_BUILDING_BUNDLE_ROOT + "VsCutin/ast_TeamBuildingVsCutinCharaParam";
        /// <summary>チーム作りイベント：VSカットインのキャラの色身を決めるエフェクトのパラメータ</summary>
        public const string TEAM_BUILDING_VS_CUTIN_IMAGE_EFFECT_AST = TEAM_BUILDING_BUNDLE_ROOT + "VsCutin/ast_param_imageffect_team_building_vs_cutin";

        /// <summary>チーム作りイベント：「スカウトレースチケット回復」ダイアログ（使用アイテム選択）</summary>
        public const string DIALOG_TEAM_BUILDING_TICKET_BUY_LIST = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingTicketBuyList";

        /// <summary> チーム作りイベント：マイチーム走法変更ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_CHANGE_RUN_STYLE = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingRunStyleChange";

        /// <summary> チーム作りイベント：パドックの「レース詳細」ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_RACE_DETAIL = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingRaceDetail";

        /// <summary> チーム作りイベント：レースリザルトの「累計勝利数報酬獲得」ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_TOTAL_WIN_REWARD_PATH = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingTotalWinReward";

        /// <summary> チーム作りイベント：レースリザルトの「チームの軌跡更新」ダイアログ  </summary>
        public const string DIALOG_TEAM_BUILDING_UPDATE_COLLECTION = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingUpdateCollection";

        /// <summary> チーム作りイベント：レースリザルトの「コンティニュー」ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_CONTINUE = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingContinue";

        /// <summary> チーム作りイベント：レースリザルトの「スカウトタイム」カットインのA2U(Flash)</summary>
        public const string TEAM_BUILDING_SCOUT_TIME_FLASH = TEAM_BUILDING_FLASH_ROOT + "pf_fl_teambuilding_txt_scouttime00";

        /// <summary> チーム作りイベント：レース名画像 </summary>
        public const string TEAM_BUILDING_RACE_TITLE_FRAME_IMAGE = RACE_TITLE_IMAGE_ROOT + "tex_frm_raceinfo_logobase_18";
        public const string TEAM_BUILDING_RACE_TITLE_IMAGE = RACE_TITLE_IMAGE_ROOT + "tex_race_rt_000_15001_00";

        /// <summary> チーム作りイベント：スカウト画面のスカウト実行確認ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_SCOUT_CONFIRM = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingScoutConfirm";
        /// <summary> チーム作りイベント：スカウト画面のメンバー重複確認ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_DUPLICATE_CHARA = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingNoticeDuplicateChara";

        /// <summary> チーム作りイベント：スカウト画面の「スカウト完了」ダイアログ/「スカウト終了」ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_SCOUT_END = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingScoutEnd";
        /// <summary> チーム作りイベント：スカウト画面の「スカウト完了」ダイアログの紙吹雪エフェクト </summary>
        public const string TEAM_BUILDING_SCOUT_END_EFFECT = TEAM_BUILDING_UI_EFFECT_ROOT + "pfb_uieff_teambuilding_scout_particle00";

        /// <summary> チーム作りイベント：スカウト画面の3dキャラビューワー </summary>
        public const string TEAM_BUILDING_SCOUT_CHARA_VIEWER_PATH = "Prefabs/TeamBuilding/TeamBuildingScoutCharaViewer";

        /// <summary>チーム作りイベント：スカウト画面の3dキャラビューワーのパラメタ</summary>
        public const string TEAM_BUILDING_SCOUT_CHARA_PARAM_AST = TEAM_BUILDING_BUNDLE_ROOT + "Scout/ast_TeamBuildingScoutCharaViewerSettingData";

        /// <summary> チーム作りイベント：チームランク報酬・累計勝利数報酬ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_REWARD_LIST = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingRewardList";

        /// <summary> チーム作りイベント：オプションダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_OPTION = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingOption";

        /// <summary> チーム作りイベント：チーム名変更ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_CHANGE_TEAM_NAME = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingChangeTeamName";

        /// <summary> チーム作りイベント：キャプテン変更確認ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_CONFIRM_CHANGE_CAPTAIN = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingConfirmChangeCaptain";

        /// <summary> チーム作りイベント：図鑑組み合わせ詳細ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_COLLECTION_SET_DETAIL = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingCollectionSetDetail";
        /// <summary> チーム作りイベント：図鑑：「獲得スカウトPtボーナス」ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_COLLECTION_SCOUT_PT_BONUS = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingCollectionScoutPtBonus";

        /// <summary> チーム作りイベント：イベント通常遷移TOP画面のロゴエフェクト </summary>
        public const string TEAM_BUILDING_LOGO_EFFECT_NORMAL = TEAM_BUILDING_UI_EFFECT_ROOT + "pfb_uieff_teambuilding_eventlogo00";
        /// <summary> チーム作りイベント：イベント初回遷移TOP画面のロゴエフェクト </summary>
        public const string TEAM_BUILDING_LOGO_EFFECT_FIRST = TEAM_BUILDING_UI_EFFECT_ROOT + "pfb_uieff_teambuilding_eventlogo01";
        /// <summary> チーム作りイベント：初回遷移のチーム登録ダイアログ </summary>
        public const string DIALOG_TEAM_BUILDING_REGISTER_TEAM = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingRegisterTeam";

        /// <summary>チーム作りイベント：「イベント開始」カットインダイアログ</summary>
        public const string DIALOG_TEAM_BUILDING_EVENT_START_CUTIN = TEAM_BUILDING_UI_PARTS_ROOT + "DialogTeamBuildingEventStartCutin";
        /// <summary>チーム作りイベント：「イベント開始」カットインのA2U(Flash)</summary>
        public const string TEAM_BUILDING_EVENT_START_CUTIN_FLASH = TEAM_BUILDING_FLASH_COMBINE_ACTION_ROOT + "fa_teambuilding_notice_eventstart00";

        public const string TEAM_BUILDING_SCOUT_NAME_FLASH = TEAM_BUILDING_FLASH_ROOT + "pf_fl_teambuilding_color_teamribbon00";

        /// <summary>
        /// チーム作りイベント：チームランク用フレーム画像
        /// </summary>
        public static string GetTeamBuildingTeamRankFrame(TeamBuildingDefine.TeamRankGroup group, bool isSmall = true)
        {
            _stringBuilder.Length = 0;
            var path = isSmall ? TEAM_BUILDING_RANK_FRAME_FORMAT_S : TEAM_BUILDING_RANK_FRAME_FORMAT_M;
            _stringBuilder.AppendFormat(path, (int)group);
            return _stringBuilder.ToString();
        }
        private const string TEAM_BUILDING_RANK_FRAME_FORMAT_S = TEAM_BUILDING_TEXTURE_PATH + "tex_teambuilding_frm_{0:D3}";
        private const string TEAM_BUILDING_RANK_FRAME_FORMAT_M = TEAM_BUILDING_TEXTURE_PATH + "tex_teambuilding_frm_m_{0:D3}";

        /// <summary>
        /// チーム作りイベント：チームランク用アイコン画像
        /// </summary>
        public static string GetTeamBuildingTeamRankIcon(TeamBuildingDefine.TeamRank rank, bool isSmall = true)
        {
            _stringBuilder.Length = 0;
            var path = isSmall ? TEAM_BUILDING_RANK_ICON_FORMAT_S : TEAM_BUILDING_RANK_ICON_FORMAT_M;
            _stringBuilder.AppendFormat(path, (int)rank);
            return _stringBuilder.ToString();
        }
        private const string TEAM_BUILDING_RANK_ICON_FORMAT_S = TEAM_BUILDING_TEXTURE_PATH + "tex_teambuilding_icon_{0:D3}";
        private const string TEAM_BUILDING_RANK_ICON_FORMAT_M = TEAM_BUILDING_TEXTURE_PATH + "tex_teambuilding_icon_m_{0:D3}";

        #endregion

        #region その他

        /// <summary>
        /// チーム競技場、育成追加の待機画面キャラ定義アセット
        /// </summary>
        public const string TEAMSTANBY_DEFINE_DATA_PATH = "3d/ast_teamstadium_stanby";

        #endregion
    }
}
