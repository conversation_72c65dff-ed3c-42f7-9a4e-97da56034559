#if CYG_DEBUG
#define USE_DOWNLOADLEAK_DETECTOR
#endif
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using Gallop.Tutorial;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    public partial class AudioManager : MonoSingleton<AudioManager>
    {
        private const string ACB_EXTENSION = ".acb";
        private const string AWB_EXTENSION = ".awb";
        public const string SOUND_FOLDER = "Sound/";

        // Gallop独自のサブフォルダ
        public const string CATEGORY_SUBFOLDER_STORY = "c/";       // ストーリー
        public const string CATEGORY_SUBFOLDER_JIKKYOU = "j/";     // 実況
        private const string CATEGORY_SUBFOLDER_LIVE = "l/";        // ライブ

        // タイトルボイスのパス
        public const string TITLE_VOICE = "snd_voi_tc_";

        // サブフォルダ
        public enum SubFolder
        {
            Bgm,
            Se,
            Voice,
            Story,
            Jikky<PERSON>,
            Live,
        }

        /// <summary>
        /// 指定アセットが存在するか
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static bool IsExistAsset(string filePath)
        {
            if (AssetBundleHelper.UsingAssetBundleAudio())
            {
                return ResourceManager.IsExistAsset(filePath);
            }

            //プリインのリソースあり・なしをチェックしようとすると、
            //ファイルをロードする必要があって高コスト。
            //本番ではチェックしないので、そのような場合には使用しない事!!
#if CYG_DEBUG || UNITY_EDITOR
            return System.IO.File.Exists(ResourcePath.NotPreinStreamingAssetsPath + filePath);
#else
            return true;
#endif
        }

        #region ボイスダウンロード設定

        /// <summary>
        /// エピソード中のボイスを再生するか。エピソード開始時ダイアログの選択に応じて決まる（デフォルトは再生する）
        /// </summary>
        /// <remarks>
        /// この値はエピソード実行中のみ値が設定されている
        /// 前回の設定値を参照したい場合は 「SaveDataManager.Instance.SaveLoader.EpisodeStartSettingIsNeedVoiceDownload」を使うこと
        /// </remarks>
        public bool IsPlayVoiceOnEpisode { get; set; } = true;

        #endregion

        #region キューシートロード判定

        /// <summary>
        /// キューシートがロード済みで有効か
        /// </summary>
        /// <param name="cueSheetName">cueSheetName</param>
        /// <returns>有効/無効</returns>
        public static bool IsAvailableCueSheet(string cueSheetName)
        {
            return Cute.Cri.AudioManager.Instance.IsAvailableCueSheet(cueSheetName);
        }

        #endregion

        #region キューシートダウンロード登録

        //-------------------------------------------------------------------------
        // ビュー開始前のリソース準備手順
        // ①、使用するサウンドアセットのリストを作成する(AudioId、CueName、CueSheet、キャラクターID)
        // ②、①のリスト使ってダウンロード登録する
        //      RegisterDownloadByAudioIds()
        //      RegisterDownloadByCueNames()
        //      RegisterDownloadByCueSheets()
        //      RegisterDownloadByCharaIds()
        // ③、ダウンロード完了後、キューシートをロードする
        //      AddCueSheetByAudioId(), AddCueSheetByAudioIds()
        //      AddCueSheetByCueName(), AddCueSheetByCueNames()
        //      AddCueSheet(),AddCueSheetByCharaIds()
        //-------------------------------------------------------------------------

        //-------------------------------------------------------------------------
        /// <summary>
        /// キューシートダウンロード登録(AudioId指定)
        /// </summary>
        /// <param name="audioIdList">AudioIdリスト</param>
        public void RegisterDownloadByAudioIds(DownloadPathRegister register, IReadOnlyCollection<AudioId> audioIdList)
        {
            RegisterDownload(GetCueSheetPathListByAudioIds(audioIdList), register);
        }
        public void RegisterDownloadByAudioId(DownloadPathRegister register, AudioId audioId)
        {
            RegisterDownloadByAudioIds(register, new List<AudioId>() { audioId });
        }

        /// <summary>
        /// キューシートダウンロード登録(キュー名指定)
        /// </summary>
        /// <param name="cueNameList">キュー名リスト</param>
        public void RegisterDownloadByCueNames(DownloadPathRegister register, List<string> cueNameList)
        {
            RegisterDownload(GetCueSheetPathListByCueNames(cueNameList), register);
        }

        /// <summary>
        /// キューシートダウンロード登録(キューシート名指定)
        /// </summary>
        /// <param name="cueSheetList">キューシート名リスト</param>
        /// <param name="subFolder">サブフォルダ</param>
        public void RegisterDownloadByCueSheets(DownloadPathRegister register, List<string> cueSheetList, SubFolder subFolder)
        {
            RegisterDownloadByCueSheetsStatic(register, cueSheetList, subFolder);
        }
        public void RegisterDownloadByCueSheet(DownloadPathRegister register, string cueSheet, SubFolder subFolder)
        {
            RegisterDownloadByCueSheets(register, new List<string>() { cueSheet }, subFolder);
        }

#if UNITY_EDITOR
        public static void RegisterDownloadByCueSheetsForEditor(DownloadPathRegister register, List<string> cueSheetList,
            SubFolder subFolder)
        {
            RegisterDownloadByCueSheetsStatic(register, cueSheetList, subFolder);
        }
#endif

        private static void RegisterDownloadByCueSheetsStatic(DownloadPathRegister register, List<string> cueSheetList,
            SubFolder subFolder)
        {
            // システム設定・ボイスダウンロード
            if (CanDownloadSubFolder(subFolder) == false)
            {
                return;
            }

            RegisterDownload(GetCueSheetPathList(cueSheetList, subFolder), register);
        }

        /// <summary>
        /// キャラクターシステムボイスのキューシートダウンロード登録
        /// </summary>
        /// <param name="charaIdList">キャラクターIDリスト</param>
        public void RegisterDownloadByCharaIds(DownloadPathRegister register, List<int> charaIdList, CharacterSystemTextGroupExtension.Scene scene, bool isUseLipSync = false)
        {
            //キャラのシートをダウンロードする際にはリップシンクもついてくるので必要に応じてダウンロードする
            if (isUseLipSync)
            {
                for (int i = 0, icnt = charaIdList.Count; i < icnt; i++)
                {
                    var charaId = charaIdList[i];
                    List<MasterCharacterSystemText.CharacterSystemText> list = MasterCharacterSystemText.GetByScene(charaId, scene);
                    if (list == null)
                    {
                        continue;
                    }
                    for (int j = 0, jcnt = list.Count; j < jcnt; j++)
                    {
                        if ((list[j] == null) || string.IsNullOrEmpty(list[j].LipSyncData))
                        {
                            continue;
                        }
                        register.RegisterPathWithoutInfo(ResourcePath.GetCharacterSystemLipsyncPath(charaId, list[j].LipSyncData));
                    }
                }
            }

            var subFolder = SubFolder.Voice;
            // システム設定・ボイスダウンロード
            if (CanDownloadSubFolder(subFolder) == false)
            {
                return;
            }
            RegisterDownload(GetCueSheetPathList(GetCueSheetListByCharaIds(charaIdList, scene), subFolder), register);
        }


        /// <summary>
        /// タイトルのキャラクターボイスをダウンロードとセーブ
        /// </summary>
        /// <param name="charaIdList"></param>
        public void RegisterDownloadTitleVoiceByCharaIds(DownloadPathRegister register, List<int> charaIdList)
        {
            var subFolder = SubFolder.Voice;
            // システム設定・ボイスダウンロード
            if (CanDownloadSubFolder(subFolder) == false)
            {
                return;
            }
            RegisterDownload(GetCueSheetPathList(GetTitleCueSheetListByCharaIds(charaIdList), subFolder), register);
        }


        /// <summary>
        /// キャラ、トリガーを指定しキャラクターシステムボイスのキューシートダウンロード登録
        /// </summary>
        /// <param name="limitedChara">キャラ限定ボイスの場合true</param>
        public void RegisterDownloadByTriggerAndCharaIds(DownloadPathRegister register, List<int> charaIdList, List<CharacterSystemLotteryTrigger> triggerList, bool limitedChara = false)
        {
            var subFolder = SubFolder.Voice;
            // システム設定・ボイスダウンロード
            if (CanDownloadSubFolder(subFolder) == false)
            {
                return;
            }

            var sysTextList = new List<MasterCharacterSystemText.CharacterSystemText>();

            void AddSysText(List<MasterCharacterSystemLottery.CharacterSystemLottery> lotteryList)
            {
                foreach (var lottery in lotteryList)
                {
                    foreach (var charaId in charaIdList)
                    {
                        var sysText = MasterDataManager.Instance.masterCharacterSystemText.Get(charaId, lottery.SysTextId);
                        if (sysText == null) continue;
                        sysTextList.Add(sysText);
                    }
                }
            }

            if (limitedChara)
            {
                //キャラ限ボイス
                foreach (var t in triggerList)
                {
                    foreach (var charaId in charaIdList)
                    {
                        var lotteryList = MasterDataManager.Instance.masterCharacterSystemLottery.GetListWithCharaIdAndTrigger(charaId, (int)t);
                        AddSysText(lotteryList);
                    }
                }
            }
            else
            {
                //汎用ボイス
                foreach (var t in triggerList)
                {
                    var lotteryList = MasterDataManager.Instance.masterCharacterSystemLottery.GetListWithCharaIdAndTrigger(0, (int)t);
                    AddSysText(lotteryList);
                }
            }

            RegisterDownload(GetCueSheetPathList(ToCueSheetList(sysTextList), subFolder), register);
        }

        //-------------------------------------------------------------------------
        /// <summary>
        /// キューシートパスリストをダウンロード登録する
        /// </summary>
        /// <param name="cueSheetPathList">キューシートパスリスト</param>
        /// <param name="register">リソース登録クラス</param>
        private static void RegisterDownload(List<string> cueSheetPathList, DownloadPathRegister register)
        {
            for (int i = 0, cnt = cueSheetPathList.Count; i < cnt; i++)
            {
                register.RegisterPathWithoutInfo(cueSheetPathList[i]);
            }
        }

        /// <summary>
        /// 対象のフォルダがダウンロード対象か判定。
        /// オプションによりボイスがダウンロード対象外となる
        /// </summary>
        private static bool CanDownloadSubFolder(SubFolder subFolder)
        {
            return true;
        }

        /// <summary>
        /// 指定のサブフォルダ名が「ボイスダウンロード」オプションによるダウンロード切り替え対象かを判定
        /// </summary>
        /// <param name="subFolderString">サブフォルダ名。 "b/", "j/" のような形式で指定する。 </param>
        public static bool IsVoiceDownloadTarget(string subFolderString)
        {
            // 除外対象はストーリー・ボイス・実況なので各々判定
            string storyPath = GetSubFolderPath(SubFolder.Story);
            if( storyPath == subFolderString ) return false;

            string voicePath = GetSubFolderPath(SubFolder.Voice);
            if( voicePath == subFolderString ) return false;

            string jikkyoPath = GetSubFolderPath(SubFolder.Jikkyo);
            if( jikkyoPath == subFolderString ) return false;

            return true;
        }

        public static IEnumerable<string> VoiceDownloadTargetExcludeSubdirectories()
        {
            return new string[]
            {
                GetSubFolderPath(SubFolder.Story),
                GetSubFolderPath(SubFolder.Voice),
                GetSubFolderPath(SubFolder.Jikkyo),
            };
        }

        #endregion

        #region キューシートダウンロードパス取得

        //-------------------------------------------------------------------------
        // キューシートパスリストを取得(AudioId指定)
        public List<string> GetCueSheetPathListByAudioIds(IReadOnlyCollection<AudioId> audioIdList)
        {
            var paths = new List<string>();
            foreach(var id in audioIdList)
            {
                AddCueSheetPath(paths,GetAudioIdData(id));
            }
            return paths;
        }

        // キューシートパスリストを取得(キュー名指定)
        private List<string> GetCueSheetPathListByCueNames(List<string> cueNameList)
        {
            List<string> paths = new List<string>();
            cueNameList.ForEach(elem => AddCueSheetPath(paths, GetAudioIdData(elem)));
            return paths;
        }

        // キューシートパス取得(キューシート指定)
        public static List<string> GetCueSheetPathList(List<string> list, SubFolder subFolder)
        {
            List<string> paths = new List<string>();
            for (int i = 0, cnt = list.Count; i < cnt; i++)
            {
                AddCueSheetPath(paths, list[i], subFolder);
            }
            return paths;
        }

        private void AddCueSheetPath(List<string> paths, AudioDefine.AudioIdData data)
        {
            AddCueSheetPath(paths, data._cueSheet, SoundGroupToSubFolder(data._soundGroup));
        }

        private static void AddCueSheetPath(List<string> paths, string cueSheet, SubFolder subFolder)
        {
            if (IsPreInstallCueSheet(cueSheet))
            {
                return; //プリインストールキューシートはダウンロードパスとしない
            }
            string acbFilePath, awbFilePath;
            GetDownloadFilePath(subFolder, cueSheet, out acbFilePath, out awbFilePath);
            if (!string.IsNullOrEmpty(acbFilePath)) paths.Add(acbFilePath);
            if (!string.IsNullOrEmpty(awbFilePath)) paths.Add(awbFilePath);
        }

        // キューシートがプリインストールか
        private static bool IsPreInstallCueSheet(string cueSheet)
        {
            return PREIN_CUESHEET_LIST.Exists(elem => elem == cueSheet);
        }

        //-----------------------------------------------------------------------------
        /// <summary>
        /// キャラクターシステムボイスのキューシートパスリストを取得
        /// </summary>
        /// <param name="charaIdList"></param>
        /// <param name="scene"></param>
        /// <returns></returns>
        public List<string> GetCueSheetPathListByCharaIds(List<int> charaIdList, CharacterSystemTextGroupExtension.Scene scene)
        {
            var subFolder = SubFolder.Voice;
            // システム設定・ボイスダウンロード
            if (CanDownloadSubFolder(subFolder) == false)
            {
                return new List<string>();
            }
            return GetCueSheetPathList(GetCueSheetListByCharaIds(charaIdList, scene), subFolder);
        }

        // キャラクターボイスのキューシート取得
        public static List<string> GetCueSheetListByCharaIds(List<int> charaIdList, CharacterSystemTextGroupExtension.Scene scene)
        {
            List<string> cueSheetList = new List<string>();
            for (int i = 0, cnt = charaIdList.Count; i < cnt; i++)
            {
                List<MasterCharacterSystemText.CharacterSystemText> list = MasterCharacterSystemText.GetByScene_DistinctByCueSheet(charaIdList[i], scene);
                if (list == null) continue;
                cueSheetList.AddRange(ToCueSheetList(list));
            }
            return cueSheetList;
        }

        // タイトルのキャラクターボイスのキューシート取得
        private static List<string> GetTitleCueSheetListByCharaIds(List<int> charaIdList)
        {
            List<string> cueSheetList = new List<string>();
            for (int i = 0, cnt = charaIdList.Count; i < cnt; i++)
            {
                var st = TITLE_VOICE + charaIdList[i].ToString();
                cueSheetList.Add(st);
            }
            return cueSheetList;
        }

        // タイトルのキャラクターボイスをセーブ
        public void SaveTitleVoiceCharacterId()
        {
            var characterTitleVoiceArray = SaveDataManager.Instance.SaveLoader.CharacterTitleVoiceArray;
            List<int> charaIdList = WorkDataManager.Instance.CharaData.GetList().Select(x => x.Id.GetDecrypted()).ToList();
            if (characterTitleVoiceArray.Length != charaIdList.Count)
            {
                List<ObscuredInt> characterTitleVoice = new List<ObscuredInt>();
                characterTitleVoice.AddRange(charaIdList.Select(x => (ObscuredInt)x));
                SaveDataManager.Instance.SaveLoader.CharacterTitleVoiceArray = characterTitleVoice.ToArray();
                SaveDataManager.Instance.Save();
            }
        }

        // キューシートリスト取り出し
        private static List<string> ToCueSheetList(List<MasterCharacterSystemText.CharacterSystemText> list)
        {
            List<string> result = new List<string>();
            list.ForEach(item => result.Add(item.CueSheet));
            return result;
        }

        //-----------------------------------------------------------------------------
        // キューシートパス取得
        public static void GetDownloadFilePath(SubFolder subFolder, string cueSheet, out string acbFilePath, out string awbFilePath)
        {
            GetDownloadFilePath(GetSubFolderPath(subFolder), cueSheet, out acbFilePath, out awbFilePath);
        }

        // キューシートパス取得(サブフォルダ指定)
        private static void GetDownloadFilePath(string subFolderPath, string cueSheet, out string acbFilePath, out string awbFilePath)
        {
            acbFilePath = SOUND_FOLDER + subFolderPath + cueSheet + ACB_EXTENSION;
            awbFilePath = SOUND_FOLDER + subFolderPath + cueSheet + AWB_EXTENSION;
        }

        // サブフォルダ変換
        private static SubFolder SoundGroupToSubFolder(Cute.Cri.SoundGroup group)
        {
            switch (group)
            {
                case Cute.Cri.SoundGroup.Bgm: return SubFolder.Bgm;
                case Cute.Cri.SoundGroup.Se: return SubFolder.Se;
                case Cute.Cri.SoundGroup.Voice: return SubFolder.Voice;
            }
            return SubFolder.Bgm;
        }

        // サブフォルダパス取得
        private static string GetSubFolderPath(SubFolder folder)
        {
            switch (folder)
            {
                case SubFolder.Bgm: return Cute.Cri.AudioManager.GetSubFolderPath(Cute.Cri.SoundGroup.Bgm);
                case SubFolder.Se: return Cute.Cri.AudioManager.GetSubFolderPath(Cute.Cri.SoundGroup.Se);
                case SubFolder.Voice: return Cute.Cri.AudioManager.GetSubFolderPath(Cute.Cri.SoundGroup.Voice);
                case SubFolder.Live: return CATEGORY_SUBFOLDER_LIVE;
                case SubFolder.Story: return CATEGORY_SUBFOLDER_STORY;
                case SubFolder.Jikkyo: return CATEGORY_SUBFOLDER_JIKKYOU;
            }
            return "";
        }

        #endregion

        #region キューシートロード

        /// <summary>
        /// キューシートロード(サブフォルダ指定)
        /// </summary>
        /// <param name="cueSheetName">キューシート名</param>
        /// <param name="folder">サブフォルダ</param>
        /// <returns></returns>
        public CriAtomCueSheet AddCueSheet(string cueSheetName, SubFolder folder)
        {
#if CYG_DEBUG
            DebugCheckDownload(cueSheetName, folder);
#endif
            return CriAudioManager.AddCueSheet(cueSheetName, GetSubFolderPath(folder));
        }

        /// <summary>
        /// キューシートロード(AudioId指定)
        /// </summary>
        /// <param name="audioId">AudioId</param>
        /// <returns>キューシートオブジェクト</returns>
        public CriAtomCueSheet AddCueSheetByAudioId(AudioId audioId)
        {
            return AddCueSheet(GetAudioIdData(audioId));
        }

        /// <summary>
        /// キューシートロード(AudioId指定)
        /// </summary>
        /// <param name="audioIdList">AudioIdリスト</param>
        public void AddCueSheetByAudioIds(List<AudioId> audioIdList)
        {
            audioIdList.ForEach(elem => AddCueSheetByAudioId(elem));
        }

        /// <summary>
        /// キューシートロード(キュー名指定)
        /// </summary>
        /// <param name="cueName">キュー名</param>
        /// <returns>キューシートオブジェクト</returns>
        public CriAtomCueSheet AddCueSheetByCueName(string cueName)
        {
            return AddCueSheet(GetAudioIdData(cueName));
        }

        /// <summary>
        /// キューシートロード(キュー名指定)
        /// </summary>
        /// <param name="cueNameList">キュー名リスト</param>
        public void AddCueSheetByCueNames(List<string> cueNameList)
        {
            cueNameList.ForEach(elem => AddCueSheetByCueName(elem));
        }


        //-----------------------------------------------------------------------------
        // キャラクターボイスのキューシートロード
        public void AddCueSheetByCharaIds(List<int> charaIdList, CharacterSystemTextGroupExtension.Scene scene)
        {
            List<string> cueSheetList = GetCueSheetListByCharaIds(charaIdList, scene);
            cueSheetList.ForEach(sheet => AddCueSheet(sheet, SubFolder.Voice));
        }

        /// <summary>
        /// Cute.Criへのつなぎ込み
        /// </summary>
        private CriAtomCueSheet AddCueSheet(AudioDefine.AudioIdData data)
        {
            return AddCueSheet(data._cueSheet, SoundGroupToSubFolder(data._soundGroup));
        }

        #endregion

        #region キューシートアンロード

        /// <summary>
        /// キューシートアンロード(AudioId指定)
        /// </summary>
        /// <param name="audioId">AudioId</param>
        public void RemoveCueSheetByAudioId(AudioId audioId)
        {
            RemoveCueSheet(GetAudioIdData(audioId)._cueSheet);
        }

        /// <summary>
        /// キューシートアンロード(AudioId指定)
        /// </summary>
        /// <param name="audioIdList">AudioIdリスト</param>
        public void RemoveCueSheetByAudioIds(List<AudioId> audioIdList)
        {
            audioIdList.ForEach(elem => RemoveCueSheetByAudioId(elem));
        }

        /// <summary>
        /// キューシートアンロード(キュー名指定)
        /// </summary>
        /// <param name="cueName">キュー名</param>
        public void RemoveCueSheetByCueName(string cueName)
        {
            RemoveCueSheet(GetAudioIdData(cueName)._cueSheet);
        }

        /// <summary>
        /// キューシートアンロード(キュー名指定)
        /// </summary>
        /// <param name="cueNameList">キュー名リスト</param>
        public void RemoveCueSheetByCueNames(List<string> cueNameList)
        {
            cueNameList.ForEach(elem => RemoveCueSheetByCueName(elem));
        }

        /// <summary>
        /// キューシートアンロード
        /// </summary>
        public static void RemoveCueSheet(string cueSheetName)
        {
            if (!string.IsNullOrEmpty(cueSheetName))
            {
                Cute.Cri.AudioManager.Instance.RemoveCueSheet(cueSheetName);
            }
        }

        #endregion

        #region キューシートリソース管理

        //  シーンごとのシート属性
        private static readonly Dictionary<SceneDefine.SceneId, CueSheetAttribute> SCENE_ATTRIBUTE_DIC = new Dictionary<SceneDefine.SceneId, CueSheetAttribute>()
        {
            { SceneDefine.SceneId.Title     , CueSheetAttribute.SceneTitle      },
            { SceneDefine.SceneId.Home      , CueSheetAttribute.SceneHome       },
            { SceneDefine.SceneId.Race      , CueSheetAttribute.SceneRace       },
            { SceneDefine.SceneId.Story     , CueSheetAttribute.SceneStory      },
            { SceneDefine.SceneId.Live      , CueSheetAttribute.SceneLive       },
        };
        //  シーン判別できないビューごとのシート属性
        private static readonly Dictionary<SceneDefine.ViewId, CueSheetAttribute> VIEW_ATTRIBUTE_DIC = new Dictionary<SceneDefine.ViewId, CueSheetAttribute>()
        {
            // 新仕様画面精査後に追加
            //{ SceneDefine.ViewId.GachaResult       , CueSheetAttribute.SceneGacha  },
        };

        private List<AudioId> _chengeViewAudioIdList = new List<AudioId>(1);
        private List<string> _chengeViewCueSheetList = new List<string>(16);

        // シーンビューのキューシート属性取得
        private static CueSheetAttribute GetSceneViewAttribute(SceneDefine.SceneId nextScene, SceneDefine.ViewId nextView)
        {
            if (VIEW_ATTRIBUTE_DIC.TryGetValue(nextView, out var attribute))
            {
                return attribute;
            }

            if (SCENE_ATTRIBUTE_DIC.TryGetValue(nextScene,  out attribute))
            {
                return attribute;
            }
            return CueSheetAttribute.None;
        }

        /// <summary>
        /// シーンビュー切り替わり時のダウンロード登録
        /// </summary>
        public void OnChangeSceneViewCollectResources(DownloadPathRegister register, SceneDefine.ViewId nextView)
        {
            var pathList = OnChangeSceneViewCollectResourcePaths(nextView);
            register.RegisterPath(pathList.ToArray());
        }
        public List<string> OnChangeSceneViewCollectResourcePaths(SceneDefine.ViewId nextView)
        {
            var retPathList = new List<string>(32);

            if (!SceneDefine.TryGetViewData(nextView, out var viewData))
            {
                return retPathList;
            }
            CueSheetAttribute attribute = GetSceneViewAttribute(viewData.SceneId, nextView);

            // SE
            if (attribute != CueSheetAttribute.None)
            {
                var alwaysMasterList = GetCueSheetsByAttribute(CueSheetAttribute.Always);
                var nextMasterList = GetCueSheetsByAttribute(attribute);
                var needMasterList = new List<MasterAudioCuesheet.AudioCuesheet>();
                needMasterList.AddRange(alwaysMasterList);
                needMasterList.AddRange(nextMasterList);
                _chengeViewCueSheetList.Clear();
                for (int i = 0; i < needMasterList.Count; i++)
                {
                    _chengeViewCueSheetList.Add(needMasterList[i].CueSheet);
                }
                retPathList.AddRange(GetCueSheetPathList(_chengeViewCueSheetList, SubFolder.Se));
            }

            // BGM
            if (viewData.BgmAudioId != AudioId.INVALID)
            {
                _chengeViewAudioIdList.Clear();
                _chengeViewAudioIdList.Add(viewData.BgmAudioId);
                retPathList.AddRange(GetCueSheetPathListByAudioIds(_chengeViewAudioIdList));
            }

            return retPathList;
        }

        /// <summary>
        /// シーンビュー切り替わり時のダウンロード完了後のキューシートロード
        /// 次のシーンビューに不要なキューシートを開放します
        /// </summary>
        /// <param name="nextView">次のビューID</param>
        public void OnChangeSceneInitializeView(SceneDefine.ViewId nextView)
        {
            if (!SceneDefine.TryGetViewData(nextView, out var viewData))
            {
                return;
            }

            CueSheetAttribute attribute = CueSheetAttribute.None;
            if (viewData != null)
            {
                attribute = GetSceneViewAttribute(viewData.SceneId, nextView);
            }

            if (attribute != CueSheetAttribute.None)
            {
                OnChangeSceneView(attribute);
            }
        }

        // キューシート属性に該当するキューシートロード
        // 不要なキューシート開放
        private void OnChangeSceneView(CueSheetAttribute attr)
        {
            UpdatePlaybackList();

            //必要なシートリスト
            var alwaysMasterList = GetCueSheetsByAttribute(CueSheetAttribute.Always);
            var nextMasterList = GetCueSheetsByAttribute(attr);
            var needMasterList = new List<MasterAudioCuesheet.AudioCuesheet>();
            needMasterList.AddRange(alwaysMasterList);
            needMasterList.AddRange(nextMasterList);
            var needCueSheetList = needMasterList.Select(elem => (string)elem.CueSheet).ToList();

            if (attr == CueSheetAttribute.SceneTitle)
            {
                // タイトル系シーン
                for (int i = needCueSheetList.Count - 1; i >= 0; i--)
                {
                    // プリインシートでないものは除外する
                    if (PREIN_CUESHEET_LIST.Contains(needCueSheetList[i]) == false)
                    {
                        needCueSheetList.RemoveAt(i);
                    }
                }
            }

            //再生中必要
            for (int i = 0; i < _allPlaybackList.Count; i++)
            {
                if (needCueSheetList.Contains(_allPlaybackList[i].Playback.CueSheetName) == false) needCueSheetList.Add(_allPlaybackList[i].Playback.CueSheetName);
            }

            // ロード済みシートの中から必要なもの以外をアンロード
            List<string> cuesheets = Cute.Cri.AudioManager.Instance.GetAddedCueSheetNameList();
            for (int i = 0; i < cuesheets.Count; i++)
            {
                if (needCueSheetList.Exists(item => item == cuesheets[i]) == false)
                {
                    RemoveCueSheet(cuesheets[i]);
                }
            }

            // キューシート事前ロード
            for (int i = 0; i < needCueSheetList.Count; i++)
            {
                string cueSheet = needCueSheetList[i];
                if (cuesheets.Contains(cueSheet)) continue;     //ロード済み
                AddCueSheet(cueSheet, SubFolder.Se);
            }

        }

        /// <summary>
        /// ソフトウェアリセット後の削除でキューシートを全て破棄する時に呼び出す
        /// </summary>
        public static void RemoveAllCueSheet()
        {
            if(Cute.Cri.AudioManager.IsInstanceEmpty())
            {
                return;
            }

            List<string> cuesheets = Cute.Cri.AudioManager.Instance.GetAddedCueSheetNameList();
            for (int i = 0; i < cuesheets.Count; i++)
            {
                RemoveCueSheet(cuesheets[i]);
            }
        }

        #endregion

        #region デバッグ機能

#if CYG_DEBUG
        // キューシートがダウンロードされているかチェックし、検出ツールにログ登録する
        private void DebugCheckDownload(string cueSheet, SubFolder subFolder)
        {
            if (IsPreInstallCueSheet(cueSheet))
            {
                return; //プリインストールキューシートはダウンロードパスとしない
            }

            // 設定で一部ディレクトリはDL対象から除外される、このsubFolderが除外されているかチェック
            if (CanDownloadSubFolder(subFolder) == false)
            {
                return;        // 除外対象ならDLチェック不要
            }

            GetDownloadFilePath(subFolder, cueSheet, out var acbFilePath, out var awbFilePath);
            string filePath = acbFilePath.ToLower();
#if USE_DOWNLOADLEAK_DETECTOR
            DownloadLeakDetector.CheckDownloadLeakAudio(filePath);
#endif
        }

        // キューシートがダウンロードされているかチェックし、検出ツールにログ登録する
        private void DebugCheckDownload(string cueSheetName, string subFolderPath)
        {
            if (IsPreInstallCueSheet(cueSheetName))
            {
                return; //プリインストールキューシートはダウンロードパスとしない
            }

            GetDownloadFilePath(subFolderPath, cueSheetName, out var acbFilePath, out var awbFilePath);
            string filePath = acbFilePath.ToLower();
#if USE_DOWNLOADLEAK_DETECTOR
            DownloadLeakDetector.CheckDownloadLeakAudio(filePath);
#endif
        }

#if UNITY_EDITOR

        /// <summary>
        /// チュートリアル用ダウンロードリストに追加登録
        /// </summary>
        /// <param name="cueSheet"></param>
        /// <param name="subFolder"></param>
        public void RegisterTutorialDownloadList(string cueSheet, SubFolder subFolder)
        {
            if (!TutorialManager.IsTutorialExecuting())
            {
                // チュートリアル実行中のみ
                return;
            }

            GetDownloadFilePath(subFolder, cueSheet, out var acbFilePath, out var awbFilePath);
            TutorialDownloadListGenerator.RegisterAsset(acbFilePath.ToLower());
            TutorialDownloadListGenerator.RegisterAsset(awbFilePath.ToLower());
        }
#endif

#endif

#endregion
        }
    }
