using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// ドリブンキーのカーブを対象となるオブジェクトに流し込む
    /// ** とりあえずフェイシャル用
    /// </summary>
    public class DrivenKeyComponent
    {
        #region 定数
#if UNITY_EDITOR || CYG_DEBUG

        public static readonly string[] FACE_TYPE_GROUP_NAME_ARRAY =
        {
            "ベース",
            "笑い",
            "怒り",
            "悲しい",
            "ドヤ",
            "普通",
            "驚き",
            "ジト目",
            "困り",
            "デレ",
            "苦笑",
            "ユニーク",
            "走り　遅い",
            "走り　通常",
            "走り　早い",
            "口",
            "口　悲しい",
        };

        public static readonly string[][] FACE_TYPE_GROUP_PARTS_NAME_ARRAY =
        {
            new string[] { "左ウインク","右ウインク", "半目", "閉じ目" },
            new string[] { "笑いA", "笑いB", "笑いC", "笑いD", },
            new string[] { "怒りA", "怒りB", "怒りC", "怒りD", },
            new string[] { "悲しいA", "悲しいB", "悲しいC", "悲しいD", },
            new string[] { "ドヤA", "ドヤB", },
            new string[] { "普通A", "普通B", },
            new string[] { "驚きA", "驚きB", "驚きC", "驚きD" },
            new string[] { "ジト目A", "ジト目B", },
            new string[] { "困りA", "困りB", "困りC", "困りD", },
            new string[] { "デレA", "デレB" },
            new string[] { "苦笑AL", "苦笑BL", "苦笑CL", "苦笑DL", },
            new string[] { "ユニークA", "ユニークB", "ユニークC", "ユニークD", "ユニークE", "ユニークF", "ユニークG", "ユニークH", "ユニークI", "ユニークJ", "ユニークK", },
            new string[] { "走り　遅い　A", "走り　遅い　B" },
            new string[] { "走り　通常　A", "走り　通常　B" },
            new string[] { "走り　早い　A", "走り　早い　B" },
            new string[] { "AS", "AM", "AL", "IS", "IM", "IL","OS","US" ,"UM","UL","ES","EM","EL","OM","OL" ,},
            new string[] { "AS", "AM", "AL", "IS", "IM", "IL", "ES", "EM", "EL", },
        };

        public static readonly FaceType[][] FACE_TYPE_GROUP_ARRAY =
        {
            new FaceType[] { FaceType.WinkL, FaceType.WinkR, FaceType.EyeHalfA, FaceType.EyeClose },
            new FaceType[] { FaceType.WaraiA, FaceType.WaraiB, FaceType.WaraiC, FaceType.WaraiD, },
            new FaceType[] { FaceType.IkariA, FaceType.IkariB, FaceType.IkariC, FaceType.IkariD, },
            new FaceType[] { FaceType.KanasiA, FaceType.KanasiB, FaceType.KanasiC, FaceType.KanasiD, },
            new FaceType[] { FaceType.DoyaA, FaceType.DoyaB, },
            new FaceType[] { FaceType.FutuA, FaceType.FutuB, },
            new FaceType[] { FaceType.OdorokiA, FaceType.OdorokiB, FaceType.OdorokiC, FaceType.OdorokiD },
            new FaceType[] { FaceType.JitomeA, FaceType.JitomeB, },
            new FaceType[] { FaceType.KomariA, FaceType.KomariB, FaceType.KomariC, FaceType.KomariD, },
            new FaceType[] { FaceType.DereA, FaceType.DereB },
            new FaceType[] { FaceType.KusyoAL, FaceType.KusyoBL, FaceType.KusyoCL, FaceType.KusyoDL, },
            new FaceType[] { FaceType.UniqueA, FaceType.UniqueB, FaceType.UniqueC, FaceType.UniqueD, FaceType.UniqueE, FaceType.UniqueF, FaceType.UniqueG, FaceType.UniqueH, FaceType.UniqueI, FaceType.UniqueJ, FaceType.UniqueK },
            new FaceType[] { FaceType.RunSlowA, FaceType.RunSlowB, },
            new FaceType[] { FaceType.RunNormalA, FaceType.RunNormalB, },
            new FaceType[] { FaceType.RunFastA, FaceType.RunFastA },
            new FaceType[] { FaceType.MouthAS, FaceType.MouthAM, FaceType.MouthAL, FaceType.MouthIS, FaceType.MouthIM, FaceType.MouthIL, FaceType.MouthOS, FaceType.MouthUS, FaceType.MouthUM, FaceType.MouthUL, FaceType.MouthES, FaceType.MouthEM, FaceType.MouthEL,FaceType.MouthOM, FaceType.MouthOL, },
            new FaceType[] { FaceType.MouthKanasiAS,FaceType.MouthKanasiAM,FaceType.MouthKanasiAL,FaceType.MouthKanasiIS,FaceType.MouthKanasiIM,FaceType.MouthKanasiIL,FaceType.MouthKanasiES,FaceType.MouthKanasiEM,FaceType.MouthKanasiEL, },
        };

        public static string[] GetFaceTypeGroupName()
        {
            return FACE_TYPE_GROUP_NAME_ARRAY;
        }

        public static string[] GetFaceTypeGroupPartsName(int groupIndex)
        {
            return FACE_TYPE_GROUP_PARTS_NAME_ARRAY[groupIndex];
        }

        public static FaceType GetFaceType(int groupIndex, int index)
        {
            return FACE_TYPE_GROUP_ARRAY[groupIndex][index];
        }

        public static void GetFaceTypeGroupIndex(FaceType face, out int groupIndex, out int index)
        {
            groupIndex = 0;
            index = 0;

            for (int i = 0; i < FACE_TYPE_GROUP_ARRAY.Length; i++)
            {
                var faceTypeArray = FACE_TYPE_GROUP_ARRAY[i];
                for (int j = 0; j < faceTypeArray.Length; j++)
                {
                    if (faceTypeArray[j] == face)
                    {
                        groupIndex = i;
                        index = j;
                        return;
                    }
                }
            }
        }

        public static readonly string[] EAR_NAME_ARRAY =
        {
            //名称変わる予定なので、直値
            "Base",
            "Base_N",
            "Kanasi",
            "Dere_N",
            "Dere",
            "Yure",
            "Biku_N",
            "Biku",
            "Ikari",
            "Tanosi",
            "Up_N",
            "Up",
            "Down",
            "Front",
            "Side",
            "Back",
            "Roll",
        };

        #region FaceEyebrowType

        public static readonly string[] FACE_EYEBROW_TYPE_GROUP_NAME_ARRAY =
        {
            "ベース",
            "笑い",
            "怒り",
            "悲しみ",
            "ドヤ",
            "デレ",
            "驚き",
            "ジト目",
            "困り",
            "苦笑",
            "憂い",
            "走り",
            "シリアス",
            "しわ",
            "Blend",
        };

        public static readonly int[][] FACE_EYEBROW_TYPE_GROUP_ARRAY =
        {
            new int[] { (int)FaceEyebrowType.Base },
            new int[] { (int)FaceEyebrowType.WaraiA, (int)FaceEyebrowType.WaraiB, (int)FaceEyebrowType.WaraiC, (int)FaceEyebrowType.WaraiD },
            new int[] { (int)FaceEyebrowType.IkariA },
            new int[] { (int)FaceEyebrowType.KanasiA },
            new int[] { (int)FaceEyebrowType.DoyaA },
            new int[] { (int)FaceEyebrowType.DereA },
            new int[] { (int)FaceEyebrowType.OdorokiA, (int)FaceEyebrowType.OdorokiB },
            new int[] { (int)FaceEyebrowType.JitoA },
            new int[] { (int)FaceEyebrowType.KomariA },
            new int[] { (int)FaceEyebrowType.KusyoA },
            new int[] { (int)FaceEyebrowType.UreiA },
            new int[] { (int)FaceEyebrowType.RunA, (int)FaceEyebrowType.RunB, },
            new int[] { (int)FaceEyebrowType.SeriousA, (int)FaceEyebrowType.SeriousB, },
            new int[] { (int)FaceEyebrowType.ShiwaA, (int)FaceEyebrowType.ShiwaB },
            new int[] { (int)FaceEyebrowType.Offset_U, (int)FaceEyebrowType.Offset_D, (int)FaceEyebrowType.Offset_L, (int)FaceEyebrowType.Offset_R },
        };

        public static readonly string[][] FACE_EYEBROW_TYPE_GROUP_PARTS_NAME_ARRAY =
        {
            new string[] { "ベース" },
            new string[] { "笑いA", "笑いB", "笑いC", "笑いD" },
            new string[] { "怒りA" },
            new string[] { "悲しみA" },
            new string[] { "ドヤA" },
            new string[] { "デレA" },
            new string[] { "驚きA", "驚きB" },
            new string[] { "ジト目A" },
            new string[] { "困りA" },
            new string[] { "苦笑A" },
            new string[] { "憂いA" },
            new string[] { "走りA", "走りB", },
            new string[] { "シリアスA", "シリアスB", },
            new string[] { "しわA", "しわB" },
            new string[] { "オフセット 上", "オフセット 下", "オフセット 左", "オフセット 右" },
        };

        public static string[] GetFaceEyebrowTypeGroupName()
        {
            return FACE_EYEBROW_TYPE_GROUP_NAME_ARRAY;
        }

        public static string[] GetFaceEyebrowTypeGroupPartsName(int groupIndex)
        {
            return FACE_EYEBROW_TYPE_GROUP_PARTS_NAME_ARRAY[groupIndex];
        }

        public static int GetFaceEyebrowType(int groupIndex, int index)
        {
            return FACE_EYEBROW_TYPE_GROUP_ARRAY[groupIndex][index];
        }

        public static void GetFaceEyebrowTypeGroupIndex(int faceEyebrowType, out int groupIndex, out int index)
        {
            groupIndex = 0;
            index = 0;

            for (int i = 0; i < FACE_EYEBROW_TYPE_GROUP_ARRAY.Length; i++)
            {
                var facePartsTypeArray = FACE_EYEBROW_TYPE_GROUP_ARRAY[i];
                for (int j = 0; j < facePartsTypeArray.Length; j++)
                {
                    if (facePartsTypeArray[j] == faceEyebrowType)
                    {
                        groupIndex = i;
                        index = j;
                        return;
                    }
                }
            }
        }

        #endregion FaceEyebrowType

        #region FaceEyeType

        public static readonly string[] FACE_EYE_TYPE_GROUP_NAME_ARRAY =
        {
            "ベース",
            "笑い",
            "怒り",
            "悲しみ",
            "デレ",
            "驚き",
            "ジト目",
            "苦笑",
            "憂い",
            "走り",
            "シリアス",
            "Pupil",
            "Blend",
        };

        public static readonly int[][] FACE_EYE_TYPE_GROUP_ARRAY =
        {
            new int[] { (int)FaceEyeType.Base, (int)FaceEyeType.CloseA, (int)FaceEyeType.HalfA, (int)FaceEyeType.HalfB, (int)FaceEyeType.HalfC, (int)FaceEyeType.EyeHideA },
            new int[] { (int)FaceEyeType.WaraiA, (int)FaceEyeType.WaraiB, (int)FaceEyeType.WaraiC, (int)FaceEyeType.WaraiD },
            new int[] { (int)FaceEyeType.IkariA },
            new int[] { (int)FaceEyeType.KanasiA },
            new int[] { (int)FaceEyeType.DereA },
            new int[] { (int)FaceEyeType.OdorokiA, (int)FaceEyeType.OdorokiB },
            new int[] { (int)FaceEyeType.JitoA },
            new int[] { (int)FaceEyeType.KusyoA },
            new int[] { (int)FaceEyeType.UreiA },
            new int[] { (int)FaceEyeType.RunA },
            new int[] { (int)FaceEyeType.SeriousA },
            new int[] { (int)FaceEyeType.PupilA, (int)FaceEyeType.PupilB, (int)FaceEyeType.PupilC },
            new int[] { (int)FaceEyeType.OdorokiC, (int)FaceEyeType.DrivenA, (int)FaceEyeType.XRange, (int)FaceEyeType.YRange, (int)FaceEyeType.EyelidHideA, (int)FaceEyeType.EyelidHideB },
        };

        public static readonly string[][] FACE_EYE_TYPE_GROUP_PARTS_NAME_ARRAY =
        {
            new string[] { "ベース", "目閉じA", "半目A", "半目B", "半目C", "目消しA" },
            new string[] { "笑いA", "笑いB", "笑いC", "笑いD" },
            new string[] { "怒りA" },
            new string[] { "悲しみA" },
            new string[] { "デレA" },
            new string[] { "驚きA", "驚きB" },
            new string[] { "ジト目A" },
            new string[] { "苦笑A" },
            new string[] { "憂いA" },
            new string[] { "走りA" },
            new string[] { "シリアスA" },
            new string[] { "PupilA", "PupilB", "PupilC" },
            new string[] { "驚きC", "DrivenA", "XRange", "YRange", "二重まぶた消しA", "二重まぶた消しB" },
        };

        public static string[] GetFaceEyeTypeGroupName()
        {
            return FACE_EYE_TYPE_GROUP_NAME_ARRAY;
        }

        public static string[] GetFaceEyeTypeGroupPartsName(int groupIndex)
        {
            return FACE_EYE_TYPE_GROUP_PARTS_NAME_ARRAY[groupIndex];
        }

        public static int GetFaceEyeType(int groupIndex, int index)
        {
            return FACE_EYE_TYPE_GROUP_ARRAY[groupIndex][index];
        }

        public static void GetFaceEyeTypeGroupIndex(int faceEyeType, out int groupIndex, out int index)
        {
            groupIndex = 0;
            index = 0;

            for (int i = 0; i < FACE_EYE_TYPE_GROUP_ARRAY.Length; i++)
            {
                var facePartsTypeArray = FACE_EYE_TYPE_GROUP_ARRAY[i];
                for (int j = 0; j < facePartsTypeArray.Length; j++)
                {
                    if (facePartsTypeArray[j] == faceEyeType)
                    {
                        groupIndex = i;
                        index = j;
                        return;
                    }
                }
            }
        }

        #endregion FaceEyeType

        #region FaceMouthType

        public static readonly string[] FACE_MOUTH_TYPE_GROUP_NAME_ARRAY =
        {
            "ベース",
            "笑い",
            "怒り",
            "悲しみ",
            "ドヤ",
            "デレ",
            "驚き",
            "ジト目",
            "困り",
            "苦笑",
            "憂い",
            "会話",
            "走り",
            "舌",
            "Blend",
        };

        public static readonly int[][] FACE_MOUTH_TYPE_GROUP_ARRAY =
        {
            new int[] { (int)FaceMouthType.Base, (int)FaceMouthType.Normal, (int)FaceMouthType.CheekA_L, (int)FaceMouthType.CheekA_R },
            new int[] { (int)FaceMouthType.WaraiA, (int)FaceMouthType.WaraiB, (int)FaceMouthType.WaraiC, (int)FaceMouthType.WaraiD, (int)FaceMouthType.WaraiE },
            new int[] { (int)FaceMouthType.IkariA, (int)FaceMouthType.IkariB },
            new int[] { (int)FaceMouthType.KanasiA },
            new int[] { (int)FaceMouthType.DoyaA },
            new int[] { (int)FaceMouthType.DereA },
            new int[] { (int)FaceMouthType.OdorokiA, (int)FaceMouthType.OdorokiB },
            new int[] { (int)FaceMouthType.JitoA },
            new int[] { (int)FaceMouthType.KomariA },
            new int[] { (int)FaceMouthType.KusyoA_L, (int)FaceMouthType.KusyoA_R, (int)FaceMouthType.KusyoB_L, (int)FaceMouthType.KusyoB_R },
            new int[] { (int)FaceMouthType.UreiA },
            new int[]
            {
                (int)FaceMouthType.TalkA_A_S, (int)FaceMouthType.TalkA_A_L,
                (int)FaceMouthType.TalkA_I_S, (int)FaceMouthType.TalkA_I_L,
                (int)FaceMouthType.TalkA_U_S, (int)FaceMouthType.TalkA_U_L,
                (int)FaceMouthType.TalkA_E_S, (int)FaceMouthType.TalkA_E_L,
                (int)FaceMouthType.TalkA_O_S, (int)FaceMouthType.TalkA_O_L,
                (int)FaceMouthType.TalkB_A_S, (int)FaceMouthType.TalkB_A_L,
                (int)FaceMouthType.TalkB_I_S, (int)FaceMouthType.TalkB_I_L,
                (int)FaceMouthType.TalkB_E_S, (int)FaceMouthType.TalkB_E_L,
                (int)FaceMouthType.TalkC_I,
            },
            new int[] { (int)FaceMouthType.RunA, (int)FaceMouthType.RunB },
            new int[] { (int)FaceMouthType.TanA, (int)FaceMouthType.TanB, (int)FaceMouthType.TanC_L, (int)FaceMouthType.TanC_R, (int)FaceMouthType.TanD_L, (int)FaceMouthType.TanD_R },
            new int[]
            {
                (int)FaceMouthType.DrivenA,
                (int)FaceMouthType.Offset_U, (int)FaceMouthType.Offset_D, (int)FaceMouthType.Offset_L, (int)FaceMouthType.Offset_R,
                (int)FaceMouthType.Scale_U, (int)FaceMouthType.Scale_D,
                (int)FaceMouthType.LowAngle,
                (int)FaceMouthType.ToothHide,
            },
        };

        public static readonly string[][] FACE_MOUTH_TYPE_GROUP_PARTS_NAME_ARRAY =
        {
            new string[] { "ベース", "ノーマル", "チークA 左", "チークA 右" },
            new string[] { "笑いA", "笑いB", "笑いC", "笑いD", "笑いE" },
            new string[] { "怒りA", "怒りB" },
            new string[] { "悲しみA" },
            new string[] { "ドヤA" },
            new string[] { "デレA" },
            new string[] { "驚きA", "驚きB" },
            new string[] { "ジト目A" },
            new string[] { "困りA" },
            new string[] { "苦笑A 左", "苦笑A 右", "苦笑B 左", "苦笑B 右" },
            new string[] { "憂いA" },
            new string[]
            {
                "会話A「あ」小", "会話A「あ」大", "会話A「い」小", "会話A「い」大", "会話A「う」小", "会話A「う」大", "会話A「え」小", "会話A「え」大", "会話A「お」小", "会話A「お」大",
                "会話B「あ」小", "会話B「あ」大", "会話B「い」小", "会話B「い」大", "会話B「え」小", "会話B「え」大",
                "会話C「い」",
            },
            new string[] { "走りA", "走りB" },
            new string[] { "舌A", "舌B", "舌C 左", "舌C 右", "舌D 左", "舌D 右" },
            new string[]
            {
                "DrivenA",
                "オフセット 上", "オフセット 下", "オフセット 左", "オフセット 右",
                "拡大", "縮小",
                "LowAngle",
                "歯消し"
            },
        };

        public static string[] GetFaceMouthTypeGroupName()
        {
            return FACE_MOUTH_TYPE_GROUP_NAME_ARRAY;
        }

        public static string[] GetFaceMouthTypeGroupPartsName(int groupIndex)
        {
            return FACE_MOUTH_TYPE_GROUP_PARTS_NAME_ARRAY[groupIndex];
        }

        public static int GetFaceMouthType(int groupIndex, int index)
        {
            return FACE_MOUTH_TYPE_GROUP_ARRAY[groupIndex][index];
        }

        public static void GetFaceMouthTypeGroupIndex(int faceMouthType, out int groupIndex, out int index)
        {
            groupIndex = 0;
            index = 0;

            for (int i = 0; i < FACE_MOUTH_TYPE_GROUP_ARRAY.Length; i++)
            {
                var facePartsTypeArray = FACE_MOUTH_TYPE_GROUP_ARRAY[i];
                for (int j = 0; j < facePartsTypeArray.Length; j++)
                {
                    if (facePartsTypeArray[j] == faceMouthType)
                    {
                        groupIndex = i;
                        index = j;
                        return;
                    }
                }
            }
        }

        #endregion FaceMouthType

#endif //UNITY_EDITOR || CYG_DEBUG

        private static readonly string[] LOCATOR_CONTROL_ARRAY = new string[] {
            "Eye_R_Base_Ctrl",
            "Eye_L_Base_Ctrl",
            "Eyebrow_R_Base_Ctrl",
            "Eyebrow_L_Base_Ctrl",
            "Mouth_Base_Ctrl"
        };

        //耳のウェイトを保持する
        private static readonly string[] LOCATOR_CONTROL_FOR_EAR_ARRAY = new string[]
        {
            "Ear_R_Ctrl",
            "Ear_L_Ctrl",
        };

        // DrivenKeyLocator直下の子の名前（目のウェイト値を保存するもの）
        private static readonly string[] LOCATOR_CONTROL_FOR_EYE_ARRAY = new string[] {
            "Eyeball_R_Ctrl",
            "Eyeball_L_Ctrl",
            "Eyeball_all_Ctrl"
        };

        //スケールアニメーションが有効なボーン
        private static readonly string[] SCALE_BONE_ARRAY = new string[] {
            "Eye_L",
            "Eye_R",
            "Tooth_up",
            "Tooth_bottom",
            "Tongue",
            "Eye_sub_01_L",
            "Eye_sub_02_L",
            "Eye_sub_03_L",
            "Eye_sub_01_R",
            "Eye_sub_02_R",
            "Eye_sub_03_R",
        };

        //左目、右目のボーン名
        public const string EYE_L = "Eye_L";
        public const string EYE_R = "Eye_R";
        public const string EYEBROW_L = "Eyebrow_L";
        public const string EYEBROW_R = "Eyebrow_R";

        //目の上下左右の移動値の限界値をもつFaceType
        private const int IN_OUT_EYE_RANGE_FACE = (int)FaceEyeType.XRange;
        private const int UP_DOWN_EYE_RANGE_FACE = (int)FaceEyeType.YRange;

        //DrivenKeyLocatorのオブジェクトからフェースタイプ名を分離するための分割文字列
        public const string FACE_TYPE_NAME_DIVIDER = "__";

        private const int RIGHT_EYE_INDEX = 0;
        private const int LEFT_EYE_INDEX = 1;
        private const int ALL_EYE_INDEX = 2;
        //Maya上のPositionをUnity上のPositionに変換するための係数
        private const float TRANSLATE_TO_UNITY_POSITION_VALUE = 100.0f;
        private const float TRANSLATE_TO_UNITY_POSITION = -TRANSLATE_TO_UNITY_POSITION_VALUE;

        private const int FACE_WEIGHT_START_INDEX = 1;    //顔Weightの開始インデックス(0はベースで値は固定となる)

        //ブレンドする顔の配列を使いまわすための番兵
        private const int BLEND_FACE_SENTINEL = -1;
        private const int BLEND_FADE_BASE = 0;  //ベース顔のインデックス

        /// <summary>
        /// 再生時に最後まで再生すると目線など表情がなくなることがあったので少し手前に来るように調整する時間
        /// </summary>
        private const float CLIP_END_MARGIN = 0.001f;

        //補間タイプ
        public enum InterpolateType
        {
            Linear,
            EaseIn,
            EaseOut,
            EaseInOut,
        }

        #endregion 定数

        #region クラス

        /// <summary>
        /// Facial初期化コンテキスト
        /// </summary>
        public struct FacialContext
        {
            public FaceDrivenKeyTarget FacialTargetData;
            public DrivenKeyTarget EarTarget;
            public FacePartsSet[] FacePartsSetArray;
            public GameObject KeyLocator;
            public ModelControllerBehaviour OwnerController;
        }

        [BurstCompile]
        private struct WeightCalcJob : IJob
        {
            private const float BASE_BLEND_WEIGHT = 1.0f;

            /// <summary>
            /// 全ウェイト情報が入っている配列
            /// WeightArrayは共有して扱う
            /// </summary>
            [ReadOnly] public NativeArray<float> CommonWeightArray;
            [ReadOnly] public NativeArray<WeightGroup> WeightArray;

            [ReadOnly] public int FacialGroupOffset;
            [ReadOnly] public NativeArray<bool> IsOverrideTarget;

            [ReadOnly] public NativeArray<int> BlendFaceArray;

            [ReadOnly] public CalcTargetInfomation.JobWorkData CalcInfomation;

            [ReadOnly] public FacialDrivenKeyJobWork DrivenKeyData;

            public NativeArray<CalcTargetInfomation.CalcTRS> ResultTRSArray;

            public void Execute()
            {
                for (int i = 0; i < BlendFaceArray.Length; i++)
                {
                    if (BlendFaceArray[i] == BLEND_FACE_SENTINEL)
                    {
                        break;
                    }

                    CalcTRSFromFace(BlendFaceArray[i]);
                }

                //上書きブレンドの計算を行う
                CalcTargetInfomation.CalcTRS blendTrs = new CalcTargetInfomation.CalcTRS();
                for (int i = 0; i < BlendFaceArray.Length; i++)
                {
                    if (BlendFaceArray[i] == BLEND_FACE_SENTINEL)
                    {
                        break;
                    }

                    BlendTRSFromFace(BlendFaceArray[i], ref blendTrs);
                }
            }

            public static void CalcTRS(ref CalcTargetInfomation.CalcTRS resultTrs, ref FacialDrivenKeyJobWork.Posture srcTrs, float weight, bool isValidScale)
            {
                resultTrs.CurWeight += weight;
                resultTrs.Position.x += srcTrs.Position.x * weight;
                resultTrs.Position.y += srcTrs.Position.y * weight;
                resultTrs.Position.z += srcTrs.Position.z * weight;
                resultTrs.Rotation.x += srcTrs.Rotation.x * weight;
                resultTrs.Rotation.y += srcTrs.Rotation.y * weight;
                resultTrs.Rotation.z += srcTrs.Rotation.z * weight;
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale.x += srcTrs.Scale.x * weight;
                    resultTrs.Scale.y += srcTrs.Scale.y * weight;
                    resultTrs.Scale.z += srcTrs.Scale.z * weight;
                }
            }

            public static void SetTRS(ref CalcTargetInfomation.CalcTRS resultTrs, ref FacialDrivenKeyJobWork.Posture srcTrs, float weight, bool isValidScale)
            {
                resultTrs.CurWeight = weight;
                resultTrs.Position.x = srcTrs.Position.x * weight;
                resultTrs.Position.y = srcTrs.Position.y * weight;
                resultTrs.Position.z = srcTrs.Position.z * weight;
                resultTrs.Rotation.x = srcTrs.Rotation.x * weight;
                resultTrs.Rotation.y = srcTrs.Rotation.y * weight;
                resultTrs.Rotation.z = srcTrs.Rotation.z * weight;
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale.x = srcTrs.Scale.x * weight;
                    resultTrs.Scale.y = srcTrs.Scale.y * weight;
                    resultTrs.Scale.z = srcTrs.Scale.z * weight;
                }
            }

            private void CalcTRSFromFace(int face)
            {
                //groupWeightArray.Lengthは反映する部位の数になるはずなので、目=2 口=1
                //srcFace._faceGroupInfo.Lengthは反映する骨の数、目=28、口=36
                //トータルループ回数は、目=2*28=56 口=1*36=36
                // ここの回数を減らす、または分散すると負荷対策になる

                var srcFaceIndex = DrivenKeyData.FacialStartIndex[face];
                var groupWeightArrayNum = WeightArray[face].Length;
                var tableIndex = WeightArray[face].StartIndex;
                if (IsOverrideTarget[face])
                {
                    if (face == BLEND_FADE_BASE)
                    {
                        for (int j = 0; j < groupWeightArrayNum; j++)
                        {
                            var srcFaceGroupNum = DrivenKeyData.FacialPostureNum[(face * groupWeightArrayNum) + j];
                            float w = CommonWeightArray[tableIndex + j];
                            if (w <= 0.0f)
                            {
                                srcFaceIndex += srcFaceGroupNum;
                                continue;
                            }

                            int offset = FacialGroupOffset + j;
                            var faceGroupIndex = CalcInfomation.FaceGroupStartIndexArray[offset];
                            int k = faceGroupIndex - 1; //ループ先頭で加算するので-1から始める
                            
                            for (int i = 0; i < srcFaceGroupNum; i++)
                            {
                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + i];
                                k++;
                                if (trs.IsOverrideTarget)
                                {
                                    //対象の骨ではないので影響を受けない
                                    continue;
                                }
                                var resultTRS = ResultTRSArray[k];
                                SetTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[k]);
                                ResultTRSArray[k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceGroupNum;
                        }
                    }
                    else
                    {
                        for (int j = 0; j < groupWeightArrayNum; j++)
                        {
                            var srcFaceGroupNum = DrivenKeyData.FacialPostureNum[(face * groupWeightArrayNum) + j];
                            float w = CommonWeightArray[tableIndex + j];
                            if (w <= 0.0f)
                            {
                                srcFaceIndex += srcFaceGroupNum;
                                continue;
                            }

                            int offset = FacialGroupOffset + j;
                            var faceGroupIndex = CalcInfomation.FaceGroupStartIndexArray[offset];
                            int k = faceGroupIndex - 1; //ループ先頭で加算するので-1から始める
                            for (int i = 0; i < srcFaceGroupNum; i++)
                            {
                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + i];
                                k++;
                                if (trs.IsOverrideTarget)
                                {
                                    //対象の骨ではないので影響を受けない
                                    continue;
                                }
                                var resultTRS = ResultTRSArray[k];
                                CalcTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[k]);
                                ResultTRSArray[k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceGroupNum;
                        }
                    }
                }
                else
                {
                    //上書き設定がないのでIsOverrideTarget判定は行わない
                    if (face == BLEND_FADE_BASE)
                    {
                        for (int j = 0; j < groupWeightArrayNum; j++)
                        {
                            var srcFaceGroupNum = DrivenKeyData.FacialPostureNum[(face * groupWeightArrayNum) + j];
                            float w = CommonWeightArray[tableIndex + j];
                            if (w <= 0.0f)
                            {
                                srcFaceIndex += srcFaceGroupNum;
                                continue;
                            }

                            int offset = FacialGroupOffset + j;
                            var faceGroupIndex = CalcInfomation.FaceGroupStartIndexArray[offset];
                            int k = faceGroupIndex - 1; //ループ先頭で加算するので-1から始める
                            for (int i = 0; i < srcFaceGroupNum; i++)
                            {
                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + i];
                                k++;
                                var resultTRS = ResultTRSArray[k];
                                SetTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[k]);
                                ResultTRSArray[k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceGroupNum;
                        }
                    }
                    else
                    {
                        for (int j = 0; j < groupWeightArrayNum; j++)
                        {
                            var srcFaceGroupNum = DrivenKeyData.FacialPostureNum[(face * groupWeightArrayNum) + j];
                            float w = CommonWeightArray[tableIndex + j];
                            if (w <= 0.0f)
                            {
                                srcFaceIndex += srcFaceGroupNum;
                                continue;
                            }

                            int offset = FacialGroupOffset + j;
                            var faceGroupIndex = CalcInfomation.FaceGroupStartIndexArray[offset];
                            int k = faceGroupIndex - 1; //ループ先頭で加算するので-1から始める
                            for (int i = 0; i < srcFaceGroupNum; i++)
                            {
                                k++;
                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + i];
                                var resultTRS = ResultTRSArray[k];
                                CalcTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[k]);
                                ResultTRSArray[k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceGroupNum;
                        }
                    }
                }
            }

            private void BlendTRSFromFace(int face, ref CalcTargetInfomation.CalcTRS tempTRS)
            {
                //tempTRSは計算の使いまわしに使用する

                //上書き項目がなければ何もしない
                if (!IsOverrideTarget[face])
                {
                    return;
                }

                //先頭に必ずベースが入っている
                var baseFaceIndex = DrivenKeyData.FacialStartIndex[BLEND_FADE_BASE];
                var srcFaceIndex = DrivenKeyData.FacialStartIndex[face];
                var num = WeightArray[face].Length;
                var tableIndex = WeightArray[face].StartIndex;
                for (int j = 0; j < num; j++)
                {
                    var srcFaceGroupNum = DrivenKeyData.FacialPostureNum[(face * num) + j];
                    float w = CommonWeightArray[tableIndex + j];
                    if (w <= 0.0f)
                    {
                        srcFaceIndex += srcFaceGroupNum;
                        baseFaceIndex += srcFaceGroupNum;
                        continue;
                    }

                    int offset = FacialGroupOffset + j;
                    var faceGroupInfo = CalcInfomation.FaceGroupStartIndexArray[offset];
                    int k = faceGroupInfo - 1;
                    for(int i=0;i< srcFaceGroupNum; i++)
                    {
                        var trs = DrivenKeyData.PostureArray[srcFaceIndex + i];
                        k++;
                        if (!trs.IsOverrideTarget)
                        {
                            //対象の骨ではないので影響を受けない
                            continue;
                        }

                        var baseTrs = DrivenKeyData.PostureArray[baseFaceIndex + i];
                        bool isScale = CalcInfomation.IsValidScaleTransformArray[k];
                        var resultTRS = ResultTRSArray[k];
                        AddTRS(ref tempTRS, ref baseTrs, ref trs, w, isScale);
                        BlendTRS(ref resultTRS, ref tempTRS, isScale);
                        ResultTRSArray[k] = resultTRS;
                    }
                    srcFaceIndex += srcFaceGroupNum;
                    baseFaceIndex += srcFaceGroupNum;
                }
            }

            /// <summary>
            /// 2項目を加算した結果を入れる
            /// </summary>
            /// <param name="resultTrs"></param>
            /// <param name="srcTrs"></param>
            /// <param name="addTrs"></param>
            /// <param name="weight"></param>
            /// <param name="isValidScale"></param>
            public static void AddTRS(ref CalcTargetInfomation.CalcTRS resultTrs, ref FacialDrivenKeyJobWork.Posture srcTrs, ref FacialDrivenKeyJobWork.Posture addTrs, float weight, bool isValidScale)
            {
                resultTrs.CurWeight = BASE_BLEND_WEIGHT + weight;    //ベースのWeight値は1なので1を加算しておく
                resultTrs.Position = srcTrs.Position + (addTrs.Position * weight);
                resultTrs.Rotation = srcTrs.Rotation + (addTrs.Rotation * weight);
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale = srcTrs.Scale + (addTrs.Scale * weight);
                }
            }

            /// <summary>
            /// ２項目をブレンドする
            /// </summary>
            /// <param name="resultTrs"></param>
            /// <param name="blendTrs"></param>
            /// <param name="isValidScale"></param>
            public static void BlendTRS(ref CalcTargetInfomation.CalcTRS resultTrs, ref CalcTargetInfomation.CalcTRS blendTrs, bool isValidScale)
            {
                float weight1 = resultTrs.CurWeight - BASE_BLEND_WEIGHT; //ベースが入っているので必ず1を超える
                float weight2 = blendTrs.CurWeight - BASE_BLEND_WEIGHT; //ベースが入っているので必ず1を超える
                float finalW = (weight1 + weight2) * 0.5f;   //中間点を新しいBlend率にする

                resultTrs.CurWeight = BASE_BLEND_WEIGHT + finalW;
                resultTrs.Position = Vector3.Lerp(resultTrs.Position, blendTrs.Position, weight2);
                resultTrs.Rotation = Vector3.Lerp(resultTrs.Rotation, blendTrs.Rotation, weight2);
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale = Vector3.Lerp(resultTrs.Scale, blendTrs.Scale, weight2);
                }
            }
        }

        public abstract class WeightGroupBase
        {
            protected const int BLNED_FACE_BASE_OFFSET = 1;
            protected const float BASE_BLEND_WEIGHT = 1.0f;

            protected const int WEIGHT_GROUP_ITEM_NUM = 3;  //WeightArray,TargetWeightArray,PrevWeightArrayで3つ

            /// <summary>
            /// 全ウェイト情報が入っている配列
            /// WeightArrayは共有して扱う
            /// </summary>
            public NativeArray<float> CommonWeightArray;

            public NativeArray<WeightGroup> WeightArray;
            public NativeArray<WeightGroup> TargetWeightArray;
            public NativeArray<WeightGroup> PrevWeightArray;

            public NativeArray<float> DurationTimeArray;
            public NativeArray<float> CurrentTimeArray;

            protected int blendFacesNum;  //コピーする際に使用する

            public int FaceNum;
            public int GroupNum;
            public int FacialGroupOffset;

            public bool IsCalcWeight;

            protected NativeArray<bool> _isOverrideTarget; //表情内にOverrideを行うターゲットが含まれているか
            protected DrivenKeyTarget _drivenKeyTarget;
            protected FacialDrivenKeyJobWork _drivenKeyJobWork;

            public int BlendFaceNum
            {
                get { return blendFacesNum; }
            }

            public virtual void Release()
            {
                _drivenKeyJobWork.Release();
                if(_isOverrideTarget.IsCreated)
                    _isOverrideTarget.Dispose();
                WeightArray.Dispose();
                TargetWeightArray.Dispose();
                PrevWeightArray.Dispose();
                CommonWeightArray.Dispose();
                DurationTimeArray.Dispose();
                CurrentTimeArray.Dispose();

                _drivenKeyTarget = null;
            }

            public void Initialize(int num, int groupNum,DrivenKeyTarget keyTarget)
            {
                this.FaceNum = num;
                this.GroupNum = groupNum;
                _drivenKeyTarget = keyTarget;

                DurationTimeArray = new NativeArray<float>(groupNum,Allocator.Persistent);
                CurrentTimeArray = new NativeArray<float>(groupNum,Allocator.Persistent);

                int weightArraySize = num * groupNum;
                int weightTableNum = weightArraySize * WEIGHT_GROUP_ITEM_NUM;
                CommonWeightArray = new NativeArray<float>(weightTableNum, Allocator.Persistent);

                WeightArray = new NativeArray<WeightGroup>(num,Allocator.Persistent,NativeArrayOptions.UninitializedMemory);
                TargetWeightArray = new NativeArray<WeightGroup>(num, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
                PrevWeightArray = new NativeArray<WeightGroup>(num, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
                for (int i = 0; i < WeightArray.Length; i++)
                {
                    int groupIndex = i * groupNum;
                    WeightArray[i]       = new WeightGroup() { StartIndex = (weightArraySize * 0) + groupIndex, Length = groupNum };
                    TargetWeightArray[i] = new WeightGroup() { StartIndex = (weightArraySize * 1) + groupIndex, Length = groupNum };
                    PrevWeightArray[i]   = new WeightGroup() { StartIndex = (weightArraySize * 2) + groupIndex, Length = groupNum };
                }

                // ベースの表情なのでWeightを1に設定(ループ中変わる事はない)
                for (int i = 0; i < groupNum; ++i)
                {
                    CommonWeightArray[WeightArray[0].StartIndex + i] = 1.0f;
                    CommonWeightArray[PrevWeightArray[0].StartIndex + i] = 1.0f;
                }

                //表情内に上書きブレンド対象骨が存在するか調べる
                if (keyTarget != null)
                {
                    _isOverrideTarget = new NativeArray<bool>(keyTarget._targetFaces.Length, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
                    for (int i = 0; i < keyTarget._targetFaces.Length; i++)
                    {
                        //上書きの対象が登録されているか
                        _isOverrideTarget[i] = System.Array.Exists(keyTarget._targetFaces[i]._faceGroupInfo,
                                                (faceGroup) => System.Array.Exists(faceGroup._trsArray, (trs) => trs.IsOverrideTarget));
                    }
                    _drivenKeyJobWork.Initialize(keyTarget);
                }
            }

            /// <summary>
            /// ブレンド遷移時間を設定する
            /// </summary>
            /// <param name="time"></param>
            public void SetDurationTime(float time)
            {
                for (int i = 0; i < DurationTimeArray.Length; i++)
                {
                    DurationTimeArray[i] = time;
                    CurrentTimeArray[i] = 0.0f;
                }
            }

            /// <summary>
            /// ブレンド遷移時間を設定する
            /// </summary>
            /// <param name="time"></param>
            public void SetDurationTime(int index, float time)
            {
                DurationTimeArray[index] = time;
                CurrentTimeArray[index] = 0.0f;
            }

            public abstract void ClearBlendFace();

            protected static void CalcTRS(ref CalcTargetInfomation.CalcTRS resultTrs, TRS srcTrs, float weight, bool isValidScale)
            {
                resultTrs.CurWeight += weight;
                resultTrs.Position.x += srcTrs._position.x * weight;
                resultTrs.Position.y += srcTrs._position.y * weight;
                resultTrs.Position.z += srcTrs._position.z * weight;
                resultTrs.Rotation.x += srcTrs._rotation.x * weight;
                resultTrs.Rotation.y += srcTrs._rotation.y * weight;
                resultTrs.Rotation.z += srcTrs._rotation.z * weight;
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale.x += srcTrs._scale.x * weight;
                    resultTrs.Scale.y += srcTrs._scale.y * weight;
                    resultTrs.Scale.z += srcTrs._scale.z * weight;
                }
            }

            /// <summary>
            /// 2項目を加算した結果を入れる
            /// </summary>
            /// <param name="resultTrs"></param>
            /// <param name="srcTrs"></param>
            /// <param name="addTrs"></param>
            /// <param name="weight"></param>
            /// <param name="isValidScale"></param>
            protected static void AddTRS(ref CalcTargetInfomation.CalcTRS resultTrs, TRS srcTrs, TRS addTrs, float weight, bool isValidScale)
            {
                resultTrs.CurWeight = BASE_BLEND_WEIGHT + weight;    //ベースのWeight値は1なので1を加算しておく
                resultTrs.Position = srcTrs._position + (addTrs._position * weight);
                resultTrs.Rotation = srcTrs._rotation + (addTrs._rotation * weight);
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale = srcTrs._scale + (addTrs._scale * weight);
                }
            }

            /// <summary>
            /// ２項目をブレンドする
            /// </summary>
            /// <param name="resultTrs"></param>
            /// <param name="blendTrs"></param>
            /// <param name="isValidScale"></param>
            protected static void BlendTRS(ref CalcTargetInfomation.CalcTRS resultTrs, ref CalcTargetInfomation.CalcTRS blendTrs, bool isValidScale)
            {
                float weight1 = resultTrs.CurWeight - BASE_BLEND_WEIGHT; //ベースが入っているので必ず1を超える
                float weight2 = blendTrs.CurWeight - BASE_BLEND_WEIGHT; //ベースが入っているので必ず1を超える
                float finalW = (weight1 + weight2) * 0.5f;   //中間点を新しいBlend率にする

                resultTrs.CurWeight = BASE_BLEND_WEIGHT + finalW;
                resultTrs.Position = Vector3.Lerp(resultTrs.Position, blendTrs.Position, weight2);
                resultTrs.Rotation = Vector3.Lerp(resultTrs.Rotation, blendTrs.Rotation, weight2);
                //限られたボーンのみスケールさせる
                if (isValidScale)
                {
                    resultTrs.Scale = Vector3.Lerp(resultTrs.Scale, blendTrs.Scale, weight2);
                }
            }

            public void ClearWeight()
            {
                const int CLEAR_START_INDEX = 1;    //0はベースが入っているのでクリアする必要はない
                int startIndex = WeightArray[CLEAR_START_INDEX].StartIndex;
                unsafe
                {
                    var weightArray = (float*)CommonWeightArray.GetUnsafePtr();
                    UnsafeUtility.MemClear(&weightArray[startIndex], sizeof(float) * (WeightArray.Length - CLEAR_START_INDEX) * GroupNum);
                }
            }

            public virtual void SetPrevWeight(bool isForce = false)
            {
                for (int i = 0; i < CurrentTimeArray.Length; i++)
                {
                    SetPrevWeight(i, isForce);
                }
            }

            public virtual void SetPrevWeight(int i, bool isForce = false)
            {
                //現在アニメ中のもの（ 0 < _currentTime < duration ）以外は現在値を設定
                //呼び出し回数が多く、currentTime == 0.0の時は外部から設定されるので、GallopUtil.IsFloatEqualLightは使用しない
                if (CurrentTimeArray[i] == 0.0f || CurrentTimeArray[i] >= DurationTimeArray[i] || isForce)
                {
                    for (int j = 0; j < WeightArray.Length; j++)
                    {
                        CommonWeightArray[PrevWeightArray[j].StartIndex + i] = CommonWeightArray[WeightArray[j].StartIndex + i];
                    }
                }
            }

            public bool IsPlaying(int faceGroup)
            {
                return DurationTimeArray[faceGroup] > CurrentTimeArray[faceGroup];
            }

            public bool IsPlaying()
            {
                for (int i = 0; i < CurrentTimeArray.Length; i++)
                {
                    if (DurationTimeArray[i] > CurrentTimeArray[i])
                    {
                        return true;
                    }
                }
                return false;
            }
        }


        /// <summary>
        /// 顔のWeight計算用クラス
        /// </summary>
        public class FaceWeightGroup : WeightGroupBase
        {
            public bool IsAnimationWeight = false;
            public NativeArray<int> BlendFaceArray;
            private JobHandle _jobHandle;

            public FaceWeightGroup(int groupOffset, int groupNum, int faceNum,DrivenKeyTarget keyTagret)
            {
                FacialGroupOffset = groupOffset;
                BlendFaceArray = new NativeArray<int>(faceNum + 1, Allocator.Persistent);
                Initialize(faceNum, groupNum,keyTagret);
            }

            public void GetFaceTypes(ref FaceTypeSet faceTypeSet, bool removeBase = false)
            {
                if (removeBase)
                {
                    faceTypeSet.count = blendFacesNum - BLNED_FACE_BASE_OFFSET;
                    NativeArray<int>.Copy(BlendFaceArray, BLNED_FACE_BASE_OFFSET, faceTypeSet.faceType, 0, blendFacesNum - BLNED_FACE_BASE_OFFSET);
                }
                else
                {
                    faceTypeSet.count = blendFacesNum;
                    NativeArray<int>.Copy(BlendFaceArray, faceTypeSet.faceType, blendFacesNum);
                }
            }

            public override void Release()
            {
                base.Release();
                BlendFaceArray.Dispose();
            }

            #region TRS計算

            public virtual void WaitJob()
            {
                _jobHandle.Complete();
            }

            public virtual JobHandle CalcTRS(CalcTargetInfomation weightSum,JobHandle prevJobHandle)
            {
                WeightCalcJob job;
                job.BlendFaceArray = BlendFaceArray;
                job.CalcInfomation = weightSum.TargetJobWorkData;
                job.DrivenKeyData =  _drivenKeyJobWork;
                job.FacialGroupOffset = FacialGroupOffset;
                job.IsOverrideTarget = _isOverrideTarget;
                job.CommonWeightArray = CommonWeightArray;
                job.ResultTRSArray = weightSum.CommonCalcTRSArray;
                job.WeightArray = WeightArray;
                _jobHandle = job.Schedule(prevJobHandle);
                return _jobHandle;
            }

            #endregion

            public void AddBlendFace(int face)
            {
                for (int i = 0; i < BlendFaceArray.Length - 1; i++)
                {
                    //既にブレンドする顔が登録済み
                    if (BlendFaceArray[i] == face)
                    {
                        break;
                    }

                    //未ブレンドの顔なのでブレンドする顔に登録
                    if (BlendFaceArray[i] == BLEND_FACE_SENTINEL)
                    {
                        BlendFaceArray[i] = face;
                        BlendFaceArray[i + 1] = BLEND_FACE_SENTINEL;
                        blendFacesNum++;
                        break;
                    }
                }
            }

            public override void ClearBlendFace()
            {
                BlendFaceArray[0] = 0;
                BlendFaceArray[1] = BLEND_FACE_SENTINEL;
                blendFacesNum = 1;
            }

            public void ClearTargetWeight()
            {
                // Base部分はクリアしないので、1から始める.
                for (int i = FACE_WEIGHT_START_INDEX; i < FaceNum; i++)
                {
                    var tableIndex = TargetWeightArray[i].StartIndex;
                    for (int j = 0; j < GroupNum; j++)
                    {
                        CommonWeightArray[tableIndex + j] = 0.0f;
                    }
                }
            }
        }

        public class EyeWeightGroup : FaceWeightGroup
        {
            public EyeWeightGroup(DrivenKeyTarget keyTarget)
                : base((int)FaceGroupType.EyeR, FaceTypeUtil.EYE_GROUP_NUM, FaceTypeUtil.GetEyeCount(), keyTarget)
            {
            }
        }

        public class EyebrowWeightGroup : FaceWeightGroup
        {
            public EyebrowWeightGroup(DrivenKeyTarget keyTarget)
                : base((int)FaceGroupType.EyebrowR, FaceTypeUtil.EYEBROW_GROUP_NUM, FaceTypeUtil.GetEyebrowCount(), keyTarget)
            {

            }
        }
        public class MouthWeightGroup : FaceWeightGroup
        {
            public MouthWeightGroup(DrivenKeyTarget keyTarget)
                : base((int)FaceGroupType.Mouth, FaceTypeUtil.MOUTH_GROUP_NUM, FaceTypeUtil.GetMouthCount(), keyTarget)
            {

            }
        }

        [BurstCompile]
        private struct EarWeightJob : IJob
        {
            [ReadOnly] public NativeArray<EarType> EarBlendFaceArray;
            [ReadOnly] public int FacialGroupOffset;
            [ReadOnly] public int GroupNum;

            [ReadOnly] public CalcTargetInfomation.JobWorkData CalcInfomation;
            [ReadOnly] public FacialDrivenKeyJobWork DrivenKeyData;

            [ReadOnly] public NativeArray<bool> IsOverrideTarget;
            [ReadOnly] public NativeArray<WeightGroup> WeightArray;

            [ReadOnly] public NativeArray<float> CommonWeightArray;

            public NativeArray<CalcTargetInfomation.CalcTRS> ResultTRSArray;

            public void Execute()
            {
                CalcTRS();
            }

            private void CalcTRSFromFace(int ear)
            {
                //groupWeightArray.Lengthは反映する部位の数になるはずなので、目=2 口=1
                //srcFace._faceGroupInfo.Lengthは反映する骨の数、目=28、口=36
                //トータルループ回数は、目=2*28=56 口=1*36=36
                // ここの回数を減らす、または分散すると負荷対策になる

                var srcFaceIndex = DrivenKeyData.FacialStartIndex[ear];
                int offset = FacialGroupOffset;
                int tableIndex = WeightArray[ear].StartIndex;
                int index = 0;
                int groupNum = GroupNum / EarWeightGroup.WEIGHT_SIZE;
                if (IsOverrideTarget[ear])
                {
                    if (ear == BLEND_FADE_BASE)
                    {
                        for (int j = 0; j < GroupNum; j += EarWeightGroup.WEIGHT_SIZE, offset++, index++)
                        {
                            var srcFaceNum = DrivenKeyData.FacialPostureNum[(ear * groupNum) + index];
                            var faceGroupInfo = CalcInfomation.FaceGroupStartIndexArray[offset];
                            for (int k = 0; k < srcFaceNum; ++k)
                            {
                                float w = CommonWeightArray[tableIndex + j + k];
                                if (w <= 0.0f)
                                    continue;

                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + k];
                                if (!trs.IsOverrideTarget)
                                {
                                    continue;
                                }
                                var resultTRS = ResultTRSArray[faceGroupInfo + k];
                                WeightCalcJob.SetTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[faceGroupInfo + k]);
                                ResultTRSArray[faceGroupInfo + k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceNum;
                        }
                    }
                    else
                    {
                        for (int j = 0; j < GroupNum; j += EarWeightGroup.WEIGHT_SIZE, offset++, index++)
                        {
                            var srcFaceNum = DrivenKeyData.FacialPostureNum[(ear * groupNum) + index];
                            var faceGroupInfo = CalcInfomation.FaceGroupStartIndexArray[offset];
                            for (int k = 0; k < srcFaceNum; ++k)
                            {
                                float w = CommonWeightArray[tableIndex + j + k];
                                if (w <= 0.0f)
                                    continue;

                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + k];
                                if (!trs.IsOverrideTarget)
                                {
                                    continue;
                                }
                                var resultTRS = ResultTRSArray[faceGroupInfo + k];
                                WeightCalcJob.CalcTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[faceGroupInfo + k]);
                                ResultTRSArray[faceGroupInfo + k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceNum;
                        }
                    }
                }
                else
                {
                    if (ear == BLEND_FADE_BASE)
                    {
                        //ベースが来ない事はない
                        for (int j = 0; j < GroupNum; j += EarWeightGroup.WEIGHT_SIZE, offset++, index++)
                        {
                            var srcFaceNum = DrivenKeyData.FacialPostureNum[(ear * groupNum) + index];
                            var faceGroupInfo = CalcInfomation.FaceGroupStartIndexArray[offset];
                            for (int k = 0; k < srcFaceNum; ++k)
                            {
                                float w = CommonWeightArray[tableIndex + j + k];
                                if (w <= 0.0f)
                                    continue;

                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + k];
                                var resultTRS = ResultTRSArray[faceGroupInfo + k];
                                WeightCalcJob.SetTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[faceGroupInfo + k]);
                                ResultTRSArray[faceGroupInfo + k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceNum;
                        }
                    }
                    else
                    {
                        for (int j = 0; j < GroupNum; j += EarWeightGroup.WEIGHT_SIZE, offset++, index++)
                        {
                            var srcFaceNum = DrivenKeyData.FacialPostureNum[(ear * groupNum) + index];
                            var faceGroupInfo = CalcInfomation.FaceGroupStartIndexArray[offset];
                            for (int k = 0; k < srcFaceNum; ++k)
                            {
                                float w = CommonWeightArray[tableIndex + j + k];
                                if (w <= 0.0f)
                                    continue;

                                var trs = DrivenKeyData.PostureArray[srcFaceIndex + k];
                                var resultTRS = ResultTRSArray[faceGroupInfo + k];
                                WeightCalcJob.CalcTRS(ref resultTRS, ref trs, w, CalcInfomation.IsValidScaleTransformArray[faceGroupInfo + k]);
                                ResultTRSArray[faceGroupInfo + k] = resultTRS;
                            }
                            srcFaceIndex += srcFaceNum;
                        }
                    }
                }
            }

            /// <summary>
            /// 上書きブレンドする処理
            /// </summary>
            /// <param name="face"></param>
            /// <param name="cachedTRS"></param>
            /// <param name="weightSum"></param>
            /// <param name="tempTRS"></param>
            private void BlendTRSFromFace(int ear, ref CalcTargetInfomation.CalcTRS tempTRS)
            {
                //tempTRSは計算の使いまわしに使用する

                //上書き項目がなければ何もしない
                if (!IsOverrideTarget[ear])
                {
                    return;
                }

                //先頭に必ずベースが入っている
                var baseFaceIndex = DrivenKeyData.FacialStartIndex[BLEND_FADE_BASE];
                var srcFaceIndex = DrivenKeyData.FacialStartIndex[ear];

                var offset = FacialGroupOffset;
                var tableIndex = WeightArray[ear].StartIndex;
                for (int j = 0; j < GroupNum; j += EarWeightGroup.WEIGHT_SIZE, srcFaceIndex++, offset++)
                {
                    var faceGroupIndex = CalcInfomation.FaceGroupStartIndexArray[offset];
                    var srcFaceNum = DrivenKeyData.FacialPostureNum[(ear * GroupNum) + j];
                    for (int k = 0; k < srcFaceNum; ++k)
                    {
                        float w = CommonWeightArray[tableIndex + j + k];
                        if (w <= 0.0f)
                        {
                            continue;
                        }

                        var trs = DrivenKeyData.PostureArray[srcFaceIndex + k];
                        if (!trs.IsOverrideTarget)
                        {
                            continue;
                        }

                        var baseTrs = DrivenKeyData.PostureArray[baseFaceIndex + k];
                        bool isScale = CalcInfomation.IsValidScaleTransformArray[faceGroupIndex + k];
                        var resultTRS = ResultTRSArray[faceGroupIndex + k];
                        WeightCalcJob.AddTRS(ref tempTRS, ref baseTrs, ref trs, w, isScale);
                        WeightCalcJob.BlendTRS(ref resultTRS, ref tempTRS, isScale);
                        ResultTRSArray[faceGroupIndex + k] = resultTRS;
                    }
                    srcFaceIndex += srcFaceNum;
                    baseFaceIndex += srcFaceNum;
                }
            }

            public void CalcTRS()
            {
                for(int i=0;i<EarBlendFaceArray.Length;i++)
                {
                    if ((int)EarBlendFaceArray[i] == BLEND_FACE_SENTINEL)
                    {
                        break;
                    }

                    CalcTRSFromFace((int)EarBlendFaceArray[i]);
                }

                //上書きブレンドの計算を行う
                CalcTargetInfomation.CalcTRS blendTrs = new CalcTargetInfomation.CalcTRS();
                for (int i = 0; i < EarBlendFaceArray.Length; i++)
                {
                    if ((int)EarBlendFaceArray[i] == BLEND_FACE_SENTINEL)
                    {
                        break;
                    }

                    BlendTRSFromFace((int)EarBlendFaceArray[i], ref blendTrs);
                }
            }
        }

        /// <summary>
        /// 耳のWeight計算用クラス
        /// </summary>
        public class EarWeightGroup : WeightGroupBase
        {
            public const int WEIGHT_SIZE = 3;    //耳はXYZにweight値が入る
            //各Weightのオフセット
            public const int WEIGHT_0 = 0;
            public const int WEIGHT_1 = 1;
            public const int WEIGHT_2 = 2;

            private JobHandle _jobHandle;

            public NativeArray<EarType> EarBlendFaceArray;

            public void WaitJob()
            {
                _jobHandle.Complete();
            }

            public EarWeightGroup(DrivenKeyTarget keyTarget)
            {
                FacialGroupOffset = (int)FaceGroupType.EarR;
                int earNum = FaceTypeUtil.GetEarCount();
                EarBlendFaceArray = new NativeArray<EarType>(earNum + 1,Allocator.Persistent);
                Initialize(earNum, FaceTypeUtil.EAR_GROUP_NUM * WEIGHT_SIZE, keyTarget);
            }

            public override void Release()
            {
                base.Release();
                EarBlendFaceArray.Dispose();
            }

            public void ClearTargetWeight(int index,ref NativeArray<WeightGroup> targetWeightGroup)
            {
                int offset = index * WEIGHT_SIZE;
                for (var j = (EarType.Base + 1); j < (EarType.Roll + 1); j++)
                {
                    var tableIndex = targetWeightGroup[(int)j].StartIndex + offset;
                    CommonWeightArray[tableIndex + WEIGHT_0] = 0.0f;
                    CommonWeightArray[tableIndex + WEIGHT_1] = 0.0f;
                    CommonWeightArray[tableIndex + WEIGHT_2] = 0.0f;
                }
            }

            public JobHandle CalcTRS(CalcTargetInfomation weightSum, JobHandle prevJobHandle)
            {
                EarWeightJob job;
                job.CalcInfomation = weightSum.TargetJobWorkData;
                job.CommonWeightArray = CommonWeightArray;
                job.DrivenKeyData = _drivenKeyJobWork;
                job.EarBlendFaceArray = EarBlendFaceArray;
                job.FacialGroupOffset = FacialGroupOffset;
                job.GroupNum = GroupNum;
                job.IsOverrideTarget = _isOverrideTarget;
                job.ResultTRSArray = weightSum.CommonCalcTRSArray;
                job.WeightArray = WeightArray;
                _jobHandle = job.Schedule(prevJobHandle);
                return _jobHandle;
            }

            public override void SetPrevWeight(bool isForce = false)
            {
                for (int i = 0; i < FaceTypeUtil.EAR_GROUP_NUM; i++)
                {
                    SetPrevWeight(i, isForce);
                }
            }

            public override void SetPrevWeight(int i, bool isForce = false)
            {
                int offset = i * WEIGHT_SIZE;
                //現在アニメ中のもの（ 0 < _currentTime < duration ）以外は現在値を設定
                if (CurrentTimeArray[i] == 0.0f || CurrentTimeArray[i] >= DurationTimeArray[i] || isForce)
                {
                    for (int j = 0; j < WeightArray.Length; j++)
                    {
                        int prevTableIndex = PrevWeightArray[j].StartIndex + offset;
                        int weightTableIndex = WeightArray[j].StartIndex + offset;

                        CommonWeightArray[prevTableIndex + WEIGHT_0] = CommonWeightArray[weightTableIndex + WEIGHT_0];
                        CommonWeightArray[prevTableIndex + WEIGHT_1] = CommonWeightArray[weightTableIndex + WEIGHT_1];
                        CommonWeightArray[prevTableIndex + WEIGHT_2] = CommonWeightArray[weightTableIndex + WEIGHT_2];
                    }
                }
            }

            public void AddBlendFace(EarType ear)
            {
                for (int i = 0; i < EarBlendFaceArray.Length - 1; i++)
                {
                    //既にブレンドする顔が登録済み
                    if (EarBlendFaceArray[i] == ear)
                    {
                        break;
                    }

                    //未ブレンドの顔なのでブレンドする顔に登録
                    if ((int)EarBlendFaceArray[i] == BLEND_FACE_SENTINEL)
                    {
                        EarBlendFaceArray[i] = ear;
                        EarBlendFaceArray[i + 1] = (EarType)BLEND_FACE_SENTINEL;
                        blendFacesNum++;
                        break;
                    }
                }
            }

            public override void ClearBlendFace()
            {
                EarBlendFaceArray[0] = 0;
                EarBlendFaceArray[1] = (EarType)BLEND_FACE_SENTINEL;
                blendFacesNum = 1;
            }

            public void GetEarTypes(ref EarTypeSet earTypeSet, bool removeBase = false)
            {
                if (removeBase)
                {
                    earTypeSet.count = blendFacesNum - BLNED_FACE_BASE_OFFSET;
                    NativeArray<EarType>.Copy(EarBlendFaceArray, BLNED_FACE_BASE_OFFSET, earTypeSet.faceType, 0, blendFacesNum - BLNED_FACE_BASE_OFFSET);
                }
                else
                {
                    earTypeSet.count = blendFacesNum;
                    NativeArray<EarType>.Copy(EarBlendFaceArray, earTypeSet.faceType, blendFacesNum);
                }
            }
        }

        [System.Serializable]
        private class ObjectTransInfo
        {
            public bool isEnable = false;
            public Transform objectTrans = null;
        }

        [System.Serializable]
        private class DrivenKeys
        {
            public GameObject rootObj;
            public Transform rootTransform;
            public ObjectTransInfo[] info;
        }

        #endregion クラス

        #region 変数
        private static Dictionary<string, int> _facePartsTypeTableDic = null;
        private static Dictionary<string, EarType> _earTypeTableDic = null;

        private DrivenKeyTarget _eyeTarget = null;
        private bool _isEyeTarget = false;

        private DrivenKeyTarget _eyebrowTarget = null;
        private bool _isEyebrowTarget = false;

        private DrivenKeyTarget _mouthTarget = null;
        private bool _isMouthTarget = false;

        private DrivenKeyTarget _earTarget = null;
        private bool _isEarTarget = false;

        private EyeWeightGroup _eyeWeight = null;
        private EyebrowWeightGroup _eyebrowWeight = null;
        private MouthWeightGroup _mouthWeight = null;
        private EarWeightGroup _earWeight = null;
#if UNITY_EDITOR
        public EyeWeightGroup EyeWeightForEditor => _eyeWeight;
        public EyebrowWeightGroup EyebrowWeightForEditor => _eyebrowWeight;
        public MouthWeightGroup MouthWeightForEditor => _mouthWeight;
        public EarWeightGroup EarWeightForEditor => _earWeight;
#endif // UNITY_EDITOR

        private FacePartsSet[] _facePartsSetArray = null;

        private System.Action<ModelControllerBehaviour> _onEndAnimationAction;

        public System.Action<ModelControllerBehaviour> EndAnimationAction
        {
            set { _onEndAnimationAction = value; }
        }

        private ModelControllerBehaviour _ownerController;

#if CYG_DEBUG
        public FacePartsSet[] FacePartsSetArray { get { return _facePartsSetArray; } }
#endif

        private GameObject _rootObject;

        private bool _isEnable = true;

        public bool IsEnable
        {
            get { return _isEnable; }
            set { _isEnable = value; }
        }

        public int BlendEarNum
        {
            get
            {
                if (_earWeight == null)
                    return 0;
                return _earWeight.BlendFaceNum;
            }
        }

        /// <summary>
        /// 仮想親子設定
        /// </summary>
        private class VirtualParentInfo
        {
            //Root->Parent->Targetという構成になる
            //実際の親子関係としてRoot->Target:Parent(ここは兄弟)となっている場合にRoot情報が必要となる
            public Transform Root;
            public Transform Parent;

            public CalcTargetInfomation.CalcTRSTarget TargetInfo;
        }

        private List<VirtualParentInfo> _virtualParentList;

        private CalcTargetInfomation _weightSum;

        private DrivenKeys[] _drivenKeyArray = null;

        private DrivenKeys[] _drivenKeyForEyeArray = null;

        private DrivenKeys[] _drivenKeyForEarArray = null;

        private Transform[] _eyeTargetTransformArray;
        private Vector3[] _eyeDefaultPositionArray;

        private bool _isInitialized = false;
        public bool IsInitialized
        {
            get
            {
                return _isInitialized;
            }
        }

        public Transform LeftEyeTransform
        {
            get { return _drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform; }
        }

        public Transform RightEyeTransform
        {
            get { return _drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform; }
        }

        public Transform AllEyeTransform
        {
            get { return _drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform; }
        }

        public Transform RootTransform => _drivenkeyRootTransform;

        #region Animationファイルで動かすときのパラメータ

        /// <summary>
        /// フェイシャルアニメーションがセットされていればture
        /// </summary>
        private bool _isClipOverride;
        public bool IsClipOverride { get { return _isClipOverride; } }

        /// <summary>
        /// アニメーションファイル再生中の場合TRUE
        /// </summary>
        private bool _isPlayingAnim;
        public bool IsPlayingAnim => _isPlayingAnim;

        /// <summary>
        /// 目線の境界チェックを行うか
        /// </summary>
        private bool _isCheckEyeRange = true;

        /// <summary>
        /// アニメーションファイル再生中の場合TRUE
        /// </summary>
        private bool _isPlayingEarAnim;
        public bool IsPlayingEarAnim { get { return _isPlayingEarAnim; } }

        //LateUpdate内でPlayDrivenなどが呼び出された場合にnormalizedTimeが正しく取れない
        private bool _isEarPlayFrame;
        private bool _isFacePlayFrame;

        private DrivenKeyPlayableAnimator _playableAnimator;

        public DrivenKeyPlayableAnimator DrivenKeyPlayableAnimator => _playableAnimator;

        private GameObject _drivenkeyLocator;
        private Transform _drivenkeyRootTransform;

        private DrivenKeyAnimation _faceKeyAnimation;
        private FaceTypeSet[] _animationFaceTypeArray;   //アニメーションしている顔種類
        private FaceTypeSet[] _setFaceTypeArray;      //SetFace時の顔種類。最大数の配列の実態をもつ。

        private DrivenKeyAnimation _earKeyAnimation;
#if CYG_DEBUG
        public DrivenKeyAnimation EarKeyAnimation
        {
            get { return _earKeyAnimation; }
        }
#endif
        private EarTypeSet[] _animationEarTypeArray;   //アニメーションしている顔種類
        private EarTypeSet[] _setEarTypeArray;

        //アニメーション終了時の両目の各表情のブレンド率を取っておく
        private float[] _lastAnimLeftEyeWeightArray;
        private float[] _lastAnimRightEyeWeightArray;
        public float[] LastAnimLeftEyeWeightArray { get { return _lastAnimLeftEyeWeightArray; } }
        public float[] LastAnimRightEyeWeightArray { get { return _lastAnimRightEyeWeightArray; } }

        #endregion

        //視線の移動限界値有効チェック
        public bool IsValidEyeLimit
        {
            get; private set;
        }

        #region 視線の移動限界位置

        // 右目寄り目限界座標
        private Vector3 _innerEyeLimitPosR;
        public Vector3 InnerEyeLimitPosR
        {
            get { return _innerEyeLimitPosR; }
        }

        // 右目外目限界座標
        private Vector3 _outerEyeLimitPosR;
        public Vector3 OuterEyeLimitPosR
        {
            get { return _outerEyeLimitPosR; }
        }

        // 左目寄り目限界座標
        private Vector3 _innerEyeLimitPosL;
        public Vector3 InnerEyeLimitPosL
        {
            get { return _innerEyeLimitPosL; }
        }

        // 左目外目限界座標
        private Vector3 _outerEyeLimitPosL;
        public Vector3 OuterEyeLimitPosL
        {
            get { return _outerEyeLimitPosL; }
        }

        // 右目上方向の限界座標
        private Vector3 _upperEyeLimitPosR;
        public Vector3 UpperEyeLimitPosR
        {
            get { return _upperEyeLimitPosR; }
        }

        //右目下方向の限界値
        private Vector3 _downerEyeLimitPosR;
        public Vector3 DownerEyeLimitPosR
        {
            get { return _downerEyeLimitPosR; }
        }

        // 左目上方向の限界座標
        private Vector3 _upperEyeLimitPosL;
        public Vector3 UpperEyeLimitPosL
        {
            get { return _upperEyeLimitPosL; }
        }

        //左目下方向の限界値
        private Vector3 _downerEyeLimitPosL;
        public Vector3 DownerEyeLimitPosL
        {
            get { return _downerEyeLimitPosL; }
        }

        // 奥行方向の限界値
        private float _eyeLimitPosZRMin;
        private float _eyeLimitPosZRMax;
        private float _eyeLimitPosZLMin;
        private float _eyeLimitPosZLMax;

        // 各限界値までのオフセット
        private Vector3 _innerEyeOffsetR;
        private Vector3 _outerEyeOffsetR;
        private Vector3 _upperEyeOffsetR;
        private Vector3 _downerEyeOffsetR;
        private Vector3 _innerEyeOffsetL;
        private Vector3 _outerEyeOffsetL;
        private Vector3 _upperEyeOffsetL;
        private Vector3 _downerEyeOffsetL;
        #endregion

        private List<int> _blendFaceIndexTmpList = new List<int>(FaceTypeUtil.GetFaceCount());

        /// <summary>
        /// ポーズ状態かどうか。
        /// </summary>
        private bool _isPause = false;
        public bool IsPause { get { return _isPause; } }
        private float _prevAnimationSpeed = 0;

        //補間タイプ
        private InterpolateType _eyeInterpolateType = InterpolateType.Linear;
        private InterpolateType _eyebrowInterpolateType = InterpolateType.Linear;
        private InterpolateType _mouthInterpolateType = InterpolateType.Linear;
        private InterpolateType _earInterpolateType = InterpolateType.Linear;

        /// <summary>
        /// 表情のアニメータ更新を自前で行うかどうかのフラグ
        /// falseは自動、trueは手動（カットで時間指定など）
        /// </summary>
        private bool _isSelfControllTime = false;
        public bool IsSelfControllTime { get { return _isSelfControllTime; } set { _isSelfControllTime = value; } }

        #endregion 変数


        public static void InitializeDrivenSystem()
        {
            if (_facePartsTypeTableDic == null)
            {
                _facePartsTypeTableDic = new Dictionary<string, int>(FaceTypeUtil.GetEyeCount() + FaceTypeUtil.GetEyebrowCount() + FaceTypeUtil.GetMouthCount());
                _earTypeTableDic = new Dictionary<string, EarType>(FaceTypeUtil.GetEarCount());
            }
        }

        public DrivenKeyComponent(GameObject rootObject)
        {
#if CYG_DEBUG
            DebugUtils.Assert(rootObject != null);
#endif
            _rootObject = rootObject;
        }

        public Transform GetDrivenKeyRootTransform(FaceGroupType faceGroupType)
        {
            if (faceGroupType == FaceGroupType.EarL || faceGroupType == FaceGroupType.EarR)
            {
                return _drivenKeyForEarArray[(int)faceGroupType].rootTransform;
            }

            return _drivenKeyArray[(int)(faceGroupType - FaceGroupType.EyeR)].rootTransform;
        }

        public void DestroyDrivenKeyLocator()
        {
            //キーロケーターを削除する、以降ドリブンを使用していはいけない
            _drivenkeyLocator = null;
            _drivenkeyRootTransform = null;

            _faceKeyAnimation = null;
            _earKeyAnimation = null;
            _animationFaceTypeArray = null;
            _setFaceTypeArray = null;
            _animationEarTypeArray = null;
            _setEarTypeArray = null;
            _playableAnimator?.ClearClip();
        }


        public void SetFaceTime(float facialFixedTime)
        {
            if (_playableAnimator == null)
                return;
            if (!_playableAnimator.ActiveSelf)
                return;

            //再生扱いにしないと、値の流し込みが有効にならない
            _isPlayingAnim = true;
            _isFacePlayFrame = false;
            //何かしらのモーションは指定されているはず
            if (_faceKeyAnimation != null)
            {
                _isCheckEyeRange = _faceKeyAnimation.IsCheckEyeRange;
                if( _faceKeyAnimation.clip.isLooping == false &&
                    _faceKeyAnimation.clip.length <= facialFixedTime )
                {
                    // アニメぴったりにするとニュートラルのようになることがあるのでほんの少し手前にする
                    facialFixedTime = _faceKeyAnimation.clip.length - CLIP_END_MARGIN;
                }
            }

            //オーバーライド扱いにしないと視線アニメが効かない
            _isClipOverride = true;
            //秒数
            _playableAnimator.Play(DrivenKeyPlayableAnimator.Layer.Base, facialFixedTime);
        }

        public void SetEarTime(float earFixedTime)
        {
            if (_playableAnimator == null)
                return;
            if (!_playableAnimator.ActiveSelf)
                return;

            //再生扱いにしないと、値の流し込みが有効にならない
            _isPlayingEarAnim = true;
            _isEarPlayFrame = true;
            _playableAnimator.Play(DrivenKeyPlayableAnimator.Layer.Ear, earFixedTime);
        }

        /// <summary>
        /// 秒指定でアニメーションの時間を指定して再生する
        /// </summary>
        /// <param name="facialFixedTime"></param>
        /// <param name="earFixedTime"></param>
        public void SetTime(float facialFixedTime, float earFixedTime)
        {
            SetFaceTime(facialFixedTime);
            SetEarTime(earFixedTime);
        }

        /// <summary>
        /// 秒指定でアニメーションの時間を指定して再生する
        /// </summary>
        /// <param name="time"></param>
        public void SetTime(float time)
        {
            SetFaceTime(time);
            SetEarTime(time);
        }

        /// <summary>
        /// 目の限界値を初期かする
        /// </summary>
        /// <param name="targetData"></param>
        /// <param name="transformCacheTable"></param>
        public void InitializeEyeRange(FaceDrivenKeyTarget faceTarget, DrivenKeyTarget earTarget, Dictionary<string, Transform> transformCacheTableDic)
        {
            IsValidEyeLimit = false;

            // ターゲットボーンの参照を持たせる
            _eyeTargetTransformArray = new Transform[LOCATOR_CONTROL_FOR_EYE_ARRAY.Length - 1];
            _eyeDefaultPositionArray = new Vector3[LOCATOR_CONTROL_FOR_EYE_ARRAY.Length - 1];

            // 毎フレーム適用するweightの総計を保持するための枠を作っておく
            // 0番の顔がすべてのボーンを持っているのでサイズは同じでいいはず
            _weightSum = new CalcTargetInfomation();

            _virtualParentList = new List<VirtualParentInfo>();

#if CYG_DEBUG
            bool boneMissing = false;
#endif
            #region FaceGroupの0番目は専用の処理が入るのでループを分ける

            //顔
            if (faceTarget != null)
            {
                for (int j = (int)FaceGroupType.EyeR; j <= (int)FaceGroupType.Mouth; j++)
                {
                    var faceGroup = faceTarget._eyeTarget[0]._faceGroupInfo;
                    var srcArray = faceGroup[0]._trsArray;
                    switch (j)
                    {
                        case (int)FaceGroupType.EyeR:
                            {
                                faceGroup = faceTarget._eyeTarget[0]._faceGroupInfo;
                                srcArray = faceGroup[0]._trsArray;
                                break;
                            }
                        case (int)FaceGroupType.EyeL:
                            {
                                faceGroup = faceTarget._eyeTarget[0]._faceGroupInfo;
                                srcArray = faceGroup[(int)FaceGroupType.EyeL - (int)FaceGroupType.EyeR]._trsArray;
                                break;
                            }
                        case (int)FaceGroupType.EyebrowR:
                            {
                                faceGroup = faceTarget._eyebrowTarget[0]._faceGroupInfo;
                                srcArray = faceGroup[0]._trsArray;
                                break;
                            }
                        case (int)FaceGroupType.EyebrowL:
                            {
                                faceGroup = faceTarget._eyebrowTarget[0]._faceGroupInfo;
                                srcArray = faceGroup[(int)FaceGroupType.EyebrowL - (int)FaceGroupType.EyebrowR]._trsArray;
                                break;
                            }
                        case (int)FaceGroupType.Mouth:
                            {
                                faceGroup = faceTarget._mouthTarget[0]._faceGroupInfo;
                                srcArray = faceGroup[0]._trsArray;
                                break;
                            }
                    }
                    int arraySize = srcArray.Length;
                    _weightSum.FaceGroupInfoArray[j].Initialize(arraySize);
                    for (int k = 0; k < srcArray.Length; k++)
                    {
                        _weightSum.FaceGroupInfoArray[j].ForceUpdateArray[k] = false;
                        var trsTargetInfo = new CalcTargetInfomation.CalcTRSTarget();

                        TRS trsAsset = srcArray[k];
                        // Baseのターゲットから両目の初期位置を取得
                        var nodeName = trsAsset._path.Substring(trsAsset._path.LastIndexOf('/') + 1);

                        if (transformCacheTableDic.TryGetValue(nodeName, out trsTargetInfo.TargetTransform))
                        {
                            if (_eyeTargetTransformArray[RIGHT_EYE_INDEX] == null
                            ||  _eyeTargetTransformArray[LEFT_EYE_INDEX] == null)
                            {
                                if (nodeName.Equals(EYE_R))
                                {
                                    _eyeTargetTransformArray[RIGHT_EYE_INDEX] = trsTargetInfo.TargetTransform;
                                    _eyeDefaultPositionArray[RIGHT_EYE_INDEX] = trsAsset._position;
                                    _weightSum.FaceGroupInfoArray[j].ForceUpdateArray[k] = true;
                                }
                                else if (nodeName.Equals(EYE_L))
                                {
                                    _eyeTargetTransformArray[LEFT_EYE_INDEX] = trsTargetInfo.TargetTransform;
                                    _eyeDefaultPositionArray[LEFT_EYE_INDEX] = trsAsset._position;
                                    _weightSum.FaceGroupInfoArray[j].ForceUpdateArray[k] = true;
                                }
                            }
                            trsTargetInfo.IsValidTargetTransform = true;
                            trsTargetInfo.IsValidScaleTransform = true;
                            _weightSum.FaceGroupInfoArray[j].CalcTrsTargetArray[k] = trsTargetInfo;
                        }
#if CYG_DEBUG
                        else
                        {
                            //ログを骨がない度に出すと重い・・・
                            boneMissing = true;
                            _weightSum.FaceGroupInfoArray[j].CalcTrsTargetArray[k] = trsTargetInfo;
                        }
#endif
                    }
                }
            }
            else
            {
                for (int j = (int)FaceGroupType.EyeR; j <= (int)FaceGroupType.Mouth; j++)
                {
                    _weightSum.FaceGroupInfoArray[j].Initialize(0);
                }
            }

            #region 耳初期化

            //耳
            var targetEar = earTarget._targetFaces[0];
            for (int j = (int)FaceGroupType.EarR; j <= (int)FaceGroupType.EarL; j++)
            {
                var faceGroup = targetEar._faceGroupInfo;
                var srcArray = faceGroup[j - (int)FaceGroupType.EarR]._trsArray;
                int arraySize = srcArray.Length;
                _weightSum.FaceGroupInfoArray[j].Initialize(arraySize);
                for (int k = 0; k < srcArray.Length; k++)
                {
                    var trsTargetInfo = new CalcTargetInfomation.CalcTRSTarget();

                    TRS trsAsset = srcArray[k];
                    // Baseのターゲットから両目の初期位置を取得
                    var nodeName = trsAsset._path.Substring(trsAsset._path.LastIndexOf('/') + 1);
                    if (transformCacheTableDic.TryGetValue(nodeName, out trsTargetInfo.TargetTransform))
                    {
                        trsTargetInfo.IsValidTargetTransform = true;
                        trsTargetInfo.IsValidScaleTransform = SCALE_BONE_ARRAY.Contains(trsAsset._path.Split('/').Last());
                        _weightSum.FaceGroupInfoArray[j].CalcTrsTargetArray[k] = trsTargetInfo;
                    }
#if CYG_DEBUG
                    else
                    {
                        //ログを骨がない度に出すと重い・・・
                        boneMissing = true;
                        _weightSum.FaceGroupInfoArray[j].CalcTrsTargetArray[k] = trsTargetInfo;
                    }
#endif
                }
            }

            #endregion

            //Job処理のための初期化する
            _weightSum.SetupJobWork();

            #endregion

#if CYG_DEBUG
            if (boneMissing) Debug.LogWarning("ドリブンキーとモデルのボーンの整合性が取れていない " + _rootObject.name);
#endif

            #region 左右の目の移動値の限界値を取得する
            if (faceTarget != null)
            {
                IsValidEyeLimit = true;
                //左目の寄り目の限界値
                var innerEyeRangeFaceEyeL = faceTarget._eyeTarget[IN_OUT_EYE_RANGE_FACE]._faceGroupInfo[(int)TargetInfomation.Part.EyeL]._trsArray.FirstOrDefault(trs => (trs._path.EndsWith("/" + EYE_L) || trs._path.Equals(EYE_L)));
                if (innerEyeRangeFaceEyeL != null)
                {
                    _innerEyeLimitPosL = innerEyeRangeFaceEyeL._position + _eyeDefaultPositionArray[LEFT_EYE_INDEX];
                    //右目の寄り目の限界値
                    _innerEyeLimitPosR = new Vector3(
                        -innerEyeRangeFaceEyeL._position.x + _eyeDefaultPositionArray[RIGHT_EYE_INDEX].x,
                        innerEyeRangeFaceEyeL._position.y + _eyeDefaultPositionArray[RIGHT_EYE_INDEX].y,
                        innerEyeRangeFaceEyeL._position.z + _eyeDefaultPositionArray[RIGHT_EYE_INDEX].z);
                }
                else
                {
                    IsValidEyeLimit = false;
                }

                //右目の外目の限界値
                var outerEyeRangeFaceEyeR = faceTarget._eyeTarget[IN_OUT_EYE_RANGE_FACE]._faceGroupInfo[(int)TargetInfomation.Part.EyeR]._trsArray.FirstOrDefault(trs => (trs._path.EndsWith("/" + EYE_R) || trs._path.Equals(EYE_R)));
                if (outerEyeRangeFaceEyeR != null)
                {
                    _outerEyeLimitPosR = outerEyeRangeFaceEyeR._position + _eyeDefaultPositionArray[RIGHT_EYE_INDEX];
                    //左目の外目の限界値
                    _outerEyeLimitPosL = new Vector3(
                        -outerEyeRangeFaceEyeR._position.x + _eyeDefaultPositionArray[LEFT_EYE_INDEX].x,
                        outerEyeRangeFaceEyeR._position.y + _eyeDefaultPositionArray[LEFT_EYE_INDEX].y,
                        outerEyeRangeFaceEyeR._position.z + _eyeDefaultPositionArray[LEFT_EYE_INDEX].z);
                }
                else
                {
                    IsValidEyeLimit = false;
                }

                //上下の目の移動量の限界値を取得する
                var upperEyeRangeFaceEyeL = faceTarget._eyeTarget[UP_DOWN_EYE_RANGE_FACE]._faceGroupInfo[(int)TargetInfomation.Part.EyeL]._trsArray.FirstOrDefault(trs => (trs._path.EndsWith("/" + EYE_L) || trs._path.Equals(EYE_L)));
                if (upperEyeRangeFaceEyeL != null)
                {
                    _upperEyeLimitPosL = upperEyeRangeFaceEyeL._position + _eyeDefaultPositionArray[LEFT_EYE_INDEX];
                    _upperEyeLimitPosR = new Vector3(
                        -upperEyeRangeFaceEyeL._position.x + _eyeDefaultPositionArray[RIGHT_EYE_INDEX].x,
                        upperEyeRangeFaceEyeL._position.y + _eyeDefaultPositionArray[RIGHT_EYE_INDEX].y,
                        upperEyeRangeFaceEyeL._position.z + _eyeDefaultPositionArray[RIGHT_EYE_INDEX].z);
                }
                else
                {
                    IsValidEyeLimit = false;
                }

                var downerEyeRangeFaceEyeR = faceTarget._eyeTarget[UP_DOWN_EYE_RANGE_FACE]._faceGroupInfo[(int)TargetInfomation.Part.EyeR]._trsArray.FirstOrDefault(trs => (trs._path.EndsWith("/" + EYE_R) || trs._path.Equals(EYE_R)));
                if (downerEyeRangeFaceEyeR != null)
                {
                    _downerEyeLimitPosR = downerEyeRangeFaceEyeR._position + _eyeDefaultPositionArray[RIGHT_EYE_INDEX];
                    _downerEyeLimitPosL = new Vector3(
                        -downerEyeRangeFaceEyeR._position.x + _eyeDefaultPositionArray[LEFT_EYE_INDEX].x,
                        downerEyeRangeFaceEyeR._position.y + _eyeDefaultPositionArray[LEFT_EYE_INDEX].y,
                        downerEyeRangeFaceEyeR._position.z + _eyeDefaultPositionArray[LEFT_EYE_INDEX].z);
                }
                else
                {
                    IsValidEyeLimit = false;
                }
            }
            else
            {
                IsValidEyeLimit = false;
            }

            // 奥行は上下と左右の範囲から調べるしかない。
            _eyeLimitPosZRMin = Mathf.Min(_innerEyeLimitPosR.z, _outerEyeLimitPosR.z, _upperEyeLimitPosR.z, _downerEyeLimitPosR.z);
            _eyeLimitPosZRMax = Mathf.Max(_innerEyeLimitPosR.z, _outerEyeLimitPosR.z, _upperEyeLimitPosR.z, _downerEyeLimitPosR.z);
            _eyeLimitPosZLMin = Mathf.Min(_innerEyeLimitPosL.z, _outerEyeLimitPosL.z, _upperEyeLimitPosL.z, _downerEyeLimitPosL.z);
            _eyeLimitPosZLMax = Mathf.Max(_innerEyeLimitPosL.z, _outerEyeLimitPosL.z, _upperEyeLimitPosL.z, _downerEyeLimitPosL.z);

            // 各限界値までのオフセットを計算しておく。
            _innerEyeOffsetR = _innerEyeLimitPosR - _eyeDefaultPositionArray[RIGHT_EYE_INDEX];
            _outerEyeOffsetR = -_outerEyeLimitPosR + _eyeDefaultPositionArray[RIGHT_EYE_INDEX];
            _upperEyeOffsetR = _upperEyeLimitPosR - _eyeDefaultPositionArray[RIGHT_EYE_INDEX];
            _downerEyeOffsetR = -_downerEyeLimitPosR + _eyeDefaultPositionArray[RIGHT_EYE_INDEX];
            _innerEyeOffsetL = _innerEyeLimitPosL - _eyeDefaultPositionArray[LEFT_EYE_INDEX];
            _outerEyeOffsetL = -_outerEyeLimitPosL + _eyeDefaultPositionArray[LEFT_EYE_INDEX];
            _upperEyeOffsetL = _upperEyeLimitPosL - _eyeDefaultPositionArray[LEFT_EYE_INDEX];
            _downerEyeOffsetL = -_downerEyeLimitPosL + _eyeDefaultPositionArray[LEFT_EYE_INDEX];

            #endregion
        }

        private void InitializeFaceDrivenKeyInfo(DrivenKeys drivenKey)
        {
            int faceNum = Mathf.Max(FaceTypeUtil.GetEyeCount(), FaceTypeUtil.GetEyebrowCount(), FaceTypeUtil.GetMouthCount());
            drivenKey.info = new ObjectTransInfo[faceNum];
            for (int i = 0; i < drivenKey.info.Length; ++i)
            {
                drivenKey.info[i] = new ObjectTransInfo();
            }

            //ドリブンキーカーブの再生結果が入っているGameObjectを探しておく
            int childNum = drivenKey.rootTransform.childCount;
            for (int j = 0; j < childNum; ++j)
            {
                var t = drivenKey.rootTransform.GetChild(j);
                int facePartsType;
                if (!_facePartsTypeTableDic.TryGetValue(t.name, out facePartsType))
                {
                    int nameStartIndex = t.name.IndexOf(FACE_TYPE_NAME_DIVIDER) + FACE_TYPE_NAME_DIVIDER.Length;
                    if (nameStartIndex >= t.name.Length)
                    {
                        Debug.LogWarning(t.name + "からフェースタイプを判別できません " + _rootObject.name);
                        continue;
                    }

                    string partName = t.name.Substring(0, nameStartIndex).Replace("_", ""); //パーツ名の取得
                    FaceGroupType partType;
                    if (!EnumUtil.TryParse<FaceGroupType>(partName, out partType))
                    {
                        Debug.LogWarning(t.name + "からフェースタイプを判別できません " + _rootObject.name);
                        continue;
                    }
                    facePartsType = -1;

                    string faceTypeName = t.name.Substring(nameStartIndex);

                    if (partType == FaceGroupType.EyebrowL || partType == FaceGroupType.EyebrowR)
                    {
                        FaceEyebrowType faceType;
                        if (EnumUtil.TryParse<FaceEyebrowType>(faceTypeName, out faceType))
                        {
                            facePartsType = (int)faceType;
                        }
                    }
                    else if (partType == FaceGroupType.EyeL || partType == FaceGroupType.EyeR)
                    {
                        FaceEyeType faceType;
                        if (EnumUtil.TryParse<FaceEyeType>(faceTypeName, out faceType))
                        {
                            facePartsType = (int)faceType;
                        }
                    }
                    else if (partType == FaceGroupType.Mouth)
                    {
                        FaceMouthType faceType;
                        if (EnumUtil.TryParse<FaceMouthType>(faceTypeName, out faceType))
                        {
                            facePartsType = (int)faceType;
                        }
                    }
                    if (facePartsType >= 0)
                    {
                        drivenKey.info[facePartsType].objectTrans = t;
                        drivenKey.info[facePartsType].isEnable = true;

                        _facePartsTypeTableDic.Add(t.name, facePartsType);
                    }
                    else
                    {
                        Debug.LogWarning(t.name + "からフェースタイプを判別できません " + _rootObject.name);
                    }
                }
                else
                {
                    drivenKey.info[facePartsType].objectTrans = t;
                    drivenKey.info[facePartsType].isEnable = true;
                }
            }
        }

        private void InitializeEarDrivenKeyInfo(DrivenKeys drivenKey)
        {
            int earNum = FaceTypeUtil.GetEarCount();
            drivenKey.info = new ObjectTransInfo[earNum];
            for (int i = 0; i < drivenKey.info.Length; ++i)
            {
                drivenKey.info[i] = new ObjectTransInfo();
            }

            //ドリブンキーカーブの再生結果が入っているGameObjectを探しておく
            int childNum = drivenKey.rootTransform.childCount;
            for (int j = 0; j < childNum; ++j)
            {
                var t = drivenKey.rootTransform.GetChild(j);
                EarType earType;
                if (!_earTypeTableDic.TryGetValue(t.name, out earType))
                {
                    int nameStartIndex = t.name.IndexOf(FACE_TYPE_NAME_DIVIDER) + FACE_TYPE_NAME_DIVIDER.Length;
                    if (nameStartIndex >= t.name.Length)
                    {
                        Debug.LogWarning(t.name + "からフェースタイプを判別できません " + _rootObject.name);
                        continue;
                    }

                    string faceTypeName = t.name.Substring(nameStartIndex);

                    if (EnumUtil.TryParse<EarType>(faceTypeName, out earType))
                    {
                        int index = (int)earType;
                        drivenKey.info[index].objectTrans = t;
                        drivenKey.info[index].isEnable = true;

                        _earTypeTableDic.Add(t.name, earType);
                    }
                    else
                    {
                        Debug.LogWarning(t.name + "からフェースタイプを判別できません " + _rootObject.name);
                    }
                }
                else
                {
                    int index = (int)earType;
                    drivenKey.info[index].objectTrans = t;
                    drivenKey.info[index].isEnable = true;
                }
            }
        }

        private bool ValidateTarget(DrivenKeyTarget target, int faceGroupNum)
        {
            if (target == null)
            {
                Debug.LogError("ドリブンキー再生結果データが見つからない ");
                return false;
            }

            // ドリブンキーターゲット内のデータが存在しない
            if (target._targetFaces.Length == 0)
            {
                Debug.Log("ドリブンキーターゲット内のデータが存在しない " + _rootObject.name);
                return false;
            }

            if (target._targetFaces[0]._faceGroupInfo.Length != faceGroupNum)
            {
                Debug.LogError("ドリブンキー再生結果データに含まれるFaceGroup数が不正な値です " + target._targetFaces[0]._faceGroupInfo.Length);
                return false;
            }

#if CYG_DEBUG
            DebugUtils.Assert(target._targetFaces.Length > 0);
#endif

            return true;
        }

        private void InitializeFaceTypeSet()
        {
            //初期は空ループを回す
            _animationFaceTypeArray = new FaceTypeSet[0];
            _animationEarTypeArray = new EarTypeSet[0];
            // 実態作成。
            _setFaceTypeArray = new FaceTypeSet[DrivenKeyAnimation.FaceNum];
            for (int i = 0; i < _setFaceTypeArray.Length; ++i)
            {
                _setFaceTypeArray[i] = new FaceTypeSet();
                _setFaceTypeArray[i].faceType = new int[FaceTypeUtil.GetFaceCount() + 1];
                _setFaceTypeArray[i].count = 0;
            }

            _setEarTypeArray = new EarTypeSet[DrivenKeyAnimation.EarNum];
            for (int i = 0; i < _setEarTypeArray.Length; ++i)
            {
                _setEarTypeArray[i] = new EarTypeSet();
                _setEarTypeArray[i].faceType = new EarType[FaceTypeUtil.GetEarCount() + 1];
                _setEarTypeArray[i].count = 0;
            }
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public void InitializeFacial(ref FacialContext context)
        {
            var facialTargetData = context.FacialTargetData;
            var earTarget = context.EarTarget;
            var facePartsSetArray = context.FacePartsSetArray;

            _ownerController = context.OwnerController;
            if (facialTargetData != null)
            {
                //再生結果のフェイシャルリストのファイル
                if (_eyeTarget == null)
                {
                    DrivenKeyTarget eyeTarget = ScriptableObject.CreateInstance<DrivenKeyTarget>();
#if UNITY_EDITOR
                    //名前未設定と分かるようにしておく
                    eyeTarget.name = "(None)";
#endif
                    eyeTarget._targetFaces = facialTargetData._eyeTarget;
                    if (!ValidateTarget(eyeTarget, DrivenKeyAnimation.EyeNum))
                    {
                        GameObject.Destroy(eyeTarget);
                        return;
                    }

                    _eyeTarget = eyeTarget;
                    _isEyeTarget = true;
                }
                if (_eyebrowTarget == null)
                {
                    DrivenKeyTarget eyebrowTarget = ScriptableObject.CreateInstance<DrivenKeyTarget>();
#if UNITY_EDITOR
                    //名前未設定と分かるようにしておく
                    eyebrowTarget.name = "(None)";
#endif
                    eyebrowTarget._targetFaces = facialTargetData._eyebrowTarget;
                    if (!ValidateTarget(eyebrowTarget, DrivenKeyAnimation.EyebrowNum))
                    {
                        GameObject.Destroy(eyebrowTarget);
                        return;
                    }

                    _eyebrowTarget = eyebrowTarget;
                    _isEyebrowTarget = true;
                }
                if (_mouthTarget == null)
                {
                    DrivenKeyTarget mouthTarget = ScriptableObject.CreateInstance<DrivenKeyTarget>();
#if UNITY_EDITOR
                    //名前未設定と分かるようにしておく
                    mouthTarget.name = "(None)";
#endif
                    mouthTarget._targetFaces = facialTargetData._mouthTarget;
                    if (!ValidateTarget(mouthTarget, DrivenKeyAnimation.MouthNum))
                    {
                        GameObject.Destroy(mouthTarget);
                        return;
                    }

                    _mouthTarget = mouthTarget;
                    _isMouthTarget = true;
                }
            }

            if (_earTarget == null)
            {
                if (!ValidateTarget(earTarget, DrivenKeyAnimation.EarNum))
                    return;

                _earTarget = earTarget;
                _isEarTarget = true;
            }

            //ウェイトグループの作成
            _eyeWeight = new EyeWeightGroup(_eyeTarget);
            _eyebrowWeight = new EyebrowWeightGroup(_eyebrowTarget);
            _mouthWeight = new MouthWeightGroup(_mouthTarget);
            _earWeight = new EarWeightGroup(_earTarget);

            _facePartsSetArray = facePartsSetArray;

            int eyeCount = FaceTypeUtil.GetEyeCount();
            _lastAnimLeftEyeWeightArray = new float[eyeCount];
            _lastAnimRightEyeWeightArray = new float[eyeCount];

            _lastAnimLeftEyeWeightArray[0] = 1.0f;
            _lastAnimRightEyeWeightArray[0] = 1.0f;

            InitializeFaceTypeSet();

#if CYG_DEBUG
            if (_facePartsTypeTableDic == null)
            {
                InitializeDrivenSystem();
            }
#endif

            //カーブ再生用ロケータ
            if (_drivenKeyArray == null)
            {
                //クリップを入れ替えたいのでOverrideController
                _drivenkeyLocator = context.KeyLocator;
                _drivenkeyLocator.name = context.KeyLocator.name;
                _drivenkeyLocator.transform.SetParent(_rootObject.transform);
                _drivenkeyRootTransform = _drivenkeyLocator.transform;
                _drivenKeyArray = new DrivenKeys[LOCATOR_CONTROL_ARRAY.Length];
                for (int i = 0; i < _drivenKeyArray.Length; i++)
                {
                    _drivenKeyArray[i] = new DrivenKeys();
                    var root = _drivenkeyLocator.transform.Find(LOCATOR_CONTROL_ARRAY[i]);
                    _drivenKeyArray[i].rootObj = root.gameObject;

                    if (_drivenKeyArray[i].rootObj != null)
                    {
                        _drivenKeyArray[i].rootTransform = _drivenKeyArray[i].rootObj.transform;
                        InitializeFaceDrivenKeyInfo(_drivenKeyArray[i]);
                    }
                }

                //耳のドリブン初期化
                _drivenKeyForEarArray = new DrivenKeys[LOCATOR_CONTROL_FOR_EAR_ARRAY.Length];
                for (int i = 0; i < _drivenKeyForEarArray.Length; i++)
                {
                    _drivenKeyForEarArray[i] = new DrivenKeys();
                    _drivenKeyForEarArray[i].rootObj = _drivenkeyLocator.transform.Find(LOCATOR_CONTROL_FOR_EAR_ARRAY[i]).gameObject;
                    if (_drivenKeyForEarArray[i].rootObj != null)
                    {
                        _drivenKeyForEarArray[i].rootTransform = _drivenKeyForEarArray[i].rootObj.transform;
                        InitializeEarDrivenKeyInfo(_drivenKeyForEarArray[i]);
                    }
                }

                // 両目のリグのXY値を取得
                _drivenKeyForEyeArray = new DrivenKeys[LOCATOR_CONTROL_FOR_EYE_ARRAY.Length];
                for (int i = 0; i < _drivenKeyForEyeArray.Length; i++)
                {
                    _drivenKeyForEyeArray[i] = new DrivenKeys();
                    _drivenKeyForEyeArray[i].rootObj = _drivenkeyLocator.transform.Find(LOCATOR_CONTROL_FOR_EYE_ARRAY[i]).gameObject;
                    if (_drivenKeyForEyeArray[i].rootObj != null)
                        _drivenKeyForEyeArray[i].rootTransform = _drivenKeyForEyeArray[i].rootObj.transform;
                }
#if UNITY_EDITOR && CYG_DEBUG
                _overwriteEyeballPosArray = new Vector3[LOCATOR_CONTROL_FOR_EYE_ARRAY.Length];
                for (int i = 0; i < _overwriteEyeballPosArray.Length; i++)
                {
                    _overwriteEyeballPosArray[i] = Math.VECTOR3_ZERO;
                }
#endif // UNITY_EDITOR && CYG_DEBUG
            }

            //Animator初期化
            if(_playableAnimator == null)
            {
                var animator = _drivenkeyLocator.GetComponent<Animator>();
                string drivenName = null;
#if CYG_DEBUG
                if (context.OwnerController != null)
                {
                    drivenName = context.OwnerController.GetBuildInfo().Name;
                }
                else
                {
                    drivenName = _rootObject.name;
                }
#endif
                _playableAnimator = new DrivenKeyPlayableAnimator(animator, drivenName);
            }

            //ブレンドする顔のインデックスを保持
            //毎フレーム配列の確保を防ぐために配列を使いまわすため、番兵を使用する。
            ClearBlendFaces();
            _isInitialized = true;
        }

        /// <summary>
        /// 耳をアニメーションドリブンによって動かす
        /// </summary>
        /// <param name="keyAnimation"></param>
        /// <param name="durationTime"></param>
        public void PlayEar(DrivenKeyAnimation keyAnimation, float durationTime = 0.0f)
        {
#if CYG_DEBUG
            if (!IsEarTargetEnable())
            {
                return;
            }
#endif
            if (keyAnimation == null)
            {
                Debug.LogWarning("DrivenKeyAnimation is null.");
                return;
            }

            //アニメデータセット
            _earKeyAnimation = keyAnimation;
            _animationEarTypeArray = keyAnimation.animationEarType;
            if (!_playableAnimator.ActiveSelf)
                return;
            _playableAnimator.SetClip(DrivenKeyPlayableAnimator.ClipState.Ear, keyAnimation.clip);
            _playableAnimator.Play(DrivenKeyPlayableAnimator.Layer.Ear, 0.0f);

            //ブレンドの時には_animationEarTypeを作り直す必要がある
            if (durationTime > 0.0f)
            {
                _earWeight.SetPrevWeight();
                _earWeight.SetDurationTime(durationTime);

                for (int i = 0; i < _setEarTypeArray.Length; i++)
                {
                    _earWeight.ClearTargetWeight(i, ref _earWeight.TargetWeightArray);
                    _earWeight.GetEarTypes(ref _setEarTypeArray[i], true);
                }

                //アニメーション分を合成する
                var animationEar = keyAnimation.animationEarType;
                for (int i = 0; i < animationEar.Length; i++)
                {
                    var earType = animationEar[i].faceType;
                    for (int j = 0; j < earType.Length; j++)
                    {
                        if (!_ContainsEar(_setEarTypeArray[i].faceType, earType[j], _setEarTypeArray[i].count))
                        {
                            _setEarTypeArray[i].faceType[_setEarTypeArray[i].count] = earType[j];
                            _setEarTypeArray[i].count++;
                        }
                    }
                }
                _animationEarTypeArray = _setEarTypeArray;
            }

            if (ClearPlayAnimationClipParameter(_playableAnimator, _isSelfControllTime))
            {
                _isPlayingEarAnim = true;
            }

            _isEarPlayFrame = true;
            _earWeight.IsCalcWeight = false;
        }

        /// <summary>
        /// Weight値をクリアする
        /// </summary>
        private void ClearWeight(bool isClearEarWeight = true)
        {
            //Weight値をリセットする必要がある
            _eyeWeight.ClearWeight();
            _eyebrowWeight.ClearWeight();
            _mouthWeight.ClearWeight();
            if (isClearEarWeight)
            {
                _earWeight.ClearWeight();
            }
        }

        private void BlendFaceTarget(DrivenKeyAnimation keyAnimation, float durationTime, bool useBlendEye)
        {
            if (useBlendEye)
            {
                BlendFaceTarget(_eyeWeight, durationTime);
            }
            BlendFaceTarget(_eyebrowWeight, durationTime);
            BlendFaceTarget(_mouthWeight, durationTime);

            //アニメーション分を合成する
            var animationFace = keyAnimation.animationFaceType;
            for (int i = 0; i < animationFace.Length; i++)
            {
                var faceType = animationFace[i].faceType;
                for (int j = 0; j < faceType.Length; j++)
                {
                    if (!_ContainsFace(_setFaceTypeArray[i].faceType, faceType[j], _setFaceTypeArray[i].count))
                    {
                        _setFaceTypeArray[i].faceType[_setFaceTypeArray[i].count] = faceType[j];
                        _setFaceTypeArray[i].count++;
                    }
                }
            }
            _animationFaceTypeArray = _setFaceTypeArray;
        }

        private void BlendFaceTarget(FaceWeightGroup faceWeight, float durationTime)
        {
            faceWeight.SetPrevWeight();
            faceWeight.SetDurationTime(durationTime);
            faceWeight.ClearTargetWeight();
            
            var faceTypeIndex = faceWeight.FacialGroupOffset - DrivenKeyAnimation.FaceNumGroupDiff;
            for (int i = 0; i < faceWeight.GroupNum; i++)
            {
                faceWeight.GetFaceTypes(ref _setFaceTypeArray[faceTypeIndex + i], true);
            }
        }

        public void Play(DrivenKeyAnimation keyAnimation, float normalizedTime = 0f, float durationTime = 0.0f, bool useBlendEye = false, bool isClearEarWeight = true)
        {
#if CYG_DEBUG
            if (!IsTargetEnable())
            {
                return;
            }
#endif

            if (keyAnimation == null)
            {
                Debug.LogWarning("DrivenKeyAnimation is null.");
                return;
            }

            //アニメデータセット
            _faceKeyAnimation = keyAnimation;
            _isCheckEyeRange = keyAnimation.IsCheckEyeRange;
            if (!_playableAnimator.ActiveSelf)
                return;
            _playableAnimator.SetClip(DrivenKeyPlayableAnimator.ClipState.Base, keyAnimation.clip);
            //再生
            // NormalizeTimeを設定しないと最初から再生されない
            _playableAnimator.PlayFromNormalizeTime(DrivenKeyPlayableAnimator.Layer.Base, normalizedTime);

            _animationFaceTypeArray = keyAnimation.animationFaceType;

            if (durationTime > 0f)
            {// アニメーション変更時の補間処理
                BlendFaceTarget(keyAnimation, durationTime, useBlendEye);
            }
            else
            {// アニメーション変更時の即切り替え
                for (int i = 0; i < _animationFaceTypeArray.Length; ++i)
                {
                    _animationFaceTypeArray[i].count = _animationFaceTypeArray[i].faceType.Length;
                }
                ClearWeight(isClearEarWeight);
            }

            if (ClearPlayAnimationClipParameter(_playableAnimator, _isSelfControllTime))
            {
                SetAllAnimationWeight(true);
            }

            _isFacePlayFrame = false;

            _isClipOverride = true;
            _eyeWeight.IsCalcWeight = false;
            _eyebrowWeight.IsCalcWeight = false;
            _mouthWeight.IsCalcWeight = false;
        }

        public float GetDrivenKeyPosition()
        {
            return _playableAnimator.GetNormalizePlayTime(DrivenKeyPlayableAnimator.Layer.Base);
        }
        public void SetDrivenKeyPosition(float normalizedTime)
        {
            _playableAnimator.SetNormalizePlayTimeWithLoop(DrivenKeyPlayableAnimator.Layer.Base, normalizedTime);
        }

        public float GetDrivenKeyEarPosition()
        {
            return _playableAnimator.GetNormalizePlayTime(DrivenKeyPlayableAnimator.Layer.Ear);
        }
        public void SetDrivenKeyEarPosition(float normalizedTime)
        {
            _playableAnimator.SetNormalizePlayTimeWithLoop(DrivenKeyPlayableAnimator.Layer.Ear, normalizedTime);
        }

        public float GetDrivenKeySpeed() => _playableAnimator.Speed;
        public void SetDrivenKeySpeed(float speed) => _playableAnimator.Speed = speed;


        /// <summary>
        /// アニメーションのドリブンキー再生パラメータをクリア
        /// </summary>
        /// <param name="currentClip"></param>
        private static bool ClearPlayAnimationClipParameter(DrivenKeyPlayableAnimator animator, bool isSelfControllTime)
        {
            animator.IsEnable = true;
            if (isSelfControllTime)
            {
                // 自分で時間をコントロールする場合は自動更新不要なのでスピード0
                animator.Speed = 0.0f;
            }
            else
            {
                animator.Speed = 1.0f;
            }
            return true;
        }

        /// <summary>
        /// アニメーションウェイトブレンドを有効にする
        /// </summary>
        /// <param name="enable"></param>
        private void SetAllAnimationWeight(bool enable)
        {
            _isPlayingAnim = enable;
            _eyeWeight.IsAnimationWeight = enable;
            _mouthWeight.IsAnimationWeight = enable;
            _eyebrowWeight.IsAnimationWeight = enable;
        }

        public void SetAnimationWeight(bool enableEye, bool enableEyebrow, bool enableMouse)
        {
            _isPlayingAnim = (enableEye || enableEyebrow || enableMouse);
            _eyeWeight.IsAnimationWeight = enableEye;
            _eyebrowWeight.IsAnimationWeight = enableEyebrow;
            _mouthWeight.IsAnimationWeight = enableMouse;
        }

        /// <summary>
        /// 再生中のドリブンキーアニメーションを停止させる
        /// </summary>
        public void Stop()
        {
            _isCheckEyeRange = true;
            SetAllAnimationWeight(false);

            _isPlayingEarAnim = false;
            _playableAnimator.Speed = 0.0f;
            _playableAnimator.IsEnable = false;
        }

        /// <summary>
        /// ポーズ。
        /// </summary>
        public void Pause()
        {
            _isPause = true;
            _prevAnimationSpeed = _playableAnimator.Speed;
            _playableAnimator.Speed = 0.0f;
        }

        /// <summary>
        /// 再開。
        /// </summary>
        public void Resume()
        {
            _isPause = false;
            _playableAnimator.Speed = _prevAnimationSpeed;
            //Unity側で進められた時間を戻す
            //SetClipでPauseして、アプリ側で時間を管理すべきだが影響範囲大きいので
            //UpdateMotionで時間だけ戻す(次のバージョンで削除したい)
            _playableAnimator.UpdateMotion(0.0f);
        }

        /// <summary>
        /// 目の位置をリセットする
        /// </summary>
        public void ResetEyePosition()
        {
            // 目は全てのフェイシャルに設定されているわけではないので専用で用意
            if (_drivenKeyForEyeArray == null)
                return;

            for (int i = 0; i < _drivenKeyForEyeArray.Length; i++)
            {
                _drivenKeyForEyeArray[i].rootTransform.localPosition = Math.VECTOR3_ZERO;
            }
        }

        #region プログラマブルなフェイシャル制御

        /// <summary>
        /// アニメーション終了時の目の表情を復元
        /// </summary>
        public void SetLastAnimEyeFace(float durationTime)
        {
            _eyeWeight.SetDurationTime(durationTime);
            for (int i = 0; i < _eyeWeight.WeightArray.Length; i++)
            {
                var tableIndex = _eyeWeight.WeightArray[i].StartIndex;
                _eyeWeight.CommonWeightArray[tableIndex + (int)TargetInfomation.Part.EyeL] = _lastAnimLeftEyeWeightArray[i];
                _eyeWeight.CommonWeightArray[tableIndex + (int)TargetInfomation.Part.EyeR] = _lastAnimRightEyeWeightArray[i];
            }
        }
        /// <summary>
        /// 耳のブレンド状態をクリアする
        /// </summary>
        private void ClearEarBlendFaces(EarType earType)
        {
            //連続して呼び出した時にどのタイミングでブレンドリストをクリアすればいいのか分からなくなるので、その対策
            _earWeight.GetEarTypes(ref _setEarTypeArray[0]); //直後で上書きされるので、0を使いまわす
            _earWeight.ClearBlendFace();

            //以前の登録分をここで再登録する
            for (int j = 0; j < _setEarTypeArray[0].count; j++)
            {
                int type = (int)_setEarTypeArray[0].faceType[j];
                for (int i = 0; i < DrivenKeyAnimation.EarNum; i++)
                {
                    int index = i * EarWeightGroup.WEIGHT_SIZE;
                    //今回ベースの場合、前回の場所が0になっている可能性がある
                    var tableIndex = _earWeight.WeightArray[type].StartIndex + index;
                    if (_earWeight.CommonWeightArray[tableIndex] > 0.0f || earType == EarType.Base)
                    {
                        AddBlendEar(_setEarTypeArray[0].faceType[j]);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// EarTypeから耳を設定する
        /// </summary>
        /// <param name="faceType"></param>
        /// <param name="weight"></param>
        /// <param name="groupTypeList"></param>
        /// <param name="durationTime">遷移時間</param>
        /// <param name="isBlend">現在のWeightを保持するならtrue</param>
        /// <param name="dontClearFaces">口パク、目パチ等でブレンドする顔リストを保持するならtrue</param>
        public void SetEar(EarType earType, FaceGroupType earGroup, float weight, float durationTime, bool isBlend, bool dontClearFaces, bool isForceSetPrevWeight = false)
        {
            if (!_isInitialized)
            {
                return;
            }

            //指定出来ないグループ
            if (!(earGroup == FaceGroupType.EarL || earGroup == FaceGroupType.EarR))
            {
                return;
            }

            int earGroupIndex = (int)earGroup;

            //0なら即時反映、0よりでかいならtargetに格納
            var targetWeight = _earWeight.WeightArray;
            if (durationTime > 0.0f)
            {
                targetWeight = _earWeight.TargetWeightArray;
                _earWeight.SetPrevWeight(earGroupIndex, isForceSetPrevWeight);
                _earWeight.SetDurationTime(earGroupIndex * EarWeightGroup.WEIGHT_SIZE, durationTime);
            }

            if (_earWeight.IsCalcWeight && !dontClearFaces)
            {
                ClearEarBlendFaces(earType);
            }
            _earWeight.IsCalcWeight = false;

            if (!isBlend)
            {
                //ブレンドしない場合は他項目のブレンドをクリアする
                _earWeight.ClearTargetWeight(earGroupIndex, ref targetWeight);
            }

            var tableIndex = targetWeight[(int)earType].StartIndex + (earGroupIndex * EarWeightGroup.WEIGHT_SIZE);
            _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_0] = weight;
            _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_1] = weight;
            _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_2] = weight;
            //どれか１つでも有効であれば
            if (weight > 0.0f)
            {
                AddBlendEar(earType);
            }

            _earWeight.GetEarTypes(ref _setEarTypeArray[earGroupIndex], true);

            //アニメーションではなくなるので無効にする
            _earKeyAnimation = null;
            _animationEarTypeArray = _setEarTypeArray;
        }

        /// <summary>
        /// EarTypeから耳を設定する
        /// </summary>
        /// <param name="faceType"></param>
        /// <param name="weight"></param>
        /// <param name="groupTypeList"></param>
        /// <param name="durationTime">遷移時間</param>
        /// <param name="isBlend">現在のWeightを保持するならtrue</param>
        /// <param name="dontClearFaces">口パク、目パチ等でブレンドする顔リストを保持するならtrue</param>
        public void SetEar(EarType earType, float weight, float durationTime, bool isBlend, bool dontClearFaces, bool isForceSetPrevWeight = false)
        {
            if (!_isInitialized)
            {
                return;
            }

            //0なら即時反映、0よりでかいならtargetに格納
            var targetWeight = _earWeight.WeightArray;
            if (durationTime > 0.0f)
            {
                targetWeight = _earWeight.TargetWeightArray;
                _earWeight.SetPrevWeight(isForceSetPrevWeight);
                _earWeight.SetDurationTime(durationTime);
            }

            if (_earWeight.IsCalcWeight && !dontClearFaces)
            {
                ClearEarBlendFaces(earType);
            }
            _earWeight.IsCalcWeight = false;

            if (!isBlend)
            {
                //ブレンドしない場合は他の表情をクリアする
                for (int i = 0; i < DrivenKeyAnimation.EarNum; i++)
                {
                    _earWeight.ClearTargetWeight(i, ref targetWeight);
                }
            }

            for (int i = 0; i < DrivenKeyAnimation.EarNum; i++)
            {
                int index = i * EarWeightGroup.WEIGHT_SIZE;
                var tableIndex = targetWeight[(int)earType].StartIndex + index;

                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_0] = weight;
                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_1] = weight;
                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_2] = weight;

                //どれか１つでも有効であれば
                if (weight > 0.0f)
                {
                    AddBlendEar(earType);
                }
            }

            for (int i = 0; i < DrivenKeyAnimation.EarNum; i++)
                _earWeight.GetEarTypes(ref _setEarTypeArray[i], true);

            //アニメーションではなくなるので無効にする
            _earKeyAnimation = null;
            _animationEarTypeArray = _setEarTypeArray;
        }

        /// <summary>
        /// 目パチを設定する
        /// </summary>
        public void SetFaceEyeBlink(FaceType faceType, float weight, FaceGroupType[] groupTypeArray, float durationTime)
        {
            // 現在のWeightを保持しない
            const bool IS_BLEND = false;

            // 目パチでブレンドする顔リストを保持する
            const bool DONT_CLEAR_FACES = true;

            if (!_isInitialized || groupTypeArray == null)
            {
                return;
            }
            var faceParts = GetFacePartsSet(faceType);
            SetFace(_eyeWeight, faceParts._eyeR, weight, groupTypeArray, durationTime, IS_BLEND, DONT_CLEAR_FACES, FaceGroupType.EyeR);
            SetFace(_eyeWeight, faceParts._eyeL, weight, groupTypeArray, durationTime, IS_BLEND, DONT_CLEAR_FACES, FaceGroupType.EyeL);
            SetFace(_eyebrowWeight, faceParts._eyebrowR, weight, groupTypeArray, durationTime, IS_BLEND, DONT_CLEAR_FACES, FaceGroupType.EyebrowR);
            SetFace(_eyebrowWeight, faceParts._eyebrowL, weight, groupTypeArray, durationTime, IS_BLEND, DONT_CLEAR_FACES, FaceGroupType.EyebrowL);
            SetFace(_mouthWeight, faceParts._mouth, weight, groupTypeArray, durationTime, IS_BLEND, DONT_CLEAR_FACES, FaceGroupType.Mouth);

            // 目パチはFaceTypeによる表情設定でアニメーションではなくなるので無効にする
            _faceKeyAnimation = null;
            SetAllAnimationWeight(false);
            _isCheckEyeRange = true;

            // ただし耳アニメーションは継続する
        }

        /// <summary>
        /// FaceTypeからフェイシャルを設定する
        /// </summary>
        /// <param name="faceType"></param>
        /// <param name="weight"></param>
        /// <param name="durationTime">遷移時間</param>
        /// <param name="isBlend">現在のWeightを保持するならtrue</param>
        /// <param name="dontClearFaces">口パク、目パチ等でブレンドする顔リストを保持するならtrue</param>
        public void SetFace(FaceType faceType, float weight, FaceGroupType[] groupTypeArray, float durationTime, bool isBlend, bool dontClearFaces, bool isPlayingEarAnim)
        {
            if (!_isInitialized || groupTypeArray == null)
            {
                return;
            }
            var faceParts = GetFacePartsSet(faceType);
            SetFace(_eyeWeight, faceParts._eyeR, weight, groupTypeArray, durationTime, isBlend, dontClearFaces, FaceGroupType.EyeR);
            SetFace(_eyeWeight, faceParts._eyeL, weight, groupTypeArray, durationTime, isBlend, dontClearFaces, FaceGroupType.EyeL);
            SetFace(_eyebrowWeight, faceParts._eyebrowR, weight, groupTypeArray, durationTime, isBlend, dontClearFaces, FaceGroupType.EyebrowR);
            SetFace(_eyebrowWeight, faceParts._eyebrowL, weight, groupTypeArray, durationTime, isBlend, dontClearFaces, FaceGroupType.EyebrowL);
            SetFace(_mouthWeight, faceParts._mouth, weight, groupTypeArray, durationTime, isBlend, dontClearFaces, FaceGroupType.Mouth);

            //アニメーションではなくなるので無効にする
            _faceKeyAnimation = null;
            SetAllAnimationWeight(false);
            _isCheckEyeRange = true;
            _isPlayingEarAnim = isPlayingEarAnim;
        }

        private void SetFace(FaceWeightGroup faceWeight, FaceParts[] facePartsArray, float weight, FaceGroupType[] groupTypeArray, float durationTime, bool isBlend, bool dontClearFaces, FaceGroupType targetGroup)
        {
            var targetWeight = faceWeight.WeightArray;
            bool containTarget = _ContainsFace(groupTypeArray, targetGroup);
            int groupIndex = (int)targetGroup - faceWeight.FacialGroupOffset;
            //各顔パーツ毎にアニメーションする時間の設定を行う
            if (containTarget)
            {
                faceWeight.DurationTimeArray[groupIndex] = durationTime;
                faceWeight.CurrentTimeArray[groupIndex] = 0.0f;
            }

            //0なら即時反映、0よりでかいならtargetに格納
            if (durationTime > 0.0f)
            {
                targetWeight = faceWeight.TargetWeightArray;
                if (containTarget)
                {
                    faceWeight.SetPrevWeight(groupIndex);
                }
            }

            //同一フレームで複数回呼び出されると直前のブレンドリストを消去してしまうので対策
            //口パク、目パチ等で元の表情に戻す時に他の部位の表情もリストから消えてしまうのでフラグで回避
            if (faceWeight.IsCalcWeight && !dontClearFaces)
            {
                // ここで使う_setFaceTypeの領域はただの作業領域。この後で上書きされる部分を使う。
                var bufIndex = faceWeight.FacialGroupOffset - DrivenKeyAnimation.FaceNumGroupDiff;
                faceWeight.GetFaceTypes(ref _setFaceTypeArray[bufIndex]);
                faceWeight.ClearBlendFace();

                //以前の登録分をここで再登録する
                for (int i = 0; i < faceWeight.GroupNum; i++)
                {
                    var faceTypeArray = _setFaceTypeArray[bufIndex].faceType;
                    for (int j = 0; j < _setFaceTypeArray[bufIndex].count; j++)
                    {
                        var tableIndex = faceWeight.WeightArray[(int)faceTypeArray[j]].StartIndex;
                        if (faceWeight.CommonWeightArray[tableIndex + i] > 0.0f)
                        {
                            faceWeight.AddBlendFace((int)faceTypeArray[j]);
                        }
                    }
                }
            }
            faceWeight.IsCalcWeight = false;

            //パーツ
            if (isBlend)
            {
                for (int faceTypeIndex = 0; faceTypeIndex < facePartsArray.Length; ++faceTypeIndex)
                {
                    var info = facePartsArray[faceTypeIndex];
                    var faceType = info._faceParts;
                    // 0（Base）はブレンド対象としない。
                    if (faceType > 0)
                    {
                        //ブレンドする場合は該当箇所のみ書き換える
                        if (weight > 0.0f)
                        {
                            faceWeight.AddBlendFace(faceType);
                        }
                        if (containTarget)
                        {
                            var tableIndex = targetWeight[faceType].StartIndex + groupIndex;
                            faceWeight.CommonWeightArray[tableIndex] = weight * info._weight;
                        }
                    }
                }
            }
            else
            {
                //ブレンドしない場合は一旦全て0を入れて該当箇所だけ入れ替える
                if (containTarget)
                {
                    for (int j = FACE_WEIGHT_START_INDEX; j < targetWeight.Length; j++)
                    {
                        var tableIndex = targetWeight[j].StartIndex + groupIndex;
                        faceWeight.CommonWeightArray[tableIndex] = 0.0f;
                    }

                    for (int faceTypeIndex = 0; faceTypeIndex < facePartsArray.Length; ++faceTypeIndex)
                    {
                        var info = facePartsArray[faceTypeIndex];
                        var faceType = info._faceParts;
                        // 0（Base）はブレンド対象としない。
                        if (faceType > 0)
                        {
                            var tableIndex = targetWeight[faceType].StartIndex + groupIndex;
                            faceWeight.CommonWeightArray[tableIndex] = weight * info._weight;
                            if (weight > 0.0f)
                            {
                                faceWeight.AddBlendFace(faceType);
                            }
                        }
                    }
                }
            }
            var index = (int)targetGroup - DrivenKeyAnimation.FaceNumGroupDiff;
            faceWeight.GetFaceTypes(ref _setFaceTypeArray[index], true);
        }

        /// <summary>
        /// FacePartsSetの各パーツを指定表情に設定する
        /// 設定された各パーツ内容を変更すると壊れるので
        /// パーツ配列をそのものを入れ替える以外使わない事
        /// </summary>
        /// <param name="faceType"></param>
        /// <param name="result"></param>
        public void SetFacePartsSet(FaceType faceType,FacePartsSet partsSet)
        {
            int faceTypeIndex = (int)faceType;
            if (_facePartsSetArray.Length <= faceTypeIndex)
            {
                faceTypeIndex = (int)FaceType.Base;
            }
            partsSet.SetFrom(_facePartsSetArray[faceTypeIndex]);
        }

        public FacePartsSet GetFacePartsSet(FaceType faceType)
        {
            int faceTypeIndex = (int)faceType;
            if (_facePartsSetArray.Length <= faceTypeIndex)
            {
                faceTypeIndex = (int)FaceType.Base;
            }
            return _facePartsSetArray[faceTypeIndex];
        }

        /// <summary>
        /// 顔のパーツごとの設定。ModelControllerで一部情報を見ているのでModelController経由で設定しないと正しく動作しない
        /// </summary>
        public void SetFaceEyebrowType(FaceParts[] facePartsArray, FaceGroupType targetGroup, float durationTime = 0f, bool isBlend = false, bool dontClearFaces = false)
        {
            if (!_isInitialized)
            {
                return;
            }
            SetFace(_eyebrowWeight, facePartsArray, 1f, ModelController.EyeBrows, durationTime, isBlend, dontClearFaces, targetGroup);
            //アニメーションではなくなるのでEyebrowのみ無効にする
            _eyebrowWeight.IsAnimationWeight = false;
        }
        public void SetFaceEyeType(FaceParts[] facePartsArray, FaceGroupType targetGroup, float durationTime = 0f, bool isBlend = false, bool dontClearFaces = false)
        {
            if (!_isInitialized)
            {
                return;
            }
            SetFace(_eyeWeight, facePartsArray, 1f, ModelController.Eyes, durationTime, isBlend, dontClearFaces, targetGroup);
            //アニメーションではなくなるのでEyeだけ無効にする
            _eyeWeight.IsAnimationWeight = false;
        }
        public void SetFaceMouthType(FaceParts[] facePartsArray, float weight, FaceGroupType targetGroup, float durationTime = 0f, bool isBlend = false, bool dontClearFaces = false)
        {
            if (!_isInitialized)
            {
                return;
            }
            SetFace(_mouthWeight, facePartsArray, weight, ModelController.Mouth, durationTime, isBlend, dontClearFaces, targetGroup);
            //アニメーションではなくなるので無効にする
            _mouthWeight.IsAnimationWeight = false;
        }

        /// <summary>
        /// 顔パーツごとにあるFaceTypeを含んだ表情をしているかを取得する
        /// </summary>
        /// <param name="facePartsEnumerable">FaceTypeのint値のリスト</param>
        /// <param name="targetGroup">検索対象の部位</param>
        public bool ContainsEyebrowFaceParts(HashSet<int> facePartsEnumerable, FaceGroupType targetGroup)
        {
            return ContainsFaceParts(facePartsEnumerable, _eyebrowWeight, targetGroup);
        }
        public bool ContainsEyeFaceParts(HashSet<int> facePartsEnumerable, FaceGroupType targetGroup)
        {
            return ContainsFaceParts(facePartsEnumerable, _eyeWeight, targetGroup);
        }
        public bool ContainsEyeFaceParts(int faceType, FaceGroupType targetGroup)
        {
            return ContainsFaceParts(faceType, _eyeWeight, targetGroup);
        }
        private static bool ContainsFaceParts(HashSet<int> facePartsEnumerable, WeightGroupBase faceWeightGroup, FaceGroupType targetGroup)
        {
            foreach (var faceParts in facePartsEnumerable)
            {
                if(ContainsFaceParts(faceParts, faceWeightGroup, targetGroup))
                {
                    return true;
                }
            }
            return false;
        }
        private static bool ContainsFaceParts(int faceType, WeightGroupBase faceWeightGroup, FaceGroupType targetGroup)
        {
            var groupIndex = (int)targetGroup - faceWeightGroup.FacialGroupOffset;
            var tableIndex = faceWeightGroup.WeightArray[faceType].StartIndex + groupIndex;
            return faceWeightGroup.CommonWeightArray[tableIndex] > Math.EPSILON;
        }


        /// <summary>
        /// Boxing対策の自前Contains
        /// </summary>
        /// <param name="groupTypeArray"></param>
        /// <param name="groupType"></param>
        /// <returns></returns>
        private static bool _ContainsFace(FaceGroupType[] groupTypeArray, FaceGroupType groupType)
        {
            foreach(var type in groupTypeArray)
            {
                if ((int)type == (int)groupType)
                    return true;
            }
            return false;
        }
        private static bool _ContainsFace(int[] faceTypeArray, int faceType, int count)
        {
            for (int i = 0; i < count; i++)
            {
                if ((int)faceTypeArray[i] == (int)faceType)
                    return true;
            }
            return false;
        }
        private static bool _ContainsEar(EarType[] earTypeArray, EarType earType, int count)
        {
            for (int i = 0; i < count; i++)
                if ((int)earTypeArray[i] == (int)earType)
                    return true;
            return false;
        }

        #endregion

        private static float GetInterpolateRate(InterpolateType interpolateType, float currentTime, float durationTime)
        {
            const float PI_HALF = Mathf.PI * 0.5f;

            float rate = Mathf.Clamp01(currentTime / durationTime);
            switch (interpolateType)
            {
                case InterpolateType.Linear:
                default:
                    break;
                case InterpolateType.EaseIn:
                    rate = 1.0f - Mathf.Cos(rate * PI_HALF);
                    break;
                case InterpolateType.EaseOut:
                    rate = Mathf.Sin(rate * PI_HALF);
                    break;
                case InterpolateType.EaseInOut:
                    rate *= 2.0f;
                    rate = (rate < 1.0f) ? (0.5f * (1.0f - Mathf.Cos(rate * PI_HALF))) : (0.5f * Mathf.Sin((rate - 1.0f) * PI_HALF) + 0.5f);
                    break;
            }
            return rate;
        }

        /// <summary>
        /// 毎フレームの適用が必要なら実行される
        /// </summary>
        /// <param name="elapsedTime">１Fにかかった時間</param>
        private void BlendProgrammable(float elapsedTime)
        {
            BlendProgrammable(_eyeWeight, elapsedTime, _eyeInterpolateType);
            BlendProgrammable(_eyebrowWeight, elapsedTime, _eyebrowInterpolateType);
            BlendProgrammable(_mouthWeight, elapsedTime, _mouthInterpolateType);

            if (_animationEarTypeArray != null)
            {
                var durationTime = _earWeight.DurationTimeArray;
                var currentTime = _earWeight.CurrentTimeArray;
                var prevWeight = _earWeight.PrevWeightArray;
                var targetWeight = _earWeight.TargetWeightArray;
                var weight = _earWeight.WeightArray;
                var weightTable = _earWeight.CommonWeightArray;
                for (int index = 0; index < _animationEarTypeArray.Length; index++)
                {
                    var offset = index * EarWeightGroup.WEIGHT_SIZE;
                    //耳は先頭の時間だけで残りのWeightブレンドを決める
                    if (durationTime[offset] > currentTime[offset])
                    {
                        currentTime[offset] += elapsedTime;

                        float rate = GetInterpolateRate(_earInterpolateType, currentTime[offset], durationTime[offset]);

                        var face = _animationEarTypeArray[index].faceType;
                        int faceNum = _animationEarTypeArray[index].count;
                        for (int i = 0; i < faceNum; i++)
                        {
                            bool addEar = false;
                            int faceIndex = (int)face[i];
                            for (int k = 0; k < EarWeightGroup.WEIGHT_SIZE; k++)
                            {
                                float preW = weightTable[prevWeight[faceIndex].StartIndex + offset + k];
                                float tarW = weightTable[targetWeight[faceIndex].StartIndex + offset + k];
                                if (preW != tarW)
                                {
                                    //↑のifがあるから0割チェックは不要
                                    float w = Mathf.Lerp(preW, tarW, rate);
                                    weightTable[weight[faceIndex].StartIndex + offset + k] = w;
                                    addEar = true;
                                }
                            }

                            if (addEar)
                            {
                                //ブレンドする耳を登録
                                AddBlendEar(face[i]);
                            }
                        }
                    }
                }
            }

            _earWeight.IsCalcWeight = true;
            _eyeWeight.IsCalcWeight = true;
            _eyebrowWeight.IsCalcWeight = true;
            _mouthWeight.IsCalcWeight = true;
        }
        private void BlendProgrammable(FaceWeightGroup faceWeight, float elapsedTime, InterpolateType interpolateType)
        {
            if (_faceKeyAnimation == null || !faceWeight.IsAnimationWeight)
            {// SetFaceされた状態か部位指定が行われている
                if (_setFaceTypeArray != null)
                {
                    var durationTime = faceWeight.DurationTimeArray;
                    var currentTime = faceWeight.CurrentTimeArray;
                    var prevWeight = faceWeight.PrevWeightArray;
                    var targetWeight = faceWeight.TargetWeightArray;
                    var weight = faceWeight.WeightArray;
                    var weightTable = faceWeight.CommonWeightArray;
                    for (int index = 0; index < faceWeight.GroupNum; index++)
                    {
                        var animIndex = index + faceWeight.FacialGroupOffset - DrivenKeyAnimation.FaceNumGroupDiff;
                        if (durationTime[index] > currentTime[index])
                        {
                            currentTime[index] += elapsedTime;

                            float rate = GetInterpolateRate(interpolateType, currentTime[index], durationTime[index]);

                            var face = _setFaceTypeArray[animIndex].faceType;
                            int faceNum = _setFaceTypeArray[animIndex].count;
                            for (int i = 0; i < faceNum; i++)
                            {
                                int faceIndex = face[i];
                                float w1 = weightTable[prevWeight[faceIndex].StartIndex + index];
                                float w2 = weightTable[targetWeight[faceIndex].StartIndex + index];
                                if (w1 != w2)
                                {
                                    //↑のifがあるから0割チェックは不要
                                    float w = Mathf.Lerp(w1, w2, rate);
                                    weightTable[weight[faceIndex].StartIndex + index] = w;
                                    //ブレンドする顔を登録
                                    faceWeight.AddBlendFace(face[i]);
                                }
                            }
                        }
                    }
                }
            }
            else
            {// アニメーションで動いている状態。
                if (_animationFaceTypeArray != null)
                {
                    var durationTime = faceWeight.DurationTimeArray;
                    var currentTime = faceWeight.CurrentTimeArray;
                    var prevWeight = faceWeight.PrevWeightArray;
                    var targetWeight = faceWeight.TargetWeightArray;
                    var weight = faceWeight.WeightArray;
                    var weightTable = faceWeight.CommonWeightArray;
                    for (int index = 0; index < faceWeight.GroupNum; index++)
                    {
                        _blendFaceIndexTmpList.Clear();
                        var animIndex = index + faceWeight.FacialGroupOffset - DrivenKeyAnimation.FaceNumGroupDiff;
                        if (durationTime[index] > currentTime[index])
                        {
                            currentTime[index] += elapsedTime;

                            float rate = GetInterpolateRate(interpolateType, currentTime[index], durationTime[index]);

                            var face = _animationFaceTypeArray[animIndex].faceType;
                            int faceNum = _animationFaceTypeArray[animIndex].count;
                            for (int i = 0; i < faceNum; i++)
                            {
                                int faceIndex = face[i];
                                var w1 = weightTable[prevWeight[faceIndex].StartIndex + index];
                                var w2 = weightTable[targetWeight[faceIndex].StartIndex + index];
                                if (w1 != w2)
                                {
                                    //↑のifがあるから0割チェックは不要
                                    float w = Mathf.Lerp(w1, w2, rate);
                                    weightTable[weight[faceIndex].StartIndex + index] = w;
                                    //ブレンドする顔を登録
                                    faceWeight.AddBlendFace(faceIndex);
                                }
                            }
                        }
                    }
                }
            }
        }

        private void UpdateEarWeight(DrivenKeys[] keyArray)
        {
            //耳の種類
            if (_earKeyAnimation == null)
                return;

            var weightTable = _earWeight.CommonWeightArray;
            for (int i = 0; i < _earKeyAnimation.animationEarType.Length; ++i)
            {
                var face = _earKeyAnimation.animationEarType[i].faceType;
                var keyObj = keyArray[i].info;
                var offset = i * EarWeightGroup.WEIGHT_SIZE;
                for (int j = 0; j < face.Length; ++j)
                {
                    int index = (int)face[j];
                    if (!keyObj[index].isEnable)
                    {
                        continue;
                    }

                    var animationWeight = keyObj[index].objectTrans.localPosition;
                    var tableIndex = _earWeight.WeightArray[index].StartIndex + offset;
                    if (_earWeight.DurationTimeArray[offset] > _earWeight.CurrentTimeArray[offset])
                    {
                        tableIndex = _earWeight.TargetWeightArray[index].StartIndex + offset;
                    }
                    weightTable[tableIndex + EarWeightGroup.WEIGHT_0] = animationWeight.x * TRANSLATE_TO_UNITY_POSITION;
                    weightTable[tableIndex + EarWeightGroup.WEIGHT_1] = animationWeight.y * TRANSLATE_TO_UNITY_POSITION_VALUE;
                    weightTable[tableIndex + EarWeightGroup.WEIGHT_2] = animationWeight.z * TRANSLATE_TO_UNITY_POSITION_VALUE;

                    //ブレンドする顔ならブレンド一覧に登録
                    var addWeight = weightTable[tableIndex + EarWeightGroup.WEIGHT_0] + weightTable[tableIndex + EarWeightGroup.WEIGHT_1] + weightTable[tableIndex + EarWeightGroup.WEIGHT_2];
                    if (addWeight > Mathf.Epsilon)
                    {
                        AddBlendEar(face[j]);
                    }
                }
            }
        }

        /// <summary>
        /// 顔のWeightを設定する
        /// </summary>
        /// <param name="keys"></param>
        /// <param name="faceAnimationKey"></param>
        private void UpdateWeight(DrivenKeys[] keyArray)
        {
            if (_faceKeyAnimation == null)
                return;

            UpdateWeight(_eyeWeight, keyArray);
            UpdateWeight(_eyebrowWeight, keyArray);
            UpdateWeight(_mouthWeight, keyArray);
        }
        private void UpdateWeight(FaceWeightGroup faceWeight, DrivenKeys[] keyArray)
        {
            //AnimationBlendが無効な場合は何もしない
            if (!faceWeight.IsAnimationWeight)
                return;

            faceWeight.ClearWeight();
            for (int i = 0; i < faceWeight.GroupNum; i++)
            {
                var keyIndex = i + faceWeight.FacialGroupOffset - DrivenKeyAnimation.FaceNumGroupDiff;
                var keyObj = keyArray[keyIndex].info;
                var face = _faceKeyAnimation.animationFaceType[keyIndex].faceType;

                bool isBlend = (faceWeight.DurationTimeArray[i] > faceWeight.CurrentTimeArray[i]);

                // アニメーションデータ直接見ているので配列のLengthでいい。
                for (int j = 0; j < face.Length; ++j)
                {
                    var index = face[j];
                    if (!keyObj[index].isEnable)
                    {
                        continue;
                    }

                    var weightIndex = faceWeight.WeightArray[index].StartIndex + i;
                    float w = keyObj[index].objectTrans.localPosition.x * -100f;
                    faceWeight.CommonWeightArray[weightIndex] = w;

                    // ブレンドが必要な場合はターゲット側にもウェイト値を設定しておく
                    // BlendProgrammableにて補間処理が入った場合にCommonWeightArray内のWeightArray側が上書きされる
                    if (isBlend)
                    {
                        weightIndex = faceWeight.TargetWeightArray[index].StartIndex + i;
                        faceWeight.CommonWeightArray[weightIndex] = w;
                    }

                    //ブレンドする顔ならブレンド一覧に登録
                    if (w > 0f)
                    {
                        faceWeight.AddBlendFace(index);
                    }
                }
            }
        }

        public void WaitJobAll()
        {
            if (_isEyeTarget)
            {
                _eyeWeight.WaitJob();
            }
            if (_isEyebrowTarget)
            {
                _eyebrowWeight.WaitJob();
            }
            if (_isMouthTarget)
            {
                _mouthWeight.WaitJob();
            }
            if (_isEarTarget)
            {
                _earWeight.WaitJob();
            }
        }

        /// <summary>
        /// アニメーションを即時反映する
        /// LateUpdateでドリブンを有効にした時にモーション反映が遅延するため
        /// </summary>
        public void EvaluateAnimation()
        {
            if(_isPlayingAnim || _isPlayingEarAnim)
            {
                //Animation再生している時のみ
                UpdateAnimator(0);
            }
        }

        public void AfterUpdate(float deltaTime)
        {
            if (!_isInitialized)
            {
                return;
            }

            // #46209 キャラが非表示で_isEnableがfalseになっているときでも内部時間だけは進める。
            if (!_isEnable)
            {
                _playableAnimator.UpdateMotion(deltaTime);
                return;
            }

            if (_isPause)
            {
                return;
            }

            //目の位置が書き込まれないので、ここで書き込む
            WriteEyeDefault();
            _playableAnimator.UpdateMotion(deltaTime);
        }

        public void AfterLateUpdate(bool isForceApply = false)
        {
#if CYG_DEBUG
            UnityEngine.Profiling.Profiler.BeginSample("DrivenKeyComponent.AfterLateUpdate()");
#endif

            WaitJobAll();

#if CYG_DEBUG
            UnityEngine.Profiling.Profiler.BeginSample("MainLoop");
#endif

            //フェイシャル姿勢を各GameObjectに入れる
            //_weightSum._faceGroupInfo.Length = 反映するパーツ数
            for (int i = 0; i < _weightSum.FaceGroupInfoArray.Length; ++i)
            {
                var faceGroupInfo = _weightSum.FaceGroupInfoArray[i];
                var trsIndex = faceGroupInfo.CalcTrsIndex;
                //calcTrs.Length = 反映するノード数
                for (int j = 0; j < faceGroupInfo.CalcTrsTargetArray.Length; ++j)
                {
                    var trsInfo = faceGroupInfo.CalcTrsTargetArray[j];
                    if (!trsInfo.IsValidTargetTransform)
                    {
                        continue;
                    }

                    var calcTrs = _weightSum.CommonCalcTRSArray[trsIndex + j];
                    // いらない更新を避けるためweight変化でチェック
                    float lastWeight = faceGroupInfo.LastWeightArray[j];
                    float curWeight = calcTrs.CurWeight;
                    if (!isForceApply && !faceGroupInfo.ForceUpdateArray[j] && Math.IsFloatEqualLight(lastWeight, curWeight) && curWeight <= 1.0f)
                    {
                        continue;
                    }
                    faceGroupInfo.LastWeightArray[j] = curWeight;

                    trsInfo.TargetTransform.localPosition = calcTrs.Position;
                    trsInfo.TargetTransform.localRotation = Math.FromMayaEuler(calcTrs.Rotation);
                    //歯の表示非表示がスケールで行われているためスケールのアニメーションを考慮する必要がある
                    if (trsInfo.IsValidScaleTransform)
                    {
                        trsInfo.TargetTransform.localScale = calcTrs.Scale;
                    }

                    if (trsInfo.IsSetTemporary)
                    {
                        //設定を保存しておく
                        trsInfo.TemporaryPosition = trsInfo.TargetTransform.localPosition;
                        trsInfo.TemporaryRotation = trsInfo.TargetTransform.localRotation;
                    }
                }
            }

#if CYG_DEBUG
            UnityEngine.Profiling.Profiler.EndSample();
#endif

#if UNITY_EDITOR && CYG_DEBUG
            // 目の位置を外部から上書きする。
            OverwriteEyeballPos();
#endif // UNITY_EDITOR && CYG_DEBUG
            //フェイシャルアニメーションが一度でも再生されていれば目のアニメーションで目線を上書きする
            if (_isClipOverride && _eyeTargetTransformArray[RIGHT_EYE_INDEX] && _eyeTargetTransformArray[LEFT_EYE_INDEX])
            {
                UpdateEyePosition();
            }

#if CYG_DEBUG
            UnityEngine.Profiling.Profiler.EndSample();
#endif
        }

        private void WriteEyeDefault()
        {
            //目の位置が書き込まれないので、ここで書き込む
            if (_drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform != null)
                _drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform.localPosition = Math.VECTOR3_ZERO;
            if (_drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform != null)
                _drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform.localPosition = Math.VECTOR3_ZERO;
            if (_drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform != null)
                _drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform.localPosition = Math.VECTOR3_ZERO;
        }
#if UNITY_EDITOR && CYG_DEBUG
        private Vector3[] _overwriteEyeballPosArray = null;
        private bool _isOverwriteEyballPos = false;
        /// <summary>
        /// 目の位置を外部から上書きする。
        /// </summary>
        private void OverwriteEyeballPos()
        {
            if (_isOverwriteEyballPos && _overwriteEyeballPosArray != null)
            {
                _isClipOverride = true;
                if (_drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform != null)
                    _drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform.localPosition = _overwriteEyeballPosArray[ALL_EYE_INDEX];
                if (_drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform != null)
                    _drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform.localPosition = _overwriteEyeballPosArray[RIGHT_EYE_INDEX];
                if (_drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform != null)
                    _drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform.localPosition = _overwriteEyeballPosArray[LEFT_EYE_INDEX];
            }
        }
        /// <summary>
        /// 上書きする瞳の位置を設定。
        /// 設定される値はMaya上の数値を想定しています。（内部でUnityアニメーションの値に変換）
        /// </summary>
        public void SetOverwriteEyeballPosFromMaya(Vector3 rightPos, Vector3 leftPos, Vector3 allPos)
        {
            if (_overwriteEyeballPosArray != null)
            {
                _isOverwriteEyballPos = true;
                _overwriteEyeballPosArray[RIGHT_EYE_INDEX] = rightPos / TRANSLATE_TO_UNITY_POSITION_VALUE;
                _overwriteEyeballPosArray[RIGHT_EYE_INDEX].x *= -1f;
                _overwriteEyeballPosArray[LEFT_EYE_INDEX] = leftPos / TRANSLATE_TO_UNITY_POSITION_VALUE;
                _overwriteEyeballPosArray[LEFT_EYE_INDEX].x *= -1f;
                _overwriteEyeballPosArray[ALL_EYE_INDEX] = allPos / TRANSLATE_TO_UNITY_POSITION_VALUE;
                _overwriteEyeballPosArray[ALL_EYE_INDEX].x *= -1f;
            }
        }
        /// <summary>
        /// 瞳の位置を上書きしない
        /// </summary>
        public void SetUnuseOverwriteEyeballPos()
        {
            if (_overwriteEyeballPosArray != null)
            {
                _isOverwriteEyballPos = false;
                _overwriteEyeballPosArray[RIGHT_EYE_INDEX] = Math.VECTOR3_ZERO;
                _overwriteEyeballPosArray[LEFT_EYE_INDEX] = Math.VECTOR3_ZERO;
                _overwriteEyeballPosArray[ALL_EYE_INDEX] = Math.VECTOR3_ZERO;
            }
        }
        /// <summary>
        /// 瞳の位置を取得。Unityアニメーションの値からMaya上の値に変換済み。
        /// </summary>
        public void GetEyeballPosForMaya(ref Vector3 rightPos, ref Vector3 leftPos, ref Vector3 allPos)
        {
            if (_drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform != null)
            {
                allPos = _drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform.localPosition * TRANSLATE_TO_UNITY_POSITION_VALUE;
                allPos.x *= -1f;
            }
            if (_drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform != null)
            {
                rightPos = _drivenKeyForEyeArray[RIGHT_EYE_INDEX].rootTransform.localPosition * TRANSLATE_TO_UNITY_POSITION_VALUE;
                rightPos.x *= -1f;
            }
            if (_drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform != null)
            {
                leftPos = _drivenKeyForEyeArray[LEFT_EYE_INDEX].rootTransform.localPosition * TRANSLATE_TO_UNITY_POSITION_VALUE;
                leftPos.x *= -1f;
            }
        }
#endif // UNITY_EDITOR && CYG_DEBUG

        public void UpdateAnimator(float elapsedTime)
        {
            WriteEyeDefault();
            _playableAnimator.UpdateMotion(elapsedTime);
            _playableAnimator.Evaluate();
        }

        public void PostJob(float elapsedTime)
        {
            if (!_isInitialized || !_isEnable)
            {
                return;
            }

            if (_isPause)
            {
                return;
            }

            // アニメーションから各グループのWeightを設定
            if (_isPlayingAnim)
            {
                const DrivenKeyPlayableAnimator.Layer TARGET_LAYER = DrivenKeyPlayableAnimator.Layer.Base;
                const DrivenKeyPlayableAnimator.ClipState TARGET_CLIP = DrivenKeyPlayableAnimator.ClipState.Base;

                if(_eyeWeight.IsAnimationWeight)
                    _eyeWeight.ClearBlendFace();
                if(_eyebrowWeight.IsAnimationWeight)
                    _eyebrowWeight.ClearBlendFace();
                if(_mouthWeight.IsAnimationWeight)
                    _mouthWeight.ClearBlendFace();

                UpdateWeight(_drivenKeyArray);
                float playTime = _playableAnimator.GetNormalizePlayTimeWithLoop(TARGET_LAYER);
                if(_playableAnimator.IsClip(TARGET_CLIP) && !_isFacePlayFrame)
                {
                    // ループじゃなければアニメーションによる更新を停止
                    if (!_playableAnimator.IsLoopClip(TARGET_CLIP) && playTime >= 1.0f)
                    {
                        _onEndAnimationAction?.Invoke(_ownerController);
                        SetLastEyeFace();
                        SetAllAnimationWeight(false);
                        _isCheckEyeRange = true;
                    }
                }
            }

            if (_isPlayingEarAnim)
            {
                const DrivenKeyPlayableAnimator.Layer TARGET_LAYER = DrivenKeyPlayableAnimator.Layer.Ear;
                const DrivenKeyPlayableAnimator.ClipState TARGET_CLIP = DrivenKeyPlayableAnimator.ClipState.Ear;

                _earWeight.ClearBlendFace();

                UpdateEarWeight(_drivenKeyForEarArray);
                var normalizeTime = _playableAnimator.GetNormalizePlayTimeWithLoop(TARGET_LAYER);
                if (_playableAnimator.IsClip(TARGET_CLIP) && !_isEarPlayFrame)
                {
                    // ループじゃなければアニメーションによる更新を停止
                    if (!_playableAnimator.IsLoopClip(TARGET_CLIP) && normalizeTime >= 1.0f)
                    {
                        _isPlayingEarAnim = false;
                    }
                }
            }

            _isEarPlayFrame = false;
            _isFacePlayFrame = false;

            BlendProgrammable(elapsedTime);

            //ドリブンキーのウェイト値から最終的なフェイシャル姿勢を算出する
            JobHandle jobHandle = default(JobHandle);
            if (_isEyeTarget)
            {
                jobHandle = _eyeWeight.CalcTRS(_weightSum, jobHandle);
            }
            if (_isEyebrowTarget)
            {
                jobHandle = _eyebrowWeight.CalcTRS(_weightSum, jobHandle);
            }
            if (_isMouthTarget)
            {
                jobHandle = _mouthWeight.CalcTRS(_weightSum, jobHandle);
            }
            if (_isEarTarget)
            {
                jobHandle = _earWeight.CalcTRS(_weightSum, jobHandle);
            }
        }

        private Vector3 CalcEyeTargetPosition(int eyeIndex,Vector3 allEyePos)
        {
            //表情のブレンドだけだと目線が正面を見てしまうので、目の位置は別のロケータにアニメーションを流している

            //wu_all -> 左右方向の目の移動値、1で左、-1で右に目が寄る
            float wu_all = allEyePos.x * TRANSLATE_TO_UNITY_POSITION;
            //wv_all -> 上下方向の目の移動値、1で上、-1で下に目が寄る
            float wv_all = allEyePos.y * -TRANSLATE_TO_UNITY_POSITION;

            //wu -> 1で内側、-1で外側に目が寄る
            var drivenEyePos = _drivenKeyForEyeArray[eyeIndex].rootTransform.localPosition;

            float wu = wu_all + drivenEyePos.x * TRANSLATE_TO_UNITY_POSITION;
            //wv -> 1で上、-1で下に目が寄る
            float wv = wv_all + drivenEyePos.y * -TRANSLATE_TO_UNITY_POSITION;
            Vector3 offsetU;
            Vector3 offsetV;
            switch(eyeIndex)
            {
                default:
                case RIGHT_EYE_INDEX:
                    offsetU = wu > 0 ? _innerEyeOffsetR : _outerEyeOffsetR;
                    offsetV = wv > 0 ? _upperEyeOffsetR : _downerEyeOffsetR;
                    break;
                case LEFT_EYE_INDEX:
                    wu = -wu;   //左目は向きが逆になる
                    offsetU = wu > 0 ? _innerEyeOffsetL : _outerEyeOffsetL;
                    offsetV = wv > 0 ? _upperEyeOffsetL : _downerEyeOffsetL;
                    break;
            }

            var resultTargetPos = _eyeTargetTransformArray[eyeIndex].localPosition + wu * offsetU + wv * offsetV;
            // 可動範囲チェック(プログラム指定の場合のみ)
            if (_isCheckEyeRange)
            {
                // 目の左右方向（X軸）の範囲について。
                // プラス） R_Outer > 右目 > R_Inner ...0... L_Inner > 左目 > L_Outer （マイナス
                // 右目は値が大きくなるほど外側（Outer）へ。左目は値が大きくなるほど内側（Inner）へ。
                float upperLimitY;
                float downerLimitY;
                switch (eyeIndex)
                {
                    default:
                    case RIGHT_EYE_INDEX:
                        //Xは左右で判定対象が違うのでそれぞれ分ける
                        if (resultTargetPos.x < _innerEyeLimitPosR.x)
                        {
                            resultTargetPos.x = _innerEyeLimitPosR.x;
                        }
                        else if (_outerEyeLimitPosR.x < resultTargetPos.x)
                        {
                            resultTargetPos.x = _outerEyeLimitPosR.x;
                        }
                        upperLimitY = _upperEyeLimitPosR.y;
                        downerLimitY = _downerEyeLimitPosR.y;
                        break;

                    case LEFT_EYE_INDEX:
                        if (_innerEyeLimitPosL.x < resultTargetPos.x)
                        {
                            resultTargetPos.x = _innerEyeLimitPosL.x;
                        }
                        else if (resultTargetPos.x < _outerEyeLimitPosL.x)
                        {
                            resultTargetPos.x = _outerEyeLimitPosL.x;
                        }

                        upperLimitY = _upperEyeLimitPosL.y;
                        downerLimitY = _downerEyeLimitPosL.y;
                        break;
                }

                //Y,Zは判定が同じなのでここで行う
                if (upperLimitY < resultTargetPos.y)
                {
                    resultTargetPos.y = upperLimitY;
                }
                else if (resultTargetPos.y < downerLimitY)
                {
                    resultTargetPos.y = downerLimitY;
                }

                if (resultTargetPos.z < _eyeLimitPosZRMin)
                {
                    resultTargetPos.z = _eyeLimitPosZRMin;
                }
                else if (_eyeLimitPosZRMax < resultTargetPos.z)
                {
                    resultTargetPos.z = _eyeLimitPosZRMax;
                }
            }
            return resultTargetPos;
        }

        /// <summary>
        /// 目の姿勢更新
        /// </summary>
        private void UpdateEyePosition()
        {
            //表情のブレンドだけだと目線が正面を見てしまうので、目の位置は別のロケータにアニメーションを流している
            var allEyePos = _drivenKeyForEyeArray[ALL_EYE_INDEX].rootTransform.localPosition;
            _eyeTargetTransformArray[RIGHT_EYE_INDEX].localPosition = CalcEyeTargetPosition(RIGHT_EYE_INDEX, allEyePos);
            _eyeTargetTransformArray[LEFT_EYE_INDEX].localPosition = CalcEyeTargetPosition(LEFT_EYE_INDEX, allEyePos);
        }

        /// <summary>
        /// アニメーション終了時の目の表情をセット
        /// </summary>
        private void SetLastEyeFace()
        {
            var weight = _eyeWeight.WeightArray;
            var weightTable = _eyeWeight.CommonWeightArray;
            for (int i = 0; i < weight.Length; i++)
            {
                int tableIndex = weight[i].StartIndex;
                _lastAnimLeftEyeWeightArray[i] = weightTable[tableIndex + (int)TargetInfomation.Part.EyeL];
                _lastAnimRightEyeWeightArray[i] = weightTable[tableIndex + (int)TargetInfomation.Part.EyeR];
            }
        }

        /// <summary>
        /// 動いてるならtrue
        /// </summary>
        /// <param name="faceGroup"></param>
        /// <returns></returns>
        public bool IsFacePlaying(FaceGroupType faceGroup)
        {
            if (!_isInitialized)
                return false;

            switch (faceGroup)
            {
                case FaceGroupType.EyeR:
                case FaceGroupType.EyeL:
                    {
                        return _eyeWeight.IsPlaying((int)faceGroup - _eyeWeight.FacialGroupOffset);
                    }
                case FaceGroupType.EyebrowR:
                case FaceGroupType.EyebrowL:
                    {
                        return _eyebrowWeight.IsPlaying((int)faceGroup - _eyebrowWeight.FacialGroupOffset);
                    }
                case FaceGroupType.Mouth:
                    {
                        return _mouthWeight.IsPlaying((int)faceGroup - _mouthWeight.FacialGroupOffset);
                    }
            }
            return false;
        }

        /// <summary>
        /// 動いてるならtrue
        /// </summary>
        /// <param name="faceGroup"></param>
        /// <returns></returns>
        public bool IsEarPlaying()
        {
            if (!_isInitialized)
                return false;

            return _earWeight.IsPlaying();
        }

        /// <summary>
        /// 目のモーション遷移時間をリセット
        /// 表情同じだが目だけ再度モーション（まばたき）させたいときがあるのでリセットをかける
        /// まばたきの時しか使わない関数
        /// </summary>
        public void ResetEyeCurrentTime()
        {
            int eyeLIndex = (int)FaceGroupType.EyeL - _eyeWeight.FacialGroupOffset;
            int eyeRIndex = (int)FaceGroupType.EyeR - _eyeWeight.FacialGroupOffset;

            _eyeWeight.CurrentTimeArray[eyeLIndex] = 0.0f;
            _eyeWeight.CurrentTimeArray[eyeRIndex] = 0.0f;

            _eyeWeight.DurationTimeArray[eyeLIndex] = 0.0f;
            _eyeWeight.DurationTimeArray[eyeRIndex] = 0.0f;
        }

        /// <summary>
        /// アニメーションによる制御を初期化する
        /// </summary>
        public void ClearAnimClip()
        {
            _playableAnimator.ClearClip();
            _isClipOverride = false;
        }

        private void ClearBlendFaces()
        {
            _eyeWeight.ClearBlendFace();
            _eyebrowWeight.ClearBlendFace();
            _mouthWeight.ClearBlendFace();
            _earWeight.ClearBlendFace();
        }

        /// <summary>
        /// 耳の対象を登録する
        /// </summary>
        /// <param name="ear"></param>
        private void AddBlendEar(EarType ear)
        {
            _earWeight.AddBlendFace(ear);
        }

        public void Destroy()
        {
            // 自前で作成したので消しておく。
            if (_eyeTarget != null)
            {
                GameObject.Destroy(_eyeTarget);
                _eyeTarget = null;
            }
            if (_eyebrowTarget != null)
            {
                GameObject.Destroy(_eyebrowTarget);
                _eyebrowTarget = null;
            }
            if (_mouthTarget != null)
            {
                GameObject.Destroy(_mouthTarget);
                _mouthTarget = null;
            }
            _playableAnimator?.Release();
            _playableAnimator = null;

            _faceKeyAnimation = null;
            _earKeyAnimation = null;

            _weightSum?.Release();
            _eyeWeight?.Release();
            _earWeight?.Release();
            _eyebrowWeight?.Release();
            _mouthWeight?.Release();
        }

        public void SetInterpolateType(FaceGroupType faceGroupType, InterpolateType interpolateType)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EarR:
                case FaceGroupType.EarL:
                    _earInterpolateType = interpolateType;
                    break;
                case FaceGroupType.EyeR:
                case FaceGroupType.EyeL:
                    _eyeInterpolateType = interpolateType;
                    break;
                case FaceGroupType.EyebrowR:
                case FaceGroupType.EyebrowL:
                    _eyebrowInterpolateType = interpolateType;
                    break;
                case FaceGroupType.Mouth:
                    _mouthInterpolateType = interpolateType;
                    break;
                case FaceGroupType.Max:
                    _eyeInterpolateType = interpolateType;
                    _eyebrowInterpolateType = interpolateType;
                    _mouthInterpolateType = interpolateType;
                    _earInterpolateType = interpolateType;
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// DrivenKeyComponent対象のオブジェクトを非アクティブにする
        /// </summary>
        /// <param name="enable"></param>
        public void SetLocatorActive(bool enable) => _drivenkeyLocator?.SetActive(enable);

        /// <summary>
        /// 仮想的な親子関係を構築する
        /// </summary>
        /// <param name="parent"></param>
        public void SetVirtualParentTransform(FaceGroupType group,string name,Transform root,Transform parent)
        {
            if (parent == null)
            {
                //この場合は外す
                int num = _virtualParentList.Count;
                for (int i = 0; i < num; i++)
                {
                    var info = _virtualParentList[i];
                    if (info.TargetInfo.TargetTransform.name.Equals(name))
                    {
                        info.TargetInfo.IsSetTemporary = false;
                        _virtualParentList.RemoveAt(i);
                        break;
                    }
                }
                return;
            }

            var groupInfo = _weightSum.FaceGroupInfoArray[(int)group];
            foreach(var target in groupInfo.CalcTrsTargetArray)
            {
                if(target.TargetTransform.name.Equals(name))
                {
                    //このターゲットの仮想親を設定する
                    VirtualParentInfo info = new VirtualParentInfo();
                    info.Parent = parent;
                    info.Root = root;
                    info.TargetInfo = target;
                    target.IsSetTemporary = true;
                    _virtualParentList.Add(info);
                    break;
                }
            }
        }

        public void ApplyVirtualParentTransform()
        {
            int num = _virtualParentList.Count;
            for(int i=0;i<num;i++)
            {
                var info = _virtualParentList[i];
                var rotation = info.Parent.rotation * Quaternion.Inverse(info.Root.rotation);
                info.TargetInfo.TargetTransform.localRotation = info.TargetInfo.TemporaryRotation * rotation;
            }
        }

        #region Global

        /// <summary>
        /// LocatorPrefabをロードする
        /// デフォルト挙動はInstantiateを返すので、Destroyする必要がある
        /// </summary>
        /// <param name="resourceHash"></param>
        /// <param name="isInstantiate"></param>
        /// <returns></returns>
        public static GameObject LoadLocatorPrefab(ResourceManager.ResourceHash resourceHash = ResourceManager.ResourceHash.InvalidHash,bool isInstantiate = true)
        {
            GameObject drivenKeyLocatorAsset;
            if (resourceHash == ResourceManager.ResourceHash.InvalidHash)
            {
                drivenKeyLocatorAsset = ResourceManager.LoadOnScene<GameObject>(ResourcePath.CharaDrivenKeyLoacatorPath);
            }
            else
            {
                drivenKeyLocatorAsset = ResourceManager.LoadOnHash<GameObject>(ResourcePath.CharaDrivenKeyLoacatorPath, resourceHash);
            }

            if(isInstantiate)
            {
                var instanceAsset = GameObject.Instantiate(drivenKeyLocatorAsset);
                instanceAsset.name = drivenKeyLocatorAsset.name;
                drivenKeyLocatorAsset = instanceAsset;
            }

            return drivenKeyLocatorAsset;
        }

        #endregion

#if CYG_DEBUG
        private bool IsTargetEnable()
        {
            if (_eyeTarget == null || _eyebrowTarget == null || _mouthTarget == null)
            {
                Debug.LogWarning("ドリブンキーターゲットが存在しません " + _rootObject.name);
                return false;
            }
            if (_eyeTarget._targetFaces.Length == 0 || _eyebrowTarget._targetFaces.Length == 0 || _mouthTarget._targetFaces.Length == 0)
            {
                Debug.Log("ドリブンキーターゲットのデータが存在していません" + _rootObject.name);
                return false;
            }

            if (!_isInitialized)
            {
                Debug.LogWarning("DrivenKeyComponent is uninitialized!");
                return false;
            }

            return true;
        }

        private bool IsEarTargetEnable()
        {
            if (_earTarget == null)
            {
                Debug.LogWarning("ドリブンキーターゲットが存在しません " + _rootObject.name);
                return false;
            }

            if (_earTarget._targetFaces.Length == 0)
            {
                Debug.Log("ドリブンキーターゲットのデータが存在していません" + _rootObject.name);
                return false;
            }

            if (!_isInitialized)
            {
                Debug.LogWarning("DrivenKeyComponent is uninitialized!");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 顔のパーツごとの設定。使うのはCharaViewerのみの想定。
        /// </summary>
        public void SetFaceEyebrowType(FaceType faceType, float weight, FaceGroupType targetGroup)
        {
            if (!_isInitialized)
            {
                return;
            }
            int faceTypeIndex = (int)faceType;
            if (_facePartsSetArray.Length <= faceTypeIndex)
            {
                faceTypeIndex = (int)FaceType.Base;
            }
            var facePartsSetArray = _facePartsSetArray[faceTypeIndex];
            var facePartsArray = (targetGroup == FaceGroupType.EyebrowL) ? facePartsSetArray._eyebrowL : facePartsSetArray._eyebrowR;
            SetFace(_eyebrowWeight, facePartsArray, weight, ModelController.EyeBrows, 0f, false, false, targetGroup);
        }

        public void SetFaceEyeType(FaceType faceType, float weight, FaceGroupType targetGroup)
        {
            if (!_isInitialized)
            {
                return;
            }
            int faceTypeIndex = (int)faceType;
            if (_facePartsSetArray.Length <= faceTypeIndex)
            {
                faceTypeIndex = (int)FaceType.Base;
            }
            var facePartsSetArray = _facePartsSetArray[faceTypeIndex];
            var facePartsArray = (targetGroup == FaceGroupType.EyeL) ? facePartsSetArray._eyeL : facePartsSetArray._eyeR;
            SetFace(_eyeWeight, facePartsArray, weight, ModelController.Eyes, 0f, false, false, targetGroup);
        }

        public void GetFacePartsList(ref List<FaceParts> eyebrowTypeLList, ref List<FaceParts> eyebrowTypeRList, ref List<FaceParts> eyeTypeLList, ref List<FaceParts> eyeTypeRList, ref List<FaceParts> mouthTypeList)
        {
            eyebrowTypeLList.Clear();
            eyebrowTypeRList.Clear();
            eyeTypeLList.Clear();
            eyeTypeRList.Clear();
            mouthTypeList.Clear();
            FaceParts faceParts = new FaceParts();

            var weightTable = _eyebrowWeight.CommonWeightArray;
            var group = _eyebrowWeight.WeightArray;
            for (int i = 1, length = group.Length; i < length; ++i)
            {
                var tableIndex = group[i].StartIndex;
                faceParts._faceParts = i;
                faceParts._weight = weightTable[tableIndex + 0];
                if (faceParts._weight > 0f)
                {
                    eyebrowTypeRList.Add(faceParts);
                }
                faceParts._weight = weightTable[tableIndex + 1];
                if (faceParts._weight > 0f)
                {
                    eyebrowTypeLList.Add(faceParts);
                }
            }
            weightTable = _eyeWeight.CommonWeightArray;
            group = _eyeWeight.WeightArray;
            for (int i = 1, length = group.Length; i < length; ++i)
            {
                var tableIndex = group[i].StartIndex;
                faceParts._faceParts = i;
                faceParts._weight = weightTable[tableIndex + 0];
                if (faceParts._weight > 0f)
                {
                    eyeTypeRList.Add(faceParts);
                }
                faceParts._weight = weightTable[tableIndex + 1];
                if (faceParts._weight > 0f)
                {
                    eyeTypeLList.Add(faceParts);
                }
            }
            weightTable = _mouthWeight.CommonWeightArray;
            group = _mouthWeight.WeightArray;
            for (int i = 1, length = group.Length; i < length; ++i)
            {
                var tableIndex = group[i].StartIndex;
                faceParts._faceParts = i;
                faceParts._weight = weightTable[tableIndex];
                if (faceParts._weight > 0f)
                {
                    mouthTypeList.Add(faceParts);
                }
            }
            faceParts._faceParts = 0;
            faceParts._weight = 1f;
            if (eyebrowTypeLList.Count <= 0)
            {
                eyebrowTypeLList.Add(faceParts);
            }
            if (eyebrowTypeRList.Count <= 0)
            {
                eyebrowTypeRList.Add(faceParts);
            }
            if (eyeTypeLList.Count <= 0)
            {
                eyeTypeLList.Add(faceParts);
            }
            if (eyeTypeRList.Count <= 0)
            {
                eyeTypeRList.Add(faceParts);
            }
            if (mouthTypeList.Count <= 0)
            {
                mouthTypeList.Add(faceParts);
            }
        }
#endif

#if UNITY_EDITOR
        #region CUSTUM_EDITOR
        private bool _isFacePartsFoldout;
        private bool _isDrivenKeyArrayFoldout;
        private bool _isDrivenKeyEyeArrayFoldout;
        private bool _isDrivenKeyEarArrayFoldout;
        private bool[] _isFacePartsArrayFoldout = new bool[(int)FaceGroupType.Max];

        private bool _isDisplayWeight;
        private bool _isDispWide;


        private void DrawArrayInspector<T>(T[] partsArray,System.Action<T> itemDrawCallback)
        {
            EditorGUILayout.LabelField("Length:" + partsArray.Length);
            foreach(var item in partsArray)
            {
                itemDrawCallback(item);
            }
        }

        private void DrawFaceParts(string label,FaceParts[] partsArray,ref bool foldOut)
        {
            if(partsArray == null)
            {
                return;
            }
            foldOut = EditorGUILayout.Foldout(foldOut,label);
            if (foldOut)
            {
                for (int i = 0; i < partsArray.Length; i++)
                {
                    partsArray[i]._faceParts = EditorGUILayout.IntField("Parts", partsArray[i]._faceParts);
                    partsArray[i]._weight = EditorGUILayout.FloatField("Weight", partsArray[i]._weight);
                }
            }
        }

        private void DrawDrivenKeyArray(DrivenKeys[] drivenKeyArray)
        {
            DrawArrayInspector(drivenKeyArray,
                (item) =>
                {
                    EditorGUILayout.ObjectField("Root Obj", item.rootObj, typeof(GameObject), false);
                    EditorGUILayout.ObjectField("Root Transform", item.rootTransform, typeof(Transform), false);
                    EditorGUI.indentLevel++;
                    if (item.info != null)
                    {
                        for (int i = 0; i < item.info.Length; i++)
                        {
                            EditorGUILayout.Toggle("Enable", item.info[i].isEnable);
                            EditorGUILayout.ObjectField("Transform", item.info[i].objectTrans, typeof(Transform), false);
                        }
                    }
                    EditorGUI.indentLevel--;
                }
            );
        }

        private void OnDrawWeightParam(WeightGroupBase weight,float width,System.Func<int,string> getTypeAction)
        {
            var weightTable = weight.CommonWeightArray;
            var group = weight.WeightArray;
            for (int i = 0, length = group.Length; i < length; ++i)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(i + ":" + getTypeAction(i), GUILayout.Width(100));
                var tableIndex = group[i].StartIndex;
                for (int j = 0; j < group[i].Length; j++)
                {
                    float w = weightTable[tableIndex + j];
                    EditorGUILayout.LabelField(w.ToString(), GUILayout.Width(width));
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        public void OnInspectorGUI()
        {
            var component = this;

            EditorGUILayout.Toggle("Enable", component.IsEnable);

            component._eyeTarget = (DrivenKeyTarget)EditorGUILayout.ObjectField("Eye", component._eyeTarget, typeof(DrivenKeyTarget), false);
            component._eyebrowTarget = (DrivenKeyTarget)EditorGUILayout.ObjectField("Eyebrow", component._eyebrowTarget, typeof(DrivenKeyTarget), false);
            component._mouthTarget = (DrivenKeyTarget)EditorGUILayout.ObjectField("Mouth", component._mouthTarget, typeof(DrivenKeyTarget), false);
            component._earTarget = (DrivenKeyTarget)EditorGUILayout.ObjectField("Ear", component._earTarget, typeof(DrivenKeyTarget), false);

            if(component._facePartsSetArray != null)
            {
                EditorGUI.indentLevel++;
                component._isFacePartsFoldout = EditorGUILayout.Foldout(_isFacePartsFoldout, "Face Parts Set");
                if(component._isFacePartsFoldout)
                {
                    DrawArrayInspector(component._facePartsSetArray,
                        (item) =>
                        {
                            DrawFaceParts("EyeL", item._eyeL, ref _isFacePartsArrayFoldout[(int)FaceGroupType.EyeL]);
                            DrawFaceParts("EyeR", item._eyeR, ref _isFacePartsArrayFoldout[(int)FaceGroupType.EyeR]);
                            DrawFaceParts("EyebrowL", item._eyebrowL, ref _isFacePartsArrayFoldout[(int)FaceGroupType.EyebrowL]);
                            DrawFaceParts("EyebrowR", item._eyebrowR, ref _isFacePartsArrayFoldout[(int)FaceGroupType.EyebrowR]);
                            DrawFaceParts("Mouth", item._mouth, ref _isFacePartsArrayFoldout[(int)FaceGroupType.Mouth]);
                        }
                    );
                }
                EditorGUI.indentLevel--;
            }

            if(component._drivenKeyArray != null)
            {
                EditorGUI.indentLevel++;
                component._isDrivenKeyArrayFoldout = EditorGUILayout.Foldout(_isDrivenKeyArrayFoldout, "DrivenKey Array");
                if(component._isDrivenKeyArrayFoldout)
                {
                    DrawDrivenKeyArray(component._drivenKeyArray);
                }
                EditorGUI.indentLevel--;
            }

            if (component._drivenKeyForEyeArray != null)
            {
                EditorGUI.indentLevel++;
                component._isDrivenKeyEyeArrayFoldout = EditorGUILayout.Foldout(_isDrivenKeyEyeArrayFoldout, "DrivenKey Eye Array");
                if (component._isDrivenKeyEyeArrayFoldout)
                {
                    DrawDrivenKeyArray(component._drivenKeyForEyeArray);
                }
                EditorGUI.indentLevel--;
            }

            if (component._drivenKeyForEarArray != null)
            {
                EditorGUI.indentLevel++;
                component._isDrivenKeyEarArrayFoldout = EditorGUILayout.Foldout(_isDrivenKeyEarArrayFoldout, "DrivenKey Ear Array");
                if (component._isDrivenKeyEarArrayFoldout)
                {
                    DrawDrivenKeyArray(component._drivenKeyForEarArray);
                }
                EditorGUI.indentLevel--;
            }

            if(component._playableAnimator.IsClip(DrivenKeyPlayableAnimator.ClipState.Base))
            {
                var clip = component._playableAnimator.GetClip(DrivenKeyPlayableAnimator.ClipState.Base);
                GUILayout.Label("Face:" + clip.name);
                if (component.IsPlayingAnim)
                {
                    GUILayout.Label("Play Normalized:" + component._playableAnimator.GetNormalizePlayTimeWithLoop(DrivenKeyPlayableAnimator.Layer.Base));
                }
                else
                {
                    GUILayout.Label("Not Play");
                }
            }

            if (component._playableAnimator.IsClip(DrivenKeyPlayableAnimator.ClipState.Ear))
            {
                var clip = component._playableAnimator.GetClip(DrivenKeyPlayableAnimator.ClipState.Ear);
                GUILayout.Label("Ear:" + clip.name);
                if (component.IsPlayingEarAnim)
                {
                    GUILayout.Label("Play Normalized:" + component._playableAnimator.GetNormalizePlayTimeWithLoop(DrivenKeyPlayableAnimator.Layer.Ear));
                }
                else
                {
                    GUILayout.Label("Not Play");
                }
            }

            if (GUILayout.Button("DisplayWeightGroup"))
            {
                _isDisplayWeight = !_isDisplayWeight;
            }
            if (!_isDisplayWeight)
            {
                return;
            }
            _isDispWide = EditorGUILayout.Toggle("横長表示", _isDispWide);
            int paramWidth = (_isDispWide) ? 100 : 32;
            {// 目
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("", GUILayout.Width(100));
                for (int i = (int)FaceGroupType.EyeR; i < (int)FaceGroupType.EyeL + 1; ++i)
                {
                    EditorGUILayout.LabelField(((FaceGroupType)i).ToString(), GUILayout.Width(paramWidth));
                }
                EditorGUILayout.EndHorizontal();

                OnDrawWeightParam(component._eyeWeight, paramWidth, (index) => ((FaceEyeType)index).ToString());
            }
            EditorGUILayout.Space();
            {// 眉
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("", GUILayout.Width(100));
                for (int i = (int)FaceGroupType.EyebrowR; i < (int)FaceGroupType.EyebrowL + 1; ++i)
                {
                    EditorGUILayout.LabelField(((FaceGroupType)i).ToString(), GUILayout.Width(paramWidth));
                }
                EditorGUILayout.EndHorizontal();

                OnDrawWeightParam(component._eyebrowWeight, paramWidth, (index) => ((FaceEyebrowType)index).ToString());
            }
            EditorGUILayout.Space();
            {// 口
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("", GUILayout.Width(100));
                for (int i = (int)FaceGroupType.Mouth; i < (int)FaceGroupType.Mouth + 1; ++i)
                {
                    EditorGUILayout.LabelField(((FaceGroupType)i).ToString(), GUILayout.Width(paramWidth));
                }
                EditorGUILayout.EndHorizontal();

                OnDrawWeightParam(component._mouthWeight, paramWidth, (index) => ((FaceMouthType)index).ToString());
            }
            EditorGUILayout.Space();

            // 耳
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("", GUILayout.Width(100));
            for (int i = (int)FaceGroupType.EarR; i <= (int)FaceGroupType.EarL; ++i)
            {
                EditorGUILayout.LabelField(((FaceGroupType)i).ToString() + "0", GUILayout.Width(paramWidth));
                EditorGUILayout.LabelField(((FaceGroupType)i).ToString() + "1", GUILayout.Width(paramWidth));
                EditorGUILayout.LabelField(((FaceGroupType)i).ToString() + "2", GUILayout.Width(paramWidth));
            }
            EditorGUILayout.EndHorizontal();
            {
                OnDrawWeightParam(component._earWeight, paramWidth, (index) => ((EarType)index).ToString());
            }
        }
        #endregion
#endif

#if UNITY_EDITOR && CYG_DEBUG
        /// <summary>
        /// 耳を設定。複数のブレンド状態を一度に設定。
        /// </summary>
        public void SetEar(List<FaceParts> earLList, List<FaceParts> earRList)
        {
            if (!_isInitialized)
            {
                return;
            }
            var targetWeight = _earWeight.WeightArray;
            if (_earWeight.IsCalcWeight)
            {
                _earWeight.ClearBlendFace();
            }
            _earWeight.IsCalcWeight = false;

            for (int i = 0; i < DrivenKeyAnimation.EarNum; i++)
            {
                _earWeight.ClearTargetWeight(i, ref targetWeight);
            }
            // 右耳
            foreach(var ear in earRList)
            {
                int index = (int)FaceGroupType.EarR * EarWeightGroup.WEIGHT_SIZE;
                var tableIndex = targetWeight[ear._faceParts].StartIndex + index;

                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_0] = ear._weight;
                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_1] = ear._weight;
                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_2] = ear._weight;

                //どれか１つでも有効であれば
                if (ear._weight > 0.0f)
                {
                    AddBlendEar((EarType)ear._faceParts);
                }
            }
            // 左耳
            foreach (var ear in earLList)
            {
                int index = (int)FaceGroupType.EarL * EarWeightGroup.WEIGHT_SIZE;
                var tableIndex = targetWeight[ear._faceParts].StartIndex + index;

                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_0] = ear._weight;
                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_1] = ear._weight;
                _earWeight.CommonWeightArray[tableIndex + EarWeightGroup.WEIGHT_2] = ear._weight;

                //どれか１つでも有効であれば
                if (ear._weight > 0.0f)
                {
                    AddBlendEar((EarType)ear._faceParts);
                }
            }

            for (int i = 0; i < DrivenKeyAnimation.EarNum; i++)
            {
                _earWeight.GetEarTypes(ref _setEarTypeArray[i], true);
            }

            //アニメーションではなくなるので無効にする
            _earKeyAnimation = null;
            _animationEarTypeArray = _setEarTypeArray;
        }
        /// <summary>
        /// 耳のブレンド状態を取得
        /// </summary>
        public void GetEarList(ref List<FaceParts> earLList, ref List<FaceParts> earRList)
        {
            earLList.Clear();
            earRList.Clear();
            FaceParts faceParts = new FaceParts();

            var weightTable = _earWeight.CommonWeightArray;
            var group = _earWeight.WeightArray;
            for (int i = 1, length = group.Length; i < length; ++i)
            {
                var tableIndex = group[i].StartIndex;
                faceParts._faceParts = i;
                faceParts._weight = weightTable[tableIndex + 0];
                if (faceParts._weight > 0f)
                {
                    earRList.Add(faceParts);
                }
                faceParts._weight = weightTable[tableIndex + EarWeightGroup.WEIGHT_SIZE];
                if (faceParts._weight > 0f)
                {
                    earLList.Add(faceParts);
                }
            }
            faceParts._faceParts = 0;
            faceParts._weight = 1f;
            if (earRList.Count <= 0)
            {
                earRList.Add(faceParts);
            }
            if (earRList.Count <= 0)
            {
                earRList.Add(faceParts);
            }
        }
#endif // UNITY_EDITOR && CYG_DEBUG
    }
}
