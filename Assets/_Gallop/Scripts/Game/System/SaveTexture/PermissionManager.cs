using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// パーミッション管理
    /// </summary>
    public class PermissionManager : MonoSingleton<PermissionManager>
    {
        /// <summary>WaitForEndOfFrame</summary>
        public readonly WaitForEndOfFrame WAIT_FOR_END_OF_FRAME = new WaitForEndOfFrame();

        /// <summary>パーミッション</summary>
        private PermissionBase permission = null;

        /// <summary>問い合わせ中かどうか</summary>
        private static bool isContact = false;

        /// <summary>
        /// 初期化
        /// </summary>
        protected override void OnInitialize()
        {
            permission = PermissionBase.CreateObject(transform, notify);
        }

        /// <summary>
        /// 実行
        /// </summary>
        /// <param name="_permission"></param>
        /// <param name="_callback"></param>
        public void Exec(ePermission _permission, Action<ePermissionResult> _callback)
        {
            if (isContact)
            {
                Debug.LogAssertion("すでに問い合わせ中です!!!");
                return;
            }
            StartCoroutine(contactCoroutine(_permission, _callback));
        }

        /// <summary>
        /// 終了通知
        /// </summary>
        private void notify()
        {
            isContact = false;
        }

        /// <summary>
        /// 問い合わせコルーチン
        /// </summary>
        /// <param name="_permission"></param>
        /// <param name="_callback"></param>
        /// <returns></returns>
        private IEnumerator contactCoroutine(ePermission _permission, Action<ePermissionResult> _callback)
        {
            isContact = true;

            permission.Request(_permission);
            while (isContact)
            {
                yield return WAIT_FOR_END_OF_FRAME;
            }

            _callback(permission.CheckPermission(_permission) ? ePermissionResult.SUCCESS : ePermissionResult.FAILED);
        }
    }

    /// <summary>
    /// パーミッション
    /// </summary>
    public enum ePermission
    {
        /// <summary>写真保存 / Androidはギャラリー、iosは写真で画像を見れるようにする</summary>
        SAVE_TEXTURE,
    }

    /// <summary>
    /// パーミッション結果
    /// </summary>
    public enum ePermissionResult
    {
        /// <summary>NONE</summary>
        NONE,
        /// <summary>成功</summary>
        SUCCESS,
        /// <summary>失敗</summary>
        FAILED,
    }

    /// <summary>
    /// ePermission / ボックス化解消用Compareオーバーライド
    /// </summary>
    public class ePermission_DictComparer : System.Collections.Generic.IEqualityComparer<ePermission>
    {
        public bool Equals(ePermission _x, ePermission _y)
        {
            return _x == _y;
        }
        public int GetHashCode(ePermission _obj)
        {
            return (int)_obj;
        }
    }
}
