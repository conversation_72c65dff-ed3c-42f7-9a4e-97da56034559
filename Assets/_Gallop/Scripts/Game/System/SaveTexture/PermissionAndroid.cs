//20200916 サークルプロフィールオミットにつき、画像保存権限の取得を停止。
//リリースする際は下記の行をAndroidManifestに追記し、falseで外されてる方のクラスを有効化してください。
//  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
#if !UNITY_EDITOR && UNITY_ANDROID
namespace Gallop
{
    public class PermissionAndroid : PermissionBase
    {
        protected override void onInit()
        {
        }
        public override bool CheckPermission(ePermission _checkPermission)
        {
            return true;
        }
        public override void Request(ePermission _requestPermission)
        {
            callback();
        }
    }
}
#endif


#if false

using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    public class PermissionAndroid : PermissionBase
    {
        /// <summary>Androidパーミッション名Dictionary</summary>
        private static readonly Dictionary<ePermission, string> ANDROID_PERMISSION_NAME_DIC = new Dictionary<ePermission, string>(new ePermission_DictComparer())
        {
            { ePermission.SAVE_TEXTURE,  "WRITE_EXTERNAL_STORAGE" } ,
        };

        /// <summary>Androidビルドクラス名</summary>
        private const string ANDROID_BUILD_CLASS_NAME = "android.os.Build$VERSION";

        /// <summary>Androidバージョン名</summary>
        private const string ANDROID_VERSION_NAME = "SDK_INT";

        /// <summary>AndroidMバージョン</summary>
        private const int ANDROID_M_VERSION = 23;

        /// <summary>AndroidM以上かどうか</summary>
        private bool isAndroidMOrGreater = false;

        /// <summary>コンストラクタ</summary
        protected override void onInit()
        {
            var androidBuildClass = new AndroidJavaClass(ANDROID_BUILD_CLASS_NAME);
            isAndroidMOrGreater = androidBuildClass.GetStatic<int>(ANDROID_VERSION_NAME) >= ANDROID_M_VERSION;

            AndroidJavaClass androidJavaClass = new AndroidJavaClass("jp.co.cygames.umamusume_activity.UmamusumeActivity");
            RequestPermissionListener requestPermissionListener = new RequestPermissionListener((result) => onRequestPermissionsResult());
            androidJavaClass.CallStatic("setRequestPermissionListener", requestPermissionListener);
        }

        /// <summary>リクエスト</summary>
        /// <param name="_requestPermission"></param>
        public override void Request(ePermission _requestPermission)
        {
            // パーミッションがすでに許可されていたらコールバックを呼ぶ
            if (CheckPermission(_requestPermission))
            {
                onRequestPermissionsResult();
                return;
            }

            Cute.Core.NativePluginWrapper.RequestPermissions(ANDROID_PERMISSION_NAME_DIC[_requestPermission]);
        }

        /// <summary>パーミッションが許可されているかどうか</summary>
        /// <param name="_checkPermission"></param>
        /// <returns></returns>
        public override bool CheckPermission(ePermission _checkPermission)
        {
            if (isAndroidMOrGreater)
            {
                return Cute.Core.NativePluginWrapper.CheckSelfPermissions(ANDROID_PERMISSION_NAME_DIC[_checkPermission]);
            }

            return true;
        }

        /// <summary>
        /// パーミッションリクエスト結果コールバック
        /// </summary>
        private void onRequestPermissionsResult()
        {
            callback();
        }
    }
}
#endif // !UNITY_EDITOR && UNITY_ANDROID