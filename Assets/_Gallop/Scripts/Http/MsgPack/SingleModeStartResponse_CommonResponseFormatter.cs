#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeStartResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeStartResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeStartResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"chara_info", 0},
                {"race_condition_array", 1},
                {"home_info", 2},
                {"unchecked_event_array", 3},
                {"tp_info", 4},
                {"user_item_array", 5},
                {"add_trained_chara_array", 6},
                {"reserved_race_array", 7},
                {"mission_list", 8},
                {"story_event_mission_list", 9},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("chara_info"),
                global::System.Text.Encoding.UTF8.GetBytes("race_condition_array"),
                global::System.Text.Encoding.UTF8.GetBytes("home_info"),
                global::System.Text.Encoding.UTF8.GetBytes("unchecked_event_array"),
                global::System.Text.Encoding.UTF8.GetBytes("tp_info"),
                global::System.Text.Encoding.UTF8.GetBytes("user_item_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_trained_chara_array"),
                global::System.Text.Encoding.UTF8.GetBytes("reserved_race_array"),
                global::System.Text.Encoding.UTF8.GetBytes("mission_list"),
                global::System.Text.Encoding.UTF8.GetBytes("story_event_mission_list"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeStartResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 10);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeChara>().Serialize(ref bytes, offset, value.chara_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeRaceCondition[]>().Serialize(ref bytes, offset, value.race_condition_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeHomeInfo>().Serialize(ref bytes, offset, value.home_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeEventInfo[]>().Serialize(ref bytes, offset, value.unchecked_event_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<TpInfo>().Serialize(ref bytes, offset, value.tp_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<UserItem[]>().Serialize(ref bytes, offset, value.user_item_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Serialize(ref bytes, offset, value.add_trained_chara_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeReservedRaceDeck[]>().Serialize(ref bytes, offset, value.reserved_race_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<UserMission[]>().Serialize(ref bytes, offset, value.mission_list, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += formatterResolver.GetFormatterWithVerify<UserMission[]>().Serialize(ref bytes, offset, value.story_event_mission_list, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeStartResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __chara_info__ = default(SingleModeChara);
            var __race_condition_array__ = default(SingleModeRaceCondition[]);
            var __home_info__ = default(SingleModeHomeInfo);
            var __unchecked_event_array__ = default(SingleModeEventInfo[]);
            var __tp_info__ = default(TpInfo);
            var __user_item_array__ = default(UserItem[]);
            var __add_trained_chara_array__ = default(TrainedChara[]);
            var __reserved_race_array__ = default(SingleModeReservedRaceDeck[]);
            var __mission_list__ = default(UserMission[]);
            var __story_event_mission_list__ = default(UserMission[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __chara_info__ = formatterResolver.GetFormatterWithVerify<SingleModeChara>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __race_condition_array__ = formatterResolver.GetFormatterWithVerify<SingleModeRaceCondition[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __home_info__ = formatterResolver.GetFormatterWithVerify<SingleModeHomeInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __unchecked_event_array__ = formatterResolver.GetFormatterWithVerify<SingleModeEventInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __tp_info__ = formatterResolver.GetFormatterWithVerify<TpInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __user_item_array__ = formatterResolver.GetFormatterWithVerify<UserItem[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __add_trained_chara_array__ = formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 7:
                        __reserved_race_array__ = formatterResolver.GetFormatterWithVerify<SingleModeReservedRaceDeck[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __mission_list__ = formatterResolver.GetFormatterWithVerify<UserMission[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 9:
                        __story_event_mission_list__ = formatterResolver.GetFormatterWithVerify<UserMission[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeStartResponse.CommonResponse();
            ____result.chara_info = __chara_info__;
            ____result.race_condition_array = __race_condition_array__;
            ____result.home_info = __home_info__;
            ____result.unchecked_event_array = __unchecked_event_array__;
            ____result.tp_info = __tp_info__;
            ____result.user_item_array = __user_item_array__;
            ____result.add_trained_chara_array = __add_trained_chara_array__;
            ____result.reserved_race_array = __reserved_race_array__;
            ____result.mission_list = __mission_list__;
            ____result.story_event_mission_list = __story_event_mission_list__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
