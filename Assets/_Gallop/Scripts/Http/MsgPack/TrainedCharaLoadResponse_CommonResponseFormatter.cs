#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class TrainedCharaLoadResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< TrainedCharaLoadResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public TrainedCharaLoadResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"trained_chara_array", 0},
                {"trained_chara_favorite_array", 1},
                {"room_match_entry_chara_id_array", 2},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("trained_chara_array"),
                global::System.Text.Encoding.UTF8.GetBytes("trained_chara_favorite_array"),
                global::System.Text.Encoding.UTF8.GetBytes("room_match_entry_chara_id_array"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, TrainedCharaLoadResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 3);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Serialize(ref bytes, offset, value.trained_chara_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedCharaFavorite[]>().Serialize(ref bytes, offset, value.trained_chara_favorite_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<int[]>().Serialize(ref bytes, offset, value.room_match_entry_chara_id_array, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.TrainedCharaLoadResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __trained_chara_array__ = default(TrainedChara[]);
            var __trained_chara_favorite_array__ = default(TrainedCharaFavorite[]);
            var __room_match_entry_chara_id_array__ = default(int[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __trained_chara_array__ = formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __trained_chara_favorite_array__ = formatterResolver.GetFormatterWithVerify<TrainedCharaFavorite[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __room_match_entry_chara_id_array__ = formatterResolver.GetFormatterWithVerify<int[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new TrainedCharaLoadResponse.CommonResponse();
            ____result.trained_chara_array = __trained_chara_array__;
            ____result.trained_chara_favorite_array = __trained_chara_favorite_array__;
            ____result.room_match_entry_chara_id_array = __room_match_entry_chara_id_array__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
