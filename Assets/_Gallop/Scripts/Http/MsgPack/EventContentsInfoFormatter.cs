#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class EventContentsInfoFormatter : global::MessagePack.Formatters.IMessagePackFormatter< EventContentsInfo >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public EventContentsInfoFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"support_card_id", 0},
                {"show_clear", 1},
                {"show_clear_sort_id", 2},
                {"choice_array", 3},
                {"is_effected_multi_chara", 4},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("support_card_id"),
                global::System.Text.Encoding.UTF8.GetBytes("show_clear"),
                global::System.Text.Encoding.UTF8.GetBytes("show_clear_sort_id"),
                global::System.Text.Encoding.UTF8.GetBytes("choice_array"),
                global::System.Text.Encoding.UTF8.GetBytes("is_effected_multi_chara"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, EventContentsInfo value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 5);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.support_card_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.show_clear);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.show_clear_sort_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<ChoiceArray[]>().Serialize(ref bytes, offset, value.choice_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteBoolean(ref bytes, offset, value.is_effected_multi_chara);
            return offset - startOffset;
        }

        public global::Gallop.EventContentsInfo Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __support_card_id__ = default(int);
            var __show_clear__ = default(int);
            var __show_clear_sort_id__ = default(int);
            var __choice_array__ = default(ChoiceArray[]);
            var __is_effected_multi_chara__ = default(bool);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __support_card_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __show_clear__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __show_clear_sort_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __choice_array__ = formatterResolver.GetFormatterWithVerify<ChoiceArray[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        if (bytes[offset] == 0x00 || bytes[offset] == 0x01)
                        {
                            var temp = default(int);
                            temp = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                            __is_effected_multi_chara__ = temp == 0 ? false : true;
                        }
                        else
                        {
                            __is_effected_multi_chara__ = global::MessagePack.MessagePackBinary.ReadBoolean(bytes, offset, out readSize);
                        }
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new EventContentsInfo();
            ____result.support_card_id = __support_card_id__;
            ____result.show_clear = __show_clear__;
            ____result.show_clear_sort_id = __show_clear_sort_id__;
            ____result.choice_array = __choice_array__;
            ____result.is_effected_multi_chara = __is_effected_multi_chara__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
