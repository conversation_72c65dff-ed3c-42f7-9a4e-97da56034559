#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class RoomMatchTrainedCharaFormatter : global::MessagePack.Formatters.IMessagePackFormatter< RoomMatchTrainedChara >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public RoomMatchTrainedCharaFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"viewer_id", 0},
                {"trained_chara_id", 1},
                {"owner_viewer_id", 2},
                {"use_type", 3},
                {"card_id", 4},
                {"name", 5},
                {"stamina", 6},
                {"speed", 7},
                {"power", 8},
                {"guts", 9},
                {"wiz", 10},
                {"fans", 11},
                {"rank_score", 12},
                {"rank", 13},
                {"proper_distance_short", 14},
                {"proper_distance_mile", 15},
                {"proper_distance_middle", 16},
                {"proper_distance_long", 17},
                {"proper_running_style_nige", 18},
                {"proper_running_style_senko", 19},
                {"proper_running_style_sashi", 20},
                {"proper_running_style_oikomi", 21},
                {"proper_ground_turf", 22},
                {"proper_ground_dirt", 23},
                {"succession_num", 24},
                {"is_locked", 25},
                {"rarity", 26},
                {"talent_level", 27},
                {"chara_grade", 28},
                {"running_style", 29},
                {"nickname_id", 30},
                {"wins", 31},
                {"skill_array", 32},
                {"support_card_list", 33},
                {"is_saved", 34},
                {"race_result_list", 35},
                {"win_saddle_id_array", 36},
                {"nickname_id_array", 37},
                {"factor_id_array", 38},
                {"factor_info_array", 39},
                {"succession_chara_array", 40},
                {"succession_history_array", 41},
                {"scenario_id", 42},
                {"member_id", 43},
                {"join_type", 44},
                {"create_time", 45},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("trained_chara_id"),
                global::System.Text.Encoding.UTF8.GetBytes("owner_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("use_type"),
                global::System.Text.Encoding.UTF8.GetBytes("card_id"),
                global::System.Text.Encoding.UTF8.GetBytes("name"),
                global::System.Text.Encoding.UTF8.GetBytes("stamina"),
                global::System.Text.Encoding.UTF8.GetBytes("speed"),
                global::System.Text.Encoding.UTF8.GetBytes("power"),
                global::System.Text.Encoding.UTF8.GetBytes("guts"),
                global::System.Text.Encoding.UTF8.GetBytes("wiz"),
                global::System.Text.Encoding.UTF8.GetBytes("fans"),
                global::System.Text.Encoding.UTF8.GetBytes("rank_score"),
                global::System.Text.Encoding.UTF8.GetBytes("rank"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_distance_short"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_distance_mile"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_distance_middle"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_distance_long"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_running_style_nige"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_running_style_senko"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_running_style_sashi"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_running_style_oikomi"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_ground_turf"),
                global::System.Text.Encoding.UTF8.GetBytes("proper_ground_dirt"),
                global::System.Text.Encoding.UTF8.GetBytes("succession_num"),
                global::System.Text.Encoding.UTF8.GetBytes("is_locked"),
                global::System.Text.Encoding.UTF8.GetBytes("rarity"),
                global::System.Text.Encoding.UTF8.GetBytes("talent_level"),
                global::System.Text.Encoding.UTF8.GetBytes("chara_grade"),
                global::System.Text.Encoding.UTF8.GetBytes("running_style"),
                global::System.Text.Encoding.UTF8.GetBytes("nickname_id"),
                global::System.Text.Encoding.UTF8.GetBytes("wins"),
                global::System.Text.Encoding.UTF8.GetBytes("skill_array"),
                global::System.Text.Encoding.UTF8.GetBytes("support_card_list"),
                global::System.Text.Encoding.UTF8.GetBytes("is_saved"),
                global::System.Text.Encoding.UTF8.GetBytes("race_result_list"),
                global::System.Text.Encoding.UTF8.GetBytes("win_saddle_id_array"),
                global::System.Text.Encoding.UTF8.GetBytes("nickname_id_array"),
                global::System.Text.Encoding.UTF8.GetBytes("factor_id_array"),
                global::System.Text.Encoding.UTF8.GetBytes("factor_info_array"),
                global::System.Text.Encoding.UTF8.GetBytes("succession_chara_array"),
                global::System.Text.Encoding.UTF8.GetBytes("succession_history_array"),
                global::System.Text.Encoding.UTF8.GetBytes("scenario_id"),
                global::System.Text.Encoding.UTF8.GetBytes("member_id"),
                global::System.Text.Encoding.UTF8.GetBytes("join_type"),
                global::System.Text.Encoding.UTF8.GetBytes("create_time"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, RoomMatchTrainedChara value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteMapHeader(ref bytes, offset, 46);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.trained_chara_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.owner_viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.use_type);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.card_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.stamina);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.speed);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.power);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.guts);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.wiz);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.fans);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[12]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank_score);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[13]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[14]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_distance_short);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[15]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_distance_mile);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[16]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_distance_middle);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[17]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_distance_long);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[18]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_running_style_nige);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[19]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_running_style_senko);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[20]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_running_style_sashi);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[21]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_running_style_oikomi);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[22]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_ground_turf);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[23]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.proper_ground_dirt);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[24]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.succession_num);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[25]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.is_locked);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[26]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rarity);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[27]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.talent_level);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[28]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.chara_grade);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[29]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.running_style);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[30]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.nickname_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[31]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.wins);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[32]);
            offset += formatterResolver.GetFormatterWithVerify<SkillData[]>().Serialize(ref bytes, offset, value.skill_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[33]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedCharaSupportCardList[]>().Serialize(ref bytes, offset, value.support_card_list, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[34]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.is_saved);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[35]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedCharaRaceResult[]>().Serialize(ref bytes, offset, value.race_result_list, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[36]);
            offset += formatterResolver.GetFormatterWithVerify<int[]>().Serialize(ref bytes, offset, value.win_saddle_id_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[37]);
            offset += formatterResolver.GetFormatterWithVerify<int[]>().Serialize(ref bytes, offset, value.nickname_id_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[38]);
            offset += formatterResolver.GetFormatterWithVerify<int[]>().Serialize(ref bytes, offset, value.factor_id_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[39]);
            offset += formatterResolver.GetFormatterWithVerify<FactorInfo[]>().Serialize(ref bytes, offset, value.factor_info_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[40]);
            offset += formatterResolver.GetFormatterWithVerify<SuccessionChara[]>().Serialize(ref bytes, offset, value.succession_chara_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[41]);
            offset += formatterResolver.GetFormatterWithVerify<SuccessionHistory[]>().Serialize(ref bytes, offset, value.succession_history_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[42]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.scenario_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[43]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.member_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[44]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.join_type);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[45]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.create_time, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.RoomMatchTrainedChara Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __viewer_id__ = default(long);
            var __trained_chara_id__ = default(int);
            var __owner_viewer_id__ = default(long);
            var __use_type__ = default(int);
            var __card_id__ = default(int);
            var __name__ = default(string);
            var __stamina__ = default(int);
            var __speed__ = default(int);
            var __power__ = default(int);
            var __guts__ = default(int);
            var __wiz__ = default(int);
            var __fans__ = default(int);
            var __rank_score__ = default(int);
            var __rank__ = default(int);
            var __proper_distance_short__ = default(int);
            var __proper_distance_mile__ = default(int);
            var __proper_distance_middle__ = default(int);
            var __proper_distance_long__ = default(int);
            var __proper_running_style_nige__ = default(int);
            var __proper_running_style_senko__ = default(int);
            var __proper_running_style_sashi__ = default(int);
            var __proper_running_style_oikomi__ = default(int);
            var __proper_ground_turf__ = default(int);
            var __proper_ground_dirt__ = default(int);
            var __succession_num__ = default(int);
            var __is_locked__ = default(int);
            var __rarity__ = default(int);
            var __talent_level__ = default(int);
            var __chara_grade__ = default(int);
            var __running_style__ = default(int);
            var __nickname_id__ = default(int);
            var __wins__ = default(int);
            var __skill_array__ = default(SkillData[]);
            var __support_card_list__ = default(TrainedCharaSupportCardList[]);
            var __is_saved__ = default(int);
            var __race_result_list__ = default(TrainedCharaRaceResult[]);
            var __win_saddle_id_array__ = default(int[]);
            var __nickname_id_array__ = default(int[]);
            var __factor_id_array__ = default(int[]);
            var __factor_info_array__ = default(FactorInfo[]);
            var __succession_chara_array__ = default(SuccessionChara[]);
            var __succession_history_array__ = default(SuccessionHistory[]);
            var __scenario_id__ = default(int);
            var __member_id__ = default(int);
            var __join_type__ = default(int);
            var __create_time__ = default(string);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 1:
                        __trained_chara_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __owner_viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 3:
                        __use_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __card_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __stamina__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 7:
                        __speed__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 8:
                        __power__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 9:
                        __guts__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 10:
                        __wiz__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 11:
                        __fans__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 12:
                        __rank_score__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 13:
                        __rank__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 14:
                        __proper_distance_short__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 15:
                        __proper_distance_mile__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 16:
                        __proper_distance_middle__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 17:
                        __proper_distance_long__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 18:
                        __proper_running_style_nige__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 19:
                        __proper_running_style_senko__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 20:
                        __proper_running_style_sashi__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 21:
                        __proper_running_style_oikomi__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 22:
                        __proper_ground_turf__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 23:
                        __proper_ground_dirt__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 24:
                        __succession_num__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 25:
                        __is_locked__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 26:
                        __rarity__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 27:
                        __talent_level__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 28:
                        __chara_grade__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 29:
                        __running_style__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 30:
                        __nickname_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 31:
                        __wins__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 32:
                        __skill_array__ = formatterResolver.GetFormatterWithVerify<SkillData[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 33:
                        __support_card_list__ = formatterResolver.GetFormatterWithVerify<TrainedCharaSupportCardList[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 34:
                        __is_saved__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 35:
                        __race_result_list__ = formatterResolver.GetFormatterWithVerify<TrainedCharaRaceResult[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 36:
                        __win_saddle_id_array__ = formatterResolver.GetFormatterWithVerify<int[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 37:
                        __nickname_id_array__ = formatterResolver.GetFormatterWithVerify<int[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 38:
                        __factor_id_array__ = formatterResolver.GetFormatterWithVerify<int[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 39:
                        __factor_info_array__ = formatterResolver.GetFormatterWithVerify<FactorInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 40:
                        __succession_chara_array__ = formatterResolver.GetFormatterWithVerify<SuccessionChara[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 41:
                        __succession_history_array__ = formatterResolver.GetFormatterWithVerify<SuccessionHistory[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 42:
                        __scenario_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 43:
                        __member_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 44:
                        __join_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 45:
                        __create_time__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new RoomMatchTrainedChara();
            ____result.viewer_id = __viewer_id__;
            ____result.trained_chara_id = __trained_chara_id__;
            ____result.owner_viewer_id = __owner_viewer_id__;
            ____result.use_type = __use_type__;
            ____result.card_id = __card_id__;
            ____result.name = __name__;
            ____result.stamina = __stamina__;
            ____result.speed = __speed__;
            ____result.power = __power__;
            ____result.guts = __guts__;
            ____result.wiz = __wiz__;
            ____result.fans = __fans__;
            ____result.rank_score = __rank_score__;
            ____result.rank = __rank__;
            ____result.proper_distance_short = __proper_distance_short__;
            ____result.proper_distance_mile = __proper_distance_mile__;
            ____result.proper_distance_middle = __proper_distance_middle__;
            ____result.proper_distance_long = __proper_distance_long__;
            ____result.proper_running_style_nige = __proper_running_style_nige__;
            ____result.proper_running_style_senko = __proper_running_style_senko__;
            ____result.proper_running_style_sashi = __proper_running_style_sashi__;
            ____result.proper_running_style_oikomi = __proper_running_style_oikomi__;
            ____result.proper_ground_turf = __proper_ground_turf__;
            ____result.proper_ground_dirt = __proper_ground_dirt__;
            ____result.succession_num = __succession_num__;
            ____result.is_locked = __is_locked__;
            ____result.rarity = __rarity__;
            ____result.talent_level = __talent_level__;
            ____result.chara_grade = __chara_grade__;
            ____result.running_style = __running_style__;
            ____result.nickname_id = __nickname_id__;
            ____result.wins = __wins__;
            ____result.skill_array = __skill_array__;
            ____result.support_card_list = __support_card_list__;
            ____result.is_saved = __is_saved__;
            ____result.race_result_list = __race_result_list__;
            ____result.win_saddle_id_array = __win_saddle_id_array__;
            ____result.nickname_id_array = __nickname_id_array__;
            ____result.factor_id_array = __factor_id_array__;
            ____result.factor_info_array = __factor_info_array__;
            ____result.succession_chara_array = __succession_chara_array__;
            ____result.succession_history_array = __succession_history_array__;
            ____result.scenario_id = __scenario_id__;
            ____result.member_id = __member_id__;
            ____result.join_type = __join_type__;
            ____result.create_time = __create_time__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
