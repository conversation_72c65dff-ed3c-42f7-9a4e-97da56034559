#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class CircleChatPostPartnerResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< CircleChatPostPartnerResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public CircleChatPostPartnerResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"circle_chat_message_array", 0},
                {"circle_chat_user_array", 1},
                {"circle_item_request_array", 2},
                {"circle_item_donate_array", 3},
                {"room_match_info_array", 4},
                {"circle_post_partner_array", 5},
                {"chat_polling_interval", 6},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("circle_chat_message_array"),
                global::System.Text.Encoding.UTF8.GetBytes("circle_chat_user_array"),
                global::System.Text.Encoding.UTF8.GetBytes("circle_item_request_array"),
                global::System.Text.Encoding.UTF8.GetBytes("circle_item_donate_array"),
                global::System.Text.Encoding.UTF8.GetBytes("room_match_info_array"),
                global::System.Text.Encoding.UTF8.GetBytes("circle_post_partner_array"),
                global::System.Text.Encoding.UTF8.GetBytes("chat_polling_interval"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, CircleChatPostPartnerResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 7);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<CircleChatMessage[]>().Serialize(ref bytes, offset, value.circle_chat_message_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<CircleChatUser[]>().Serialize(ref bytes, offset, value.circle_chat_user_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<CircleItemRequest[]>().Serialize(ref bytes, offset, value.circle_item_request_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<CircleItemDonate[]>().Serialize(ref bytes, offset, value.circle_item_donate_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<RoomMatchRoomInfo[]>().Serialize(ref bytes, offset, value.room_match_info_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<CirclePostPartner[]>().Serialize(ref bytes, offset, value.circle_post_partner_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.chat_polling_interval);
            return offset - startOffset;
        }

        public global::Gallop.CircleChatPostPartnerResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __circle_chat_message_array__ = default(CircleChatMessage[]);
            var __circle_chat_user_array__ = default(CircleChatUser[]);
            var __circle_item_request_array__ = default(CircleItemRequest[]);
            var __circle_item_donate_array__ = default(CircleItemDonate[]);
            var __room_match_info_array__ = default(RoomMatchRoomInfo[]);
            var __circle_post_partner_array__ = default(CirclePostPartner[]);
            var __chat_polling_interval__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __circle_chat_message_array__ = formatterResolver.GetFormatterWithVerify<CircleChatMessage[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __circle_chat_user_array__ = formatterResolver.GetFormatterWithVerify<CircleChatUser[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __circle_item_request_array__ = formatterResolver.GetFormatterWithVerify<CircleItemRequest[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __circle_item_donate_array__ = formatterResolver.GetFormatterWithVerify<CircleItemDonate[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __room_match_info_array__ = formatterResolver.GetFormatterWithVerify<RoomMatchRoomInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __circle_post_partner_array__ = formatterResolver.GetFormatterWithVerify<CirclePostPartner[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __chat_polling_interval__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new CircleChatPostPartnerResponse.CommonResponse();
            ____result.circle_chat_message_array = __circle_chat_message_array__;
            ____result.circle_chat_user_array = __circle_chat_user_array__;
            ____result.circle_item_request_array = __circle_item_request_array__;
            ____result.circle_item_donate_array = __circle_item_donate_array__;
            ____result.room_match_info_array = __room_match_info_array__;
            ____result.circle_post_partner_array = __circle_post_partner_array__;
            ____result.chat_polling_interval = __chat_polling_interval__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
