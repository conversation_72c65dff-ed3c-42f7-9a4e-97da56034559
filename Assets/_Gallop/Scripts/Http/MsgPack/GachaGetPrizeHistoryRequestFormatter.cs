#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class GachaGetPrizeHistoryRequestFormatter : global::MessagePack.Formatters.IMessagePackFormatter< GachaGetPrizeHistoryRequest >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public GachaGetPrizeHistoryRequestFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"gacha_id", 0},
                {"viewer_id", 1},
                {"device", 2},
                {"device_id", 3},
                {"device_name", 4},
                {"graphics_device_name", 5},
                {"ip_address", 6},
                {"platform_os_version", 7},
                {"carrier", 8},
                {"keychain", 9},
                {"locale", 10},
                {"dmm_viewer_id", 11},
                {"dmm_onetime_token", 12},
                {"buma_viewer_id", 13},
                {"channel", 14},
                {"buma_client_time", 15},
                {"b_zone", 16},
                {"b_device_type", 17},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("gacha_id"),
                global::System.Text.Encoding.UTF8.GetBytes("viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device"),
                global::System.Text.Encoding.UTF8.GetBytes("device_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("graphics_device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("ip_address"),
                global::System.Text.Encoding.UTF8.GetBytes("platform_os_version"),
                global::System.Text.Encoding.UTF8.GetBytes("carrier"),
                global::System.Text.Encoding.UTF8.GetBytes("keychain"),
                global::System.Text.Encoding.UTF8.GetBytes("locale"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_onetime_token"),
                global::System.Text.Encoding.UTF8.GetBytes("buma_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("channel"),
                global::System.Text.Encoding.UTF8.GetBytes("buma_client_time"),
                global::System.Text.Encoding.UTF8.GetBytes("b_zone"),
                global::System.Text.Encoding.UTF8.GetBytes("b_device_type"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, GachaGetPrizeHistoryRequest value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteMapHeader(ref bytes, offset, 18);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.gacha_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.device);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.graphics_device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.ip_address, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.platform_os_version, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.carrier, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.keychain);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.locale, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_viewer_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[12]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_onetime_token, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[13]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.buma_viewer_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[14]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.channel, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[15]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.buma_client_time, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[16]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.b_zone, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[17]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.b_device_type);
            return offset - startOffset;
        }

        public global::Gallop.GachaGetPrizeHistoryRequest Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __gacha_id__ = default(int);
            var __viewer_id__ = default(long);
            var __device__ = default(int);
            var __device_id__ = default(string);
            var __device_name__ = default(string);
            var __graphics_device_name__ = default(string);
            var __ip_address__ = default(string);
            var __platform_os_version__ = default(string);
            var __carrier__ = default(string);
            var __keychain__ = default(long);
            var __locale__ = default(string);
            var __dmm_viewer_id__ = default(string);
            var __dmm_onetime_token__ = default(string);
            var __buma_viewer_id__ = default(string);
            var __channel__ = default(string);
            var __buma_client_time__ = default(string);
            var __b_zone__ = default(string);
            var __b_device_type__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __gacha_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 2:
                        __device__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __device_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __graphics_device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __ip_address__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 7:
                        __platform_os_version__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __carrier__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 9:
                        __keychain__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 10:
                        __locale__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 11:
                        __dmm_viewer_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 12:
                        __dmm_onetime_token__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 13:
                        __buma_viewer_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 14:
                        __channel__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 15:
                        __buma_client_time__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 16:
                        __b_zone__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 17:
                        __b_device_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new GachaGetPrizeHistoryRequest();
            ____result.gacha_id = __gacha_id__;
            ____result.viewer_id = __viewer_id__;
            ____result.device = __device__;
            ____result.device_id = __device_id__;
            ____result.device_name = __device_name__;
            ____result.graphics_device_name = __graphics_device_name__;
            ____result.ip_address = __ip_address__;
            ____result.platform_os_version = __platform_os_version__;
            ____result.carrier = __carrier__;
            ____result.keychain = __keychain__;
            ____result.locale = __locale__;
            ____result.dmm_viewer_id = __dmm_viewer_id__;
            ____result.dmm_onetime_token = __dmm_onetime_token__;
            ____result.buma_viewer_id = __buma_viewer_id__;
            ____result.channel = __channel__;
            ____result.buma_client_time = __buma_client_time__;
            ____result.b_zone = __b_zone__;
            ____result.b_device_type = __b_device_type__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
