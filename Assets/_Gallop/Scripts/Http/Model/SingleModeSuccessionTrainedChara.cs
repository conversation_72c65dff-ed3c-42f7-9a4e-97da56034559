/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeSuccessionTrainedChara
    {
        public UserInfoAtFriend[] summary_user_info_array;
        public TrainedChara[] succession_trained_chara_array;
    }
}
