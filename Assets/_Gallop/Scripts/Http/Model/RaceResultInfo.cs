/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class RaceResultInfo
    {
        public int race_instance_id;
        public RaceHorseData[] race_horse_data_array;
        public int season;
        public int weather;
        public int ground_condition;
        public int random_seed;
        public string race_scenario;
    }
}
