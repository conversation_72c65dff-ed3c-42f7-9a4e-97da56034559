/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class TeamStadiumOpponent
    {
        public int strength;
        public long opponent_viewer_id;
        public int evaluation_point;
        public UserInfoAtFriend user_info;
        public TeamStadiumTeamData[] team_data_array;
        public TrainedChara[] trained_chara_array;
        public int winning_reward_guarantee_status;
    }
}
