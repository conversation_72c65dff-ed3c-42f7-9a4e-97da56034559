/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// ItemShowExchangeAPIのリクエスト
    /// </summary>
    public sealed class ItemShowExchangeRequest : RequestBase<ItemShowExchangeResponse>
    {

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<ItemShowExchangeResponse> onSuccess,
            Action<ErrorType, int, ItemShowExchangeResponse> onError)
        {
            return new ItemShowExchangeTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// ItemShowExchangeAPIのレスポンス
    /// </summary>
    public sealed class ItemShowExchangeResponse : ResponseCommon
    {
        public class CommonResponse
        {
            public ItemExchangeLimit[] limit_list;
            public int[] disabled_id_array;
            public int[] release_id_array;
            public LimitedShopInfo limited_shop_info;
            public LimitedGoodsInfo[] limited_goods_info_array;
            public int new_aniv_shop_flag;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class ItemShowExchangeTask : IHttpTask
    {
        private byte[] postData;
        private Action<ItemShowExchangeResponse> onSuccess;
        private Action<ErrorType, int, ItemShowExchangeResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "ItemShowExchangeTask" class.
        /// </summary>
        public ItemShowExchangeTask(
           ItemShowExchangeRequest request,
           Action<ItemShowExchangeResponse> onSuccess,
           Action<ErrorType, int, ItemShowExchangeResponse> onError)
        {
            init(request, onSuccess, onError);
        }
        void init(
            ItemShowExchangeRequest request,
           Action<ItemShowExchangeResponse> onSuccess,
           Action<ErrorType, int, ItemShowExchangeResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

    #if BUMA_T
            request.b_zone = "CN";
            request.b_device_type = DeviceHelper.GetPlatform();
    #endif
            postData = MessagePack.MessagePackSerializer.Serialize(request);
            #if BUMA_T
            // 用于服务端校验消息数据一致性 by xuxinwei
            if (HttpConfig_BUMA.BumaGeeVerify)
            {
                using (var md5 = new System.Security.Cryptography.MD5CryptoServiceProvider())
                {
                    var rawOutput = md5.ComputeHash(postData);
                    foreach (byte myByte in rawOutput)
                    {
                        request.buma_viewer_id += myByte.ToString("x2");
                    }
                    postData = MessagePack.MessagePackSerializer.Serialize(request);
                }
            }


            /*
            * 按服务器要求，对每一个请求，在请求头里面添加 RequestId
            * --------------------------------------------
            * changed by wangyudi at 2021/11/19
            */
            SetHeader("BUMA-RID", HttpHelper.GenerateRequestId_BUMA());
            #endif
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/item/show_exchange", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            ItemShowExchangeResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<ItemShowExchangeResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【ItemShowExchangeResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
