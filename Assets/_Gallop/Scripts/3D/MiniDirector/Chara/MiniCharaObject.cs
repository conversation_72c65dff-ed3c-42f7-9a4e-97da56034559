using System.Collections.Generic;
using UnityEngine;
using static Gallop.MiniDirectorDefines;
using Gallop.Model.Component;

namespace Gallop
{
    /// <summary>
    /// ミニキャラ演出：キャラ制御
    /// </summary>
    public class MiniCharaObject : MonoBehaviour, Mini.IMiniCharaTimelineActorObject, INavigationGridAgentModel
    {
        protected const string HIP = CharaNodeName.Hip;
        public const float HEAD_HIGHT = 1.15f;
        protected const float SHADOW_SCALE = 0.4f;
        protected const float MOB_HEAD_SCALE = 0.95f;
        protected const float DEFAULT_HIP_POS_Y = 0.26f;

        private const float MAX_DISTANCE_FIX = 1.5f; //カメラとグリッドの最大距離をそのまま使うと減衰がかかりすぎることがあったので補正する

        protected const int STENCIL_OFFSET = ShaderManager.DEFAULT_CHARA_STENCIL_MASK;
        protected const UnityEngine.Rendering.CompareFunction STENCIL_COMPARE = UnityEngine.Rendering.CompareFunction.GreaterEqual;
        protected const UnityEngine.Rendering.StencilOp STENCIL_OP = UnityEngine.Rendering.StencilOp.Replace;

        public MiniModelController Model => _modelController;
        public NavigationGridAgent Agent => _naviAgent;
        public Mini.MiniCharaTimelineActor TimelineActor => _timelineActor;
        public Transform HeadLocator => _headLocator;
        public MasterMiniBg.MiniBg BgData { get; set; }
        public int BgId => BgData != null ? (int)BgData.Id : 0;
        public IMiniCharaData CharaData { get; private set; } = null;
        public int CharaId { get; private set; }
        public int DressId { get; private set; }
        public bool IsEmpty => CharaData == null;
        public bool IsCurrentClickable => _stateController.IsCurrentClickable();
        public bool IsVisible { get; private set; }
        public Vector3 HipPos { get; private set; }
        public Transform Hip { get; private set; }
        public MiniCharaCommand CurrentCommand => _command;
        private int RenderOrder
        {
            get
            {
#if CYG_DEBUG
                if (_debugRenderOrder > -1)
                    return _debugRenderOrder;
#endif
                return _renderOrder;
            }
        }


        [SerializeField] protected NavigationGridAgent _naviAgent = null;
        [SerializeField] protected Transform _headLocator = null; //キャラのボーンをバインドできればいいが身長差がないとのことなので固定でもOK
        [SerializeField] protected Mini.MiniCharaTimelineActor _timelineActor;
        [SerializeField] private int _debugRenderOrder = -1;

        protected NavigationGrid _grid = null;
        protected MiniModelController _modelController = null;
        protected GameObject _shadow = null;
        protected Renderer[] _shadowRenderArray = null;
        protected Material[][] _shadowMaterialArray = null;
        protected MiniEnvParam _envParam;
        protected int _renderOrder = 0;
        protected int _stencilMask = 0;
        private MiniMotionSetController.OnEndMotion _timelineOnEnd;
        private System.Action _onMoveEnd;

#if CYG_DEBUG
        //デバッグ用に外部公開
        public MiniCharaStateController StateController => _stateController;
#endif
        public bool IsState(MiniDirectorDefines.CharaState state) { return _stateController != null ? _stateController.State == state : false; }

        private MiniCharaObjectManager _charaManager = null;

        //要nullチェック。Viewerなどで起動されるとViewが無いためUIを作成できないことがある。
        private MiniCharaObjUI _charaUI = null;

        private MiniCharaCommand _command = null;
        private MiniCharaCommand _stashCommand = null;
        private MiniCharaStateController _stateController = null;
        private MiniBgPositionLocator _positionLocator;
        private float _currentScale;
        private Transform _boneScaleTarget;
        private float _boneScale;
        private bool _isPlaySe = false; //_sePlaybackが構造体なのでnullかどうかの判別がつかないのでフラグ別途持つ
        private Cute.Cri.AudioPlayback _sePlayback;
        private Transform _tailCtrl;
        private Quaternion _defaultTailRotation;    //TailCtrlにキーが入る時と入らない時があるので、モーションの切り替わりでリセット
        private GraphicSettings.LayerIndex _layer;
        private IModelLoaderAsyncMediator _mediator = null;

        /// <summary>
        /// 初期化
        /// </summary>
        public void OnInitialize(NavigationGrid grid, MiniCharaObjectManager charaManager, MiniCharaObjUI charaUI, MiniEnvParam envParam, GraphicSettings.LayerIndex layer)
        {
            _grid = grid;
            _layer = layer;
            _envParam = envParam;
            _charaManager = charaManager;
            _charaUI = charaUI;

            //ナビ初期化
            _naviAgent.OnInitialize(_grid, this, false);

            //先に影作っとく
            CreateShadow();

            //ロケータ位置初期化
            _headLocator.localPosition = new Vector3(0, HEAD_HIGHT, 0);

            //ステート遷移
            _stateController = new MiniCharaStateController(this);
            if (_charaUI != null)
            {
                _charaUI.SetOnClick(()=>
                {
                    //ステートごとにクリック処理あれば飛ばす
                    _stateController.OnClick();
                    if (CharaData != null && _charaManager.CharaParam.OnClick != null)
                    {
                        //シーン指定のクリック処理
                        _charaManager.CharaParam.OnClick(CharaData);
                    }
                });
                _charaUI.SetOnClickMessage(() =>
                {
                    //吹き出しタップ
                    if (_charaManager.CharaParam.OnClickMessage != null && CharaData != null)
                    {
                        _charaManager.CharaParam.OnClickMessage(CharaData);
                    }
                });
            }
        }

        /// <summary>
        /// 破棄
        /// </summary>
        public void OnFinalize()
        {
            //ナビ破棄処理
            _naviAgent.OnFinalize();
            if (_modelController)
            {
                //以下の警告を抑制するためActiveをfalseにする
                //The Animator Controller() you have used is not valid. Animations will not play
                _modelController.gameObject.SetActive(false);
            }
            Clear();
        }

        /// <summary>
        /// ユーザーデータ更新
        /// </summary>
        /// <param name="userData"></param>
        public void SetCharaData(IMiniCharaData charaData)
        {
            if (charaData == null)
                return;

#if CYG_DEBUG
            //分かりづらいのでデバッグ用に名前にID突っ込む
            gameObject.name = charaData.Name;
#endif

            //UI表示更新
            if (_charaUI != null)
            {
                _charaUI.SetCharaData(charaData);
            }

            //プロパティセット
            CharaData = charaData;
            CharaId = charaData.CharaId;
            DressId = charaData.DressId;
        }

        /// <summary>
        /// 既にモデル生成されてたら削除する
        /// </summary>
        public void Clear()
        {
            CharaData = null;
            _currentScale = 1;
            _onMoveEnd = null;
            _stencilMask = 0;

            //追従解除
            UnSetFollowTarget();

            //ステート強制遷移
            SetCommandNone();

            //非表示
            SetVisible(false);
            _naviAgent.UnSetPosition();

            if (_mediator != null)
            {
                _mediator.CancelLoad();
                _mediator = null;
            }
            //最後にモデル削除
            if (_modelController)
            {
                Destroy(_modelController.gameObject);
                _modelController = null;
            }
        }

        /// <summary>
        /// 表示非表示
        /// </summary>
        /// <param name="visible"></param>
        public void SetVisible(bool visible)
        {
            SetVisibleCharaUI(visible);
            if (_modelController != null)
            {
                var culling = visible ? AnimatorCullingMode.AlwaysAnimate : AnimatorCullingMode.CullCompletely;
                _modelController.SetCullingMode(culling);
                _modelController.SetVisible(visible, true);
                if (visible)
                {
                    _modelController.ResumeAnimation();
                    _modelController.ResumeCySpring();
                    _modelController.ResetCySpring();
                }
                else
                {
                    _modelController.PauseAnimation();
                    _modelController.PauseCySpring();
                }
            }

            SetVisibleShadow(visible);

            if (!visible)
            {
                StopSe();
            }

            if (!IsEmpty)
            {
                _naviAgent.SetEnable(visible);
            }

            IsVisible = visible;
        }
        /// <summary>
        /// UI表示設定。
        /// </summary>
        private void SetVisibleCharaUI(bool visible)
        {
            if (_charaUI)
            {
                _charaUI.SetVisible(visible);
                if (visible)
                {
                    // 更新順によっては一瞬原点に名前が表示されるのを回避するため表示時に１回更新。
                    //UIのヒエラルキー順は0~ (DIRECTOR_CHARA_MAX - 1) なのでその間に収める
                    var uiOrder = Mathf.Max(0, -RenderOrder + (CHARA_MAX - 1));
                    if (_charaUI != null)
                    {
                        _charaUI.OnUpdate(uiOrder);
                    }
                }
            }
        }

        /// <summary>
        /// 何もしない状態にする
        /// </summary>
        public void SetCommandNone()
        {
            _stashCommand = null; 
            SetCommand(new MiniCharaCommand(MiniDirectorDefines.CharaState.None));
        }

        /// <summary>
        /// モデル生成
        /// </summary>
        public void Create(IMiniCharaData charaData)
        {
            //モデル生成
            CharacterBuildInfo buildInfo = CreatePre(charaData);
            var modelObj = ModelLoader.CreateModel(buildInfo);
            CreatePost(modelObj);
        }
        /// <summary>
        /// モデル生成（非同期）
        /// </summary>
        public void CreateAsync(IMiniCharaData charaData)
        {
            //モデル生成
            CharacterBuildInfo buildInfo = CreatePre(charaData);
            _mediator = ModelLoader.CreateModelAsync(buildInfo, (modelObj) => { CreatePost(modelObj); });
        }
        /// <summary>
        /// モデル生成前準備
        /// </summary>
        private CharacterBuildInfo CreatePre(IMiniCharaData charaData)
        {
            //いるなら削除
            if (_mediator != null)
            {
                _mediator.CancelLoad();
                _mediator = null;
            }
            if (_modelController != null)
            {
                Destroy(_modelController.gameObject);
                _modelController = null;
            }

            //ユーザーデータセット
            SetCharaData(charaData);

            //衣装、キャラ固有以外は背景に設定された衣装を使う
            var dressId = charaData.DressId;
            if (dressId == (int)ModelLoader.DressID.UniformSummer
                || dressId == (int)ModelLoader.DressID.UniformWinter)
            {
                if (BgData != null)
                {
                    dressId = BgData.DressId;
                    //もし衣装IDが制服だった場合は季節に合わせた衣装に変更する。それ以外はデータがない可能性があるので何もしない
                    if (dressId == (int) ModelLoader.DressID.UniformSummer || dressId == (int) ModelLoader.DressID.UniformWinter)
                    {
                        dressId = MasterDataManager.Instance.masterDressData.ConvertClothIdBySeason(TimeUtil.GetDateTimeJst(), dressId);
                    }
                }
            }

            //モデル生成情報を返す
            return new CharacterBuildInfo(charaData.CharaId, dressId, ModelLoader.ControllerType.Mini);
        }
        /// <summary>
        /// モデル生成後の設定
        /// </summary>
        private void CreatePost(GameObject modelObj)
        {
            _modelController = modelObj.GetComponent<MiniModelController>();
            _modelController.SetEnableFacialLocatorControll(true);  //ミニ演出のモーションのフェイシャルはすべてモーションで制御される
            _modelController.transform.SetParent(transform);
            _modelController.transform.localPosition = Math.VECTOR3_ZERO;
            _modelController.transform.localRotation = Math.QUATERNION_IDENTITY;
            _modelController.transform.localScale = Math.VECTOR3_ONE;
            _modelController.SetCullingMode(AnimatorCullingMode.AlwaysAnimate);
            _modelController.SetVisible(IsVisible);
            _modelController.SetLayerIndex(_layer);
            _modelController.UpdateRenderQueue(RenderOrder, true);
            _currentScale = 1;

            //Tail取得
            _tailCtrl = _modelController.FindTransform(CharaNodeName.TAIL_CTRL);
            if (_tailCtrl != null)
            {
                _defaultTailRotation = _tailCtrl.localRotation;
            }

            //Hipに位置のアニメーションが入る
            Hip = _modelController.FindTransform(HIP);
            HipPos = Hip.position;

            //色反映
            ApplyEnv(_envParam);

            //影表示
            SetVisibleShadow(IsVisible);

            _modelController.ResetCySpring();

            //ステンシル設定
            UpdateStencil(true);

            //UI表示
            SetVisibleCharaUI(IsVisible);

            _naviAgent.SetEnable(true);

            if (_command != null)
            {
                //今命令されてるコマンドを続行させる
                SetCommand(_command);
            }

            //タイムラインコールバックの初期化
            _timelineOnEnd = null;

            _mediator = null;
        }

        #region 更新

        /// <summary>
        /// 毎フレームMiniCharaObjManagerから計算して渡される
        /// </summary>
        /// <param name="renderOrder"></param>
        public void SetRenderOrder(int renderOrder)
        {
            _renderOrder = renderOrder;
        }

        /// <summary>
        /// Update
        /// </summary>
        public void OnUpdate()
        {
            //描画順更新
            if (_modelController != null)
            {
                _modelController.UpdateRenderQueue(RenderOrder, true);
            }

            //ステンシル値上書き
            UpdateStencil(false);

            //UIのヒエラルキー順は0~ (DIRECTOR_CHARA_MAX - 1) なのでその間に収める
            var uiOrder = Mathf.Max(0, -RenderOrder + (CHARA_MAX - 1));
            if (_charaUI != null)
            {
                _charaUI.OnUpdate(uiOrder);
                if( CharaData != null )
                {
                    _charaUI.SetVisibleMessage(CharaData.IsVisibleMessage);
                }
            }

            //ステート更新
            _stateController.Update();

            //スケールが変わってたらCySpringをリセット
            UpdateCyspringScale();

            //タイムラインで位置移動指定されてるなら終了チェック
            if(_onMoveEnd != null && _naviAgent.CurrentState == NavigationGridAgent.State.Idle)
            {
                _onMoveEnd();
                _onMoveEnd = null;
            }
        }

        //ステンシル上書き
        //ステンシル設定の初期化も行うなら引数true
        private void UpdateStencil(bool init)
        {
            if (Model == null)
                return;

            //一番手前(描画順最小)＝ステンシル値最大となるようにする
            _stencilMask = MiniModelController.RENDER_ORDER_MAX - RenderOrder;
            _stencilMask += STENCIL_OFFSET;

            if (init)
                Model.SetStencilSetting(_stencilMask, STENCIL_COMPARE, STENCIL_OP);
            else
                Model.SetStencilMask(_stencilMask);

            //小物のステンシル設定がReplaceになってるのでバッファを保持させるようにする
            Model.SetStencilSetttingProp(ShaderManager.DEFAULT_CHARA_STENCIL_MASK, UnityEngine.Rendering.CompareFunction.Always, UnityEngine.Rendering.StencilOp.Keep);
        }

        /// <summary>
        /// LateUpdate
        /// </summary>
        public void OnLateUpdate()
        {
            UpdateHipPos();

            if (_boneScaleTarget)
            {
                _boneScaleTarget.localScale = Math.VECTOR3_ONE * _boneScale;
            }
        }

        /// <summary>
        /// 尻尾の姿勢をリセット
        /// </summary>
        public void ResetTailCtrl()
        {
            if (_tailCtrl == null)
                return;

            _tailCtrl.localRotation = _defaultTailRotation;
        }

        /// <summary>
        /// Hipの位置にオブジェクトを追従
        /// </summary>
        protected void UpdateHipPos()
        {
            if (Hip == null)
                return;

            HipPos = Hip.position;

            //UIロケータ
            //頭の高さはキャラからの相対で都度計算する
            var headY = HEAD_HIGHT + (HipPos.y - DEFAULT_HIP_POS_Y);
            _headLocator.position = new Vector3(HipPos.x, headY, HipPos.z);
            //丸影
            _shadow.transform.position = new Vector3(HipPos.x, _shadow.transform.position.y, HipPos.z);
        }

        /// <summary>
        /// スケールが変わったらCySpringをリセット
        /// </summary>
        protected void UpdateCyspringScale()
        {
            if (Model == null || _positionLocator == null)
                return;

            //縦長とか横長じゃない前提なのでscaleのxで指定
            var targetScale = _positionLocator.TargetLocator.localScale.x;
            if (Math.IsFloatEqualLight(targetScale, _currentScale))
                return;

            _currentScale = targetScale;

            //モブキャラ(ライブの観客とか)で頭部だけスケールかけたいみたいな話があるのでその時は固定倍率をスケールにかける
            for (int p = 0; p < (int)CySpringController.Parts.NumParts; p++)
            {
                Model.ResetCyspringScale((CySpringController.Parts)p);
            }
        }

        /// <summary>
        /// タップエフェクトのリアクション表示を変更するか
        /// </summary>
        /// <returns></returns>
        public bool IsTouchEffectReaction()
        {
            // タップエフェクトを変えるかは、「その位置でそのまま指を話した場合に処理対象があるか」で決める
            return _stateController.IsCurrentClickable() ||
                   (CharaData != null && _charaManager.CharaParam.OnClick != null);
        }

        #endregion

        #region 環境設定

        /// <summary>
        /// 環境設定更新
        /// </summary>
        public void ApplyEnv(MiniEnvParam envParam)
        {
            _envParam = envParam;
            SetCharaColor(_envParam.LightProbeColor);
            SetShadowColor(_envParam.CharaShadowColor);
        }

        public void SetCharaColor(Color color)
        {
            if (_modelController == null)
                return;

            _modelController.MaterialBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CharaColor), color);
            _modelController.UpdateMaterialPropertyBlock();
        }

        public void SetShadowColor(Color color)
        {
            if (_shadowRenderArray == null)
                return;

            foreach (var r in _shadowRenderArray)
            {
                foreach (var m in r.materials)
                {
                    m.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ShadowColor), color);
                }
            }
        }

        #endregion

        #region 影

        public void SetVisibleShadow(bool visible)
        {
            if (_shadow == null)
                return;
            
            _shadow.SetActive(visible);
        }

        protected void CreateShadow()
        {
            if (_shadow != null)
                return;

            var prefab = ResourceManager.LoadOnScene<GameObject>(ResourcePath.MINI_CHARA_SHADOW);
            if (prefab == null)
                return;

            _shadow = Instantiate(prefab, transform);
            _shadow.transform.localPosition = Math.VECTOR3_ZERO;
            _shadow.transform.localRotation = Math.QUATERNION_IDENTITY;
            _shadow.transform.localScale = Math.VECTOR3_ONE * SHADOW_SCALE;
            _shadow.SetLayerRecursively(GraphicSettings.GetLayer(_layer));
            _shadowRenderArray = _shadow.GetComponentsInChildren<Renderer>();

#if UNITY_EDITOR
            //Material差分でないように
            foreach (var r in _shadowRenderArray)
            {
                GallopUtil.ReplaceMaterialCopy(r);
            }
#endif

            //共通のシェーダーだと鏡に映らないので専用のミニキャラ影シェーダーに差し替える
            var shader = ShaderManager.GetShader(ShaderManager.ShaderKinds.MiniCharaShadow);
            _shadowMaterialArray = new Material[_shadowRenderArray.Length][];
            var index = 0;
            foreach (var r in _shadowRenderArray)
            {
                //materialsはコピーなので破棄する必要がある
                var materials = r.materials;
                foreach (var m in materials)
                {
                    m.shader = shader;
                }
                _shadowMaterialArray[index] = materials;
                index++;
            }
        }

        #endregion

        #region キャラコマンド

        /// <summary>
        /// コマンド実行
        /// </summary>
        public void SetCommand(MiniCharaCommand command)
        {
            _command = command;
            _stateController.ChangeState(command);
        }

        /// <summary>
        /// 現在実行中のコマンドに引数のコマンドを割り込んで実行
        /// ** 現状タッチなどで割り込みが発生し得るステートは限られてるためinterfaceを使ってるが、
        /// ** 全てのステートであり得るなら基底化したほうがいい。
        /// </summary>
        /// <param name="command"></param>
        public void InterruptCommand(MiniCharaCommand command)
        {
            _stateController.Pause();
            _stashCommand = _command;
            SetCommand(command);
        }

        /// <summary>
        /// 現在ステート終了時に呼ばれる
        /// </summary>
        public void NextState()
        {
            if (_stateController.IsStash)
            {
                //一時停止してたなら戻す
                _stateController.Resume();
                _command = _stashCommand;
                return;
            }

            _charaManager.RequestCommand(this);
        }

        #endregion

        #region たずな

        /// <summary>
        /// たずな用生成関数
        /// </summary>
        public void CreateTazuna()
        {
            var tazunaData = new MiniCharaData("", GameDefine.TAZUNA_CHARA_ID, GameDefine.TAZUNA_DRESS_ID);
            Create(tazunaData);
            _naviAgent.SetPosition(Vector2Int.zero);
        }

        #endregion

        #region ミラー

        /// <summary>
        /// 鏡用に描画順を反転する
        /// </summary>
        public void InverseRenderOrder()
        {
            _renderOrder = MiniModelController.RENDER_ORDER_MAX - RenderOrder;
            if (_modelController != null)
            {
                _modelController.UpdateRenderQueue(RenderOrder);
            }
        }

        #endregion

        #region 追従指定

        /// <summary>
        /// 追従指定
        /// ** 追従自体はナビ側で行う(位置、回転の決定はナビで行いたい)
        /// ** スケールの流し込みをModelControllerに対しする必要があるため入り口はここ
        /// </summary>
        /// <param name="target"></param>
        public void SetFollowTarget(MiniBgPositionLocator locator)
        {
            _naviAgent.SetFollowTarget(locator.TargetLocator);
            _positionLocator = locator;
        }

        /// <summary>
        /// 追従解除
        /// </summary>
        public void UnSetFollowTarget()
        {
            if(_positionLocator == null)
            {
                return;
            }

            _naviAgent.UnSetFollowTarget();
            _positionLocator = null;
        }

        #endregion

        #region IMiniCharaTimelineActorObject

        //モーション再生
        public void PlayMotion(string motionName, string nextMotionName = "")
        {
            if (Model == null)
                return;

            //同じモーションが連続する場合を許容するため、Forceオプションで制御する、ただしアイドルはいい
            var currentLabel = Model.GetCurrentMotionLabel();
            var force = currentLabel == motionName && currentLabel != MiniMotionSetController.IDLE_LABLE;
            Model.PlayMotion(motionName, NextLabel: nextMotionName, isForce: force);
        }

        //アイドル再生
        public void PlayIdle()
        {
            if (Model == null)
                return;

            Model.PlayMotion(MiniCharaStateIdle.SUDACHI);
        }
        
        //モーション終了コールバック登録
        public void AddOnMotionEnd(System.Action callback)
        {
            if (Model == null)
                return;

            if (_timelineOnEnd != null)
            {
                Debug.LogWarning("前回登録されたコールバックが残りっぱなし、RemoveOnMotionEndCallbackを呼ぶこと");
                return;
            }

            _timelineOnEnd = motionset => callback();
            Model.RegisterEndMotionDelegate(_timelineOnEnd);
        }

        //モーション終了コールバック破棄
        public void RemoveOnMotionEnd()
        {
            if (Model == null || _timelineOnEnd == null)
                return;

            Model.UnRegistEndMotionDelegate(_timelineOnEnd);
            _timelineOnEnd = null;
        }

        //向き指定
        public void SetDirection(Mini.SetDirection.Direction direction)
        {
            //本当はintに一回パースしてDirectorの向きに変えちゃっても動く
            //けど密な作りになるのを避けるためいちいちswitchで変換してる
            var directorDirection = GridDirection.None;
            switch(direction)
            {
                case Mini.SetDirection.Direction.Right: directorDirection = GridDirection.Right; break;
                case Mini.SetDirection.Direction.RightBack: directorDirection = GridDirection.RightBack; break;
                case Mini.SetDirection.Direction.RightForward: directorDirection = GridDirection.RightForward; break;
                case Mini.SetDirection.Direction.Left: directorDirection = GridDirection.Left; break;
                case Mini.SetDirection.Direction.LeftBack: directorDirection = GridDirection.LeftBack; break;
                case Mini.SetDirection.Direction.LeftForward: directorDirection = GridDirection.LeftForward; break;
                case Mini.SetDirection.Direction.Back: directorDirection = GridDirection.Back; break;
                case Mini.SetDirection.Direction.Forward: directorDirection = GridDirection.Forward; break;
            }

            _naviAgent.SetDirection(directorDirection);
        }

        //位置移動
        public void MoveGridPosition(Vector2Int start, Vector2Int end, float time, System.Action onEnd)
        {
            if (_onMoveEnd != null)
            {
                Debug.LogWarning("前回登録されたコールバックが残りっぱなし、CancelMoveGridPositionを呼ぶこと");
                return;
            }
            var route = _grid.FindRoute(start, end, true);  //開始-終了をループする場合、終了地点が通れないことにならないようにforce
            _naviAgent.SetPosition(start);
            _naviAgent.Move(route, time);
            _onMoveEnd = onEnd;
        }

        //位置移動キャンセル
        public void CancelMoveGridPosition()
        {
            _naviAgent.CompleteMoveForce();
            _onMoveEnd = null;
        }

        //グリッド位置指定
        public void SetGridPosition(Vector2Int position)
        {
            _naviAgent.SetPosition(position);
        }

        //ポジションアニメの再生
        public void PlayPositionMotion()
        {
            if (_command == null ||
               _command.ObjectAnimInfo == null ||
               _command.ObjectAnimInfo.PositionLocator == null)
                return;

            _command.ObjectAnimInfo.PositionLocator.PlayAnim(_command.ObjectAnimInfo.PositionAnim);
        }

        //ポジションアニメの停止
        public void StopPositionMotion()
        {
            if (_command == null ||
             _command.ObjectAnimInfo == null ||
             _command.ObjectAnimInfo.PositionLocator == null)
                return;

            _command.ObjectAnimInfo.PositionLocator.StopAnim();
        }

        #endregion

        #region INavigationGirAgentModel

        public int GetStencilMask()
        {
            return _stencilMask;
        }

        #endregion

        #region SE

        /// <summary>
        /// キャラに紐づくSE再生
        /// </summary>
        /// <param name="cueSheet"></param>
        /// <param name="cueName"></param>
        public void PlaySe(string cueSheet, string cueName)
        {
            if (IsVisible == false)
                return;

            StopSe();
            _sePlayback = AudioManager.Instance.PlaySe(cueSheet, cueName,
                trans3d: transform,
                minDistance: MiniCameraController.MIN_DISTANCE_TO_GRID,
                maxDistance: MiniCameraController.MAX_DISTANCE_TO_GRID * MAX_DISTANCE_FIX
            );
            _isPlaySe = true;
        }

        private void StopSe()
        {
            if(_isPlaySe)
            {
                AudioManager.Instance.StopSe(_sePlayback);
            }
            _isPlaySe = false;
        }

        #endregion

        /// <summary>
        /// ボーンスケール設定
        /// </summary>
        /// <param name="bone"></param>
        /// <param name="scale"></param>
        public void SetBoneScale(string bone, float scale)
        {
            if (Model != null)
            {
                _boneScaleTarget = Model.FindTransform(bone);
            }
            _boneScale = scale;
        }

        private void OnDestroy()
        {
            if(_shadowMaterialArray != null)
            {
                foreach(var materialArray in _shadowMaterialArray)
                {
                    foreach (var material in materialArray)
                    {
                        if (material == null)
                            continue;
                        Destroy(material);
                    }
                }
                _shadowMaterialArray = null;
            }
        }

    }
}