using System;
using System.Linq;
using DG.Tweening;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    public class TrainingMenuImageEffect : GallopCharacterImageEffect
    {
        public void InitializeResources()
        {
            ColorCorrectionParam.InitializeResources();
        }
    }

#if CYG_DEBUG && UNITY_EDITOR
    [CustomEditor(typeof(TrainingMenuImageEffect))]
    public class TrainingMenuImageEffectEditor : Editor
    {
        private TrainingMenuImageEffect _imageEffect = null;
        private TrainingMenuEnvParam _singleModeTrainingMenuEnvParam = null;
        private bool _isDisplayTrainingmenu => SceneManager.HasInstance() && SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.SingleMode;
        private SingleModeMainViewController _viewController => SceneManager.Instance.GetCurrentViewController<SingleModeMainViewController>();

        // ファイル保存・読み込み
        public static int SingleModeMenuBgId = 100;
        public static int SingleModeMenuBgIdSub = 0;
        public static int SingleModeMenuBgIdForLoad = 0;
        public static int SingleModeMenuBgIdForLoadSub = 0;

        // 背景ID選択
        public static bool IsSingleModeTrainingTop = true;
        public static int SingleModeTrainingMonth;
        public static int SingleModeRemainTurnNum;
        private bool _isGuiFoldedMonthSelector = false;
        private int _bgIdPopupIndexTop = 0;
        
        //背景選択用。背景IDとSubIdの組み合わせで決まる
        private int[] _masterIdPopupValueTopArray; // MasterSingleModeTopのIdか定数
        private string[] _bgIdPopupLabelTopArray;
        
        private int _bgIdPopupIndexMenu = 0;
        private readonly (int bgId, int subId)[] _bgIdPopupValueMenu = TrainingMenuEnvParamHelper.GetBgIdEnumerableForTrainingMenu().ToArray();
        private readonly string[] _bgIdPopupLabelMenuArray = TrainingMenuEnvParamHelper.GetBgIdEnumerableForTrainingMenu()
            .Select(id => $"{id.bgId:D4}_{id.subId:D5}")
            .ToArray();

        // CardId選択
        private static MasterCardData.CardData _singleModeCard = null;
        private readonly MasterCardData.CardData[] _cardIdPopupValueArray =
            MasterDataManager.Instance.masterCardData.dictionary.Values
            .OrderBy(cardData => cardData.Id)
            .ToArray();
        private readonly string[] _cardIdPopupLabelArray = MasterDataManager.Instance.masterCardData.dictionary.Values
            .OrderBy(cardData => cardData.Id)
            .Select(cardData => $"{cardData.Id.ToString()} ({cardData.Charaname})")
            .ToArray();

        private void OnEnable()
        {
            _imageEffect = target as TrainingMenuImageEffect;

            var masterSingleModeTopBgArray = TrainingMenuEnvParamHelper.GetTrainingTopBgMasterArray();
            var bgCount = masterSingleModeTopBgArray.Length;
            _masterIdPopupValueTopArray = new int[bgCount + 1];
            _bgIdPopupLabelTopArray = new string[bgCount + 1];
            for (var i = 0; i < bgCount; ++i)
            {
                var masterSingleMode = masterSingleModeTopBgArray[i];
                _masterIdPopupValueTopArray[i] = masterSingleMode.Id;
                _bgIdPopupLabelTopArray[i] = TextUtil.Format("BgID:{0}  SubId:{1}", masterSingleMode.BgId.ToString(), masterSingleMode.BgSubId.ToString());
            }
            //一番後ろに特殊背景を入れる
            _masterIdPopupValueTopArray[bgCount] = TrainingMenuEnvParamHelper.SPECIAL_ID_SINGLEMODEPRIORITYENTRY_MAIN;
            _bgIdPopupLabelTopArray[bgCount] =  TextUtil.Format("BgID:{0}" , TrainingMenuEnvParamHelper.SPECIAL_ID_SINGLEMODEPRIORITYENTRY_MAIN.ToString());
        }

        public override void OnInspectorGUI()
        {
            // ImageEffectプロパティ
            _imageEffect.OnInspectorGUI();

            // トレーニング画面以外では環境編集GUIは非表示
            if (!_isDisplayTrainingmenu) return;

            // メニュー
            GUIUtil.Separator();
            EditorGUILayout.LabelField("トレーニング画面用設定", EditorStyles.boldLabel);

            // 背景選択
            OnInspectorGUI_BgSelect();

            // カード（キャラ）選択
            OnInspectorGUI_CardSelect();

            // 保存・読み込み
            GUIUtil.Separator();
            OnInspectorGUI_SaveAndLoadMenu();
        }

        private void OnInspectorGUI_TopBackground()
        {
            EditorGUILayout.LabelField("トップ画面向け設定", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(!IsSingleModeTrainingTop);
            {
                SingleModeTrainingMonth = EditorGUILayout.IntField("月", SingleModeTrainingMonth);
                SingleModeRemainTurnNum = EditorGUILayout.IntField("残りターン数", SingleModeRemainTurnNum);
                if (GUILayout.Button("Top背景読み込み"))
                {
                    _viewController?.SetTopBg(SingleModeTrainingMonth);
                    _viewController?.SetTrainingImageEffectByMonth(SingleModeTrainingMonth, SingleModeRemainTurnNum);
                }
            }
            EditorGUI.EndDisabledGroup();
        }

        private void OnInspectorGUI_CardSelect()
        {
            EditorGUILayout.BeginHorizontal();
            var oldIndex = Mathf.Clamp(Array.IndexOf(_cardIdPopupValueArray, _singleModeCard), 0, _cardIdPopupValueArray.Length);
            var newIndex = EditorGUILayout.Popup("CardId", oldIndex, _cardIdPopupLabelArray);
            if (oldIndex != newIndex)
            {
                _singleModeCard = _cardIdPopupValueArray[newIndex];
            }
            if (GUILayout.Button("読み込み"))
            {
                _viewController?.ReplaceModelForEditor(_singleModeCard);
            }
            EditorGUILayout.EndHorizontal();
        }

        private void OnInspectorGUI_BgSelect()
        {
            // local func : InspectorGUIのタイミングだとUnityEngine.Screenのサイズが別ウインドウのサイズになるので背景サイズがずれる症状が発生。GameViewのアップデートタイミングで事項してもらう
            void DelayedCall(TweenCallback onAction)
            {
                DOVirtual.DelayedCall(0.01f, onAction);
            }
            
            if (IsSingleModeTrainingTop)
            {
                var old = _bgIdPopupIndexTop;
                _bgIdPopupIndexTop = EditorGUILayout.Popup("トップ画面背景選択", _bgIdPopupIndexTop, _bgIdPopupLabelTopArray);
                if (_bgIdPopupIndexTop != old)
                {
                    var topId = _masterIdPopupValueTopArray[_bgIdPopupIndexTop];
                    if (topId == TrainingMenuEnvParamHelper.SPECIAL_ID_SINGLEMODEPRIORITYENTRY_MAIN)
                    {
                        // 優先出走権の背景を表示するためのパラメータ
                        const int REMAINING_TURN_NUM = SingleModeMainViewController.RACE_ENTRY_TURN_NUM; // 残りターン数はレース出場時の物
                        const int MONTH = 1; // 月は何でもいい
                        DelayedCall(() =>
                        {
                            _viewController?.SetTopBg(MONTH);
                            _viewController?.SetTrainingImageEffectByMonth(MONTH, REMAINING_TURN_NUM);
                        });
                    }
                    else
                    {
                        var masterSingleModeTopBg = MasterDataManager.Instance.masterSingleModeTopBg.Get(topId);
                        var bgId = masterSingleModeTopBg.BgId;
                        var subId = masterSingleModeTopBg.BgSubId;
                        DelayedCall(() =>
                        {
                            _viewController?.SetBg(bgId, subId, true);
                            _viewController?.SetTrainingImageEffect(bgId, subId);
                        });
                    }
                }
            }
            else
            {
                var old = _bgIdPopupIndexMenu;
                _bgIdPopupIndexMenu = EditorGUILayout.Popup("選択画面背景選択", _bgIdPopupIndexMenu, _bgIdPopupLabelMenuArray);
                if (_bgIdPopupIndexMenu != old)
                {
                    var (bgId, subId) = _bgIdPopupValueMenu[_bgIdPopupIndexMenu];
                    DelayedCall(() =>
                    {
                        _viewController?.SetBg(bgId, subId, true);
                        _viewController?.SetTrainingImageEffect(bgId, subId);
                    });
                }
            }

            _isGuiFoldedMonthSelector = EditorGUILayout.Foldout(_isGuiFoldedMonthSelector, "月で選択");
            if (_isGuiFoldedMonthSelector)
            {
                GUIUtil.Separator();
                OnInspectorGUI_TopBackground();
            }
        }

        private void OnInspectorGUI_SaveAndLoadMenu()
        {
            EditorGUILayout.LabelField("保存・読込", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            SingleModeMenuBgId = EditorGUILayout.IntField("書き込み 背景ID", SingleModeMenuBgId);
            SingleModeMenuBgIdSub = EditorGUILayout.IntField(SingleModeMenuBgIdSub);
            if (GUILayout.Button("設定を保存"))
            {
                _singleModeTrainingMenuEnvParam = _singleModeTrainingMenuEnvParam
                    ? _singleModeTrainingMenuEnvParam
                    : CreateInstance<TrainingMenuEnvParam>();
                _singleModeTrainingMenuEnvParam.CopyFrom(_imageEffect);
                TrainingMenuEnvParamHelper.SaveEnv(_singleModeTrainingMenuEnvParam, SingleModeMenuBgId, SingleModeMenuBgIdSub);
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginHorizontal();
            SingleModeMenuBgIdForLoad = EditorGUILayout.IntField("読み込み 背景ID", SingleModeMenuBgIdForLoad);
            SingleModeMenuBgIdForLoadSub = EditorGUILayout.IntField(SingleModeMenuBgIdForLoadSub);
            if (GUILayout.Button("設定を読み込む"))
            {
                var backupMain = SingleModeMenuBgId;
                var backupSub = SingleModeMenuBgIdSub;
                _viewController?.SetTrainingImageEffect(SingleModeMenuBgIdForLoad, SingleModeMenuBgIdForLoadSub);
                SingleModeMenuBgId = backupMain;
                SingleModeMenuBgIdSub = backupSub;
            }
            EditorGUILayout.EndHorizontal();
        }
    }
#endif
}
