#if BUMA_T && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Gallop;
using UnityEngine;

public partial class LocalizePrefab_BUMA
{
    public static string jsonRoot => Application.dataPath.Replace("Assets","/BUMA/文本/");
    #region 函数

    public static string GetRoute(ICustomTextComponent text)
    {
        if (text is TextCommon) return GetRoute((text as TextCommon).transform);
        else if (text is TextMeshProUguiCommon) return GetRoute((text as TextMeshProUguiCommon).transform);
        return string.Empty;
    }

    // 获取节点在预制上的路径
    public static string GetRoute(Transform transform)
    {
        var names = new Queue<string>();
        names.Enqueue(transform.name);
        var parent = transform.parent;
        while (parent != null)
        {
            names.Enqueue(parent.name);
            parent = parent.parent;
        }

        var result = string.Empty;
        var cnt = names.Count;
        for (var i = 0; i < cnt - 1; i++)
        {
            result = '-' + names.Dequeue() + result;
        }

        if (result.Length > 0)
        {
            return result.Substring(1);
        }

        return result;
    }

    static readonly Regex reg = new Regex("^[\x00-\x40\x5b-\x60\x7b-\x7f\\s]*$"); //不包含英文，全为Ascii范围，忽略空白符
    public static bool CheckText(ICustomTextComponent textComponent)
    {
        if (string.IsNullOrEmpty(textComponent.text.Trim())) return false;
        
        if (textComponent.TextId != TextId.None) return false;
        
        if (!string.IsNullOrEmpty(textComponent.TextIdString.Trim()) &&
            textComponent.TextIdString != EnumUtil.GetEnumDisplayName<TextId>(TextId.None)
            && LocalizeExtention.TextDic.ContainsKey(textComponent.TextIdString)) return false;
        
        if (reg.IsMatch(textComponent.text)) return false;
        
        return true;
    }
    #endregion

    #region view
    
    public class TextAsset
    {
        public List<TextItem> items = new List<TextItem>();
    } 
    
   
    public class TextItem
    {
        public string key;
        public string source;
        public string dest;
        public string version;
        public string modifyTime;
        public Dictionary<string, string> extra; // 存放辅助信息，比如 Story 中的选项跳转ID
        
        public ICustomTextComponent textComponent { get; set; }
        public GameObject prefab { get; set; }
    }

 
    
    
    [Serializable]
    public class PrefabTextAssetJson
    {
        public int type = 6;
        public List<Item> items = new List<Item>();
    }

    [Serializable]
    public class PrefabMapping
    {
        public List<MappingItem> prefabs = new List<MappingItem>();
    }
    
    [Serializable]
    public class Item
    {
        public string route;
        public TextTuple value = new TextTuple();
    
        public Item(string route, TextTuple value)
        {
            this.route = route;
            this.value = value;
        }
    
        public Item()
        {
        }
    }
    
    [Serializable]
    public class MappingItem
    {
        public string path;
    }
    
    [Serializable]
    public class TextTuple
    {
        public string source;
        public string dest;
        public string key;
        public string version;

        public TextTuple(string source, string version)
        {
            this.source = source;
            this.version = version;
        }

        public TextTuple()
        {
        }
    }
    
    #endregion
}
#endif