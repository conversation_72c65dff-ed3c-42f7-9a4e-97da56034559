#if CYG_DEBUG
using System.Linq;
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// エディタ拡張：お問い合わせ用に各種レースモードのレースを復元する：レジェンド。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class EditorRestoreRaceInfoFromResponse : EditorWindow
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        [System.Serializable]
        public sealed class LegendRaceRaceEntryResponseDummy
        {
            public class CommonResponse
            {
                public RaceHorseDataDummy[] race_horse_data_array;
                public int season;
                public int weather;
                public int ground_condition;
                public int random_seed;
                public int race_instance_id;
                public int state;
                public int trained_chara_id;
            }
            public CommonResponse data;
        }
        
        [System.Serializable]
        public sealed class LegendRaceRaceStartResponseDummy
        {
            public class CommonResponse
            {
                public string race_scenario;
                public int state;
                public int running_style;
            }
            public CommonResponse data;
        }

     
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>BigQueryからDLしたjsonをロードしたもの。</summary>
        private BigQueryJsonRoot _legendRaceEntryJsonRoot;
        private BigQueryJsonRoot _legendRaceStartJsonRoot;
        /// <summary>レジェンド１レース。</summary>
        private LegendRaceRaceEntryResponseDummy.CommonResponse _selectedLegendRaceEntryResponse;
        private LegendRaceRaceStartResponseDummy.CommonResponse _selectedLegendRaceStartResponse;
        /// <summary>現在選択中のレジェンドレース。_legendRaceEntryJsonRoot.Array/_legendRaceStartJsonRoot.Arrayのインデックス。</summary>
        private int _selectedLegendRaceIndex = -1;
        /// <summary>スクロール値。</summary>
        private Vector2 _legendScroll;

        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        #region Legend
        private void OutputLegendRaceInfoJson()
        {
            CreateLegendRaceInfo(_selectedLegendRaceEntryResponse, _selectedLegendRaceStartResponse);
            RaceSimulatorTool.OutputLoadRaceInfoJson();
        }

        private void SelectLegendRaceInfo(LegendRaceRaceEntryResponseDummy.CommonResponse raceEntry, LegendRaceRaceStartResponseDummy.CommonResponse raceStart, int index)
        {
            _selectedLegendRaceEntryResponse = raceEntry;
            _selectedLegendRaceStartResponse = raceStart;
            _selectedLegendRaceIndex = index;
        }   
        
        private void CreateLegendRaceInfo(LegendRaceRaceEntryResponseDummy.CommonResponse raceEntry, LegendRaceRaceStartResponseDummy.CommonResponse raceStart)
        {
            var masterDataManager = MasterDataManager.Instance;
            if (masterDataManager == null)
            {
                Debug.LogError("MasterDataManager is null!");
                return;
            }
            
            // LoadRaceInfo/RaceInfo生成。
            var horseDataArray = raceEntry.race_horse_data_array;
            if (_viewerId != VIEWER_ID_NULL)
            {
                ReplaceViewerId(horseDataArray, _viewerId, Certification.ViewerId);
            }

            // パドックで変更されたかもしれない走法がLegendRaceRaceStartResponse.running_styleで返されているので、それをユーザーのRaceHorseDataに反映する。
            if (_viewerId != VIEWER_ID_NULL)
            {
                var userHorse = horseDataArray.FirstOrDefault(x => x.viewer_id == Certification.ViewerId);
                if (userHorse != null)
                {
                    userHorse.running_style = raceStart.running_style;
                }
                else
                {
                    EditorUtility.DisplayDialog("走法反映失敗", $"ViewerId({_viewerId})がRaceHorseDataに見つかりません。\nViewerIdの入力が間違っているか、読み込んだrace_entry / race_sartのjsonが間違っています", "ok");
                }
            }
            else
            {
                EditorUtility.DisplayDialog("ViewerId未入力", $"ViewerIdを入力してください。", "ok");
            }

            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                raceEntry.race_instance_id,
                horseDataArray.Select(x => x.ToRaceHorseData()).ToArray(),
                raceEntry.random_seed,
                RaceDefine.RaceType.Legend,
                (GameDefine.BgSeason)raceEntry.season,
                (RaceDefine.Weather)raceEntry.weather,
                (RaceDefine.GroundCondition)raceEntry.ground_condition,
                raceStart.race_scenario
            );
            var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
            RaceDirectScene._ApplyLoadRaceInfo(loadRaceInfo);
        }

        /// <summary>
        /// チャンミレース復元機能描画。
        /// </summary>
        private void DrawLegend()
        {
            // ヘッダ。
            using(new EditorGUILayout.VerticalScope(GUI.skin.box))
            {
                _viewerId = EditorGUILayout.LongField("ViewerId", _viewerId);
                DrawGUILoadJson((json) => _legendRaceEntryJsonRoot = json, "race_entry json読込");
                DrawGUILoadJson((json) => _legendRaceStartJsonRoot = json, "race_start json読込");
                DrawSeparator();
                DrawGUIFilter(false);
            }
            DrawSeparator();

            using (var scrollView = new EditorGUILayout.ScrollViewScope(_legendScroll))
            using(new EditorGUILayout.VerticalScope())
            {
                _legendScroll = scrollView.scrollPosition;

                if (_legendRaceEntryJsonRoot != null && 
                    _legendRaceStartJsonRoot != null)
                {
                    /*
                     * レジェンドレースのAPIは
                     * legend_race/race_entry
                     * legend_race/race_start
                     * の２つに分かれている。
                     * race_entryとrace_startを紐づける情報はログの中に無いため、要素数(BigQueryJsonRoot.Array.Length)は一致するものとし、
                     * 同じインデックスのログ同士が対になるものとして処理を組んである。
                     * サーバーの久保さんによるとタイムスタンプの近いrace_entry/race_start同士が対になるはずということなので、↑の単純なインデックスでの紐づけがうまくいかない場合はタイムスタンプで対を作る。
                     */
                    if (_legendRaceEntryJsonRoot.Array.Length != _legendRaceStartJsonRoot.Array.Length)
                    {
                        EditorGUILayout.LabelField("legend_race/race_entryとlegend_race/race_startの要素数が違うため表示できません");
                        return;
                    }
                    
                    for (int i = 0; i < _legendRaceEntryJsonRoot.Array.Length; i++)
                    {
                        // 選択中のレースは赤で表示する。
                        var col = _selectedLegendRaceIndex == i ? Color.red : Color.white;
                        
                        using (new EditorUtil.ScopeBGColor(col))
                        using (new EditorGUILayout.HorizontalScope())
                        {
                            var entryJsonBody = _legendRaceEntryJsonRoot.Array[i].body;
                            var entryJsonTime = _legendRaceEntryJsonRoot.Array[i].time;
                            var startJsonBody = _legendRaceStartJsonRoot.Array[i].body;
                            var startJsonTime = _legendRaceStartJsonRoot.Array[i].time;
                            
                            // jsonからレスポンスオブジェクトをデシリアライズ。
                            var entryRes = JsonUtility.FromJson(entryJsonBody, typeof(LegendRaceRaceEntryResponseDummy.CommonResponse)) as LegendRaceRaceEntryResponseDummy.CommonResponse;
                            var startRes = JsonUtility.FromJson(startJsonBody, typeof(LegendRaceRaceStartResponseDummy.CommonResponse)) as LegendRaceRaceStartResponseDummy.CommonResponse;
                            var raceName = GetRaceName(entryRes.race_instance_id);

                            if (!FilterRaceName(raceName))
                            {
                                continue;
                            }
                            if (!FilterCharaName(entryRes.race_horse_data_array))
                            {
                                continue;
                            }

                            using (new EditorGUILayout.HorizontalScope())
                            {
                                // 選択ボタン押したらRaceSimulatorToolに読み込ませる。
                                if (GUILayout.Button((i+1).ToString(), GUILayout.Width(50)))
                                {
                                    SelectLegendRaceInfo(entryRes, startRes, i);
                                    CreateLegendRaceInfo(_selectedLegendRaceEntryResponse, _selectedLegendRaceStartResponse);
                                }

                                if (GUILayout.Button($"json出力", GUILayout.Width(OUTPUT_JSON_WIDTH)))
                                {
                                    SelectLegendRaceInfo(entryRes, startRes, i);
                                    OutputLegendRaceInfoJson();
                                }
                            }
                            
                            // レスポンスのタイムスタンプ。
                            EditorGUILayout.LabelField(entryJsonTime + " | " + startJsonTime, GUILayout.Width(280));

                            // レース名。
                            EditorGUILayout.LabelField(raceName, GUILayout.Width(200));
                            
                            // ViewerIdのユーザーのキャラ表示。
                            if (_viewerId != VIEWER_ID_NULL)
                            {
                                var charaNameStr = GenerateCharaNameStr(entryRes.race_horse_data_array, _viewerId);
                                EditorGUILayout.LabelField(charaNameStr);
                            }
                        }                        
                    }
                }
            }
        }
        #endregion
    }
}

#endif
