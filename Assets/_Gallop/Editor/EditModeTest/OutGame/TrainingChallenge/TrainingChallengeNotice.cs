#if CYG_DEBUG && UNITY_EDITOR

using System.Collections.Generic;
using NUnit.Framework;

namespace Gallop
{
    using TrainingChallenge;

    namespace Test
    {
        using Master = MasterTrainingChallengeMaster.TrainingChallengeMaster;

        /// <summary>
        /// <see cref="Gallop.TrainingChallenge.TrainingChallengeNoticeControl"/>のテスト
        /// 検証のメソッドをテストする
        /// </summary>
        public class TrainingChallengeTest
        {
            private static readonly int ResultNone = (int)TrainingChallenge.TrainingChallengeDefine.ResultType.None;
            private static readonly int ResultGood = (int)TrainingChallenge.TrainingChallengeDefine.ResultType.Good;
            private static readonly int ResultGreat = (int)TrainingChallenge.TrainingChallengeDefine.ResultType.Great;
            private static readonly int ResultExcellent = (int)TrainingChallenge.TrainingChallengeDefine.ResultType.Excellent;

            private static readonly int MasterID1 = 1;
            private static readonly int MasterID2 = 2;
            private static readonly int MasterID3 = 3;
            private static readonly int MasterID4 = 4;
            private static readonly int MasterIDEx = 5;
            private static readonly int MasterIDFree = 6;

            // マスタ
            private static Master 全試験があるマスタ = new Master(examId1: MasterID1, examId2: MasterID2, examId3: MasterID3, examId4: MasterID4, exExamId: MasterIDEx, freeExamId: MasterIDFree);
            private static Master ID4の試験がないマスタ = new Master(examId1: MasterID1, examId2: MasterID2, examId3: MasterID3, examId4: 0, exExamId: MasterIDEx, freeExamId: MasterIDFree);
            private static Master ID3以上の試験がないマスタ = new Master(examId1: MasterID1, examId2: MasterID2, examId3: 0, examId4: 0, exExamId: MasterIDEx, freeExamId: MasterIDFree);
            private static Master ID2以上の試験がないマスタ = new Master(examId1: MasterID1, examId2: 0, examId3: 0, examId4: 0, exExamId: MasterIDEx, freeExamId: MasterIDFree);

            // 試験結果情報
            private static TrainingChallengeExamInfo[] 全て可以上 = CreateExamInfo(ResultGood, ResultGreat, ResultExcellent, ResultGood, ResultGreat);
            private static TrainingChallengeExamInfo[] 全て可以上_ID4試験ない = CreateExamInfo(ResultGood, ResultGreat, ResultExcellent, -1, ResultGreat);
            private static TrainingChallengeExamInfo[] 全て可以上_ID3試験ない = CreateExamInfo(ResultGood, ResultGreat, -1, -1, ResultGreat);
            private static TrainingChallengeExamInfo[] 全て可以上_ID2試験ない = CreateExamInfo(ResultGood, -1, -1, -1, ResultGreat);

            private static TrainingChallengeExamInfo[] ID4まで達成 = CreateExamInfo(ResultGood, ResultGreat, ResultExcellent, ResultGood, ResultNone);

            private static TrainingChallengeExamInfo[] ID3まで達成 = CreateExamInfo(ResultGood, ResultGreat, ResultExcellent, ResultNone, ResultNone);
            private static TrainingChallengeExamInfo[] ID3まで達成_ID4試験ない = CreateExamInfo(ResultGood, ResultGreat, ResultExcellent, -1, ResultNone);

            private static TrainingChallengeExamInfo[] ID2まで達成 = CreateExamInfo(ResultGood, ResultGreat, ResultNone, ResultNone, ResultNone);
            private static TrainingChallengeExamInfo[] ID2まで達成_ID4試験ない = CreateExamInfo(ResultGood, ResultGreat, ResultNone, -1, ResultNone);
            private static TrainingChallengeExamInfo[] ID2まで達成_ID3試験ない = CreateExamInfo(ResultGood, ResultGreat, -1, -1, ResultNone);

            private static TrainingChallengeExamInfo[] ID1まで達成 = CreateExamInfo(ResultGood, ResultNone, ResultNone, ResultNone, ResultNone);
            private static TrainingChallengeExamInfo[] ID1まで達成_ID4試験ない = CreateExamInfo(ResultGood, ResultNone, ResultNone, -1, ResultNone);
            private static TrainingChallengeExamInfo[] ID1まで達成_ID2試験ない = CreateExamInfo(ResultGood, ResultNone, -1, -1, ResultNone);
            private static TrainingChallengeExamInfo[] ID1まで達成_ID3試験ない = CreateExamInfo(ResultGood, -1, -1, -1, ResultNone);

            private static TrainingChallengeExamInfo[] 全て未達成 = CreateExamInfo(ResultNone, ResultNone, ResultNone, ResultNone, ResultNone);
            private static TrainingChallengeExamInfo[] 全て未達成_ID4試験ない = CreateExamInfo(ResultNone, ResultNone, ResultNone, -1, ResultNone);
            private static TrainingChallengeExamInfo[] 全て未達成_ID3試験ない = CreateExamInfo(ResultNone, ResultNone, -1, -1, ResultNone);
            private static TrainingChallengeExamInfo[] 全て未達成_ID2試験ない = CreateExamInfo(ResultNone, -1, -1, -1, ResultNone);

            
            
            /// <summary>
            /// 試験情報リストを作成するヘルパクラス
            /// </summary>
            private static TrainingChallengeExamInfo[] CreateExamInfo(int result1, int result2, int result3, int result4, int resultEx)
            {
                return new[]
                    {
                        result1 >= 0 ? new TrainingChallengeExamInfo { exam_id = MasterID1, result_type = result1 } : null,
                        result2 >= 0 ? new TrainingChallengeExamInfo { exam_id = MasterID2, result_type = result2 } : null,
                        result3 >= 0 ? new TrainingChallengeExamInfo { exam_id = MasterID3, result_type = result3 } : null,
                        result4 >= 0 ? new TrainingChallengeExamInfo { exam_id = MasterID4, result_type = result4 } : null,
                        resultEx >= 0 ? new TrainingChallengeExamInfo { exam_id = MasterIDEx, result_type = resultEx } : null,
                    };
            }

                
            /// <summary>
            /// 保存しているIDがフリーIDの時は必ず通知はなし
            /// 全部見ると数が多すぎるので大体のテストケースを見る
            /// </summary>
            private static IEnumerable<TestCaseData> 保存IDがフリーIDの時の大体のテストケース
            {
                get
                {
                    yield return new TestCaseData(全て可以上, 全試験があるマスタ).SetName("全て可以上_全試験があるマスタ");
                    yield return new TestCaseData(全て未達成, ID4の試験がないマスタ).SetName("全て未達成_ID4の試験がないマスタ");
                    yield return new TestCaseData(ID1まで達成, ID2以上の試験がないマスタ).SetName("ID1まで達成_ID2以上の試験がないマスタ");
                    yield return new TestCaseData(ID2まで達成, ID3以上の試験がないマスタ).SetName("ID2まで達成_ID3以上の試験がないマスタ");
                    yield return new TestCaseData(ID3まで達成, ID4の試験がないマスタ).SetName("ID3まで達成_ID4の試験がないマスタ");
                    yield return new TestCaseData(ID4まで達成, 全試験があるマスタ).SetName("ID4まで達成_全試験があるマスタ");
                }
            }

            [TestCaseSource(nameof(保存IDがフリーIDの時の大体のテストケース))]
            public void 保存されている中断IDがフリーIDの時は通知無し(TrainingChallengeExamInfo[] infos, Master master)
            {
                Assert.False(TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, MasterIDFree).need);
            }


            /// <summary>
            /// 保存しているIDが０の時は未達成以外必ず通知あり
            /// 全部見ると数が多すぎるので大体のテストケースを見る
            /// </summary>
            private static IEnumerable<TestCaseData> 保存IDが0の時の大体のテストケース
            {
                get
                {
                    yield return new TestCaseData(全て可以上, 全試験があるマスタ).SetName("全て可以上_全試験があるマスタ").Returns(MasterIDFree);
                    yield return new TestCaseData(ID1まで達成, ID2以上の試験がないマスタ).SetName("ID1まで達成_ID2以上の試験がないマスタ").Returns(MasterIDEx);
                    yield return new TestCaseData(ID2まで達成, ID3以上の試験がないマスタ).SetName("ID2まで達成_ID3以上の試験がないマスタ").Returns(MasterIDEx);
                    yield return new TestCaseData(ID3まで達成, ID4の試験がないマスタ).SetName("ID3まで達成_ID4の試験がないマスタ").Returns(MasterIDEx);
                    yield return new TestCaseData(ID4まで達成, 全試験があるマスタ).SetName("ID4まで達成_全試験があるマスタ").Returns(MasterIDEx);
                }
            }
            [TestCaseSource(nameof(保存IDが0の時の大体のテストケース))]
            public int 保存されている中断IDが０の時は未達成以外通知する(TrainingChallengeExamInfo[] infos, Master master)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0);
                Assert.True(result.need);

                return result.examId;
            }


            /// <summary>
            /// 保存しているIDが０の時で全て未達成の場合は必ず通知なし
            /// </summary>
            private static IEnumerable<TestCaseData> 保存IDが0の時で全未達成時のテストケース
            {
                get
                {
                    yield return new TestCaseData(全て未達成, 全試験があるマスタ).SetName("全て未達成_全試験があるマスタ");
                    yield return new TestCaseData(全て未達成_ID4試験ない, ID4の試験がないマスタ).SetName("全て未達成ID4試験ない_ID4の試験がないマスタ");
                    yield return new TestCaseData(全て未達成_ID3試験ない, ID3以上の試験がないマスタ).SetName("全て未達成ID3試験ない_ID3以上の試験がないマスタ");
                    yield return new TestCaseData(全て未達成_ID2試験ない, ID2以上の試験がないマスタ).SetName("全て未達成ID2試験ない_ID2以上の試験がないマスタ");
                }
            }
            [TestCaseSource(nameof(保存IDが0の時で全未達成時のテストケース))]
            public void 保存されている中断IDが０の時でも全未達成は通知なし(TrainingChallengeExamInfo[] infos, Master master)
            {
                Assert.False(TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0).need);
            }


            /// <summary>
            /// フリーID
            /// </summary>
            private static IEnumerable<TestCaseData> 全クリアテストケース
            {
                get
                {
                    yield return new TestCaseData(全て可以上, 全試験があるマスタ, MasterIDFree).SetName("全て可以上_全試験があるマスタ");
                    yield return new TestCaseData(全て可以上_ID4試験ない, ID4の試験がないマスタ, MasterIDFree).SetName("全て可以上ID4試験ない_ID4の試験がないマスタ");
                    yield return new TestCaseData(全て可以上_ID3試験ない, ID3以上の試験がないマスタ, MasterIDFree).SetName("全て可以上ID3試験ない_ID3以上の試験がないマスタ");
                    yield return new TestCaseData(全て可以上_ID2試験ない, ID2以上の試験がないマスタ, MasterIDFree).SetName("全て可以上ID2試験ない_ID2以上の試験がないマスタ");
                }
            }

            [TestCaseSource(nameof(全クリアテストケース))]
            public void 全クリア状態でフリーIDが保存されていない時は通知が必要(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0);

                Assert.True(result.need, "通知が必要");
                Assert.AreEqual(expected, result.examId, "期待したIDが返ってくるはず");
            }
            [TestCaseSource(nameof(全クリアテストケース))]
            public void 全クリア状態でフリーIDが保存されている時は通知が必要ない(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, MasterIDFree);

                Assert.False(result.need, "通知が必要ない");
                Assert.AreEqual(0, result.examId, "Idは何も返ってこない");
            }


            /// <summary>
            /// エクストラID
            /// </summary>
            private static IEnumerable<TestCaseData> ID4までクリアテストケース
            {
                get
                {
                    yield return new TestCaseData(ID4まで達成, 全試験があるマスタ, MasterIDEx).SetName("ID4まで達成_全試験があるマスタ");
                }
            }

            [TestCaseSource(nameof(ID4までクリアテストケース))]
            public void ID4までクリア状態でエクストラIDが保存されていない時は通知が必要(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0);

                Assert.True(result.need, "通知が必要");
                Assert.AreEqual(expected, result.examId, "期待したIDが返ってくるはず");
            }
            [TestCaseSource(nameof(ID4までクリアテストケース))]
            public void ID4までクリア状態でエクストラIDが保存されている時は通知が必要ない(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, expected);

                Assert.False(result.need, "通知が必要ない");
                Assert.AreEqual(0, result.examId, "Idは何も返ってこない");
            }


            /// <summary>
            /// ID4
            /// </summary>
            private static IEnumerable<TestCaseData> ID3までクリアテストケース
            {
                get
                {
                    yield return new TestCaseData(ID3まで達成, 全試験があるマスタ, MasterID4).SetName("ID3まで達成_全試験があるマスタ");
                    yield return new TestCaseData(ID3まで達成_ID4試験ない, ID4の試験がないマスタ, MasterIDEx).SetName("ID3まで達成ID4試験ない_ID4の試験がないマスタ");
                }
            }

            [TestCaseSource(nameof(ID3までクリアテストケース))]
            public void ID3までクリア状態でID4が保存されていない時は通知が必要(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0);

                Assert.True(result.need, "通知が必要");
                Assert.AreEqual(expected, result.examId, "期待したIDが返ってくるはず");
            }
            [TestCaseSource(nameof(ID3までクリアテストケース))]
            public void ID3までクリア状態でID4が保存されている時は通知が必要ない(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, expected);

                Assert.False(result.need, "通知が必要ない");
                Assert.AreEqual(0, result.examId, "Idは何も返ってこない");
            }


            /// <summary>
            /// ID3
            /// </summary>
            private static IEnumerable<TestCaseData> ID2までクリアテストケース
            {
                get
                {
                    yield return new TestCaseData(ID2まで達成, 全試験があるマスタ, MasterID3).SetName("ID2まで達成_全試験があるマスタ");
                    yield return new TestCaseData(ID2まで達成_ID4試験ない, ID4の試験がないマスタ, MasterID3).SetName("ID2まで達成ID4試験ない_ID4の試験がないマスタ");
                    yield return new TestCaseData(ID2まで達成_ID3試験ない, ID3以上の試験がないマスタ, MasterIDEx).SetName("ID2まで達成ID3試験ない_ID3以上の試験がないマスタ");
                }
            }

            [TestCaseSource(nameof(ID2までクリアテストケース))]
            public void ID2までクリア状態でID3が保存されていない時は通知が必要(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0);

                Assert.True(result.need, "通知が必要");
                Assert.AreEqual(expected, result.examId, "期待したIDが返ってくるはず");
            }
            [TestCaseSource(nameof(ID2までクリアテストケース))]
            public void ID2までクリア状態でID3が保存されている時は通知が必要ない(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, expected);

                Assert.False(result.need, "通知が必要ない");
                Assert.AreEqual(0, result.examId, "Idは何も返ってこない");
            }


            /// <summary>
            /// ID2
            /// </summary>
            private static IEnumerable<TestCaseData> ID1までクリアテストケース
            {
                get
                {
                    yield return new TestCaseData(ID1まで達成, 全試験があるマスタ, MasterID2).SetName("ID1まで達成_全試験があるマスタ");
                    yield return new TestCaseData(ID1まで達成_ID4試験ない, ID4の試験がないマスタ, MasterID2).SetName("ID1まで達成ID4試験ない_ID4の試験がないマスタ");
                    yield return new TestCaseData(ID1まで達成_ID3試験ない, ID3以上の試験がないマスタ, MasterID2).SetName("ID1まで達成ID3試験ない_ID3以上の試験がないマスタ");
                    yield return new TestCaseData(ID1まで達成_ID2試験ない, ID2以上の試験がないマスタ, MasterIDEx).SetName("ID1まで達成ID2試験ない_ID2以上の試験がないマスタ");
                }
            }
            [TestCaseSource(nameof(ID1までクリアテストケース))]
            public void ID1までクリア状態でID2が保存されていない時は通知が必要(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, 0);

                Assert.True(result.need, "通知が必要");
                Assert.AreEqual(expected, result.examId, "期待したIDが返ってくるはず");
            }
            [TestCaseSource(nameof(ID1までクリアテストケース))]
            public void ID1までクリア状態でID2が保存されている時は通知が必要ない(TrainingChallengeExamInfo[] infos, Master master, int expected)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, expected);

                Assert.False(result.need, "通知が必要ない");
                Assert.AreEqual(0, result.examId, "Idは何も返ってこない");
            }


            private static IEnumerable<TestCaseData> 全未達成テストケース
            {
                get
                {
                    yield return new TestCaseData(全て未達成, 全試験があるマスタ).SetName("全て未達成_全試験があるマスタ");
                    yield return new TestCaseData(全て未達成_ID4試験ない, ID4の試験がないマスタ).SetName("全て未達成_ID4の試験がないマスタ");
                    yield return new TestCaseData(全て未達成_ID3試験ない, ID3以上の試験がないマスタ).SetName("全て未達成_ID3以上の試験がないマスタ");
                    yield return new TestCaseData(全て未達成_ID2試験ない, ID2以上の試験がないマスタ).SetName("全て未達成_ID2以上の試験がないマスタ");
                }
            }

            [TestCaseSource(nameof(全未達成テストケース))]
            public void 何か値が保存されていても全未達成の場合は通知はない(TrainingChallengeExamInfo[] infos, Master master)
            {
                var result = TrainingChallengeNoticeControl.NeedsShowNoticeDialog(infos, master, MasterID2);

                Assert.False(result.need, "通知が必要ない");
                Assert.AreEqual(0, result.examId, "Idは何も返ってこない");
            }
        }
    }
}

#endif
